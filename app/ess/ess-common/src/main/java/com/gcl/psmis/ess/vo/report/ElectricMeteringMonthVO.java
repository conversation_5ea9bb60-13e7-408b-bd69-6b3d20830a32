package com.gcl.psmis.ess.vo.report;

import com.gcl.psmis.framework.common.annotation.Dict;
import com.gcl.psmis.framework.common.annotation.ImportConfig;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@Data
public class ElectricMeteringMonthVO {
    @ApiModelProperty("电站编号")
    @ImportConfig(value = "电站编号")
    private String psCode;

    @ApiModelProperty("日期")
    @ImportConfig(value = "日期")
    private String recordDate;

    @ApiModelProperty("0号保安变电量")
    @ImportConfig(value = "0号保安变电量（万kWh）")
    private String zeroPowerConversion;

    @ApiModelProperty("计划停运小时")
    @ImportConfig(value = "计划停运小时（h）")
    private String planOutageHour;

    @ApiModelProperty("计划停运次数")
    @ImportConfig(value = "计划停运次数(次)")
    private String planOutageCount;

    @ApiModelProperty("状态")
    @Dict(kind = "report_calc_state", target = "stateLabel")
    private Integer state;

    @ApiModelProperty("状态翻译")
    private String stateLabel;
}
