package com.gcl.psmis.ess.vo.detail;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

@Data
public class FunctionalTrendsVO {
    @ApiModelProperty("用户总负荷")
    private List<STNumericalVO> totalUserLoadList;

    @ApiModelProperty("需量阈值")
    private List<STNumericalVO> demandThresholdList;

    @ApiModelProperty("最大需量")
    private List<STNumericalVO> maxDemandList;

    @ApiModelProperty("储能功率")
    private List<STNumericalVO> storagePowerList;
}
