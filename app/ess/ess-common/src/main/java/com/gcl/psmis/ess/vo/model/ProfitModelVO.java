package com.gcl.psmis.ess.vo.model;

import com.baomidou.mybatisplus.annotation.TableField;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.gcl.psmis.framework.common.annotation.ExportConfig;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.springframework.format.annotation.DateTimeFormat;

import java.math.BigDecimal;
import java.util.Date;

/**
* @className: ProfitModelVO
* @author: xinan.yuan
* @create: 2025/3/10 10:27
* @description: 利润模型
*/
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
@ApiModel(value = "ProfitModelVO", description = "利润模型")
public class ProfitModelVO {

    @ApiModelProperty("电站名称")
    @ExportConfig(colIndex = 0)
    private String abbName;

    @ApiModelProperty("电站编号")
    @ExportConfig(colIndex = 1)
    private String psCode;

    @ApiModelProperty("放电电量、上网电量（万kWh）")
    @ExportConfig(colIndex = 2)
    private BigDecimal monthDischarge;

    @ApiModelProperty("充电电量、下网电量（万kWh）")
    @ExportConfig(colIndex = 3)
    private BigDecimal monthCharge;

    @ApiModelProperty("上网电价（元/kWh）")
    @ExportConfig(colIndex = 4)
    private BigDecimal feedTariff ;

    @ApiModelProperty("下网电价（元/kWh）")
    @ExportConfig(colIndex = 5)
    private BigDecimal feedPrice ;

    @ApiModelProperty("上网补贴（元/kWh）")
    @ExportConfig(colIndex = 6)
    private BigDecimal internetSubsidy;

    @ApiModelProperty("放电收入（万元）")
    @ExportConfig(colIndex = 7)
    private BigDecimal dischargeIncome;

    @ApiModelProperty("补贴收入（万元）")
    @ExportConfig(colIndex = 8)
    private BigDecimal subsidyIncome;

    @ApiModelProperty("租赁收入（万元）")
    @ExportConfig(colIndex = 9)
    private BigDecimal leaseIncome;

    @ApiModelProperty("收入小计（万元）")
    @ExportConfig(colIndex = 10)
    private BigDecimal subtotalIncome;

    @ApiModelProperty("购电费（万元）")
    @ExportConfig(colIndex = 11)
    private BigDecimal eleFee;

    @ApiModelProperty("其他成本（万元）")
    @ExportConfig(colIndex = 12)
    private BigDecimal otherCosts;

    @ApiModelProperty("成本小计（万元）")
    @ExportConfig(colIndex = 13)
    private BigDecimal cost;

    @ApiModelProperty("财务费用（万元）")
    @ExportConfig(colIndex = 14)
    private BigDecimal financialExpense;

    @ApiModelProperty("利润总额（万元）")
    @ExportConfig(colIndex = 15)
    private BigDecimal grossProfit;

    @ApiModelProperty("所得税（万元）")
    @ExportConfig(colIndex = 16)
    private BigDecimal incomeTax;

    @ApiModelProperty("净利润（万元）")
    @ExportConfig(colIndex = 17)
    private BigDecimal netProfit;
}
