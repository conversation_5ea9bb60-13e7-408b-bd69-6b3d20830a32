package com.gcl.psmis.ess.vo.detail;

import com.gcl.psmis.framework.common.annotation.Dict;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;

@Data
public class PCSVO {
    @ApiModelProperty("PCS编号")
    private String pcsCode;

    @ApiModelProperty("PCS状态")
    @Dict(kind = "device_charge_state", target = "pcsStateName")
    private String pcsState;

    @ApiModelProperty("PCS状态翻译")
    private String pcsStateName;

    @ApiModelProperty("SOC %")
    private BigDecimal soc;

    @ApiModelProperty("有功功率 MW")
    private BigDecimal activePower;

    @ApiModelProperty("总容量 MWh")
    private BigDecimal totalCapacity;

    @ApiModelProperty("当日充电量 WMh")
    private BigDecimal todayCharge;

    @ApiModelProperty("当日放电量 WMh")
    private BigDecimal todayDischarge;
}
