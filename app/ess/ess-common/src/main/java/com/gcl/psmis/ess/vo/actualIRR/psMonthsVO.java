package com.gcl.psmis.ess.vo.actualIRR;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;

@Data
public class psMonthsVO {

    @ApiModelProperty("电站id")
    private Long psId;

    @ApiModelProperty("电站编码")
    private String psCode;

    @ApiModelProperty("电站运行首日")
    private Date firstDay;

    @ApiModelProperty("省Id")
    private Long provinceId;

    @ApiModelProperty("高峰月份（,分割） ")
    private String peakMonths;

    @ApiModelProperty("非高峰月份（,分割）")
    private String lowMonths;

}
