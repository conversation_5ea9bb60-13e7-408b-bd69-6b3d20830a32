package com.gcl.psmis.ess.vo.detail;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;
import java.util.List;

@Data
public class PsGeneralBusinessStateBasicVO {
    @ApiModelProperty("电站编号")
    private String psCode;

    @ApiModelProperty("电站名称")
    private String psName;

    // 基础数值
    @ApiModelProperty("额定功率(MW)")
    private BigDecimal power;

    @ApiModelProperty("装机容量(MW)")
    private BigDecimal capacity;

    @ApiModelProperty("储能单元数量")
    private Long storageUnitNum;

    @ApiModelProperty("安全运行天数")
    private Long safeRunDays;

    // 电站信息
    @ApiModelProperty("电站简称")
    private String abbName;

    @ApiModelProperty("省市区")
    private String provinceCityRegion;

    @ApiModelProperty("省id")
    private Long provinceId;

    @ApiModelProperty("市id")
    private Long cityId;

    @ApiModelProperty("区id")
    private Long regionId;

    @ApiModelProperty("详细地址")
    private String address;

    @ApiModelProperty("经纬度")
    private String lngLat;

    // 投运信息
    @ApiModelProperty("并网日期")
    @JsonFormat(pattern="yyyy/MM/dd")
    private String powerCheckTime;

    @ApiModelProperty("SOC上限")
    private BigDecimal socUpLimit;

    @ApiModelProperty("SOC下限")
    private BigDecimal socFloorLimit;

    @ApiModelProperty("电芯电压上限")
    private BigDecimal cellVoltageUpLimit;

    @ApiModelProperty("电芯电压下限")
    private BigDecimal cellVoltageFloorLimit;

    @ApiModelProperty("并网点数量")
    private Long networkNum;

    @ApiModelProperty("并网电压等级")
    private Integer voltageLevel;

    @ApiModelProperty("运行首日")
    @JsonFormat(pattern="yyyy/MM/dd")
    private String workingFirstDay;

    // 地理位置
    @ApiModelProperty("经度")
    private BigDecimal lng;

    @ApiModelProperty("纬度")
    private BigDecimal Lat;

    // 项目信息
    @ApiModelProperty("项目公司编号")
    private String projectCompanyCode;

    @ApiModelProperty("项目公司")
    private String projectCompanyName;

    @ApiModelProperty("联系人-项目公司")
    private String projectCompanyPerson;

    @ApiModelProperty("联系电话-项目公司")
    private String projectCompanyPhone;

    @ApiModelProperty("运维商")
    private String operationName;

    @ApiModelProperty("联系人-运维商")
    private String contacts;

    @ApiModelProperty("联系电话-运维商")
    private String phone;

    // 摄像头信息
    @ApiModelProperty("摄像头信息")
    List<CameraInfoVO> cameraInfoList;

    // 设备信息
    @ApiModelProperty("设备信息")
    List<DeviceInfoVO> deviceInfoList;
}
