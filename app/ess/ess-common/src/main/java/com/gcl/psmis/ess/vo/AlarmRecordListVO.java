package com.gcl.psmis.ess.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@Data
public class AlarmRecordListVO {
    @ApiModelProperty("设备类型")
    private String deviceType;

    @ApiModelProperty("设备SN")
    private String deviceSn;

    @ApiModelProperty("告警等级")
    private String alarmLevel;

    @ApiModelProperty("故障名称")
    private String alarmName;

    @ApiModelProperty("开始时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private String startTime;
}
