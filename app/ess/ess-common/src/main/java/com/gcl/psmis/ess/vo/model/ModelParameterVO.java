package com.gcl.psmis.ess.vo.model;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.gcl.psmis.framework.common.annotation.ExportConfig;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.springframework.format.annotation.DateTimeFormat;

import java.math.BigDecimal;
import java.util.Date;

/**
* @className: ModelParameterVO
* @author: xinan.yuan
* @create: 2025/3/10 10:27
* @description: 模型参数详情
*/
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
@ApiModel(value = "ModelParameterVO", description = "模型参数详情")
public class ModelParameterVO {

    @ApiModelProperty("主键id")
    private Long id;

    @ApiModelProperty("版本号")
    @ExportConfig(width = 150, value = "版本号")
    private String version;

    @ApiModelProperty("创建人")
    @ExportConfig(width = 150, value = "创建人")
    private String createByName;

    @ApiModelProperty("生效日期")
    @DateTimeFormat(pattern = "yyyy/MM/dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy/MM/dd HH:mm:ss", timezone = "GMT+8")
    @ExportConfig(width = 150, value = "生效日期")
    private Date effectiveDate;

    @ApiModelProperty("失效日期")
    @DateTimeFormat(pattern = "yyyy/MM/dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy/MM/dd HH:mm:ss", timezone = "GMT+8")
    @ExportConfig(width = 150, value = "失效日期")
    private Date expiringDate;

    @ApiModelProperty("状态")
    @ExportConfig(width = 150, value = "状态")
    private String status;

    @ApiModelProperty("人工成本(元/日)")
    @ExportConfig(width = 150, value = "人工成本(元/日)")
    private BigDecimal laborCost;

    @ApiModelProperty("折旧(元/日)")
    @ExportConfig(width = 150, value = "折旧(元/日)")
    private BigDecimal depreciationCost;

    @ApiModelProperty("保险费(元/日)")
    @ExportConfig(width = 150, value = "保险费(元/日)")
    private BigDecimal insuranceCost;

    @ApiModelProperty("运维费(元/日)")
    @ExportConfig(width = 150, value = "运维费(元/日)")
    private BigDecimal operationCost;

    @ApiModelProperty("检验检测费(元/日)")
    @ExportConfig(width = 150, value = "检验检测费(元/日)")
    private BigDecimal detectionCost;

    @ApiModelProperty("技术服务费(元/日)")
    @ExportConfig(width = 150, value = "技术服务费(元/日)")
    private BigDecimal technologyCost;

    @ApiModelProperty("财务费用(元/日)")
    @ExportConfig(width = 150, value = "财务费用(元/日)")
    private BigDecimal financeCost;

    @ApiModelProperty("电费(税率)")
    @ExportConfig(width = 150, value = "电费(税率)")
    private BigDecimal electricityRate;

    @ApiModelProperty("租赁费(税率)")
    @ExportConfig(width = 150, value = "租赁费(税率)")
    private BigDecimal leaseRate;

    @ApiModelProperty("创建时间")
    @ExportConfig(width = 150, value = "创建时间",dateFormat = "yyyy/MM/dd HH:mm:ss")
    @DateTimeFormat(pattern = "yyyy/MM/dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy/MM/dd HH:mm:ss", timezone = "GMT+8")
    private Date createTime;

    @ApiModelProperty("更新时间")
    @ExportConfig(width = 150, value = "更新时间",dateFormat = "yyyy/MM/dd HH:mm:ss")
    @DateTimeFormat(pattern = "yyyy/MM/dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy/MM/dd HH:mm:ss", timezone = "GMT+8")
    private Date updateTime;

    @ApiModelProperty("空档状态0不空档1空档")
    private Integer neutralState;

}
