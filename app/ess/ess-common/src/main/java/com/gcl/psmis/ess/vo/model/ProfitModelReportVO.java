package com.gcl.psmis.ess.vo.model;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;

/**
 * @author: wuqiong
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
@ApiModel(value = "ProfitModelReportVO", description = "利润模型报表")
public class ProfitModelReportVO {

    @ApiModelProperty("电站id")
    private Long psId;

    @ApiModelProperty("年(yyyy)")
    private String year;

    @ApiModelProperty("月(yyyymm)")
    private String month;

    @ApiModelProperty("利润模型id(t_model_parameter主键id)")
    private Long modelId;

    @ApiModelProperty("放电电量（万kWh）")
    private BigDecimal monthDischarge;

    @ApiModelProperty("充电电量（万kWh）")
    private BigDecimal monthCharge;

    @ApiModelProperty("上网电价（元/kWh）")
    private BigDecimal feedTariff;

    @ApiModelProperty("下网电价（元/kWh）")
    private BigDecimal feedPrice;

    @ApiModelProperty("上网补贴（元/kWh）")
    private BigDecimal internetSubsidy;

    @ApiModelProperty("放电收入（万元）")
    private BigDecimal dischargeIncome;

    @ApiModelProperty("补贴收入（万元）")
    private BigDecimal subsidyIncome;

    @ApiModelProperty("租赁收入（万元）")
    private BigDecimal leaseIncome;

    @ApiModelProperty("收入小计（万元）")
    private BigDecimal subtotalIncome;

    @ApiModelProperty("购电费（万元）")
    private BigDecimal eleFee;

    @ApiModelProperty("其他成本（万元）")
    private BigDecimal otherCosts;

    @ApiModelProperty("成本小计（万元）")
    private BigDecimal cost;

    @ApiModelProperty("财务费用（万元）")
    private BigDecimal financialExpense;

    @ApiModelProperty("利润总额（万元）")
    private BigDecimal grossProfit;

    @ApiModelProperty("所得税（万元）")
    private BigDecimal incomeTax;

    @ApiModelProperty("净利润（万元）")
    private BigDecimal netProfit;
}
