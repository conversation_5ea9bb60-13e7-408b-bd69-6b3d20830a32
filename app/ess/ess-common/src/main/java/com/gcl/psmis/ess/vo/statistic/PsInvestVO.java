package com.gcl.psmis.ess.vo.statistic;

import com.gcl.psmis.framework.common.annotation.ExportConfig;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;

@Data
@ApiModel(value = "PsInvestVO", description = "查询电站排名返参")
public class PsInvestVO {

    @ApiModelProperty("电站编号")
    @ExportConfig(width = 150, value = "电站编号")
    private String psCode;

    @ApiModelProperty("电站名称")
    @ExportConfig(width = 150, value = "电站名称")
    private String psName;

    @ApiModelProperty("IRR(%)")
    @ExportConfig(width = 150, value = "IRR(%)")
    private BigDecimal irr;

    @ApiModelProperty("单位投资(元/Wh)")
    @ExportConfig(width = 200, value = "单位投资(元/Wh)")
    private BigDecimal unitInvestment;

    @ApiModelProperty("设计放电深度(%)")
    @ExportConfig(width = 150, value = "设计放电深度(%)")
    private BigDecimal designDepth;

    @ApiModelProperty("设计综合效率(%)")
    @ExportConfig(width = 150, value = "设计综合效率(%)")
    private BigDecimal designEfficiency;

}
