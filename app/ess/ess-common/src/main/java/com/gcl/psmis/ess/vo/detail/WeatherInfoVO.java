package com.gcl.psmis.ess.vo.detail;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * @className: WeatherInfoVO
 * @author: wuchenyu
 * @description:
 * @date: 2023/7/28 14:13
 * @version: 1.0
 */
@Data
@ApiModel
public class WeatherInfoVO {

    @ApiModelProperty("市")
    private String city;

    @ApiModelProperty("省")
    private String province;

    @ApiModelProperty("日期")
    private String date;

    @ApiModelProperty("星期")
    private String week;

    @ApiModelProperty("白天天气")
    private String dayweather;

    @ApiModelProperty("晚上天气")
    private String nightweather;

    @ApiModelProperty("白天温度")
    private String daytemp;

    @ApiModelProperty("晚上温度")
    private String nighttemp;

    @ApiModelProperty("白天风向")
    private String daywind;

    @ApiModelProperty("晚上风向")
    private String nightwind;

    @ApiModelProperty("白天气压")
    private String daypower;

    @ApiModelProperty("晚上气压")
    private String nightpower;

}
