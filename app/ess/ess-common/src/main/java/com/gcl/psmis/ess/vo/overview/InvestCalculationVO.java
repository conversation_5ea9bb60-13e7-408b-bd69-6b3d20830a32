package com.gcl.psmis.ess.vo.overview;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Builder;
import lombok.Data;

@Data
@Builder
@ApiModel(value = "InvestCalculationVO",description = "首页概览-投资测算")
public class InvestCalculationVO {

    @ApiModelProperty("电站编号")
    private String psCode;

    @ApiModelProperty("电站名称")
    private String psName;

    @ApiModelProperty("过会IRR(%)")
    private String ghIRR;

    @ApiModelProperty("实际IRR(%)")
    private String actualIRR;
}
