package com.gcl.psmis.ess.vo.report;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;

@Data
public class MeteringDaySumVO {

    @ApiModelProperty("储能单元下网电量")
    private Float batteryOffNetPower = 0f;

    @ApiModelProperty("储能单元上网电量 ")
    private Float batteryNetPower = 0f;

    @ApiModelProperty("上网电量")
    private Float onGridPower = 0f;

    @ApiModelProperty("下网电量 ")
    private Float offGridPower = 0f;

    @ApiModelProperty("充电时下网电量")
    private Float chargeOffGridPower = 0f;

    @ApiModelProperty("放电时上网电量")
    private Float dischargeOnGridPower = 0f;

    @ApiModelProperty("1#2#站用变电量（站用变电量）")
    private Float stationPowerConversion = 0f;

}
