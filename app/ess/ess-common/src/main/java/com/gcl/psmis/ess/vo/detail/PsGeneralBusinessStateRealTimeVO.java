package com.gcl.psmis.ess.vo.detail;

import com.gcl.psmis.framework.common.annotation.Dict;
import com.gcl.psmis.framework.common.vo.powerstation.WeatherInfoVO;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;
import java.util.List;

@Data
public class PsGeneralBusinessStateRealTimeVO {
    @ApiModelProperty("电站编号")
    private String psCode;

    @ApiModelProperty("电站名称")
    private String psName;

    // 投资测算 - 暂无数据
    @ApiModelProperty("全投资内部收益率IRR(%)")
    private String irr;

    @ApiModelProperty("设计放电深度(%)")
    private BigDecimal dischargeDepth;

    @ApiModelProperty("单位投资(元/Wh)")
    private String unitInvestment;

    @ApiModelProperty("设计综合效率(%)")
    private BigDecimal designCombinedEfficiency;

    // 收益分析 - 暂无数据
    @ApiModelProperty("租赁率(%)")
    private String leaseRate;

    @ApiModelProperty("充放电毛利(万元)")
    private BigDecimal chargeDischargeProfit;

    @ApiModelProperty("租赁预计收入(万元)")
    private BigDecimal leaseProfit;

    @ApiModelProperty("运营利润估算(万元)")
    private BigDecimal operationProfit;

    // 运行状态
    @ApiModelProperty("运行状态")
    @Dict(kind = "device_charge_state", target = "runStatusName")
    private Integer runStatus;

    @ApiModelProperty("运行状态翻译")
    private String runStatusName;

    @ApiModelProperty("剩余电量SOC(%)")
    private BigDecimal soc;

    @ApiModelProperty("累计放电量(MWh)")
    private BigDecimal dischargeCapacity;

    @ApiModelProperty("综合效率(%)")
    private BigDecimal combinedEfficiency;

    @ApiModelProperty("放电深度DOD(%)")
    private BigDecimal dod;

    @ApiModelProperty("利用小时(h)")
    private BigDecimal utilHour;

    @ApiModelProperty("负荷率(%)")
    private BigDecimal loadRate;

    @ApiModelProperty("电池转换效率(%)")
    private BigDecimal batteryConversionEfficiency;
}
