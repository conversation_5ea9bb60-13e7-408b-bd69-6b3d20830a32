package com.gcl.psmis.ess.vo.report;

import com.gcl.psmis.framework.common.annotation.Dict;
import com.gcl.psmis.framework.common.annotation.ExportConfig;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Builder;
import lombok.Data;

import java.math.BigDecimal;

@Data
@ApiModel(value = "ReportIndexDataVO",description = "储能日报、月报指标数据返参")
public class ReportIndexDataVO {

    @ApiModelProperty("指标Code")
    @Dict(kind = "report_index", target = "name")
    private String code;

    @ApiModelProperty("指标名称")
    @ExportConfig(width = 150, value = "指标名称")
    private String name;

    @ApiModelProperty("单位")
    @ExportConfig(width = 150, value = "单位")
    private String unit;

    @ApiModelProperty("当日")
    @ExportConfig(width = 150, value = "当日")
    private String day;

    @ApiModelProperty("月累计")
    @ExportConfig(width = 150, value = "月累计")
    private String month;

    @ApiModelProperty("年累计")
    @ExportConfig(width = 150, value = "年累计")
    private String year;

}
