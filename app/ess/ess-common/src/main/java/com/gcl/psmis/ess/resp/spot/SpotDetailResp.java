package com.gcl.psmis.ess.resp.spot;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 * @ClassName SpotTransactionResp
 * @description: TODO
 * @date 2025年01月07日
 * @version: 1.0
 */
@Data
public class SpotDetailResp {
    @ApiModelProperty("现货交易基本信息")
    private BaseSpotResp baseSpotResp;

    @ApiModelProperty("日前申报-充电")
    private List<DecayResp> chargingDecayResp;

    @ApiModelProperty("日前申报-放电")
    private List<DecayResp> dischargeDecayResp;

    @ApiModelProperty("日前出清")
    private List<LiquidateResp> liquidateResp;

    @ApiModelProperty("交易清算")
    private List<TransactionClearResp> transactionClearResp;
}
