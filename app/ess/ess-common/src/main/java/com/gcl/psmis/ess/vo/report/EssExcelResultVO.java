package com.gcl.psmis.ess.vo.report;

import com.gcl.psmis.framework.common.annotation.CellGetConfig;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;


@Data
@ApiModel(value = "Ess日报表、月报表Excel出参")
public class EssExcelResultVO {

    @ApiModelProperty("运行天数(总)")
    @CellGetConfig(sheetIndex = 1,rowIndex = 1,colIndex = 3)
    private Double runDays;

    @ApiModelProperty("安全天数（总）")
    @CellGetConfig(sheetIndex = 1,rowIndex = 2,colIndex = 3)
    private Double securityDays;

    @ApiModelProperty("充放电毛利-日")
    @CellGetConfig(sheetIndex = 1,rowIndex = 6,colIndex = 3)
    private Double totalProfitDay;

    @ApiModelProperty("充放电毛利-月")
    @CellGetConfig(sheetIndex = 1,rowIndex = 6,colIndex = 4)
    private Double totalProfitMonth;

    // 充放电毛利-年--自行计算
    /*    @ApiModelProperty("充放电毛利-年")
    @CellGetConfig(sheetIndex = 1,rowIndex = 6,colIndex = 5)
    private Double totalProfitYear;
    */

    @ApiModelProperty("待机期间站用电量-日")
    @CellGetConfig(sheetIndex = 1,rowIndex = 7,colIndex = 3)
    private Double standbyElectricityDay;

    @ApiModelProperty("待机期间站用电量-月")
    @CellGetConfig(sheetIndex = 1,rowIndex = 7,colIndex = 4)
    private Double standbyElectricityMonth;

    @ApiModelProperty("待机期间站用电量-年")
    @CellGetConfig(sheetIndex = 1,rowIndex = 7,colIndex = 5)
    private Double standbyElectricityYear;

    @ApiModelProperty("待机期间小时消耗站用电量(待机期间每小时站用电量)-日")
    @CellGetConfig(sheetIndex = 1,rowIndex = 8,colIndex = 3)
    private Double standbyHourElectricityDay;

    @ApiModelProperty("待机期间小时消耗站用电量(待机期间每小时站用电量)-月")
    @CellGetConfig(sheetIndex = 1,rowIndex = 8,colIndex = 4)
    private Double standbyHourElectricityMonth;

    @ApiModelProperty("待机期间小时消耗站用电量(待机期间每小时站用电量)-年")
    @CellGetConfig(sheetIndex = 1,rowIndex = 8,colIndex = 5)
    private Double standbyHourElectricityYear;

    @ApiModelProperty("充放电期间转换效率(充放电转换效率)-日")
    @CellGetConfig(sheetIndex = 1,rowIndex = 9,colIndex = 3)
    private Double efficiencyDay;

    @ApiModelProperty("充放电期间转换效率(充放电转换效率)-月")
    @CellGetConfig(sheetIndex = 1,rowIndex = 9,colIndex = 4)
    private Double efficiencyMonth;

    @ApiModelProperty("充放电期间转换效率(充放电转换效率)-年")
    @CellGetConfig(sheetIndex = 1,rowIndex = 9,colIndex = 5)
    private Double efficiencyYear;

    @ApiModelProperty("全厂综合耗电量-日")
    @CellGetConfig(sheetIndex = 1,rowIndex = 10,colIndex = 3)
    private Double totalElectricityDay;

    @ApiModelProperty("全厂综合耗电量-月")
    @CellGetConfig(sheetIndex = 1,rowIndex = 10,colIndex = 4)
    private Double totalElectricityMonth;

    @ApiModelProperty("全厂综合耗电量-年")
    @CellGetConfig(sheetIndex = 1,rowIndex = 10,colIndex = 5)
    private Double totalElectricityYear;

    @ApiModelProperty("站用电量-日")
    @CellGetConfig(sheetIndex = 1,rowIndex = 11,colIndex = 3)
    private Double siteElectricityDay;

    @ApiModelProperty("站用电量-月")
    @CellGetConfig(sheetIndex = 1,rowIndex = 11,colIndex = 4)
    private Double siteElectricityMonth;

    @ApiModelProperty("站用电量-年")
    @CellGetConfig(sheetIndex = 1,rowIndex = 11,colIndex = 5)
    private Double siteElectricityYear;

    @ApiModelProperty("电池储能损电量-日")
    @CellGetConfig(sheetIndex = 1,rowIndex = 12,colIndex = 3)
    private Double batteryLossDay;

    @ApiModelProperty("电池储能损电量-月")
    @CellGetConfig(sheetIndex = 1,rowIndex = 12,colIndex = 4)
    private Double batteryLossMonth;

    @ApiModelProperty("电池储能损电量-年")
    @CellGetConfig(sheetIndex = 1,rowIndex = 12,colIndex = 5)
    private Double batteryLossYear;

    @ApiModelProperty("变配电损耗电量-日")
    @CellGetConfig(sheetIndex = 1,rowIndex = 13,colIndex = 3)
    private Double transformerLossDay;

    @ApiModelProperty("变配电损耗电量-月")
    @CellGetConfig(sheetIndex = 1,rowIndex = 13,colIndex = 4)
    private Double transformerLossMonth;

    @ApiModelProperty("变配电损耗电量-年")
    @CellGetConfig(sheetIndex = 1,rowIndex = 13,colIndex = 5)
    private Double transformerLossYear;

    @ApiModelProperty("电池充、放电转换效率(%)-日")
    @CellGetConfig(sheetIndex = 1,rowIndex = 14,colIndex = 3)
    private Double batteryEfficiencyDay;

    @ApiModelProperty("电池充、放电转换效率(%)-月")
    @CellGetConfig(sheetIndex = 1,rowIndex = 14,colIndex = 4)
    private Double batteryEfficiencyMonth;

    @ApiModelProperty("电池充、放电转换效率(%)-年")
    @CellGetConfig(sheetIndex = 1,rowIndex = 14,colIndex = 5)
    private Double batteryEfficiencyYear;

    @ApiModelProperty("电池充、放电转换效率(%)-总计")
    @CellGetConfig(sheetIndex = 1,rowIndex = 14,colIndex = 6)
    private Double batteryEfficiencyAll;

    @ApiModelProperty("PCS储能充、放电效率(%)-日")
    @CellGetConfig(sheetIndex = 1,rowIndex = 15,colIndex = 3)
    private Double pcsEfficiencyDay;

    @ApiModelProperty("PCS储能充、放电效率(%)-月")
    @CellGetConfig(sheetIndex = 1,rowIndex = 15,colIndex = 4)
    private Double pcsEfficiencyMonth;

    @ApiModelProperty("PCS储能充、放电效率(%)-年")
    @CellGetConfig(sheetIndex = 1,rowIndex = 15,colIndex = 5)
    private Double pcsEfficiencyYear;

    @ApiModelProperty("交流侧充电效率-日")
    @CellGetConfig(sheetIndex = 1,rowIndex = 16,colIndex = 3)
    private Double acChargeEfficiencyDay;

    @ApiModelProperty("交流侧充电效率-月")
    @CellGetConfig(sheetIndex = 1,rowIndex = 16,colIndex = 4)
    private Double acChargeEfficiencyMonth;

    @ApiModelProperty("交流侧充电效率-年")
    @CellGetConfig(sheetIndex = 1,rowIndex = 16,colIndex = 5)
    private Double acChargeEfficiencyYear;

    @ApiModelProperty("充电过程转换效率-日")
    @CellGetConfig(sheetIndex = 1,rowIndex = 17,colIndex = 3)
    private Double chargeEfficiencyDay;

    @ApiModelProperty("充电过程转换效率-月")
    @CellGetConfig(sheetIndex = 1,rowIndex = 17,colIndex = 4)
    private Double chargeEfficiencyMonth;

    @ApiModelProperty("充电过程转换效率-年")
    @CellGetConfig(sheetIndex = 1,rowIndex = 17,colIndex = 5)
    private Double chargeEfficiencyYear;

    @ApiModelProperty("交流侧放电效率-日")
    @CellGetConfig(sheetIndex = 1,rowIndex = 18,colIndex = 3)
    private Double acDischargeEfficiencyDay;

    @ApiModelProperty("交流侧放电效率-月")
    @CellGetConfig(sheetIndex = 1,rowIndex = 18,colIndex = 4)
    private Double acDischargeEfficiencyMonth;

    @ApiModelProperty("交流侧放电效率-年")
    @CellGetConfig(sheetIndex = 1,rowIndex = 18,colIndex = 5)
    private Double acDischargeEfficiencyYear;

    @ApiModelProperty("放电过程转换效率-日")
    @CellGetConfig(sheetIndex = 1,rowIndex = 19,colIndex = 3)
    private Double dischargeEfficiencyDay;

    @ApiModelProperty("放电过程转换效率-月")
    @CellGetConfig(sheetIndex = 1,rowIndex = 19,colIndex = 4)
    private Double dischargeEfficiencyMonth;

    @ApiModelProperty("放电过程转换效率-年")
    @CellGetConfig(sheetIndex = 1,rowIndex = 19,colIndex = 5)
    private Double dischargeEfficiencyYear;

    @ApiModelProperty("充放电周期效率-日")
    @CellGetConfig(sheetIndex = 1,rowIndex = 20,colIndex = 3)
    private Double cycleEfficiencyDay;

    @ApiModelProperty("充放电周期效率-月")
    @CellGetConfig(sheetIndex = 1,rowIndex = 20,colIndex = 4)
    private Double cycleEfficiencyMonth;

    @ApiModelProperty("充放电周期效率-年")
    @CellGetConfig(sheetIndex = 1,rowIndex = 20,colIndex = 5)
    private Double cycleEfficiencyYear;

    @ApiModelProperty("电站负荷率-日")
    @CellGetConfig(sheetIndex = 1,rowIndex = 21,colIndex = 3)
    private Double stationLoadRateDay;

    @ApiModelProperty("电站负荷率-月")
    @CellGetConfig(sheetIndex = 1,rowIndex = 21,colIndex = 4)
    private Double stationLoadRateMonth;

    @ApiModelProperty("电站负荷率-年")
    @CellGetConfig(sheetIndex = 1,rowIndex = 21,colIndex = 5)
    private Double stationLoadRateYear;

    @ApiModelProperty("电站综合效率-日")
    @CellGetConfig(sheetIndex = 1,rowIndex = 22,colIndex = 3)
    private Double stationComprehensiveEfficiencyDay;

    @ApiModelProperty("电站综合效率-月")
    @CellGetConfig(sheetIndex = 1,rowIndex = 22,colIndex = 4)
    private Double stationComprehensiveEfficiencyMonth;

    @ApiModelProperty("电站综合效率-年")
    @CellGetConfig(sheetIndex = 1,rowIndex = 22,colIndex = 5)
    private Double stationComprehensiveEfficiencyYear;

    @ApiModelProperty("站用电率-日")
    @CellGetConfig(sheetIndex = 1,rowIndex = 23,colIndex = 3)
    private Double stationElectricityRateDay;

    @ApiModelProperty("站用电率-月")
    @CellGetConfig(sheetIndex = 1,rowIndex = 23,colIndex = 4)
    private Double stationElectricityRateMonth;

    @ApiModelProperty("站用电率-年")
    @CellGetConfig(sheetIndex = 1,rowIndex = 23,colIndex = 5)
    private Double stationElectricityRateYear;

    @ApiModelProperty("站用电率-总计")
    @CellGetConfig(sheetIndex = 1,rowIndex = 23,colIndex = 6)
    private Double stationElectricityRateAll;

    @ApiModelProperty("电池储能损耗率-日")
    @CellGetConfig(sheetIndex = 1,rowIndex = 24,colIndex = 3)
    private Double batteryLossRateDay;

    @ApiModelProperty("电池储能损耗率-月")
    @CellGetConfig(sheetIndex = 1,rowIndex = 24,colIndex = 4)
    private Double batteryLossRateMonth;

    @ApiModelProperty("电池储能损耗率-年")
    @CellGetConfig(sheetIndex = 1,rowIndex = 24,colIndex = 5)
    private Double batteryLossRateYear;

    @ApiModelProperty("变配电损耗率-日")
    @CellGetConfig(sheetIndex = 1,rowIndex = 25,colIndex = 3)
    private Double transformerLossRateDay;

    @ApiModelProperty("变配电损耗率-月")
    @CellGetConfig(sheetIndex = 1,rowIndex = 25,colIndex = 4)
    private Double transformerLossRateMonth;

    @ApiModelProperty("变配电损耗率-年")
    @CellGetConfig(sheetIndex = 1,rowIndex = 25,colIndex = 5)
    private Double transformerLossRateYear;

    @ApiModelProperty("变配电损耗率-总计")
    @CellGetConfig(sheetIndex = 1,rowIndex = 25,colIndex = 6)
    private Double transformerLossRateAll;

    @ApiModelProperty("运行利用小时数-日")
    @CellGetConfig(sheetIndex = 1,rowIndex = 26,colIndex = 3)
    private Double runningHoursDay;

    @ApiModelProperty("运行利用小时数-月")
    @CellGetConfig(sheetIndex = 1,rowIndex = 26,colIndex = 4)
    private Double runningHoursMonth;

    @ApiModelProperty("运行利用小时数-年")
    @CellGetConfig(sheetIndex = 1,rowIndex = 26,colIndex = 5)
    private Double runningHoursYear;

    @ApiModelProperty("等效利用系数-日")
    @CellGetConfig(sheetIndex = 1,rowIndex = 27,colIndex = 3)
    private Double equivalentUtilizationCoefficientDay;

    @ApiModelProperty("等效利用系数-月")
    @CellGetConfig(sheetIndex = 1,rowIndex = 27,colIndex = 4)
    private Double equivalentUtilizationCoefficientMonth;

    @ApiModelProperty("等效利用系数-年")
    @CellGetConfig(sheetIndex = 1,rowIndex = 27,colIndex = 5)
    private Double equivalentUtilizationCoefficientYear;

    @ApiModelProperty("pcs损耗-日")
    @CellGetConfig(sheetIndex = 1,rowIndex = 28,colIndex = 3)
    private Double pcsLossDay;

    @ApiModelProperty("pcs损耗-月")
    @CellGetConfig(sheetIndex = 1,rowIndex = 28,colIndex = 4)
    private Double pcsLossMonth;

    @ApiModelProperty("pcs损耗-年")
    @CellGetConfig(sheetIndex = 1,rowIndex = 28,colIndex = 5)
    private Double pcsLossYear;

    @ApiModelProperty("pcs损耗-总计")
    @CellGetConfig(sheetIndex = 1,rowIndex = 28,colIndex = 6)
    private Double pcsLossAll;

    @ApiModelProperty("并网点充电平均功率-日")
    @CellGetConfig(sheetIndex = 1,rowIndex = 29,colIndex = 3)
    private Double averageChargingPowerDay;

    @ApiModelProperty("并网点充电平均功率-月")
    @CellGetConfig(sheetIndex = 1,rowIndex = 29,colIndex = 4)
    private Double averageChargingPowerMonth;

    @ApiModelProperty("并网点充电平均功率-年")
    @CellGetConfig(sheetIndex = 1,rowIndex = 29,colIndex = 5)
    private Double averageChargingPowerYear;

    @ApiModelProperty("并网点放电平均功率-日")
    @CellGetConfig(sheetIndex = 1,rowIndex = 30,colIndex = 3)
    private Double averageDischargingPowerDay;

    @ApiModelProperty("并网点放电平均功率-月")
    @CellGetConfig(sheetIndex = 1,rowIndex = 30,colIndex = 4)
    private Double averageDischargingPowerMonth;

    @ApiModelProperty("并网点放电平均功率-年")
    @CellGetConfig(sheetIndex = 1,rowIndex = 30,colIndex = 5)
    private Double averageDischargingPowerYear;

    @ApiModelProperty("等效充放电次数-日")
    @CellGetConfig(sheetIndex = 1,rowIndex = 31,colIndex = 3)
    private Double equivalentChargeDay;

    @ApiModelProperty("等效充放电次数-月")
    @CellGetConfig(sheetIndex = 1,rowIndex = 31,colIndex = 4)
    private Double equivalentChargeMonth;

    @ApiModelProperty("等效充放电次数-年")
    @CellGetConfig(sheetIndex = 1,rowIndex = 31,colIndex = 5)
    private Double equivalentChargeYear;

    @ApiModelProperty("运行小时(充放电总时长)-日")
    @CellGetConfig(sheetIndex = 1,rowIndex = 32,colIndex = 3)
    private Double runningHoursTotalDay;

    @ApiModelProperty("运行小时(充放电总时长)-月")
    @CellGetConfig(sheetIndex = 1,rowIndex = 32,colIndex = 4)
    private Double runningHoursTotalMonth;

    @ApiModelProperty("运行小时(充放电总时长)-年")
    @CellGetConfig(sheetIndex = 1,rowIndex = 32,colIndex = 5)
    private Double runningHoursTotalYear;

    @ApiModelProperty("充电利用小时-日")
    @CellGetConfig(sheetIndex = 1,rowIndex = 33,colIndex = 3)
    private Double chargingUtilizationHoursDay;

    @ApiModelProperty("充电利用小时-月")
    @CellGetConfig(sheetIndex = 1,rowIndex = 33,colIndex = 4)
    private Double chargingUtilizationHoursMonth;

    @ApiModelProperty("充电利用小时-年")
    @CellGetConfig(sheetIndex = 1,rowIndex = 33,colIndex = 5)
    private Double chargingUtilizationHoursYear;

    @ApiModelProperty("放电利用小时-日")
    @CellGetConfig(sheetIndex = 1,rowIndex = 34,colIndex = 3)
    private Double dischargingUtilizationHoursDay;

    @ApiModelProperty("放电利用小时-月")
    @CellGetConfig(sheetIndex = 1,rowIndex = 34,colIndex = 4)
    private Double dischargingUtilizationHoursMonth;

    @ApiModelProperty("放电利用小时-年")
    @CellGetConfig(sheetIndex = 1,rowIndex = 34,colIndex = 5)
    private Double dischargingUtilizationHoursYear;

    @ApiModelProperty("利用小时-日")
    @CellGetConfig(sheetIndex = 1,rowIndex = 35,colIndex = 3)
    private Double utilizationHoursDay;

    @ApiModelProperty("利用小时-月")
    @CellGetConfig(sheetIndex = 1,rowIndex = 35,colIndex = 4)
    private Double utilizationHoursMonth;

    @ApiModelProperty("利用小时-年")
    @CellGetConfig(sheetIndex = 1,rowIndex = 35,colIndex = 5)
    private Double utilizationHoursYear;

    @ApiModelProperty("利用系数-日")
    @CellGetConfig(sheetIndex = 1,rowIndex = 36,colIndex = 3)
    private Double utilizationCoefficientDay;

    @ApiModelProperty("利用系数-月")
    @CellGetConfig(sheetIndex = 1,rowIndex = 36,colIndex = 4)
    private Double utilizationCoefficientMonth;

    @ApiModelProperty("利用系数-年")
    @CellGetConfig(sheetIndex = 1,rowIndex = 36,colIndex = 5)
    private Double utilizationCoefficientYear;

    @ApiModelProperty("利用率指数-日")
    @CellGetConfig(sheetIndex = 1,rowIndex = 37,colIndex = 3)
    private Double utilizationRateIndexDay;

    @ApiModelProperty("利用率指数-月")
    @CellGetConfig(sheetIndex = 1,rowIndex = 37,colIndex = 4)
    private Double utilizationRateIndexMonth;

    @ApiModelProperty("利用率指数-年")
    @CellGetConfig(sheetIndex = 1,rowIndex = 37,colIndex = 5)
    private Double utilizationRateIndexYear;

    @ApiModelProperty("计划停运系数-日")
    @CellGetConfig(sheetIndex = 1,rowIndex = 38,colIndex = 3)
    private Double planStopCoefficientDay;

    @ApiModelProperty("计划停运系数-月")
    @CellGetConfig(sheetIndex = 1,rowIndex = 38,colIndex = 4)
    private Double planStopCoefficientMonth;

    @ApiModelProperty("计划停运系数-年")
    @CellGetConfig(sheetIndex = 1,rowIndex = 38,colIndex = 5)
    private Double planStopCoefficientYear;

    @ApiModelProperty("非计划停运系数-日")
    @CellGetConfig(sheetIndex = 1,rowIndex = 39,colIndex = 3)
    private Double nonPlanStopCoefficientDay;

    @ApiModelProperty("非计划停运系数-月")
    @CellGetConfig(sheetIndex = 1,rowIndex = 39,colIndex = 4)
    private Double nonPlanStopCoefficientMonth;

    @ApiModelProperty("非计划停运系数-年")
    @CellGetConfig(sheetIndex = 1,rowIndex = 39,colIndex = 5)
    private Double nonPlanStopCoefficientYear;

    @ApiModelProperty("平均计划停运小时-日")
    @CellGetConfig(sheetIndex = 1,rowIndex = 40,colIndex = 3)
    private Double averagePlanStopHoursDay;

    @ApiModelProperty("平均计划停运小时-月")
    @CellGetConfig(sheetIndex = 1,rowIndex = 40,colIndex = 4)
    private Double averagePlanStopHoursMonth;

    @ApiModelProperty("平均计划停运小时-年")
    @CellGetConfig(sheetIndex = 1,rowIndex = 40,colIndex = 5)
    private Double averagePlanStopHoursYear;

    @ApiModelProperty("平均非计划停运小时-日")
    @CellGetConfig(sheetIndex = 1,rowIndex = 41,colIndex = 3)
    private Double averageNonPlanStopHoursDay;

    @ApiModelProperty("平均非计划停运小时-月")
    @CellGetConfig(sheetIndex = 1,rowIndex = 41,colIndex = 4)
    private Double averageNonPlanStopHoursMonth;

    @ApiModelProperty("平均非计划停运小时-年")
    @CellGetConfig(sheetIndex = 1,rowIndex = 41,colIndex = 5)
    private Double averageNonPlanStopHoursYear;

    @ApiModelProperty("可用小时-日")
    @CellGetConfig(sheetIndex = 1,rowIndex = 42,colIndex = 3)
    private Double availableHoursDay;

    @ApiModelProperty("可用小时-月")
    @CellGetConfig(sheetIndex = 1,rowIndex = 42,colIndex = 4)
    private Double availableHoursMonth;

    @ApiModelProperty("可用小时-年")
    @CellGetConfig(sheetIndex = 1,rowIndex = 42,colIndex = 5)
    private Double availableHoursYear;

    @ApiModelProperty("可用系数-日")
    @CellGetConfig(sheetIndex = 1,rowIndex = 43,colIndex = 3)
    private Double availableCoefficientDay;

    @ApiModelProperty("可用系数-月")
    @CellGetConfig(sheetIndex = 1,rowIndex = 43,colIndex = 4)
    private Double availableCoefficientMonth;

    @ApiModelProperty("可用系数-年")
    @CellGetConfig(sheetIndex = 1,rowIndex = 43,colIndex = 5)
    private Double availableCoefficientYear;

    @ApiModelProperty("备用小时-日")
    @CellGetConfig(sheetIndex = 1,rowIndex = 44,colIndex = 3)
    private Double standbyHoursDay;

    @ApiModelProperty("备用小时-月")
    @CellGetConfig(sheetIndex = 1,rowIndex = 44,colIndex = 4)
    private Double standbyHoursMonth;

    @ApiModelProperty("备用小时-年")
    @CellGetConfig(sheetIndex = 1,rowIndex = 44,colIndex = 5)
    private Double standbyHoursYear;

    @ApiModelProperty("备用系数-日")
    @CellGetConfig(sheetIndex = 1,rowIndex = 45,colIndex = 3)
    private Double standbyCoefficientDay;

    @ApiModelProperty("备用系数-月")
    @CellGetConfig(sheetIndex = 1,rowIndex = 45,colIndex = 4)
    private Double standbyCoefficientMonth;

    @ApiModelProperty("备用系数-年")
    @CellGetConfig(sheetIndex = 1,rowIndex = 45,colIndex = 5)
    private Double standbyCoefficientYear;

    @ApiModelProperty("统计周期小时数（统计期投运时长）-日")
    @CellGetConfig(sheetIndex = 1,rowIndex = 46,colIndex = 3)
    private Double totalCycleHoursDay;

    @ApiModelProperty("统计周期小时数（统计期投运时长）-月")
    @CellGetConfig(sheetIndex = 1,rowIndex = 46,colIndex = 4)
    private Double totalCycleHoursMonth;

    @ApiModelProperty("统计周期小时数（统计期投运时长）-年")
    @CellGetConfig(sheetIndex = 1,rowIndex = 46,colIndex = 5)
    private Double totalCycleHoursYear;

    @ApiModelProperty("统计周期内运行天数-日")
    @CellGetConfig(sheetIndex = 1,rowIndex = 47,colIndex = 3)
    private Double totalCyclePlanStopHoursDay;

    @ApiModelProperty("统计周期内运行天数-月")
    @CellGetConfig(sheetIndex = 1,rowIndex = 47,colIndex = 4)
    private Double totalCyclePlanStopHoursMonth;

    @ApiModelProperty("统计周期内运行天数-年")
    @CellGetConfig(sheetIndex = 1,rowIndex = 47,colIndex = 5)
    private Double totalCyclePlanStopHoursYear;

    @ApiModelProperty("设计日充放电小时-日")
    @CellGetConfig(sheetIndex = 1,rowIndex = 48,colIndex = 3)
    private Double designDailyChargeHoursDay;

    @ApiModelProperty("设计日充放电小时-月")
    @CellGetConfig(sheetIndex = 1,rowIndex = 48,colIndex = 4)
    private Double designDailyChargeHoursMonth;

    @ApiModelProperty("设计日充放电小时-年")
    @CellGetConfig(sheetIndex = 1,rowIndex = 48,colIndex = 5)
    private Double designDailyChargeHoursYear;

    @ApiModelProperty("非计划停运小时-日")
    @CellGetConfig(sheetIndex = 1,rowIndex = 49,colIndex = 3)
    private Double nonPlanStopHoursDay;

    @ApiModelProperty("非计划停运小时-月")
    @CellGetConfig(sheetIndex = 1,rowIndex = 49,colIndex = 4)
    private Double nonPlanStopHoursMonth;

    @ApiModelProperty("非计划停运小时-年")
    @CellGetConfig(sheetIndex = 1,rowIndex = 49,colIndex = 5)
    private Double nonPlanStopHoursYear;

    @ApiModelProperty("PCS损耗电率-日")
    @CellGetConfig(sheetIndex = 1,rowIndex = 51,colIndex = 3)
    private Double pcsLossRateDay;

    @ApiModelProperty("PCS损耗电率-月")
    @CellGetConfig(sheetIndex = 1,rowIndex = 51,colIndex = 4)
    private Double pcsLossRateMonth;

    @ApiModelProperty("PCS损耗电率-年")
    @CellGetConfig(sheetIndex = 1,rowIndex = 51,colIndex = 5)
    private Double pcsLossRateYear;

    @ApiModelProperty("PCS损耗电率-统计")
    @CellGetConfig(sheetIndex = 1,rowIndex = 51,colIndex = 6)
    private Double pcsLossRateTotal;

    @ApiModelProperty("主变损耗电率-日")
    @CellGetConfig(sheetIndex = 1,rowIndex = 52,colIndex = 3)
    private Double mainLossRateDay;

    @ApiModelProperty("主变损耗电率-月")
    @CellGetConfig(sheetIndex = 1,rowIndex = 52,colIndex = 4)
    private Double mainLossRateMonth;

    @ApiModelProperty("主变损耗电率-年")
    @CellGetConfig(sheetIndex = 1,rowIndex = 52,colIndex = 5)
    private Double mainLossRateYear;

    @ApiModelProperty("主变损耗电率-统计")
    @CellGetConfig(sheetIndex = 1,rowIndex = 52,colIndex = 6)
    private Double mainLossRateTotal;

}
