package com.gcl.psmis.ess.dto.discharge;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.math.BigDecimal;

/**
 * 充放电次数表分页VO
 */
@Builder
@AllArgsConstructor
@NoArgsConstructor
@Data
@ApiModel(value = "DischargeConfigDto")
public class DischargeConfigDto {

    @ApiModelProperty("主键")
    private Long id;

    @ApiModelProperty("年份")
    @NotBlank(message = "年份不能为空!")
    private String year;

    @ApiModelProperty("省份id")
    @NotNull(message = "省份不能为空!")
    private Long provinceId;

    @ApiModelProperty("变更月份")
    private String changeMonths;

    @ApiModelProperty("一月")
    private BigDecimal jan;

    @ApiModelProperty("二月")
    private BigDecimal feb;

    @ApiModelProperty("三月")
    private BigDecimal mar;

    @ApiModelProperty("四月")
    private BigDecimal apr;

    @ApiModelProperty("五月")
    private BigDecimal may;

    @ApiModelProperty("六月")
    private BigDecimal jun;

    @ApiModelProperty("七月")
    private BigDecimal jul;

    @ApiModelProperty("八月")
    private BigDecimal aug;

    @ApiModelProperty("九月")
    private BigDecimal sept;

    @ApiModelProperty("十月")
    private BigDecimal oct;

    @ApiModelProperty("十一月")
    private BigDecimal nov;

    @ApiModelProperty("十二月")
    private BigDecimal dec;

}
