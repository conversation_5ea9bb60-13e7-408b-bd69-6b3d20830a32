package com.gcl.psmis.ess.req.detail;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@Data
public class FunctionalTrendReq {
    @ApiModelProperty("电站编号")
    private String psCode;

    @ApiModelProperty("开始时间")
    @JsonFormat(pattern = "yyyy/MM/dd", timezone = "GMT+8")
    private String startDate;

    @ApiModelProperty("结束时间")
    @JsonFormat(pattern = "yyyy/MM/dd", timezone = "GMT+8")
    private String endDate;
}
