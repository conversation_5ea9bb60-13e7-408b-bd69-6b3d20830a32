package com.gcl.psmis;

import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.boot.context.properties.EnableConfigurationProperties;
import org.springframework.cloud.client.discovery.EnableDiscoveryClient;
import org.springframework.context.annotation.ComponentScan;
import org.springframework.context.annotation.ComponentScans;
import org.springframework.scheduling.annotation.EnableAsync;

@SpringBootApplication
@EnableDiscoveryClient
@EnableConfigurationProperties
@EnableAsync
@ComponentScans(@ComponentScan(basePackages = {"com.gcl.psmis.framework"}))
public class EssKafkaApplication {

    public static void main(String[] args) {
        SpringApplication.run(EssKafkaApplication.class, args);
    }

}
