package com.gcl.psmis.ess.config;

import com.baomidou.mybatisplus.core.handlers.MetaObjectHandler;
import com.gcl.framework.data.dto.UserDTO;
import com.gcl.framework.data.holder.CurrentUserHolder;
import org.apache.ibatis.reflection.MetaObject;
import org.springframework.stereotype.Component;

import java.util.Date;

/**
 * @className: CustomMetaObjectHandler
 * @author: xinan.yuan
 * @create: 2023/7/10 14:21
 * @description: 自定义sql字段填充器，自动填充创建修改相关字段
 */
@Component
public class CustomMetaObjectHandler implements MetaObjectHandler {

    private static final String DEL_FLAG = "delFlag";
    private static final String CREATE_TIME = "createTime";
    private static final String CREATE_EMP_NO = "createByNo";
    private static final String CREATE_EMP_NAME = "createByName";
    private static final String UPDATE_EMP_NO = "updateByNo";
    private static final String UPDATE_EMP_NAME = "updateByName";
    private static final String UPDATE_TIME = "updateTime";

    @Override
    public void insertFill(MetaObject metaObject) {
        Date date = new Date();

        this.setFieldValByName(CREATE_TIME, date, metaObject);
        this.setFieldValByName(UPDATE_TIME, date, metaObject);
        this.setFieldValByName(DEL_FLAG, 0, metaObject);

        UserDTO userDTO = CurrentUserHolder.getUserDTOThreadLocal();
        if (null != userDTO) {
            this.strictInsertFill(metaObject, CREATE_EMP_NO, String.class, userDTO.getEmpNo());
            this.strictInsertFill(metaObject, CREATE_EMP_NAME, String.class, userDTO.getRealName());
        }

    }

    @Override
    public void updateFill(MetaObject metaObject) {
        Date date = new Date();
        this.setFieldValByName(UPDATE_TIME, date, metaObject);

        UserDTO userDTO = CurrentUserHolder.getUserDTOThreadLocal();
        if (null != userDTO) {
            this.strictUpdateFill(metaObject, UPDATE_EMP_NO, String.class, userDTO.getEmpNo());
            this.strictUpdateFill(metaObject, UPDATE_EMP_NAME, String.class, userDTO.getRealName());
        }
    }
}
