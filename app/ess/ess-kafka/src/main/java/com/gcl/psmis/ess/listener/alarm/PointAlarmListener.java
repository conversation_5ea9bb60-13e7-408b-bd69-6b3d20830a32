package com.gcl.psmis.ess.listener.alarm;

import cn.hutool.core.date.TimeInterval;
import com.gcl.psmis.ess.mbg.entity.TFaultCodeEntity;
import com.gcl.psmis.ess.mbg.service.TFaultCodeService;
import com.gcl.psmis.framework.common.util.JacksonHelper;
import com.gcl.psmis.framework.kafka.msg.IotMqttMsg;
import com.gcl.psmis.framework.kafka.msg.PointAlarmMsg;
import com.gcl.psmis.framework.taos.domain.entity.IotPointAlarmStPointEntity;
import com.gcl.psmis.framework.taos.wrapper.TdWrappers;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.kafka.annotation.KafkaListener;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

/**
* @className: PointAlarmListener
* @author: xinan.yuan
* @create: 2024/12/17 10:31
* @description:
*/
@Slf4j
@Component
public class PointAlarmListener {


    @Resource(name = "redisTemplate6")
    private RedisTemplate<String, Object> redisTemplate6;

    @Autowired
    private TFaultCodeService tFaultCodeService;

    @KafkaListener(topics = "${kafka.topic.pointAlarm}", groupId = "${kafka.group-id}")
    public void pointAlarmListener(String message) {

        log.info("pointAlarmListener:开始处理 {} 报文", message);
        TimeInterval watch = new TimeInterval();
        IotMqttMsg iotMqttMsg = JacksonHelper.jsonToObject(message, IotMqttMsg.class);
        List<PointAlarmMsg> alarmMsgs = JacksonHelper.jsonToList(iotMqttMsg.getContent(), PointAlarmMsg.class);

        List<IotPointAlarmStPointEntity> pointAlarmStPointEntities =new ArrayList<>();
        for (PointAlarmMsg alarmMsg : alarmMsgs) {
            IotPointAlarmStPointEntity pointAlarm = new IotPointAlarmStPointEntity();
            BeanUtils.copyProperties(alarmMsg, pointAlarm);
            pointAlarm.setAlarmStatus(alarmMsg.getData());
            pointAlarm.setTs(alarmMsg.getStartTime());
            pointAlarm.setFactoryCode(alarmMsg.getFactoryCode());
            pointAlarm.setDeviceSn(alarmMsg.getSn());

            TFaultCodeEntity tAlarmFaultCode = tFaultCodeService.lambdaQuery().eq(TFaultCodeEntity::getFaultCode, alarmMsg.getAlarmCode()).one();
            if(tAlarmFaultCode==null){
                continue;
            }
            pointAlarm.setAlarmLevel(tAlarmFaultCode.getDefectLevel());
            pointAlarm.setAlarmName(tAlarmFaultCode.getFaultName());
            if (pointAlarm.getAlarmStatus() == 0) {
                redisTemplate6.delete(alarmMsg.alarmRedisKey());
            }
            pointAlarmStPointEntities.add(pointAlarm);
        }


        TdWrappers.lambdaUpdate(IotPointAlarmStPointEntity.class).saveBatch(pointAlarmStPointEntities);

        log.info("pointAlarmListener处理结束，耗时:[{}]ms", watch.intervalRestart());

    }

}
