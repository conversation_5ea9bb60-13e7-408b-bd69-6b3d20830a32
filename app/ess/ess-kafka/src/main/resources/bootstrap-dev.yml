spring:
  cloud:
    nacos:
      discovery:
        server-addr: http://10.10.15.120:58848
        username: nacos
        password: Gcl!@#123
      config:
        server-addr: http://10.10.15.120:58848
        username: nacos
        password: Gcl!@#123
        file-extension: yaml
        namespace: 849c87ac-4ce9-4358-84d6-be7aaef09e1c
        shared-configs:
          - data-id: common-ess-datasource-dev.yaml
            group: DEFAULT_GROUP
          - data-id: common-kafka-dev.yaml
            group: DEFAULT_GROUP
          - data-id: common-redis-dev.yaml
            group: DEFAULT_GROUP
          - data-id: common-other-dev.yaml
            group: DEFAULT_GROUP
          - data-id: common-rocketmq-dev.yaml
            group: DEFAULT_GROUP
swagger:
  enabled: true
kafka:
  group-id: GID_ESS_KAFKA_DEV
  topic:
    pointAlarm: dev_point_iot_mqtt_alarm
rocketmq:
  group-id: GID_ESS_RMQ_DEV