<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 https://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>
    <parent>
        <groupId>com.gcl.psmis.ess</groupId>
        <artifactId>ess</artifactId>
        <version>0.0.1-SNAPSHOT</version>
    </parent>
    <groupId>com.gcl</groupId>
    <artifactId>ess-kafka</artifactId>
    <version>1.0-SNAPSHOT</version>
    <name>ess-kafka</name>

    <dependencies>
        <dependency>
            <groupId>com.gcl.psmis.framework</groupId>
            <artifactId>common</artifactId>
        </dependency>
        <dependency>
            <groupId>com.gcl.psmis.ess</groupId>
            <artifactId>ess-common</artifactId>
            <version>0.0.1-SNAPSHOT</version>
        </dependency>
        <!-- 导入 redis 依赖 -->
        <dependency>
            <groupId>com.gcl.psmis.framework</groupId>
            <artifactId>redis</artifactId>
        </dependency>

        <dependency>
            <groupId>com.gcl.psmis.framework</groupId>
            <artifactId>config</artifactId>
        </dependency>
        <dependency>
            <groupId>com.gcl.psmis.framework</groupId>
            <artifactId>common</artifactId>
        </dependency>

        <dependency>
            <groupId>com.gcl.psmis.framework</groupId>
            <artifactId>taos</artifactId>
        </dependency>
        <dependency>
            <groupId>com.gcl.psmis.framework</groupId>
            <artifactId>kafka-manager</artifactId>
            <version>0.0.1-SNAPSHOT</version>
        </dependency>
        <dependency>
            <groupId>com.gcl.psmis.framework</groupId>
            <artifactId>gencode-util</artifactId>
        </dependency>
        <dependency>
            <groupId>com.gcl.psmis.framework</groupId>
            <artifactId>taos</artifactId>
        </dependency>

        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-web</artifactId>
        </dependency>
        <dependency>
            <groupId>mysql</groupId>
            <artifactId>mysql-connector-java</artifactId>
        </dependency>
        <dependency>
            <groupId>org.springframework.cloud</groupId>
            <artifactId>spring-cloud-starter-bootstrap</artifactId>
        </dependency>
        <dependency>
            <groupId>org.springframework.cloud</groupId>
            <artifactId>spring-cloud-starter-openfeign</artifactId>
        </dependency>
        <dependency>
            <groupId>org.springframework.cloud</groupId>
            <artifactId>spring-cloud-loadbalancer</artifactId>
            <version>3.1.7</version>
        </dependency>

    </dependencies>

    <build>
        <finalName>gcl-ess-kafka</finalName>
        <plugins>
            <plugin>
                <groupId>org.springframework.boot</groupId>
                <artifactId>spring-boot-maven-plugin</artifactId>
                <configuration>
                    <includeSystemScope>true</includeSystemScope>
                </configuration>
            </plugin>

        </plugins>
    </build>

</project>
