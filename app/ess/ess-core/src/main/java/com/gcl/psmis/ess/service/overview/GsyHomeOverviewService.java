package com.gcl.psmis.ess.service.overview;

import cn.hutool.core.collection.CollStreamUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.date.DateTime;
import cn.hutool.core.date.DateUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.gcl.psmis.ess.constants.enums.PointAlarmStatusEnum;
import com.gcl.psmis.ess.constants.enums.ReportTypeEnum;
import com.gcl.psmis.ess.dao.ps.PsDao;
import com.gcl.psmis.ess.dto.EssPsIotPointDTO;
import com.gcl.psmis.ess.dto.detail.EssPsBatteryDTO;
import com.gcl.psmis.ess.mbg.entity.TDayReportDetailEntity;
import com.gcl.psmis.ess.mbg.entity.TDayReportEntity;
import com.gcl.psmis.ess.mbg.entity.TElectricMeteringDayEntity;
import com.gcl.psmis.ess.mbg.entity.TEsStationEntity;
import com.gcl.psmis.ess.mbg.service.TDayReportDetailService;
import com.gcl.psmis.ess.mbg.service.TDayReportService;
import com.gcl.psmis.ess.mbg.service.TElectricMeteringDayService;
import com.gcl.psmis.ess.mbg.service.TEsStationService;
import com.gcl.psmis.ess.req.PsManageRankReq;
import com.gcl.psmis.ess.req.model.ProfitModelReq;
import com.gcl.psmis.ess.req.ps.PsEleCountReq;
import com.gcl.psmis.ess.req.ps.QueryPsListReq;
import com.gcl.psmis.ess.resp.PsEleCountResp;
import com.gcl.psmis.ess.resp.PsManageRankResp;
import com.gcl.psmis.ess.resp.ps.EssPsTotalResp;
import com.gcl.psmis.ess.resp.ps.PsListResp;
import com.gcl.psmis.ess.service.detail.DetailService;
import com.gcl.psmis.ess.service.model.ModelService;
import com.gcl.psmis.ess.service.statistic.StatisticService;
import com.gcl.psmis.ess.vo.AlarmVO;
import com.gcl.psmis.ess.vo.model.ProfitModelVO;
import com.gcl.psmis.ess.vo.overview.InvestCalculationVO;
import com.gcl.psmis.ess.vo.overview.OperateInfoVO;
import com.gcl.psmis.ess.vo.overview.ProfitEstimationVO;
import com.gcl.psmis.framework.dict.util.ExpandBeanUtils;
import com.gcl.psmis.framework.taos.domain.entity.IotPointAlarmStPointEntity;
import com.gcl.psmis.framework.taos.domain.entity.IotPointStPointEntity;
import com.gcl.psmis.framework.taos.wrapper.TdWrappers;
import jodd.util.StringUtil;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 */
@Service
@Slf4j
@RequiredArgsConstructor
public class GsyHomeOverviewService {

    private final PsDao psDao;
    private final TEsStationService esStationService;
    private final TElectricMeteringDayService entityMeteringDayService;
    private final TDayReportService tDayReportService;
    private final TDayReportDetailService tDayReportDetailService;
    private final StatisticService statisticService;
    private final ModelService modelService;

    /**
     * 运行信息
     */
    public OperateInfoVO operateInfo() {

        OperateInfoVO result = OperateInfoVO.builder().build();
        // 取所有工商业储能电站
        List<TEsStationEntity> eleList = esStationService.lambdaQuery()
                .eq(TEsStationEntity::getDelFlag, 0)
                .eq(TEsStationEntity::getType, 2)
                .isNotNull(TEsStationEntity::getCapacity)
                .list();
        // 获取昨日日报表
        if (CollUtil.isEmpty(eleList)) {
            return result;
        }
        List<String> psCodeList = CollStreamUtil.toList(eleList, TEsStationEntity::getPsCode);
        String queryDate = DateUtil.format(DateUtil.offsetDay(DateUtil.date(), -1), "yyyy/MM/dd");
        List<TDayReportEntity> dayReportList = tDayReportService.lambdaQuery()
                .eq(TDayReportEntity::getDelFlag, 0)
                .eq(TDayReportEntity::getReportDate, queryDate)
                .in(TDayReportEntity::getPsCode, psCodeList)
                .list();
        if (CollUtil.isNotEmpty(dayReportList)) {
            // 取日报表指标明细的负荷率、综合效率指标
            List<Long> mainIds = CollStreamUtil.toList(dayReportList, TDayReportEntity::getId);
            List<TDayReportDetailEntity> iotPointStPointEntityList = tDayReportDetailService.lambdaQuery()
                    .eq(TDayReportDetailEntity::getDelFlag, 0)
                    .in(TDayReportDetailEntity::getIndexCode, "ps_load_rate", "ps_comprehensive_efficient")
                    .in(TDayReportDetailEntity::getDayReportId, mainIds)
                    .isNotNull(TDayReportDetailEntity::getDay)
                    .list();
            if (CollUtil.isNotEmpty(iotPointStPointEntityList) && CollUtil.isNotEmpty(eleList)) {
                // 计算所有装机容量之和
                BigDecimal capacityCount = eleList.stream().map(TEsStationEntity::getCapacity).reduce(BigDecimal.ZERO, BigDecimal::add);
                if (capacityCount.compareTo(BigDecimal.ZERO) > 0) {
                    BigDecimal loadRate = BigDecimal.ZERO;
                    BigDecimal comEfficiency = BigDecimal.ZERO;
                    // 根据mainID和indexCode进行分组
                    Map<String, TDayReportDetailEntity> detailMap = CollStreamUtil.toMap(iotPointStPointEntityList, item -> item.getDayReportId() + item.getIndexCode(), Function.identity());

                    for (TEsStationEntity temp : eleList) {
                        String loadRateKey = temp.getId() + "ps_load_rate";
                        String comprehensiveKey = temp.getId() + "ps_comprehensive_efficient";
                        BigDecimal midLoadRate = detailMap.get(loadRateKey) == null ? BigDecimal.ZERO
                                : detailMap.get(loadRateKey).getDay().multiply(temp.getCapacity()).divide(capacityCount, 4, RoundingMode.HALF_UP);
                        BigDecimal midComprehensive = detailMap.get(comprehensiveKey) == null ? BigDecimal.ZERO
                                : detailMap.get(comprehensiveKey).getDay().multiply(temp.getCapacity()).divide(capacityCount, 4, RoundingMode.HALF_UP);
                        loadRate = loadRate.add(midLoadRate);
                        comEfficiency = comEfficiency.add(midComprehensive);
                    }
                    result.setLoadRate(loadRate.multiply(BigDecimal.valueOf(100)).setScale(2, RoundingMode.HALF_UP) + "%");
                    result.setComEfficiency(comEfficiency.multiply(BigDecimal.valueOf(100)).setScale(2, RoundingMode.HALF_UP) + "%");
                }
            }
        }
        //获取运营利润估算
        result.setProfitEstimation(this.getProfitEstimation(psCodeList));
        return result;
    }

    /**
     * 获取累计运营利润
     *
     * @return
     */
    private String getProfitEstimation(List<String> psCodeList) {
        //初始2024年1月
        Date startDate = DateUtil.parse("2024/01/31");
        int index = 0;
        BigDecimal result = BigDecimal.ZERO;
        while (true) {
            DateTime endDate = DateUtil.offsetMonth(startDate, index);
            if (DateUtil.compare(DateUtil.endOfMonth(DateUtil.date()), endDate) < 0) {
                //当前时间比结束时间小，结束循环
                break;
            }
            List<ProfitModelVO> vos = modelService.getProfitModelPage2(ProfitModelReq.builder()
                    .chooseMonth(endDate)
                    .psCodes(psCodeList)
                    .start(DateUtil.beginOfMonth(endDate))
                    .end(DateUtil.endOfMonth(endDate))
                    .build());
            if (CollUtil.isNotEmpty(vos)) {
                BigDecimal reduce = vos.stream().map(ProfitModelVO::getGrossProfit).reduce(BigDecimal.ZERO, BigDecimal::add);
                result = result.add(reduce);
            }
            index++;
        }
        return result.setScale(2, RoundingMode.HALF_UP).toPlainString();
    }

    /**
     * 投资测算
     */
    public List<InvestCalculationVO> investCalculation() {

        DateTime lastDate = DateUtil.offsetMonth(new Date(), -12);
        return psDao.getGsyPsInvestCalculation(String.valueOf(DateUtil.year(lastDate)));
    }

    /**
     * 运营利润估算
     */
    public List<ProfitEstimationVO> profitEstimation() {
        // 查询所有的工商业储能电站
        List<TEsStationEntity> eleList = esStationService.lambdaQuery()
                .eq(TEsStationEntity::getDelFlag, 0)
                .eq(TEsStationEntity::getType, 2)
                .isNotNull(TEsStationEntity::getCapacity)
                .list();
        if (CollUtil.isEmpty(eleList)) {
            return new ArrayList<>();
        }
        // 获取电站编码
        List<String> psCodeList = CollStreamUtil.toList(eleList, TEsStationEntity::getPsCode);
        //获取当前月
        int month = DateUtil.month(new Date()) + 1;
        List<ProfitEstimationVO> result = new ArrayList<>();
        for (int i = 1; i <= 12; i++) {
            //构造参数
            if (i > month) {
                //大于当前月直接返回
                break;
            }
            DateTime choseDate = DateUtil.offsetMonth(new Date(), i - month);
            List<ProfitModelVO> vos = modelService.getProfitModelPage2(ProfitModelReq.builder()
                    .chooseMonth(choseDate)
                    .psCodes(psCodeList)
                    .start(DateUtil.beginOfMonth(choseDate))
                    .end(DateUtil.endOfMonth(choseDate))
                    .build());
            BigDecimal reduce = vos.stream().map(ProfitModelVO::getGrossProfit).reduce(BigDecimal.ZERO, BigDecimal::add).setScale(2, RoundingMode.HALF_UP);
            ProfitEstimationVO build = ProfitEstimationVO.builder()
                    .monthName(String.valueOf(i))
                    .data(reduce)
                    .build();
            result.add(build);
        }
        result.forEach(item -> item.setMonthName(item.getMonthName() + "月"));
        return result;
    }

    /**
     * 电站管理排名
     */
    public List<PsManageRankResp> psManageRank(PsManageRankReq psManageRankReq) {
        List<PsManageRankResp> result = new ArrayList<>();
        if ("2".equals(psManageRankReq.getSearchType())) {
            return this.getLeaseRate(psManageRankReq);
        }
        // 当月当年都是当前月份数据  包含月 年报表累计
        psManageRankReq.setMonthStartDate(DateUtil.format(DateUtil.beginOfMonth(DateUtil.date()), "yyyy/MM/dd"));
        psManageRankReq.setMonthEndDate(DateUtil.format(DateUtil.endOfMonth(DateUtil.date()), "yyyy/MM/dd"));
        if (StringUtil.isBlank(ReportTypeEnum.getDescByCode(psManageRankReq.getSearchType()))) {
            return result;
        }
        psManageRankReq.setSearchCode(ReportTypeEnum.getDescByCode(psManageRankReq.getSearchType()));
        result = psDao.getMonthPsRank(psManageRankReq);
        return result;
    }

    /**
     * 获取租赁率
     *
     * @param psManageRankReq 参数
     * @return PsManageRankResp
     */
    private List<PsManageRankResp> getLeaseRate(PsManageRankReq psManageRankReq) {
        List<PsManageRankResp> result = new ArrayList<>();
        //租赁率
        Integer listType = null;
        String chosenDate = "";
        if (psManageRankReq.getTimeType() == 1) {
            //当月
            listType = 2;
            chosenDate = DateUtil.format(DateUtil.date(), "yyyy/MM");

        } else {
            //当年
            listType = 3;
            chosenDate = DateUtil.format(DateUtil.date(), "yyyy");
        }
        //查询独立储能电站数据
        List<TEsStationEntity> stations = esStationService.lambdaQuery()
                .eq(TEsStationEntity::getDelFlag, 0)
                .eq(TEsStationEntity::getType, 1)
                .isNotNull(TEsStationEntity::getCapacity)
                .list();
        if (CollUtil.isEmpty(stations)) {
            return result;
        }
        Set<Long> psIds = CollStreamUtil.toSet(stations, TEsStationEntity::getId);
        //获取租赁率数据
        Map<Long, BigDecimal> leaseRate = statisticService.getLeaseRate(listType, psIds, chosenDate);
        for (TEsStationEntity station : stations) {
            result.add(PsManageRankResp.builder()
                    .data(leaseRate.getOrDefault(station.getId(), BigDecimal.ZERO))
                    .psName(station.getAbbName())
                    .psCode(station.getPsCode())
                    .build());
        }
        //倒序返回
        return result.stream().sorted(Comparator.comparing(PsManageRankResp::getData).reversed()).collect(Collectors.toList());
    }

    /**
     * 充放电统计
     */
    public PsEleCountResp psEleCount(PsEleCountReq psEleCountReq) {
        PsEleCountResp psEleCountResp = new PsEleCountResp();
        // 获取所有工商业储能电站ps_code
        List<TEsStationEntity> list = esStationService.lambdaQuery()
                .select(TEsStationEntity::getPsCode, TEsStationEntity::getCapacity, TEsStationEntity::getPower)
                .eq(TEsStationEntity::getDelFlag, 0)
                .eq(TEsStationEntity::getType, 2)
                .list();
        if (CollectionUtils.isEmpty(list)) {
            return psEleCountResp;
        }
        // 获取总的装机容量
        BigDecimal capacityCount = list.stream().map(TEsStationEntity::getCapacity).reduce(BigDecimal.ZERO, BigDecimal::add);
        // 获取电站总功率
        BigDecimal powerCount = list.stream().map(TEsStationEntity::getPower).reduce(BigDecimal.ZERO, BigDecimal::add);

        List<String> psCodes = list.stream().map(TEsStationEntity::getPsCode).collect(Collectors.toList());
        // ----------------------------------------月----------------------------------------
        String startMonthTime = DateUtil.format(DateUtil.beginOfMonth(DateUtil.date()), "yyyy/MM/dd");
        String endMonthTime = DateUtil.format(DateUtil.endOfMonth(DateUtil.date()), "yyyy/MM/dd");
        psEleCountReq.setStartTime(startMonthTime);
        psEleCountReq.setEndTime(endMonthTime);
        LambdaQueryWrapper<TElectricMeteringDayEntity> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.in(TElectricMeteringDayEntity::getPsCode, psCodes);
        queryWrapper.between(TElectricMeteringDayEntity::getRecordDate, psEleCountReq.getStartTime(), psEleCountReq.getEndTime());
        List<TElectricMeteringDayEntity> eleMonth = entityMeteringDayService.list(queryWrapper);
        // 上网电量
        BigDecimal onGridPowers = eleMonth.stream().filter(s -> Objects.nonNull(s.getOnGridPower())).map(TElectricMeteringDayEntity::getOnGridPower).reduce(BigDecimal.ZERO, BigDecimal::add).divide(BigDecimal.valueOf(10));
        // 下网电量
        BigDecimal offGridPowers = eleMonth.stream().filter(s -> Objects.nonNull(s.getOffGridPower())).map(TElectricMeteringDayEntity::getOffGridPower).reduce(BigDecimal.ZERO, BigDecimal::add).divide(BigDecimal.valueOf(10));
        // 统计周期天
        Integer monthDay = DateUtil.dayOfMonth(DateUtil.date());
        // 电站负荷率=上网电量/统计周期天数*电站容量*100%
        BigDecimal psLoadRate = onGridPowers.divide(BigDecimal.valueOf(monthDay), 4, RoundingMode.HALF_UP).multiply(capacityCount).multiply(BigDecimal.valueOf(100)).setScale(2, RoundingMode.HALF_UP);
        psEleCountResp.setPsLoadRateMonth(psLoadRate.toString());
        // 电站综合效率=上网电量/下网电量*100%
        if (offGridPowers.compareTo(BigDecimal.ZERO) != 0) {
            BigDecimal sumEle = onGridPowers.divide(offGridPowers, 4, RoundingMode.HALF_UP).multiply(BigDecimal.valueOf(100)).setScale(2, RoundingMode.HALF_UP);
            psEleCountResp.setPsComprehensiveEfficientMonth(sumEle.toString());
        }
        // sum(各堆的当日堆内累计放电电量)  电站月放电量	ST8215Yc
        psEleCountReq.setFaultCode("ST8215Yc");
        psEleCountReq.setStartTime(DateUtil.format(DateUtil.beginOfMonth(DateUtil.date()), "yyyy-MM-dd HH:mm:ss"));
        psEleCountReq.setEndTime(DateUtil.format(DateUtil.endOfMonth(DateUtil.date()), "yyyy-MM-dd HH:mm:ss"));
        psEleCountReq.setPsCodes(psCodes);
        String countEleMonth = psDao.getTdEle(psEleCountReq);
        psEleCountResp.setEleCountMonth(countEleMonth);
        // 利用小时=充电利用小时+放电利用小时    充电利用小时=电池充电量/额定功率  放电利用小时=电池放电量/额定功率
        this.avgMonthHours(psEleCountResp, powerCount, psCodes);

        // ----------------------------------------年-------------------------------------------------
        String startTime = DateUtil.format(DateUtil.beginOfYear(DateUtil.date()), "yyyy/MM/dd HH:mm:ss");
        String endTime = DateUtil.format(DateUtil.endOfYear(DateUtil.date()), "yyyy/MM/dd HH:mm:ss");
        psEleCountReq.setStartTime(startTime);
        psEleCountReq.setEndTime(endTime);
        Integer year = DateUtil.year(DateUtil.date());
        psEleCountReq.setYear(year.toString());
        // 电量抄表
        LambdaQueryWrapper<TElectricMeteringDayEntity> queryWrapperYear = new LambdaQueryWrapper<>();
        queryWrapperYear.in(TElectricMeteringDayEntity::getPsCode, psCodes);
        queryWrapperYear.like(TElectricMeteringDayEntity::getRecordDate, psEleCountReq.getYear());
        List<TElectricMeteringDayEntity> eleYear = entityMeteringDayService.list(queryWrapperYear);
        // 上网电量（wkwh）
        BigDecimal onGridPowersYear = eleYear.stream().filter(s -> Objects.nonNull(s.getOnGridPower())).map(TElectricMeteringDayEntity::getOnGridPower).reduce(BigDecimal.ZERO, BigDecimal::add).divide(BigDecimal.valueOf(10));
        // 下网电量
        BigDecimal offGridPowersYear = eleYear.stream().filter(s -> Objects.nonNull(s.getOffGridPower())).map(TElectricMeteringDayEntity::getOffGridPower).reduce(BigDecimal.ZERO, BigDecimal::add).divide(BigDecimal.valueOf(10));
        // 电站负荷率=上网电量/统计周期天数*电站容量*100%
        BigDecimal psLoadRateYear = onGridPowersYear.divide(BigDecimal.valueOf(monthDay), 4, RoundingMode.HALF_UP).multiply(capacityCount).multiply(BigDecimal.valueOf(100)).setScale(2, RoundingMode.HALF_UP);
        psEleCountResp.setPsLoadRateYear(psLoadRateYear.toString());
        // 电站综合效率=上网电量/下网电量*100%
        if (offGridPowers.compareTo(BigDecimal.ZERO) != 0) {

            BigDecimal sumEleYear = offGridPowersYear.divide(offGridPowers, 4, RoundingMode.HALF_UP).multiply(BigDecimal.valueOf(100)).setScale(2, RoundingMode.HALF_UP);
            psEleCountResp.setPsComprehensiveEfficientYear(sumEleYear.toString());
        }
        // sum(各堆的当日堆内累计放电电量)  电站月放电量	ST8215Yc
        psEleCountReq.setFaultCode("ST8215Yc");
        psEleCountReq.setStartTime(DateUtil.format(DateUtil.beginOfYear(DateUtil.date()), "yyyy-MM-dd HH:mm:ss"));
        psEleCountReq.setEndTime(DateUtil.format(DateUtil.endOfYear(DateUtil.date()), "yyyy-MM-dd HH:mm:ss"));
        psEleCountReq.setPsCodes(psCodes);
        String countEleYear = psDao.getTdEle(psEleCountReq);
        psEleCountResp.setEleCountYear(countEleYear);
        // 利用小时=充电利用小时+放电利用小时    充电利用小时=电池充电量/额定功率  放电利用小时=电池放电量/额定功率
        this.avgYearHours(psEleCountReq, psEleCountResp, powerCount, psCodes);
        return psEleCountResp;
    }

    private void avgYearHours(PsEleCountReq psEleCountReq, PsEleCountResp psEleCountResp, BigDecimal powerCount, List<String> psCodes) {
        List<EssPsBatteryDTO> essPsBatteryDTOList = TdWrappers.lambdaQuery(IotPointStPointEntity.class)
                .in(IotPointStPointEntity::getPsCode, psCodes)
                .gt(IotPointStPointEntity::getTs, psEleCountReq.getStartTime())
                .lt(IotPointStPointEntity::getTs, psEleCountReq.getEndTime())
                .convertList(EssPsBatteryDTO.class);
        BigDecimal kwPower = powerCount.multiply(BigDecimal.valueOf(1000));
        // 获取所有电池的总充电量
        BigDecimal totalChargePowerYear = essPsBatteryDTOList.stream().map(t -> Optional.ofNullable(t.getPCS8Yc()).orElse(BigDecimal.ZERO)).reduce(BigDecimal.ZERO, BigDecimal::add);
        // 获取所有电池的总放电量
        BigDecimal totalDischargePowerYear = essPsBatteryDTOList.stream().map(t -> Optional.ofNullable(t.getPCS9Yc()).orElse(BigDecimal.ZERO)).reduce(BigDecimal.ZERO, BigDecimal::add);
        // 充电利用小时=电池充电量/额定功率
        BigDecimal chargeUtilHourYear = totalChargePowerYear.divide(kwPower, 4, RoundingMode.HALF_UP).multiply(new BigDecimal(100));
        // 放电利用小时=电池放电量/额定功率
        BigDecimal dischargeUtilHourYear = totalDischargePowerYear.divide(kwPower, 4, RoundingMode.HALF_UP).multiply(new BigDecimal(100));
        // 利用小时：充电利用小时+放电利用小时
        BigDecimal utilHourYear = chargeUtilHourYear.add(dischargeUtilHourYear).setScale(2, RoundingMode.HALF_UP);
        psEleCountResp.setAvgEleHourYear(utilHourYear.toString());

    }

    private void avgMonthHours(PsEleCountResp psEleCountResp, BigDecimal powerCount, List<String> psCodes) {
        List<EssPsBatteryDTO> essPsBatteryDTOList = TdWrappers.lambdaQuery(IotPointStPointEntity.class)
                .in(IotPointStPointEntity::getPsCode, psCodes)
                .gt(IotPointStPointEntity::getTs, DateUtil.format(DateUtil.beginOfMonth(DateUtil.date()), "yyyy-MM-dd HH:mm:ss"))
                .lt(IotPointStPointEntity::getTs, DateUtil.format(DateUtil.endOfMonth(DateUtil.date()), "yyyy-MM-dd HH:mm:ss"))
                .convertList(EssPsBatteryDTO.class);
        BigDecimal kwPower = powerCount.multiply(BigDecimal.valueOf(1000));
        // 获取所有电池的总充电量
        BigDecimal totalChargePowerMonth = essPsBatteryDTOList.stream().map(t -> Optional.ofNullable(t.getPCS8Yc()).orElse(BigDecimal.ZERO)).reduce(BigDecimal.ZERO, BigDecimal::add);
        // 获取所有电池的总放电量
        BigDecimal totalDischargePowerMonth = essPsBatteryDTOList.stream().map(t -> Optional.ofNullable(t.getPCS9Yc()).orElse(BigDecimal.ZERO)).reduce(BigDecimal.ZERO, BigDecimal::add);
        // 充电利用小时=电池充电量/额定功率
        BigDecimal chargeUtilHourMonth = totalChargePowerMonth.divide(kwPower, 4, RoundingMode.HALF_UP).multiply(new BigDecimal(100));
        // 放电利用小时=电池放电量/额定功率
        BigDecimal dischargeUtilHourMonth = totalDischargePowerMonth.divide(kwPower, 4, RoundingMode.HALF_UP).multiply(new BigDecimal(100));
        // 利用小时：充电利用小时+放电利用小时
        BigDecimal utilHourMonth = chargeUtilHourMonth.add(dischargeUtilHourMonth).setScale(2, RoundingMode.HALF_UP);
        psEleCountResp.setAvgEleHourMonth(utilHourMonth.toString());
    }

    /**
     * 工商业储能总览-地图总计
     */
    public EssPsTotalResp essPsMapTotal() {
        return psDao.essGsyPsMapTotal();
    }

}
