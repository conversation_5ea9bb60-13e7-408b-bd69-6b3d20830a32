package com.gcl.psmis.ess.contoller.ps;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.gcl.psmis.ess.req.AddPsReq;
import com.gcl.psmis.ess.req.EssDeviceReq;
import com.gcl.psmis.ess.req.ps.QueryPsCameraReq;
import com.gcl.psmis.ess.req.ps.QueryPsListReq;
import com.gcl.psmis.ess.resp.ProjectCompanyResp;
import com.gcl.psmis.ess.resp.ps.EssPsTotalResp;
import com.gcl.psmis.ess.resp.ps.PsCameraResp;
import com.gcl.psmis.ess.resp.ps.PsListResp;
import com.gcl.psmis.ess.service.detail.DetailService;
import com.gcl.psmis.ess.service.ps.PsService;
import com.gcl.psmis.ess.vo.detail.PsGeneralBusinessStateBasicVO;
import com.gcl.psmis.ess.vo.detail.PsGeneralBusinessStateRealTimeVO;
import com.gcl.psmis.framework.common.annotation.GetGclApiByToken;
import com.gcl.psmis.framework.common.annotation.PostGclApiByToken;
import com.gcl.psmis.framework.dict.util.ExpandBeanUtils;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.multipart.MultipartFile;

import java.util.List;

/**
 * <AUTHOR>
 * @ClassName PsController
 * @description: TODO
 * @date 2024年12月16日
 * @version: 1.0
 */
@Api(value = "工商业储能电站")
@Slf4j
@RestController
@RequiredArgsConstructor
@RequestMapping("gsy/ps")
public class GsyPsController {

    private final PsService psService;
    private final DetailService detailService;

    @ApiOperation("新增电站")
    @PostGclApiByToken("/addPs")
    public void addPs(@RequestBody @Validated AddPsReq addPsReq) {
        addPsReq.setType(2);
        psService.addPs(addPsReq);
    }

    @ApiOperation("详情-电站综合经营情况-基础信息")
    @GetGclApiByToken("/getPsGeneralBusinessBasicState")
    public PsGeneralBusinessStateBasicVO getPsGeneralBusinessBasicState(@ApiParam("电站编号") String psCode) {
        PsGeneralBusinessStateBasicVO res = detailService.getPsGeneralBusinessState(psCode);
        ExpandBeanUtils.convert(res, res);
        return res;
    }

    @ApiOperation("详情-电站综合经营情况-实时信息")
    @GetGclApiByToken("/getPsGeneralBusinessRealTimeState")
    public PsGeneralBusinessStateRealTimeVO getPsGeneralBusinessRealTimeState(@ApiParam("电站编号") String psCode) {
        PsGeneralBusinessStateRealTimeVO res = detailService.getPsGeneralBusinessRealTimeState(psCode);
        ExpandBeanUtils.convert(res, res);
        return res;
    }



}
