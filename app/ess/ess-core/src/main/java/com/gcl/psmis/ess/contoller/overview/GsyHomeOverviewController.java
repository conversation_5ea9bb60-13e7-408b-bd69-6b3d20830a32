package com.gcl.psmis.ess.contoller.overview;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.gcl.psmis.ess.req.PsManageRankReq;
import com.gcl.psmis.ess.req.detail.FunctionalTrendReq;
import com.gcl.psmis.ess.req.detail.STChargeDisReq;
import com.gcl.psmis.ess.req.detail.STSOCReq;
import com.gcl.psmis.ess.req.ps.PsEleCountReq;
import com.gcl.psmis.ess.req.ps.QueryPsListReq;
import com.gcl.psmis.ess.resp.PsEleCountResp;
import com.gcl.psmis.ess.resp.PsManageRankResp;
import com.gcl.psmis.ess.resp.ps.EssPsTotalResp;
import com.gcl.psmis.ess.resp.ps.PsListResp;
import com.gcl.psmis.ess.service.detail.CommerceDetailService;
import com.gcl.psmis.ess.service.detail.DetailService;
import com.gcl.psmis.ess.service.overview.GsyHomeOverviewService;
import com.gcl.psmis.ess.service.overview.HomeOverviewService;
import com.gcl.psmis.ess.service.ps.PsService;
import com.gcl.psmis.ess.vo.AlarmVO;
import com.gcl.psmis.ess.vo.detail.ChargeDisChargeVO;
import com.gcl.psmis.ess.vo.detail.FunctionalTrendsVO;
import com.gcl.psmis.ess.vo.detail.OperationMonitorVO;
import com.gcl.psmis.ess.vo.detail.SOCListVO;
import com.gcl.psmis.ess.vo.overview.InvestCalculationVO;
import com.gcl.psmis.ess.vo.overview.OperateInfoVO;
import com.gcl.psmis.ess.vo.overview.ProfitEstimationVO;
import com.gcl.psmis.framework.common.annotation.GetGclApiByToken;
import com.gcl.psmis.framework.common.annotation.PostGclApiByToken;
import com.gcl.psmis.framework.dict.util.ExpandBeanUtils;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

/**
 * <AUTHOR>
 */
@Api(value = "工商业储能-首页概览")
@Slf4j
@RestController
@RequiredArgsConstructor
@RequestMapping("/gsyOverview")
public class GsyHomeOverviewController {

    private final GsyHomeOverviewService gsyHomeOverviewService;
    private final DetailService detailService;
    private final PsService psService;
    private final CommerceDetailService commerceDetailService;


    @ApiOperation(value = "运行信息",tags = "工商业储能")
    @GetGclApiByToken("/gsyOperateInfo")
    public OperateInfoVO operateInfo() {
        return gsyHomeOverviewService.operateInfo();
    }

    @ApiOperation(value = "投资测算",tags = "工商业储能")
    @GetGclApiByToken("/gsyInvestCalculation")
    public List<InvestCalculationVO> investCalculation() {
        return gsyHomeOverviewService.investCalculation();
    }

    @ApiOperation(value = "运营利润估算--当年",tags = "工商业储能")
    @GetGclApiByToken("/gsyProfitEstimation")
    public List<ProfitEstimationVO> profitEstimation() {
        return gsyHomeOverviewService.profitEstimation();
    }

    @ApiOperation(value = "电站管理排名",tags = "工商业储能")
    @PostGclApiByToken("/gsyPsManageRank")
    public List<PsManageRankResp> psManageRank(@RequestBody PsManageRankReq psManageRankReq) {
        return gsyHomeOverviewService.psManageRank(psManageRankReq);
    }

    @ApiOperation(value = "充放电统计",tags = "工商业储能")
    @PostGclApiByToken("/gsyPsEleCount")
    public PsEleCountResp psEleCount(@RequestBody PsEleCountReq psEleCountReq) {
        return gsyHomeOverviewService.psEleCount(psEleCountReq);
    }

    @ApiOperation(value = "工商业储能总览-地图总计",tags = "工商业储能")
    @PostGclApiByToken("/gsyEssPsMapTotal")
    public EssPsTotalResp essPsMapTotal() {
        return gsyHomeOverviewService.essPsMapTotal();
    }

    @ApiOperation(value = "工商业储能-实时告警",tags = "工商业储能")
    @GetGclApiByToken("/gsyFirstAlarmList")
    public List<AlarmVO> getFirstAlarmList() {
        return detailService.getFirstAlarmList(2);
    }

    @ApiOperation("工商业储能-详情-运行监控-获取充放电量信息")
    @PostGclApiByToken("/getPSCommerceChargeDisList")
    public ChargeDisChargeVO getPSCommerceChargeDisList(@RequestBody @Validated STChargeDisReq req) {
        return commerceDetailService.getPSCommerceChargeDisList(req);
    }

    @ApiOperation("工商业储能-详情-运行监控-功率趋势：用户总负荷、需量阈值、最大需量、储能功率")
    @PostGclApiByToken("/getFunctionalTrendList")
    public FunctionalTrendsVO getFunctionalTrendList(@RequestBody @Validated FunctionalTrendReq req) {
        return commerceDetailService.getFunctionalTrendList(req);
    }

    @ApiOperation("工商业储能-详情-运行监控-获取SOC")
    @PostGclApiByToken("/getCommerceSOCList")
    public List<SOCListVO> getCommerceSOCList(@RequestBody @Validated STSOCReq req) {
        return commerceDetailService.getCommerceSOCList(req);
    }

    @ApiOperation("工商业储能-详情-运行监控-告警记录")
    @GetGclApiByToken("/getComFirstAlarmList")
    public List<AlarmVO> getComFirstAlarmList() {
        return commerceDetailService.getComFirstAlarmList();
    }

    @ApiOperation("工商业储能-详情-运行监控-设备监测：获取PCS、BMS信息")
    @GetGclApiByToken("/getComOperationMonitor")
    public OperationMonitorVO getComOperationMonitor(@ApiParam("电站编号") String psCode, @ApiParam("PCS编号") String pcsCode) {
        OperationMonitorVO res = commerceDetailService.getComOperationMonitor(psCode, pcsCode);
        ExpandBeanUtils.convert(res, res);
        return res;
    }

}
