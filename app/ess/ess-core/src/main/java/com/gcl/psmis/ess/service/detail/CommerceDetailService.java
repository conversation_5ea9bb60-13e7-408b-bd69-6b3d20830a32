package com.gcl.psmis.ess.service.detail;

import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.ObjectUtil;
import com.gcl.psmis.ess.dao.detail.DetailDao;
import com.gcl.psmis.ess.req.detail.FunctionalTrendReq;
import com.gcl.psmis.ess.req.detail.STChargeDisReq;
import com.gcl.psmis.ess.req.detail.STSOCReq;
import com.gcl.psmis.ess.vo.AlarmVO;
import com.gcl.psmis.ess.vo.detail.*;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import java.math.RoundingMode;
import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

@Service
@Slf4j
@RequiredArgsConstructor
public class CommerceDetailService {

    @Autowired
    private DetailDao detailDao;

    @Autowired
    private DetailService detailService;
    public ChargeDisChargeVO getPSCommerceChargeDisList(STChargeDisReq req) {
        ChargeDisChargeVO res = new ChargeDisChargeVO();
        List<STNumericalVO> chargeList = null;
        List<STNumericalVO> dischargeList = null;
        String startDate = null;
        String endDate = null;
        switch (req.getChargeDisType()) {
            case 1:
                startDate = DateUtil.beginOfDay(DateUtil.parse(req.getDate(), "yyyy/MM/dd")).toString();
                endDate = DateUtil.endOfDay(DateUtil.parse(req.getDate(), "yyyy/MM/dd")).toString();
                // todo 充放电的normalKey需要确定，对应的是t_quota_point表中的norm_key,然后对应的是td里面的code。
                chargeList = detailDao.getSTNumericalFiveMinList("ST73Yc", req.getPsCode(), startDate, endDate);
                dischargeList = detailDao.getSTNumericalFiveMinList("ST74Yc", req.getPsCode(), startDate, endDate);
                break;
            case 2:
                startDate = DateUtil.beginOfMonth(DateUtil.parse(req.getDate() + "/01", "yyyy/MM/dd")).toString();
                endDate = DateUtil.endOfMonth(DateUtil.parse(req.getDate() + "/01", "yyyy/MM/dd")).toString();
                chargeList = detailDao.getSTNumericalOneDayList("ST73Yc", req.getPsCode(), startDate, endDate);
                dischargeList = detailDao.getSTNumericalOneDayList("ST74Yc", req.getPsCode(), startDate, endDate);
                break;
            case 3:
                startDate = DateUtil.beginOfYear(DateUtil.parse(req.getDate() + "/01/01", "yyyy/MM/dd")).toString();
                endDate = DateUtil.endOfYear(DateUtil.parse(req.getDate() + "/01/01", "yyyy/MM/dd")).toString();
                chargeList = detailDao.getSTNumericalOneMonthList("ST142Yc", req.getPsCode(), startDate, endDate);
                dischargeList = detailDao.getSTNumericalOneMonthList("ST143Yc", req.getPsCode(), startDate, endDate);
                break;
        }
        chargeList = chargeList.stream().filter(t -> ObjectUtil.isNotEmpty(t.getData())).collect(Collectors.toList());
        dischargeList = dischargeList.stream().filter(t -> ObjectUtil.isNotEmpty(t.getData())).collect(Collectors.toList());
        chargeList.forEach(t -> {
            t.setTs(t.getTs());
            t.setData(t.getData().setScale(4, RoundingMode.HALF_UP));
        });
        dischargeList.forEach(t -> {
            t.setTs(t.getTs());
            t.setData(t.getData().setScale(4, RoundingMode.HALF_UP));
        });
        res.setChargeList(chargeList);
        res.setDisCharge(dischargeList);
        return res;
    }

    /**
     * 工商业储能电站-电站详情-运行监控-功率趋势：用户总负荷、需量阈值、最大需量、储能功率
     * @param req
     * @return
     */
    public FunctionalTrendsVO getFunctionalTrendList(FunctionalTrendReq req) {
        String startDate = DateUtil.beginOfDay(DateUtil.parse(req.getStartDate())).toString();
        String endDate = DateUtil.endOfDay(DateUtil.parse(req.getEndDate())).toString();
        FunctionalTrendsVO res = new FunctionalTrendsVO();


        // todo 目前测点还没到，这边的返回参数都要做修改的，以保持和需求文档中的参数一致：修改normalKey
        List<STNumericalVO> totalUserLoadList = detailDao.getSTNumericalFiveMinList("ST41Yc", req.getPsCode(), startDate, endDate);
        List<STNumericalVO> demandThresholdList = detailDao.getSTNumericalFiveMinList("ST42Yc", req.getPsCode(), startDate, endDate);
        List<STNumericalVO> maxDemandList = detailDao.getSTNumericalFiveMinList("ST41Yc", req.getPsCode(), startDate, endDate);
        List<STNumericalVO> storagePowerList = detailDao.getSTNumericalFiveMinList("ST42Yc", req.getPsCode(), startDate, endDate);


        totalUserLoadList = totalUserLoadList.stream().filter(t -> ObjectUtil.isNotEmpty(t.getData())).collect(Collectors.toList());
        demandThresholdList = demandThresholdList.stream().filter(t -> ObjectUtil.isNotEmpty(t.getData())).collect(Collectors.toList());
        maxDemandList = maxDemandList.stream().filter(t -> ObjectUtil.isNotEmpty(t.getData())).collect(Collectors.toList());
        storagePowerList = storagePowerList.stream().filter(t -> ObjectUtil.isNotEmpty(t.getData())).collect(Collectors.toList());
        totalUserLoadList.forEach(t -> {
            t.setTs(t.getTs());
            t.setData(t.getData().setScale(4, RoundingMode.HALF_UP));
        });
        demandThresholdList.forEach(t -> {
            t.setTs(t.getTs());
            t.setData(t.getData().setScale(4, RoundingMode.HALF_UP));
        });
        maxDemandList.forEach(t -> {
            t.setTs(t.getTs());
            t.setData(t.getData().setScale(4, RoundingMode.HALF_UP));
        });
        storagePowerList.forEach(t -> {
            t.setTs(t.getTs());
            t.setData(t.getData().setScale(4, RoundingMode.HALF_UP));
        });
        res.setTotalUserLoadList(totalUserLoadList);
        res.setDemandThresholdList(demandThresholdList);
        res.setMaxDemandList(maxDemandList);
        res.setStoragePowerList(storagePowerList);
        return res;
    }

    /**
     * 工商业储能电站-电站详情-运行监控-SOC：需要去拿到t_quota_point表中的norm_key,然后对应的是td里面的code
     * @param req
     * @return
     */
    public List<SOCListVO> getCommerceSOCList(STSOCReq req) {
        String startDate = DateUtil.beginOfDay(DateUtil.parse(req.getStartDate())).toString();
        String endDate = DateUtil.endOfDay(DateUtil.parse(req.getEndDate())).toString();
        List<SOCListVO> res = new ArrayList<>();
        // todo t_quota_point表中的norm_key,然后对应的是td里面的code
        List<STNumericalVO> activatePowerList = detailDao.getSTNumericalFiveMinList("BMS202Yc", req.getPsCode(), startDate, endDate);
        activatePowerList = activatePowerList.stream().filter(t -> ObjectUtil.isNotEmpty(t.getData())).collect(Collectors.toList());
        activatePowerList.forEach(t -> {
            SOCListVO vo = new SOCListVO();
            vo.setDate(DateUtil.parse(t.getTs()));
            vo.setSoc(t.getData().setScale(4, RoundingMode.HALF_UP));
            res.add(vo);
        });
        return res;
    }

    /**
     * 工商业储能电站-电站详情-运行监控-告警记录：1：独立储能;2：工商业储能
     * @return
     */
    public List<AlarmVO> getComFirstAlarmList() {
        return detailService.getFirstAlarmList(2);
    }

    /**
     * 工商业储能电站-电站详情-运行监控-设备监测：获取PCS、BMS信息
     * @param psCode
     * @param pcsCode
     * @return
     */
    public OperationMonitorVO getComOperationMonitor(String psCode, String pcsCode){
        return detailService.getOperationMonitor(psCode, pcsCode);
    }
}
