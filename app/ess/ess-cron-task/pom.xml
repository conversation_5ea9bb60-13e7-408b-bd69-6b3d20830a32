<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>
    <parent>
        <groupId>com.gcl.psmis.ess</groupId>
        <artifactId>ess</artifactId>
        <version>0.0.1-SNAPSHOT</version>
    </parent>

    <artifactId>ess-cron-task</artifactId>

    <packaging>jar</packaging>
    <dependencies>
        <dependency>
            <groupId>org.springframework.cloud</groupId>
            <artifactId>spring-cloud-loadbalancer</artifactId>
        </dependency>
        <dependency>
            <groupId>com.gcl.psmis.framework</groupId>
            <artifactId>taos</artifactId>
        </dependency>

        <dependency>
            <groupId>com.gcl.psmis.framework</groupId>
            <artifactId>redis</artifactId>
        </dependency>

        <dependency>
            <groupId>com.gcl.psmis.framework</groupId>
            <artifactId>kafka-core</artifactId>
            <version>0.0.1-SNAPSHOT</version>
        </dependency>

        <dependency>
            <groupId>org.springframework.cloud</groupId>
            <artifactId>spring-cloud-starter-openfeign</artifactId>
        </dependency>
        <dependency>
            <groupId>mysql</groupId>
            <artifactId>mysql-connector-java</artifactId>
        </dependency>

        <dependency>
            <groupId>org.springframework.cloud</groupId>
            <artifactId>spring-cloud-starter-bootstrap</artifactId>
        </dependency>

        <dependency>
            <groupId>p6spy</groupId>
            <artifactId>p6spy</artifactId>
        </dependency>
        <dependency>
            <groupId>com.gcl.psmis.ess</groupId>
            <artifactId>ess-common</artifactId>
            <version>0.0.1-SNAPSHOT</version>
        </dependency>

        <dependency>
            <groupId>com.xuxueli</groupId>
            <artifactId>xxl-job-core</artifactId>
        </dependency>
        <dependency>
            <groupId>com.gcl.psmis.framework</groupId>
            <artifactId>export-manager</artifactId>
        </dependency>

        <dependency>
            <groupId>com.gcl.psmis.framework</groupId>
            <artifactId>oss</artifactId>
        </dependency>

        <dependency>
            <groupId>com.dtflys.forest</groupId>
            <artifactId>forest-spring-boot-starter</artifactId>
            <version>1.5.14</version>
        </dependency>
        <dependency>
            <groupId>com.gcl.psmis.msg.center</groupId>
            <artifactId>psmis-msg-center-api</artifactId>
            <version>0.0.1-SNAPSHOT</version>
        </dependency>

    </dependencies>

    <build>
        <finalName>gcl-psmis-ess-cron-task</finalName>
        <plugins>
            <plugin>
                <groupId>org.springframework.boot</groupId>
                <artifactId>spring-boot-maven-plugin</artifactId>
                <configuration>
                    <includeSystemScope>true</includeSystemScope>
                </configuration>
            </plugin>

        </plugins>
    </build>

</project>