package com.gcl.psmis.ess;

import com.gcl.psmis.msg.api.SendRpcService;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.boot.context.properties.EnableConfigurationProperties;
import org.springframework.cloud.client.discovery.EnableDiscoveryClient;
import org.springframework.cloud.openfeign.EnableFeignClients;
import org.springframework.context.annotation.ComponentScan;
import org.springframework.context.annotation.ComponentScans;
import org.springframework.scheduling.annotation.EnableAsync;
import org.springframework.scheduling.annotation.EnableScheduling;

@SpringBootApplication
@EnableDiscoveryClient
@EnableConfigurationProperties
@EnableScheduling
@EnableAsync
@EnableFeignClients(clients = {SendRpcService.class})
@ComponentScans(@ComponentScan(basePackages = {"com.gcl.psmis.framework","com.gcl.psmis.msg"}))
public class PsmisEssCronTaskApplication {

    public static void main(String[] args) {
        SpringApplication.run(PsmisEssCronTaskApplication.class, args);
    }
}

