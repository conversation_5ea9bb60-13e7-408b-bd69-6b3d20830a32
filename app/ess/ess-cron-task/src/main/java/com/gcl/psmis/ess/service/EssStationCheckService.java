package com.gcl.psmis.ess.service;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.exceptions.ExceptionUtil;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.gcl.psmis.ess.dao.EssDayAndMonthReportDao;
import com.gcl.psmis.ess.mbg.entity.TEsStationEntity;
import com.gcl.psmis.ess.mbg.service.TEsStationService;
import com.gcl.psmis.ess.util.PushWeChatMsgService;
import com.gcl.psmis.framework.common.ResponseResult;
import com.gcl.psmis.framework.common.dto.EssPsCountDTO;
import com.gcl.psmis.framework.common.req.msg.MessageReq;
import com.gcl.psmis.framework.common.req.msg.SendReq;
import com.gcl.psmis.framework.mbg.entity.TMsgTemplateEntity;
import com.gcl.psmis.framework.mbg.service.TMsgTemplateService;
import com.gcl.psmis.msg.api.SendRpcService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @ClassName EssStationCheckService
 * @description: TODO
 * @date 2025年06月05日
 * @version: 1.0
 */
@Service
@Slf4j
@RequiredArgsConstructor
public class EssStationCheckService {
    @Autowired
    private TEsStationService esStationService;
    @Autowired
    private EssDayAndMonthReportDao essDayAndMonthReportDao;
    private final String wxWebHoke = "d18baf41-9912-4e48-b1d7-52e67ac24552";
    @Autowired
    private TMsgTemplateService tMsgTemplateService;
    private final SendRpcService sendRpcService;
    public void essStationCheck() {
        // 获取td各电站数据信息
        List<TEsStationEntity> list = esStationService.lambdaQuery().eq(
                TEsStationEntity::getDelFlag, 0
        ).list();
        if (CollectionUtil.isEmpty(list)) {
            return;
        }
        List<String> psCodes = list.stream().map(esStationEntity -> esStationEntity.getPsCode()).collect(Collectors.toList());
        List<EssPsCountDTO> essPsCountDTOS = essDayAndMonthReportDao.getPsCount(psCodes, DateUtil.beginOfDay(DateUtil.date()).toString(), DateUtil.endOfDay(DateUtil.date()).toString());
        if(CollectionUtil.isNotEmpty(essPsCountDTOS)){
            List<String> psCodeList = essPsCountDTOS.stream().map(s -> s.getPsCode()).collect(Collectors.toList());
            // 获取两个pscodes差集
            List<String> collect = CollectionUtil.subtract(psCodes, psCodeList).stream().collect(Collectors.toList());
            if(CollectionUtil.isNotEmpty(collect)){
        /*        try {
                    pushWeChatMsgService.send("未接收到点表异常电站信息:"+collect.toString(), "markdown",wxWebHoke);
                } catch (Exception e) {
                    log.error("消息发送失败!!!{}", ExceptionUtil.stacktraceToString(e));
                }*/
                String psNames = esStationService.lambdaQuery().in(TEsStationEntity::getPsCode, collect).list().stream().map(TEsStationEntity::getAbbName).collect(Collectors.joining());
                //获取发送消息模板
                TMsgTemplateEntity templateServiceOne = tMsgTemplateService.getOne(Wrappers.<TMsgTemplateEntity>lambdaQuery()
                        .select(TMsgTemplateEntity::getId).eq(TMsgTemplateEntity::getTmpCode, "es_alarm"));
                // 发送消息
                Map<String, String> variables = new HashMap<>();
                variables.put("content", psNames);
                //获取发送消息模板
                MessageReq messageReq = MessageReq.builder()
                        .receiver(Collections.singleton(wxWebHoke))
                        .variables(variables).build();
                SendReq sendReq = SendReq.builder().messageTemplateId(templateServiceOne.getId())
                        .messageParam(messageReq).build();
                ResponseResult responseResult = sendRpcService.send(sendReq);
                System.out.println(responseResult);
            }
        }
    }
}
