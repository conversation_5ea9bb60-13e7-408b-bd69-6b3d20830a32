package com.gcl.psmis.ess.service;

import cn.hutool.core.collection.CollStreamUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.date.DateTime;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.ObjectUtil;
import com.baomidou.dynamic.datasource.annotation.DSTransactional;
import com.gcl.psmis.ess.dao.EssDayAndMonthReportDao;
import com.gcl.psmis.ess.mbg.entity.*;
import com.gcl.psmis.ess.mbg.service.*;
import com.gcl.psmis.ess.req.prodreport.EssExcelParamReq;
import com.gcl.psmis.ess.req.prodreport.EssTaskPriceReq;
import com.gcl.psmis.ess.vo.report.*;
import com.gcl.psmis.framework.export.enums.ComputeExcelEnum;
import com.gcl.psmis.framework.export.util.poi.ComputeExcelTool;
import com.gcl.psmis.framework.taos.domain.entity.IotPointStPointEntity;
import com.gcl.psmis.framework.taos.wrapper.TdWrappers;
import com.xxl.job.core.context.XxlJobHelper;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

import java.lang.reflect.Field;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.*;
import java.util.function.Function;

@Component
@Slf4j
@RequiredArgsConstructor
public class EssDayAndMonthReportService {

    private final TEsStationService tEsStationService;
    private final TElectricMeteringDayService tElectricMeteringDayService;
    private final TElectricMeteringMonthService tElectricMeteringMonthService;
    private final EssDayAndMonthReportDao essDayAndMonthReportDao;
    private final ComputeExcelTool computeExcelTool;
    private final TDayReportService tDayReportService;
    private final TDayReportDetailService tDayReportDetailService;
    private final TMonthReportService tMonthReportService;
    private final TMonthReportDetailService tMonthReportDetailService;
    private final TElectricPriceConfigService tElectricPriceConfigService;
    private final TChargeDischargeConfigService tChargeDischargeConfigService;
    private final CommonConfigService commonConfigService;


    // 生成、更新日报表、月报表数据
    @DSTransactional
    public void essDayAndMonthReport() {

        // 获取所有有效的电站
        List<TEsStationEntity> psCodeList = tEsStationService.lambdaQuery()
                .eq(TEsStationEntity::getDelFlag, 0)
                .list();
        // 获取当前日期所对应的年份-月份
        String currentMonth = DateUtil.format(new Date(), "yyyy/MM");
        if (CollUtil.isNotEmpty(psCodeList)) {
            Map<String, TEsStationEntity> esStationEntityMap = new HashMap<>();
            esStationEntityMap = CollStreamUtil.toMap(psCodeList, TEsStationEntity::getPsCode, Function.identity());
            // Step1：扫描天表,对状态为”未计算“的更新对应的天表、月表(前提是当前月份已经生成过数据)
            List<TElectricMeteringDayEntity> dayList = tElectricMeteringDayService.lambdaQuery()
                    .eq(TElectricMeteringDayEntity::getDelFlag, 0)
                    .eq(TElectricMeteringDayEntity::getCalcFlag, 1)
                    .list();
            this.dayPublicMethod(dayList, esStationEntityMap);

            // Step2：扫描月表,对状态为”未计算“的更新对应的月表 (准确来说只有更新,没有保存,因为当月1号已经把上个月的月报表生成过了,且维护抄表时不可维护本月的数据)
            List<TElectricMeteringMonthEntity> updateMonthList = tElectricMeteringMonthService.lambdaQuery()
                    .eq(TElectricMeteringMonthEntity::getDelFlag, 0)
                    .eq(TElectricMeteringMonthEntity::getCalcFlag, 1)
                    .list();
            // 更新所有状态为未计算的月表数据
            this.monthPublicMethod(updateMonthList, esStationEntityMap);

            // Step3：判断当日是否为当月的第一天,如果是则生成上个月的月报表数据（准确来说只有保存,没有更新,每月1号是生成上个月报表的唯一入口）
            Date now = new Date();
            if (DateUtil.format(now, "yyyy/MM/dd").equals(DateUtil.format(DateUtil.beginOfMonth(now), "yyyy/MM/dd"))) {
                // 生成上个月的月报表数据(生成某月份的一批电站的月报表)
                String lastMonthDate = DateUtil.format(DateUtil.offsetDay(now, -1), "yyyy/MM");
                List<TElectricMeteringMonthEntity> saveMonthList = tElectricMeteringMonthService.lambdaQuery()
                        .eq(TElectricMeteringMonthEntity::getDelFlag, 0)
                        .eq(TElectricMeteringMonthEntity::getCalcFlag, 1)
                        .eq(TElectricMeteringMonthEntity::getRecordDate, lastMonthDate)
                        .list();
                this.monthPublicMethod(saveMonthList, esStationEntityMap);
            }

            // Step4：上网电价、补贴电价、下网电价,是否发生改变,如果发生改变则重新计算所在月所有的日报表、月报表数据
            List<TElectricPriceConfigEntity> priceList = tElectricPriceConfigService.lambdaQuery()
                    .eq(TElectricPriceConfigEntity::getDelFlag, 0)
                    .isNotNull(TElectricPriceConfigEntity::getChangeMonths)
                    .list();
            if (CollUtil.isNotEmpty(priceList)) {
                for (TElectricPriceConfigEntity onGridPrice : priceList) {
                    this.priceAndNumMethod(currentMonth, esStationEntityMap, onGridPrice.getChangeMonths(), onGridPrice.getYear(), onGridPrice.getProvinceId());
                }
                // 更新change_months字段为null
                tElectricPriceConfigService.lambdaUpdate()
                        .in(TElectricPriceConfigEntity::getId, CollStreamUtil.toList(priceList, TElectricPriceConfigEntity::getId))
                        .set(TElectricPriceConfigEntity::getChangeMonths, null)
                        .update();
            }

            // Step5：判断设计日充放电次数是否改变,如果改变则重新计算所在月所有的日报表、月报表数据
            List<TChargeDischargeConfigEntity> numlist = tChargeDischargeConfigService.lambdaQuery()
                    .eq(TChargeDischargeConfigEntity::getDelFlag, 0)
                    .isNotNull(TChargeDischargeConfigEntity::getChangeMonths)
                    .list();
            if (CollUtil.isNotEmpty(numlist)) {
                for (TChargeDischargeConfigEntity num : numlist) {
                    this.priceAndNumMethod(currentMonth, esStationEntityMap, num.getChangeMonths(), num.getYear(), num.getProvinceId());
                }
                // 更新change_months字段为null
                tChargeDischargeConfigService.lambdaUpdate()
                        .in(TChargeDischargeConfigEntity::getId, CollStreamUtil.toList(numlist, TChargeDischargeConfigEntity::getId))
                        .set(TChargeDischargeConfigEntity::getChangeMonths, null)
                        .update();
            }
        }
    }

    // 上网电价、补贴电价、下网电价、设计日充放电次数改变时的公共方法
    public void priceAndNumMethod(String currentMonth, Map<String, TEsStationEntity> esStationEntityMap, String changeMonths, String year, Long provinceId) {
        // 查询此省份下所有的电站
        List<TEsStationEntity> psList = tEsStationService.lambdaQuery()
                .eq(TEsStationEntity::getDelFlag, 0)
                .eq(TEsStationEntity::getProvinceId, provinceId)
                .list();
        if (CollUtil.isNotEmpty(psList)) {
            String[] monthArr = StringUtils.split(changeMonths, ",");
            for (String month : monthArr) {
                // 如果修改日期小于等于当前日期则执行更新
                String recordDate = year + '/' + month;
                if (recordDate.compareTo(currentMonth) <= 0) {
                    String startDate = DateUtil.beginOfMonth(DateUtil.parse(recordDate, "yyyy/MM")).toString("yyyy/MM/dd");
                    String endDate = DateUtil.endOfMonth(DateUtil.parse(recordDate, "yyyy/MM")).toString("yyyy/MM/dd");
                    List<TElectricMeteringDayEntity> priceDayList = tElectricMeteringDayService.lambdaQuery()
                            .eq(TElectricMeteringDayEntity::getDelFlag, 0)
                            .in(TElectricMeteringDayEntity::getPsCode, CollStreamUtil.toList(psList, TEsStationEntity::getPsCode))
                            .ge(TElectricMeteringDayEntity::getRecordDate, startDate)
                            .le(TElectricMeteringDayEntity::getRecordDate, endDate)
                            .list();
                    this.dayPublicMethod(priceDayList, esStationEntityMap);
                }
            }
        }
    }

    // 日报表公共方法
    public void dayPublicMethod(List<TElectricMeteringDayEntity> dayList, Map<String, TEsStationEntity> esStationEntityMap) {

        if (CollUtil.isNotEmpty(dayList)) {
            List<EssExcelParamReq> essExcelParamReqDay = new ArrayList<>();
            List<TElectricMeteringDayEntity> removeDayList = new ArrayList<>();
            for (TElectricMeteringDayEntity day : dayList) {
                // 判断当前日期所在的省份&&月份是否已经维护上网电价、下网电价、设计日充放电次数
                EssTaskPriceReq essTaskPriceReq = this.queryPriceInfo(esStationEntityMap.get(day.getPsCode()).getProvinceId(), StringUtils.substring(day.getRecordDate(), 0, 4), Integer.valueOf(StringUtils.substring(day.getRecordDate(), 5, 7)));
                if (!essTaskPriceReq.getEditFlag().equals(1)) {
                    // 当月上网电价、下网电价、补贴电价、设计日充放电次数数据缺失,则无法计算
                    XxlJobHelper.log("【ESS日报表】当前日期所在的省份&&月份数据缺失,无法计算{}", day.getRecordDate());
                    removeDayList.add(day);  // 统计日期不在运行首日之前,则无法计算
                    continue;
                }
                // 判断统计日期是否大于等于运行首日
                if (!DateUtil.parse(day.getRecordDate(), "yyyy/MM/dd").isAfter(esStationEntityMap.get(day.getPsCode()).getWorkingFirstDay())) {
                    removeDayList.add(day);  // 统计日期不在运行首日之前,则无法计算
                    continue;
                }
                // 封装入参数据
                this.wrapDayQueryData(day, essExcelParamReqDay, esStationEntityMap, essTaskPriceReq);
            }
            dayList.removeAll(removeDayList);

            // 调用Excel计算模板
            List<Object> objectList = new ArrayList<>(essExcelParamReqDay);
            List<EssExcelResultVO> resultList = computeExcelTool.computeExcel(EssExcelResultVO.class, objectList, ComputeExcelEnum.EssTypeUrl);
            if (CollectionUtils.isNotEmpty(resultList) && resultList.size() == essExcelParamReqDay.size()) {
                // 封装所有的结果到 t_day_report、t_day_report_detail
                // 如果当前日期所在的月份已经存在月表数据,则更新 t_month_report、t_month_report_detail
                for (int i = 0; i < resultList.size(); i++) {
                    EssExcelResultVO excelResult = resultList.get(i);
                    EssExcelParamReq essExcelParamReq = essExcelParamReqDay.get(i);
                    TElectricMeteringDayEntity tElectricMeteringDayEntity = dayList.get(i);
                    DateTime dayTime = DateUtil.parse(tElectricMeteringDayEntity.getRecordDate(), "yyyy/MM/dd");
                    String beginOfYear = DateUtil.beginOfYear(dayTime).toString("yyyy/MM/dd");

                    // 打印日志
                    XxlJobHelper.log("入参：{}", essExcelParamReq);
                    XxlJobHelper.log("结果：{}", excelResult);
                    // 获取截至当日所有日报表的充放电毛利
                    BigDecimal sumYearCfProfit = essDayAndMonthReportDao.sumYearCfProfit(tElectricMeteringDayEntity.getPsCode(), beginOfYear, tElectricMeteringDayEntity.getRecordDate());
                    // 判断当前日报表是否已经生成,如果生成则更新,否则保存
                    TDayReportEntity tDayReportEntity = tDayReportService.lambdaQuery()
                            .eq(TDayReportEntity::getPsCode, tElectricMeteringDayEntity.getPsCode())
                            .eq(TDayReportEntity::getReportDate, tElectricMeteringDayEntity.getRecordDate())
                            .eq(TDayReportEntity::getDelFlag, 0)
                            .one();
                    if (ObjectUtil.isNotEmpty(tDayReportEntity)) {
                        // 更新日报表主表数据
                        tDayReportEntity.setSecurityDay(excelResult.getSecurityDays().intValue());
                        // 删除日报表的指标明细数据
                        tDayReportDetailService.lambdaUpdate()
                                .eq(TDayReportDetailEntity::getDayReportId, tDayReportEntity.getId())
                                .remove();
                    } else {
                        // 新增保存
                        tDayReportEntity = TDayReportEntity.builder()
                                .psCode(tElectricMeteringDayEntity.getPsCode())
                                .reportDate(tElectricMeteringDayEntity.getRecordDate())
                                .securityDay(excelResult.getSecurityDays().intValue()).build();
                    }
                    tDayReportService.saveOrUpdate(tDayReportEntity);
                    tDayReportDetailService.saveBatch(this.wrapDayReData(tDayReportEntity.getId(), excelResult, essExcelParamReq, sumYearCfProfit));

                    // 将t_electric_metering_day 表中的数据状态变更为 已计算
                    tElectricMeteringDayService.lambdaUpdate()
                            .in(TElectricMeteringDayEntity::getId, CollStreamUtil.toList(dayList, TElectricMeteringDayEntity::getId))
                            .set(TElectricMeteringDayEntity::getCalcFlag, 2)
                            .update();
                    // 判断当月是否已经生成月表,如果生成则更新
                    TMonthReportEntity monthEntity = tMonthReportService.lambdaQuery()
                            .eq(TMonthReportEntity::getDelFlag, 0)
                            .eq(TMonthReportEntity::getPsCode, tElectricMeteringDayEntity.getPsCode())
                            .eq(TMonthReportEntity::getReportMonth, DateUtil.format(dayTime, "yyyy/MM"))
                            .one();
                    if (ObjectUtil.isNotEmpty(monthEntity)) {
                        // 更新月表指标数据 （重新计算整个月的数据）
                        List<TElectricMeteringMonthEntity> updateMonthList = tElectricMeteringMonthService.lambdaQuery()
                                .eq(TElectricMeteringMonthEntity::getDelFlag, 0)
                                .eq(TElectricMeteringMonthEntity::getRecordDate, monthEntity.getReportMonth())
                                .eq(TElectricMeteringMonthEntity::getPsCode, tElectricMeteringDayEntity.getPsCode())
                                .list();
                        this.monthPublicMethod(updateMonthList, esStationEntityMap);
                    }
                }
            }
        }
    }

    // 月报表的公共方法
    public void monthPublicMethod(List<TElectricMeteringMonthEntity> monthList, Map<String, TEsStationEntity> esStationEntityMap) {

        if (CollUtil.isNotEmpty(monthList)) {
            List<EssExcelParamReq> essExcelParamReqMonth = new ArrayList<>();
            List<TElectricMeteringMonthEntity> removeMonthList = new ArrayList<>();
            for (TElectricMeteringMonthEntity month : monthList) {
                // 封装入参数据
                DateTime monthDate = DateUtil.parse(month.getRecordDate(), "yyyy/MM");
                String dateEnd = DateUtil.format(DateUtil.endOfMonth(monthDate), "yyyy/MM/dd");
                // 判断当前日期所在的省份&&月份是否已经维护上网电价、下网电价、设计日充放电次数
                EssTaskPriceReq essTaskPriceReq = this.queryPriceInfo(esStationEntityMap.get(month.getPsCode()).getProvinceId(), StringUtils.substring(month.getRecordDate(), 0, 4), Integer.valueOf(StringUtils.substring(month.getRecordDate(), 5, 7)));
                if (!essTaskPriceReq.getEditFlag().equals(1)) {
                    // 当月上网电价、下网电价、补贴电价、设计日充放电次数数据缺失,则无法计算
                    XxlJobHelper.log("【ESS月报表】当前日期所在的省份&&月份数据缺失,无法计算{}", month.getRecordDate());
                    removeMonthList.add(month);
                    continue;
                }
                // 查询当月的所有日报表,取日期最新的一条
                List<TElectricMeteringDayEntity> tElectricMeteringDayEntityList = tElectricMeteringDayService.lambdaQuery()
                        .eq(TElectricMeteringDayEntity::getPsCode, month.getPsCode())
                        .eq(TElectricMeteringDayEntity::getDelFlag, 0)
                        .ge(TElectricMeteringDayEntity::getRecordDate, DateUtil.format(monthDate, "yyyy/MM/dd"))
                        .le(TElectricMeteringDayEntity::getRecordDate, dateEnd)
                        .orderByDesc(TElectricMeteringDayEntity::getRecordDate)
                        .list();
                if (CollUtil.isEmpty(tElectricMeteringDayEntityList)) {
                    removeMonthList.add(month);
                    continue;
                }
                TElectricMeteringDayEntity dayLast = tElectricMeteringDayEntityList.get(0);
                if (!DateUtil.parse(dayLast.getRecordDate(), "yyyy/MM/dd").isAfter(esStationEntityMap.get(dayLast.getPsCode()).getWorkingFirstDay())) {
                    removeMonthList.add(month); // 当月有数值的最后一天的统计日期不在运行首日之前,则无法计算
                    continue;
                }
                // 封装Excel公式调用模板,批量计算
                this.wrapDayQueryData(dayLast, essExcelParamReqMonth, esStationEntityMap, essTaskPriceReq);
            }
            monthList.removeAll(removeMonthList);
            if (CollUtil.isNotEmpty(essExcelParamReqMonth)) {
                // 调用Excel计算模板
                List<Object> objectList = new ArrayList<>(essExcelParamReqMonth);
                List<EssExcelResultVO> resultList = computeExcelTool.computeExcel(EssExcelResultVO.class, objectList, ComputeExcelEnum.EssTypeUrl);
                if (CollUtil.isNotEmpty(resultList) && resultList.size() == essExcelParamReqMonth.size()) {
                    // 将月表t_electric_metering_month的计算状态更新为【已计算】
                    tElectricMeteringMonthService.lambdaUpdate()
                            .in(TElectricMeteringMonthEntity::getId, CollStreamUtil.toList(monthList, TElectricMeteringMonthEntity::getId))
                            .set(TElectricMeteringMonthEntity::getCalcFlag, 2)
                            .update();
                    // 封装所有的结果到 t_month_report、t_month_report_detail
                    for (int i = 0; i < resultList.size(); i++) {
                        EssExcelResultVO excelResult = resultList.get(i);
                        EssExcelParamReq req = essExcelParamReqMonth.get(i);
                        TElectricMeteringMonthEntity tElectricMeteringMonthEntity = monthList.get(i);
                        // 判断当月是否已经生成月表,如果生成则更新,否则保存
                        TMonthReportEntity monthEntity = tMonthReportService.lambdaQuery()
                                .eq(TMonthReportEntity::getDelFlag, 0)
                                .eq(TMonthReportEntity::getPsCode, tElectricMeteringMonthEntity.getPsCode())
                                .eq(TMonthReportEntity::getReportMonth, tElectricMeteringMonthEntity.getRecordDate())
                                .one();
                        if (ObjectUtil.isNotEmpty(monthEntity)) {
                            // 更新月报表主表数据
                            monthEntity.setSecurityDay(excelResult.getSecurityDays().intValue());
                            // 删除月报表的指标明细数据
                            tMonthReportDetailService.lambdaUpdate()
                                    .eq(TMonthReportDetailEntity::getMonthReportId, monthEntity.getId())
                                    .remove();
                        } else {
                            // 新增保存
                            monthEntity = TMonthReportEntity.builder()
                                    .psCode(tElectricMeteringMonthEntity.getPsCode())
                                    .reportMonth(tElectricMeteringMonthEntity.getRecordDate())
                                    .securityDay(excelResult.getSecurityDays().intValue())
                                    .build();
                        }
                        tMonthReportService.saveOrUpdate(monthEntity);
                        tMonthReportDetailService.saveBatch(this.wrapMonthData(monthEntity.getId(), excelResult, req));
                    }
                }
            }
        }
    }

    // 查询某个省份、某月的上网电价、补贴电价、下网电价、设计日充放电次数
    public EssTaskPriceReq queryPriceInfo(Long provinceId, String year, Integer month) {
        // 充放电次数  Map<省id, 充放电次数>
        Map<Long, Long> dischargeMap = commonConfigService.getDischargeMap(provinceId, year, month);
        // 上网电价、补贴电价、下网电价 Map<省id, Map<电价类型0-上网电价1-下网电价2-上网补贴, 电价>>
        Map<Long, Map<Integer, BigDecimal>> priceMap = commonConfigService.getPriceMap(provinceId, year, month);
        int flag = 1;
        if (dischargeMap == null || dischargeMap.get(provinceId) == null || priceMap == null || priceMap.get(provinceId) == null || priceMap.get(provinceId).get(0) == null || priceMap.get(provinceId).get(1) == null || priceMap.get(provinceId).get(2) == null) {
            flag = 0;
        }
        return EssTaskPriceReq.builder()
                .designDailyChargeDischargeCount(dischargeMap == null ? null : dischargeMap.get(provinceId))
                .onGridPrice(priceMap == null || priceMap.get(provinceId) == null ? null : priceMap.get(provinceId).get(0))
                .offGridPrice(priceMap == null || priceMap.get(provinceId) == null ? null : priceMap.get(provinceId).get(1))
                .subsidyPrice(priceMap == null || priceMap.get(provinceId) == null ? null : priceMap.get(provinceId).get(2))
                .editFlag(flag)
                .build();
    }

    // 封装请求入参数据
    public void wrapDayQueryData(TElectricMeteringDayEntity day, List<EssExcelParamReq> essExcelParamReqs,
                                 Map<String, TEsStationEntity> esStationEntityMap, EssTaskPriceReq essTaskPriceReq) {
        // 获取电站信息
        TEsStationEntity tEsStationEntity = esStationEntityMap.get(day.getPsCode());
        // 获取当前日期对应月份、年份的第一天
        DateTime chooseDay = DateUtil.parse(day.getRecordDate(), "yyyy/MM/dd");
        String dayStart = DateUtil.beginOfDay(chooseDay).toString();
        String dayEnd = DateUtil.endOfDay(chooseDay).toString();
        DateTime beginOfMonth = DateUtil.beginOfMonth(chooseDay);
        DateTime beginOfYear = DateUtil.beginOfYear(chooseDay);
        String beginOfMonthStrOne = DateUtil.format(beginOfMonth, "yyyy/MM/dd");
        String beginMonthStrTWO = DateUtil.format(beginOfMonth, "yyyy/MM");
        String beginOfYearStrOne = DateUtil.format(beginOfYear, "yyyy/MM/dd");
        String beginYearTwo = DateUtil.format(beginOfYear, "yyyy/MM");

        // 获取当月截至当前日期的所有数据、当年截至当前日期所有的数据、总计数据 (t_electric_metering_day)
        MeteringDaySumVO meteringDayMonth = essDayAndMonthReportDao.sumMeteringDay(day.getPsCode(), beginOfMonthStrOne, day.getRecordDate());
        meteringDayMonth = meteringDayMonth == null ? new MeteringDaySumVO() : meteringDayMonth;
        MeteringDaySumVO meteringDayYear = essDayAndMonthReportDao.sumMeteringDay(day.getPsCode(), beginOfYearStrOne, day.getRecordDate());
        meteringDayYear = meteringDayYear == null ? new MeteringDaySumVO() : meteringDayYear;
        MeteringDaySumVO meteringDayAll = essDayAndMonthReportDao.sumMeteringDay(day.getPsCode(), null, day.getRecordDate());
        meteringDayAll = meteringDayAll == null ? new MeteringDaySumVO() : meteringDayAll;

        // 获取当月所有数据、当年截至当前月所有的数据 (t_electric_metering_Month)
        MeteringMonthSumVO meteringMonth = essDayAndMonthReportDao.sumMeteringMonth(day.getPsCode(), beginMonthStrTWO, beginMonthStrTWO);
        meteringMonth = meteringMonth == null ? new MeteringMonthSumVO() : meteringMonth;
        MeteringMonthSumVO meteringMonthYear = essDayAndMonthReportDao.sumMeteringMonth(day.getPsCode(), beginYearTwo, beginMonthStrTWO);
        meteringMonthYear = meteringMonthYear == null ? new MeteringMonthSumVO() : meteringMonthYear;
        /*MeteringMonthSumVO meteringMonthAll = essDayAndMonthReportDao.sumMeteringMonth(day.getPsCode(), null, beginMonthStrTWO);
        meteringMonthAll = meteringMonthAll == null ? new MeteringMonthSumVO() : meteringMonthAll;*/

        // 查询各PCS日充、放电量之和
        List<SumPcsCFDayVO> sumPcsCFDay = TdWrappers.lambdaQuery(IotPointStPointEntity.class)
                .eq(IotPointStPointEntity::getPsCode, day.getPsCode())
                .ge(IotPointStPointEntity::getTs, dayStart)
                .le(IotPointStPointEntity::getTs, dayEnd)
                .convertList(SumPcsCFDayVO.class);
        Float pcs6Yc = this.getData(sumPcsCFDay, "PCS6Yc");
        Float pcs7Yc = this.getData(sumPcsCFDay, "PCS7Yc");

        // 查询各PCS当月充、放电量之和
        List<SumPcsCFMonthVO> sumPcsCFMonth = TdWrappers.lambdaQuery(IotPointStPointEntity.class)
                .eq(IotPointStPointEntity::getPsCode, day.getPsCode())
                .ge(IotPointStPointEntity::getTs, beginOfMonth.toString())
                .le(IotPointStPointEntity::getTs, dayEnd)
                .convertList(SumPcsCFMonthVO.class);
        Float pcs49Yc = this.getData(sumPcsCFMonth, "PCS49Yc");
        Float pcs50Yc = this.getData(sumPcsCFMonth, "PCS50Yc");
        // 查询各PCS当年充、放电量之和
        List<SumPcsCFYearVO> sumPcsCFYear = TdWrappers.lambdaQuery(IotPointStPointEntity.class)
                .eq(IotPointStPointEntity::getPsCode, day.getPsCode())
                .ge(IotPointStPointEntity::getTs, beginOfYear.toString())
                .le(IotPointStPointEntity::getTs, dayEnd)
                .convertList(SumPcsCFYearVO.class);
        Float pcs51Yc = this.getData(sumPcsCFYear, "PCS51Yc");
        Float pcs52Yc = this.getData(sumPcsCFYear, "PCS52Yc");
        // 苏州晟能 PCS Mwh-->万千瓦时
        if (StringUtils.equals("30320506002", day.getPsCode())) {
            pcs6Yc = pcs6Yc * Float.parseFloat("1000");
            pcs7Yc = pcs7Yc * Float.parseFloat("1000");
            pcs49Yc = pcs49Yc * Float.parseFloat("1000");
            pcs50Yc = pcs50Yc * Float.parseFloat("1000");
            pcs51Yc = pcs51Yc * Float.parseFloat("1000");
            pcs52Yc = pcs52Yc * Float.parseFloat("1000");
        }
        // 查询各PCS截至当日充、放电量之和
        BigDecimal sumPcsCd = essDayAndMonthReportDao.SumPcsCFAll(day.getPsCode(), dayEnd, "PCS6Yc");
        BigDecimal sumPcsFd = essDayAndMonthReportDao.SumPcsCFAll(day.getPsCode(), dayEnd, "PCS7Yc");

        // 查询各堆的截至当日堆内累计充电电量、充电量
        List<SumBMSAllVO> sumBMSDay = TdWrappers.lambdaQuery(IotPointStPointEntity.class)
                .eq(IotPointStPointEntity::getPsCode, day.getPsCode())
                .le(IotPointStPointEntity::getTs, dayEnd)
                .convertList(SumBMSAllVO.class);
        // 查询各堆的截至昨日堆内累计充电电量、充电量
        List<SumBMSAllVO> sumBMSYesterday = TdWrappers.lambdaQuery(IotPointStPointEntity.class)
                .eq(IotPointStPointEntity::getPsCode, day.getPsCode())
                .le(IotPointStPointEntity::getTs, DateUtil.endOfDay(DateUtil.offsetDay(chooseDay, -1)).toString())
                .convertList(SumBMSAllVO.class);
        // 查询各堆的截至上个月最后一天的堆内累计充电电量、充电量
        List<SumBMSAllVO> sumBMSLastMonth = TdWrappers.lambdaQuery(IotPointStPointEntity.class)
                .eq(IotPointStPointEntity::getPsCode, day.getPsCode())
                .le(IotPointStPointEntity::getTs, DateUtil.endOfDay(DateUtil.offsetDay(beginOfMonth, -1)).toString())
                .convertList(SumBMSAllVO.class);
        // 查询各堆的截至上年最后一天的堆内累计充电电量、充电量
        List<SumBMSAllVO> sumBMSLastYear = TdWrappers.lambdaQuery(IotPointStPointEntity.class)
                .eq(IotPointStPointEntity::getPsCode, day.getPsCode())
                .le(IotPointStPointEntity::getTs, DateUtil.endOfDay(DateUtil.offsetDay(beginOfYear, -1)).toString())
                .convertList(SumBMSAllVO.class);

        // 运行状态 1-停机、2-待机、3-充电、4-放电、5-零功率
        // 查询电站的运行状态-日
        List<PointDataTsVO> statusStDay = essDayAndMonthReportDao.get5mAll(day.getPsCode(), "ST67Yc", dayStart, dayEnd);
        STStatusHourVO stStatusHourVODay = this.getSTStatusHourVO(statusStDay);
        // 查询电站的运行状态(截止到当日)-月
        List<PointDataTsVO> statusStMonth = essDayAndMonthReportDao.get5mAll(day.getPsCode(), "ST67Yc", beginOfMonth.toString(), dayEnd);
        STStatusHourVO stStatusHourVOMonth = this.getSTStatusHourVO(statusStMonth);   // 运行小时统计
        STStatusTimesVO statusTimesMonth = this.getStatusTimes(statusStMonth);        // 运行次数统计
        // 查询电站的运行状态(截止到当日)-年
        List<PointDataTsVO> statusStYear = essDayAndMonthReportDao.get5mAll(day.getPsCode(), "ST67Yc", beginOfYear.toString(), dayEnd);
        STStatusHourVO stStatusHourVOYear = this.getSTStatusHourVO(statusStYear);     // 运行小时统计
        STStatusTimesVO statusTimesYear = this.getStatusTimes(statusStYear);          // 运行次数统计

        // 电站的运行状态和下面所有的PCS状态相同
        // 查询max(非充放电状态下各堆的【堆内单体电压压差极差值】)-月
        List<PointDataTsVO> siteStMonth = essDayAndMonthReportDao.get5mAll(day.getPsCode(), "BMS16Yc", beginOfMonth.toString(), dayEnd);
        Float maxSiteMonth = this.getMaxBMSAllVO(statusStMonth, siteStMonth);
        // 查询max(非充放电状态下各堆的【堆内单体电压压差极差值】)-年
        List<PointDataTsVO> siteStYear = essDayAndMonthReportDao.get5mAll(day.getPsCode(), "BMS16Yc", beginOfYear.toString(), dayEnd);
        Float maxSiteYear = this.getMaxBMSAllVO(statusStYear, siteStYear);
        // 查询max(非充放电状态下各堆的【堆内簇电压差极差值】)-月
        List<PointDataTsVO> clusterStMonth = essDayAndMonthReportDao.get5mAll(day.getPsCode(), "BMS6Yc", beginOfMonth.toString(), dayEnd);
        Float maxClusterMonth = this.getMaxBMSAllVO(statusStMonth, clusterStMonth);
        // 查询max(非充放电状态下各堆的【堆内簇电压差极差值】)-年
        List<PointDataTsVO> clusterStYear = essDayAndMonthReportDao.get5mAll(day.getPsCode(), "BMS6Yc", beginOfYear.toString(), dayEnd);
        Float maxClusterYear = this.getMaxBMSAllVO(statusStYear, clusterStYear);

        List<String> strings = new ArrayList<>(Arrays.asList("30320506001", "30320506002", "30320506003"));
        SumSpecialBMSVO sumSpecialBMSVO = new SumSpecialBMSVO();
        if (CollectionUtil.contains(strings, day.getPsCode())) {
            sumSpecialBMSVO = this.specialBmsInfo(day.getPsCode(), dayStart, dayEnd, chooseDay);
        }
        // 封装Excel公式调用模板,批量计算
        String stringStaticDay = DateUtil.parse(day.getRecordDate(), "yyyy/MM/dd").toString("yyyy-MM-dd");  // 统计日期
        String runDate = DateUtil.format(tEsStationEntity.getWorkingFirstDay(), "yyyy-MM-dd"); // 运行首日
        EssExcelParamReq essExcelParamReq = EssExcelParamReq.builder()
                .capacity(tEsStationEntity.getCapacity().floatValue())
                .power(tEsStationEntity.getPower().floatValue())
                .runDate(runDate)
                .launchDate(DateUtil.format(tEsStationEntity.getPowerCheckTime(), "yyyy-MM-dd"))
                .statisticsDate(stringStaticDay)
                .socUpperLimit(tEsStationEntity.getSocUpLimit() == null ? 0f : tEsStationEntity.getSocUpLimit().floatValue())
                .socLowerLimit(tEsStationEntity.getSocFloorLimit() == null ? 0f : tEsStationEntity.getSocFloorLimit().floatValue())
                .voltageUpperLimit(tEsStationEntity.getCellVoltageUpLimit() == null ? 0f : tEsStationEntity.getCellVoltageUpLimit().floatValue())
                .voltageLowerLimit(tEsStationEntity.getCellVoltageFloorLimit() == null ? 0f : tEsStationEntity.getCellVoltageFloorLimit().floatValue())
                .netPowerDay(day.getOnGridPower() == null ? 0f : day.getOnGridPower().floatValue())
                .netPowerMonth(meteringDayMonth.getOnGridPower())
                .netPowerYear(meteringDayYear.getOnGridPower())
                .netPowerAll(meteringDayAll.getOnGridPower()).offNetPowerDay(day.getOffGridPower() == null ? 0f : day.getOffGridPower().floatValue())
                .offNetPowerMonth(meteringDayMonth.getOffGridPower())
                .offNetPowerYear(meteringDayYear.getOffGridPower())
                .offNetPowerAll(meteringDayAll.getOffGridPower())
                .chargeOffNetPowerDay(day.getChargeOffGridPower() == null ? 0f : day.getChargeOffGridPower().floatValue())
                .chargeOffNetPowerMonth(meteringDayMonth.getChargeOffGridPower())
                .chargeOffNetPowerYear(meteringDayYear.getChargeOffGridPower())
                .netPowerPriceDay((essTaskPriceReq.getSubsidyPrice().add(essTaskPriceReq.getOnGridPrice())).floatValue())
                .netPowerPriceMonth((essTaskPriceReq.getSubsidyPrice().add(essTaskPriceReq.getOnGridPrice())).floatValue())
                .offNetPowerPriceDay(essTaskPriceReq.getOffGridPrice().floatValue())
                .offNetPowerPriceMonth(essTaskPriceReq.getOffGridPrice().floatValue())
                .pcsChargePowerDay(pcs6Yc)
                .pcsChargePowerMonth(pcs49Yc)
                .pcsChargePowerYear(pcs51Yc)
                .pcsChargePowerAll(sumPcsCd == null ? 0f : sumPcsCd.floatValue())
                .pcsDischargePowerDay(pcs7Yc)
                .pcsDischargePowerMonth(pcs50Yc)
                .pcsDischargePowerYear(pcs52Yc)
                .pcsDischargePowerAll(sumPcsFd == null ? 0f : sumPcsFd.floatValue())
                .batteryChargePowerDay(null != sumSpecialBMSVO ? sumSpecialBMSVO.getSumDayCDBms() : this.getBMSValue(this.getData(sumBMSDay, "BMS25Yc"), this.getData(sumBMSYesterday, "BMS25Yc")))
                .batteryChargePowerMonth(null != sumSpecialBMSVO ? sumSpecialBMSVO.getSumMonthCDBms() : this.getBMSValue(this.getData(sumBMSDay, "BMS25Yc"), this.getData(sumBMSLastMonth, "BMS25Yc")))
                .batteryChargePowerYear(null != sumSpecialBMSVO ? sumSpecialBMSVO.getSumYearCDBms() : this.getBMSValue(this.getData(sumBMSDay, "BMS25Yc"), this.getData(sumBMSLastYear, "BMS25Yc")))
                .batteryChargePowerAll(this.getData(sumBMSDay, "BMS25Yc"))
                .batteryDischargePowerDay(null != sumSpecialBMSVO ? sumSpecialBMSVO.getSumDayFDBms() : this.getBMSValue(this.getData(sumBMSDay, "BMS26Yc"), this.getData(sumBMSYesterday, "BMS26Yc")))
                .batteryDischargePowerMonth(null != sumSpecialBMSVO ? sumSpecialBMSVO.getSumMonthFDBms() : this.getBMSValue(this.getData(sumBMSDay, "BMS26Yc"), this.getData(sumBMSLastMonth, "BMS26Yc")))
                .batteryDischargePowerYear(null != sumSpecialBMSVO ? sumSpecialBMSVO.getSumYearFDBms() : this.getBMSValue(this.getData(sumBMSDay, "BMS26Yc"), this.getData(sumBMSLastYear, "BMS26Yc")))
                .batteryDischargePowerAll(this.getData(sumBMSDay, "BMS26Yc"))
                .dischargeNetPowerDay(day.getDischargeOnGridPower() == null ? 0f : day.getDischargeOnGridPower().floatValue())
                .dischargeNetPowerMonth(meteringDayMonth.getDischargeOnGridPower())
                .dischargeNetPowerYear(meteringDayYear.getDischargeOnGridPower())
                .batteryOffNetPowerDay(day.getUnitOffGridPower() == null ? 0f : day.getUnitOffGridPower().floatValue())
                .batteryOffNetPowerMonth(meteringDayMonth.getBatteryOffNetPower())
                .batteryOffNetPowerYear(meteringDayYear.getBatteryOffNetPower())
                .batteryOffNetPowerAll(meteringDayAll.getBatteryOffNetPower())
                .batteryNetPowerDay(day.getUnitOnGridPower() == null ? 0f : day.getUnitOnGridPower().floatValue())
                .batteryNetPowerMonth(meteringDayMonth.getBatteryNetPower())
                .batteryNetPowerYear(meteringDayYear.getBatteryNetPower())
                .batteryNetPowerAll(meteringDayAll.getBatteryNetPower())
                .stationPowerDay(day.getStationPowerConversion() == null ? 0f : day.getStationPowerConversion().floatValue())
                .stationPowerMonth(meteringDayMonth.getStationPowerConversion())
                .stationPowerYear(meteringDayYear.getStationPowerConversion())
                .stationPowerAll(meteringDayAll.getStationPowerConversion())
                .chargeTimeSumDay(stStatusHourVODay.getCdHour())
                .chargeTimeSumMonth(stStatusHourVOMonth.getCdHour())
                .chargeTimeSumYear(stStatusHourVOYear.getCdHour())
                .dischargeTimeSumDay(stStatusHourVODay.getFdPower())
                .dischargeTimeSumMonth(stStatusHourVOMonth.getFdPower())
                .dischargeTimeSumYear(stStatusHourVOYear.getFdPower())
                .designChargeDischargeTimesDay(Float.valueOf(essTaskPriceReq.getDesignDailyChargeDischargeCount()))
                .designChargeDischargeTimesMonth(Float.valueOf(essTaskPriceReq.getDesignDailyChargeDischargeCount()))
                .designChargeDischargeTimesYear(Float.valueOf(essTaskPriceReq.getDesignDailyChargeDischargeCount()))
                .securityPowerMonth(meteringMonth.getSecurityPower())
                .securityPowerYear(meteringMonthYear.getSecurityPower())
                .stopHoursMonth(stStatusHourVOMonth.getTyHour())
                .stopHoursYear(stStatusHourVOYear.getTyHour())
                .standbyHoursMonth(stStatusHourVOMonth.getDjHour())
                .standbyHoursYear(stStatusHourVOYear.getDjHour())
                .planStopHoursMonth(meteringMonth.getPlanStopHours())
                .planStopHoursYear(meteringMonthYear.getPlanStopHours())
                .planStopTimesMonth(meteringMonth.getPlanStopTimes())
                .planStopTimesYear(meteringMonthYear.getPlanStopTimes())
                .maxCellVoltageMonth(maxSiteMonth)
                .maxCellVoltageYear(maxSiteYear)
                .maxClusterVoltageMonth(maxClusterMonth)
                .maxClusterVoltageYear(maxClusterYear)
                .chargeTimesMonth(statusTimesMonth.getCdTimes())
                .chargeTimesYear(statusTimesYear.getCdTimes())
                .dischargeTimesMonth(statusTimesMonth.getFdTimes())
                .dischargeTimesYear(statusTimesYear.getFdTimes())
                .maxDateOne(tEsStationEntity.getWorkingFirstDay().after(beginOfMonth) ? runDate : beginOfMonth.toString("yyyy-MM-dd"))
                .maxDateTwo(tEsStationEntity.getWorkingFirstDay().after(beginOfYear) ? runDate : beginOfYear.toString("yyyy-MM-dd"))
                .maxDateThree(tEsStationEntity.getWorkingFirstDay().after(chooseDay) ? runDate : stringStaticDay)
                .build();
        essExcelParamReqs.add(essExcelParamReq);

    }

    private SumSpecialBMSVO specialBmsInfo(String psCode, String dayStart, String dayEnd, DateTime chooseDay) {
        SumSpecialBMSVO sumSpecialBMSVO = new SumSpecialBMSVO();
        DateTime beginOfMonth = DateUtil.beginOfMonth(chooseDay);
        DateTime beginOfYear = DateUtil.beginOfYear(chooseDay);
        Float sumMonthCDBms = null;
        Float sumMonthFDBms = null;
        Float sumYearCDBms = null;
        Float sumYearFDBms = null;
        // "30320506001", "30320506002"
        if (StringUtils.equals(psCode, "30320506001") || StringUtils.equals(psCode, "30320506002")) {
            // 苏州蓝天、苏州北部 bms日冲日放取电流测日冲日放电量
            // 查询各bms日充、放电量之和
            List<SumBMSCFVO> sumBMSCFDay = TdWrappers.lambdaQuery(IotPointStPointEntity.class)
                    .eq(IotPointStPointEntity::getPsCode, psCode)
                    .ge(IotPointStPointEntity::getTs, dayStart)
                    .le(IotPointStPointEntity::getTs, dayEnd)
                    .convertList(SumBMSCFVO.class);
            Float stszbb0Yc = this.getSpecialData(sumBMSCFDay, "STSZBB0Yc") / 10;
            sumSpecialBMSVO.setSumDayCDBms(stszbb0Yc);
            Float stszbb1Yc = this.getSpecialData(sumBMSCFDay, "STSZBB1Yc") / 10;
            sumSpecialBMSVO.setSumDayFDBms(stszbb1Yc);
            // 查询各bms当月充、放电量之和
            List<SumBMSCFVO> sumBMSCFMonth = TdWrappers.lambdaQuery(IotPointStPointEntity.class)
                    .eq(IotPointStPointEntity::getPsCode, psCode)
                    .ge(IotPointStPointEntity::getTs, beginOfMonth.toString())
                    .le(IotPointStPointEntity::getTs, dayEnd)
                    .convertList(SumBMSCFVO.class);

            if (CollectionUtil.isNotEmpty(sumBMSCFDay)) {
                // 聚合月数据
                sumMonthCDBms = sumBMSCFMonth.stream()
                        .map(s -> s.getSTSZBB0Yc())
                        .reduce((a, b) -> a + b).get() / 10;
                sumMonthFDBms = sumBMSCFMonth.stream()
                        .map(s -> s.getSTSZBB1Yc())
                        .reduce((a, b) -> a + b).get() / 10;
            }
            // 查询各bms当年充、放电量之和
            List<SumBMSCFVO> sumBMSCFYear = TdWrappers.lambdaQuery(IotPointStPointEntity.class)
                    .eq(IotPointStPointEntity::getPsCode, psCode)
                    .ge(IotPointStPointEntity::getTs, beginOfYear.toString())
                    .le(IotPointStPointEntity::getTs, dayEnd)
                    .convertList(SumBMSCFVO.class);
            if (CollectionUtil.isNotEmpty(sumBMSCFDay)) {
                // 聚合月数据
                sumYearCDBms = sumBMSCFYear.stream()
                        .map(s -> s.getSTSZBB0Yc())
                        .reduce((a, b) -> a + b).get() / 10;
                sumYearFDBms = sumBMSCFYear.stream()
                        .map(s -> s.getSTSZBB1Yc())
                        .reduce((a, b) -> a + b).get() / 10;
            }
        }


        // 东吴热电 bms日冲日放取电流测日冲日放电量
        // 查询各bms日充、放电量之和
        if (StringUtils.equals(psCode, "30320506003")) {
            List<DwrdSumBMSCFVO> sumDwrdBMSCFDay = TdWrappers.lambdaQuery(IotPointStPointEntity.class)
                    .eq(IotPointStPointEntity::getPsCode, psCode)
                    .ge(IotPointStPointEntity::getTs, dayStart)
                    .le(IotPointStPointEntity::getTs, dayEnd)
                    .convertList(DwrdSumBMSCFVO.class);
            Float stszbb0Yc = this.getData(sumDwrdBMSCFDay, "BMS1318Yc");
            sumSpecialBMSVO.setSumDayCDBms(stszbb0Yc);
            Float stszbb1Yc = this.getData(sumDwrdBMSCFDay, "BMS1320Yc");
            sumSpecialBMSVO.setSumDayFDBms(stszbb1Yc);

            // 查询各bms当月充、放电量之和
            List<DwrdSumBMSCFVO> sumDwrdBMSCFMonth = TdWrappers.lambdaQuery(IotPointStPointEntity.class)
                    .eq(IotPointStPointEntity::getPsCode, psCode)
                    .ge(IotPointStPointEntity::getTs, beginOfMonth.toString())
                    .le(IotPointStPointEntity::getTs, dayEnd)
                    .convertList(DwrdSumBMSCFVO.class);
            if (CollectionUtil.isNotEmpty(sumDwrdBMSCFDay)) {
                // 聚合月数据
                sumMonthCDBms = sumDwrdBMSCFDay.stream()
                        .map(s -> s.getBMS1318Yc())
                        .reduce((a, b) -> a + b).get() / 10;
                sumMonthFDBms = sumDwrdBMSCFMonth.stream()
                        .map(s -> s.getBMS1320Yc())
                        .reduce((a, b) -> a + b).get() / 10;
            }
            // 查询各bms当年充、放电量之和
            List<DwrdSumBMSCFVO> sumBMSCFYear = TdWrappers.lambdaQuery(IotPointStPointEntity.class)
                    .eq(IotPointStPointEntity::getPsCode, psCode)
                    .ge(IotPointStPointEntity::getTs, beginOfYear.toString())
                    .le(IotPointStPointEntity::getTs, dayEnd)
                    .convertList(DwrdSumBMSCFVO.class);

            if (CollectionUtil.isNotEmpty(sumBMSCFYear)) {
                // 聚合月数据
                sumYearCDBms = sumBMSCFYear.stream()
                        .map(s -> s.getBMS1318Yc())
                        .reduce((a, b) -> a + b).get() / 10;
                sumYearFDBms = sumBMSCFYear.stream()
                        .map(s -> s.getBMS1320Yc())
                        .reduce((a, b) -> a + b).get() / 10;
            }

        }


        sumSpecialBMSVO.setSumMonthFDBms(sumMonthFDBms);
        sumSpecialBMSVO.setSumMonthCDBms(sumMonthCDBms);
        sumSpecialBMSVO.setSumYearFDBms(sumYearFDBms);
        sumSpecialBMSVO.setSumYearCDBms(sumYearCDBms);

        return sumSpecialBMSVO;
    }

    // 校验数据,如果数组为空直接返回null,否则返回数组第一个且字段名为fieldName的值
    private <T> Float getData(List<T> data, String fieldName) {
        // 判断列表是否为空或大小为0
        Float result = 0f;
        if (CollUtil.isEmpty(data)) {
            return result;
        }

        // 获取列表的第一个元素
        T firstElement = data.get(0);
        try {
            // 使用反射获取指定字段
            Field field = firstElement.getClass().getDeclaredField(fieldName);
            field.setAccessible(true); // 确保字段可访问
            Object value = field.get(firstElement);
            if (value != null) {
                result = (Float) value / 10000;
            }
            return result;
        } catch (Exception e) {
            throw new RuntimeException("无法获取指定数据" + e.getMessage(), e);
        }
    }

    private <T> Float getSpecialData(List<T> data, String fieldName) {
        // 判断列表是否为空或大小为0
        Float result = 0f;
        if (CollUtil.isEmpty(data)) {
            return result;
        }

        // 获取列表的第一个元素
        T firstElement = data.get(0);
        try {
            // 使用反射获取指定字段
            Field field = firstElement.getClass().getDeclaredField(fieldName);
            field.setAccessible(true); // 确保字段可访问
            Object value = field.get(firstElement);
            if (value != null) {
                result = (Float) value ;
            }
            return result;
        } catch (Exception e) {
            throw new RuntimeException("无法获取指定数据" + e.getMessage(), e);
        }
    }



    // 计堆的累计值
    public Float getBMSValue(Float value1, Float value2) {
        if (value1 == null || value2 == null) {
            return 0f;
        }
        return value1 - value2;

    }

    // 计算各个状态下的运行时长
    STStatusHourVO getSTStatusHourVO(List<PointDataTsVO> statusHourVOS) {

        STStatusHourVO result = new STStatusHourVO();
        if (CollUtil.isEmpty(statusHourVOS)) {
            return result;
        }
        Map<Integer, List<PointDataTsVO>> statusMap = CollStreamUtil.groupByKey(statusHourVOS, item -> item.getData().intValue());
        for (Map.Entry<Integer, List<PointDataTsVO>> map : statusMap.entrySet()) {
            // 转换成小时数 点位数量 * 5 / 60 保留两位小数
            BigDecimal size = BigDecimal.valueOf(map.getValue().size());
            Float hours = size.multiply(BigDecimal.valueOf(5))
                    .divide(BigDecimal.valueOf(60), 4, RoundingMode.HALF_UP).floatValue();
            switch (map.getKey()) {
                case 1:     // 停运
                    result.setTyHour(hours);
                    break;
                case 2:     // 待机
                    result.setDjHour(hours);
                    break;
                case 3:     // 充电
                    result.setCdHour(hours);
                    break;
                case 4:     //放电
                    result.setFdPower(hours);
                    break;
                default:
                    break;
            }
        }
        return result;
    }

    // 计算充电、放电的次数
    public STStatusTimesVO getStatusTimes(List<PointDataTsVO> statusHourVOS) {

        STStatusTimesVO result = new STStatusTimesVO();
        if (CollUtil.isEmpty(statusHourVOS)) {
            return result;
        }

        int cdTimes = 0, fdTimes = 0;
        for (int i = 0; i < statusHourVOS.size() - 1; i++) {
            // 当前状态和下一个状态
            int currentStatus = statusHourVOS.get(i).getData().intValue();
            int nextStatus = statusHourVOS.get(i + 1).getData().intValue();
            // 如果状态发生突变为充电或放电
            if (currentStatus != nextStatus) {
                if (currentStatus == 3) {
                    cdTimes++;
                } else if (currentStatus == 4) {
                    fdTimes++;
                }
            }
        }
        // 判断最后一个点位是否为充电或放电状态
        int lastStatus = statusHourVOS.get(statusHourVOS.size() - 1).getData().intValue();
        if (lastStatus == 3) {
            cdTimes++;
        } else if (lastStatus == 4) {
            fdTimes++;
        }

        result.setCdTimes(cdTimes);
        result.setFdTimes(fdTimes);
        return result;
    }

    // 判断非充放电状态下的最大值
    public Float getMaxBMSAllVO(List<PointDataTsVO> statusList, List<PointDataTsVO> dataList) {

        Float result = 0f;
        if (CollUtil.isEmpty(statusList) || CollUtil.isEmpty(dataList)) {
            return result;
        }
        // 将状态List根据时间转为Map
        Map<String, PointDataTsVO> statusMap = CollStreamUtil.toMap(statusList, PointDataTsVO::getTs, Function.identity());
        for (PointDataTsVO data : dataList) {
            // 获取与当前数据点时间戳匹配的状态
            PointDataTsVO status = statusMap.get(data.getTs());
            if (status != null) {
                // 判断状态是否为非充电和非放电状态
                if (status.getData().intValue() != 3 && status.getData().intValue() != 4) {
                    // 更新最大值
                    if (result == 0f || (data.getData().compareTo(result) > 0)) {
                        result = data.getData();
                    }
                }
            }
        }
        return result;
    }

    // 封装日报表结果数据
    public List<TDayReportDetailEntity> wrapDayReData(Long mainId, EssExcelResultVO vo, EssExcelParamReq req, BigDecimal sumYearCfProfit) {

        List<TDayReportDetailEntity> dayReportDetailEntityList = new ArrayList<>();
        TDayReportDetailEntity cfProfit = TDayReportDetailEntity.builder().indexCode("cf_profit").unit("万元")
                .dayReportId(mainId)
                .day(this.checkAndSet(vo.getTotalProfitDay()))
                .month(this.checkAndSet(vo.getTotalProfitMonth()))
                .year(sumYearCfProfit == null ? this.checkAndSet(vo.getTotalProfitDay()) : sumYearCfProfit.add(this.checkAndSet(vo.getTotalProfitDay()))).build();     // 充放电毛利
        dayReportDetailEntityList.add(cfProfit);

        TDayReportDetailEntity swPower = TDayReportDetailEntity.builder().indexCode("sw_power").unit("万kWh")
                .dayReportId(mainId)
                .day(this.checkAndSet(req.getNetPowerDay()))
                .month(this.checkAndSet(req.getNetPowerMonth()))
                .year(this.checkAndSet(req.getNetPowerYear())).build();// 上网电量
        dayReportDetailEntityList.add(swPower);

        TDayReportDetailEntity xwPower = TDayReportDetailEntity.builder().indexCode("xw_power").unit("万kWh")
                .dayReportId(mainId)
                .day(this.checkAndSet(req.getOffNetPowerDay()))
                .month(this.checkAndSet(req.getOffNetPowerMonth()))
                .year(this.checkAndSet(req.getOffNetPowerYear())).build();   // 下网电量
        dayReportDetailEntityList.add(xwPower);

        TDayReportDetailEntity cdXwPower = TDayReportDetailEntity.builder().indexCode("cd_xw_power").unit("万kWh")
                .dayReportId(mainId)
                .day(this.checkAndSet(req.getChargeOffNetPowerDay()))
                .month(this.checkAndSet(req.getChargeOffNetPowerMonth()))
                .year(this.checkAndSet(req.getChargeOffNetPowerYear())).build();// 充电时下网电量
        dayReportDetailEntityList.add(cdXwPower);

        TDayReportDetailEntity standbyPower = TDayReportDetailEntity.builder().indexCode("standby_power").unit("万kWh")
                .dayReportId(mainId)
                .day(this.checkAndSet(vo.getStandbyElectricityDay()))
                .month(this.checkAndSet(vo.getStandbyElectricityMonth()))
                .year(this.checkAndSet(vo.getStandbyElectricityYear())).build();  //待机期间用电量
        dayReportDetailEntityList.add(standbyPower);

        TDayReportDetailEntity standbyHourPower = TDayReportDetailEntity.builder().indexCode("standby_hour_power").unit("万kWh")
                .dayReportId(mainId)
                .day(this.checkAndSet(vo.getStandbyHourElectricityDay()))
                .month(this.checkAndSet(vo.getStandbyHourElectricityMonth()))
                .year(this.checkAndSet(vo.getStandbyHourElectricityYear())).build();  //待机期间小时用电量
        dayReportDetailEntityList.add(standbyHourPower);

        TDayReportDetailEntity cfConvertEfficient = TDayReportDetailEntity.builder().indexCode("cf_convert_efficient").unit("%")
                .dayReportId(mainId)
                .day(this.checkAndSetPercent(vo.getEfficiencyDay()))
                .month(this.checkAndSetPercent(vo.getEfficiencyMonth()))
                .year(this.checkAndSetPercent(vo.getEfficiencyYear())).build();  //充放电转换效率
        dayReportDetailEntityList.add(cfConvertEfficient);

        TDayReportDetailEntity allDepletePower = TDayReportDetailEntity.builder().indexCode("all_deplete_power").unit("万kWh")
                .dayReportId(mainId)
                .day(this.checkAndSet(vo.getTotalElectricityDay()))
                .month(this.checkAndSet(vo.getTotalElectricityMonth()))
                .year(this.checkAndSet(vo.getTotalElectricityYear())).build();  // 全场综合耗电量
        dayReportDetailEntityList.add(allDepletePower);

        TDayReportDetailEntity siteDepletePower = TDayReportDetailEntity.builder().indexCode("site_deplete_power").unit("万kWh")
                .dayReportId(mainId)
                .day(this.checkAndSet(vo.getSiteElectricityDay()))
                .month(this.checkAndSet(vo.getSiteElectricityMonth()))
                .year(this.checkAndSet(vo.getSiteElectricityYear())).build();  // 站用电量
        dayReportDetailEntityList.add(siteDepletePower);

        TDayReportDetailEntity pcsChargePower = TDayReportDetailEntity.builder().indexCode("pcs_charge_power").unit("万kWh")
                .dayReportId(mainId)
                .day(this.checkAndSet(req.getPcsChargePowerDay()))
                .month(this.checkAndSet(req.getPcsChargePowerMonth()))
                .year(this.checkAndSet(req.getPcsChargePowerYear())).build();  // PCS充电电量
        dayReportDetailEntityList.add(pcsChargePower);

        TDayReportDetailEntity pcsDischargePower = TDayReportDetailEntity.builder().indexCode("pcs_discharge_power").unit("万kWh")
                .dayReportId(mainId)
                .day(this.checkAndSet(req.getPcsDischargePowerDay()))
                .month(this.checkAndSet(req.getPcsDischargePowerMonth()))
                .year(this.checkAndSet(req.getPcsDischargePowerYear())).build();  // PCS放电电量
        dayReportDetailEntityList.add(pcsDischargePower);

        TDayReportDetailEntity storageDepletePower = TDayReportDetailEntity.builder().indexCode("storage_deplete_power").unit("万kWh")
                .dayReportId(mainId)
                .day(this.checkAndSet(vo.getBatteryLossDay()))
                .month(this.checkAndSet(vo.getBatteryLossMonth()))
                .year(this.checkAndSet(vo.getBatteryLossYear())).build();  // 电池储能损耗电量
        dayReportDetailEntityList.add(storageDepletePower);

        TDayReportDetailEntity bpdDepletePower = TDayReportDetailEntity.builder().indexCode("bpd_deplete_power").unit("万kWh")
                .dayReportId(mainId)
                .day(this.checkAndSet(vo.getTransformerLossDay()))
                .month(this.checkAndSet(vo.getTransformerLossMonth()))
                .year(this.checkAndSet(vo.getTransformerLossYear())).build();  // 变配电损耗电量
        dayReportDetailEntityList.add(bpdDepletePower);

        TDayReportDetailEntity batteryChargePower = TDayReportDetailEntity.builder().indexCode("battery_charge_power").unit("万kWh")
                .dayReportId(mainId)
                .day(this.checkAndSet(req.getBatteryChargePowerDay()))
                .month(this.checkAndSet(req.getBatteryChargePowerMonth()))
                .year(this.checkAndSet(req.getBatteryChargePowerYear()))
                .total(this.checkAndSet(req.getBatteryChargePowerAll())).build();  // 电池充电电量
        dayReportDetailEntityList.add(batteryChargePower);

        TDayReportDetailEntity batteryDischargePower = TDayReportDetailEntity.builder().indexCode("battery_discharge_power").unit("万kWh")
                .dayReportId(mainId)
                .day(this.checkAndSet(req.getBatteryDischargePowerDay()))
                .month(this.checkAndSet(req.getBatteryDischargePowerMonth()))
                .year(this.checkAndSet(req.getBatteryDischargePowerYear()))
                .total(this.checkAndSet(req.getBatteryDischargePowerAll())).build();  // 电池放电电量
        dayReportDetailEntityList.add(batteryDischargePower);

        TDayReportDetailEntity batteryConvertEfficient = TDayReportDetailEntity.builder().indexCode("battery_convert_efficient").unit("%")
                .dayReportId(mainId)
                .day(this.checkAndSetPercent(vo.getBatteryEfficiencyDay()))
                .month(this.checkAndSetPercent(vo.getBatteryEfficiencyMonth()))
                .year(this.checkAndSetPercent(vo.getBatteryEfficiencyYear()))
                .total(this.checkAndSetPercent(vo.getBatteryEfficiencyAll())).build();  // 电池充、放电转换效率
        dayReportDetailEntityList.add(batteryConvertEfficient);

        TDayReportDetailEntity pcsConvertEfficient = TDayReportDetailEntity.builder().indexCode("pcs_convert_efficient").unit("%")
                .dayReportId(mainId)
                .day(this.checkAndSetPercent(vo.getPcsEfficiencyDay()))
                .month(this.checkAndSetPercent(vo.getPcsEfficiencyMonth()))
                .year(this.checkAndSetPercent(vo.getPcsEfficiencyYear())).build();  // PCS储能充、放电效率
        dayReportDetailEntityList.add(pcsConvertEfficient);

        TDayReportDetailEntity jlChargeEfficient = TDayReportDetailEntity.builder().indexCode("jl_charge_efficient").unit("%")
                .dayReportId(mainId)
                .day(this.checkAndSetPercent(vo.getAcChargeEfficiencyDay()))
                .month(this.checkAndSetPercent(vo.getAcChargeEfficiencyMonth()))
                .year(this.checkAndSetPercent(vo.getAcChargeEfficiencyYear())).build();  // 交流侧充电效率
        dayReportDetailEntityList.add(jlChargeEfficient);

        TDayReportDetailEntity chargeConvertEfficient = TDayReportDetailEntity.builder().indexCode("charge_convert_efficient").unit("%")
                .dayReportId(mainId)
                .day(this.checkAndSetPercent(vo.getChargeEfficiencyDay()))
                .month(this.checkAndSetPercent(vo.getChargeEfficiencyMonth()))
                .year(this.checkAndSetPercent(vo.getChargeEfficiencyYear())).build();  // 充电过程转换效率
        dayReportDetailEntityList.add(chargeConvertEfficient);

        TDayReportDetailEntity jlDischargeEfficient = TDayReportDetailEntity.builder().indexCode("jl_discharge_efficient").unit("%")
                .dayReportId(mainId)
                .day(this.checkAndSetPercent(vo.getAcDischargeEfficiencyDay()))
                .month(this.checkAndSetPercent(vo.getAcDischargeEfficiencyMonth()))
                .year(this.checkAndSetPercent(vo.getAcDischargeEfficiencyYear())).build();  // 交流侧放电效率
        dayReportDetailEntityList.add(jlDischargeEfficient);

        TDayReportDetailEntity dischargeConvertEfficient = TDayReportDetailEntity.builder().indexCode("discharge_convert_efficient").unit("%")
                .dayReportId(mainId)
                .day(this.checkAndSetPercent(vo.getDischargeEfficiencyDay()))
                .month(this.checkAndSetPercent(vo.getDischargeEfficiencyMonth()))
                .year(this.checkAndSetPercent(vo.getDischargeEfficiencyYear())).build();  // 放电过程转换效率
        dayReportDetailEntityList.add(dischargeConvertEfficient);

        TDayReportDetailEntity cfCycleEfficient = TDayReportDetailEntity.builder().indexCode("cf_cycle_efficient").unit("%")
                .dayReportId(mainId)
                .day(this.checkAndSetPercent(vo.getCycleEfficiencyDay()))
                .month(this.checkAndSetPercent(vo.getCycleEfficiencyMonth()))
                .year(this.checkAndSetPercent(vo.getCycleEfficiencyYear())).build();  // 充放电周期效率（储能合同）
        dayReportDetailEntityList.add(cfCycleEfficient);

        TDayReportDetailEntity psLoadRate = TDayReportDetailEntity.builder().indexCode("ps_load_rate").unit("%")
                .dayReportId(mainId)
                .day(this.checkAndSetPercent(vo.getStationLoadRateDay()))
                .month(this.checkAndSetPercent(vo.getStationLoadRateMonth()))
                .year(this.checkAndSetPercent(vo.getStationLoadRateYear())).build();  // 电站负荷率
        dayReportDetailEntityList.add(psLoadRate);

        TDayReportDetailEntity psComprehensiveEfficient = TDayReportDetailEntity.builder().indexCode("ps_comprehensive_efficient").unit("%")
                .dayReportId(mainId)
                .day(this.checkAndSetPercent(vo.getStationComprehensiveEfficiencyDay()))
                .month(this.checkAndSetPercent(vo.getStationComprehensiveEfficiencyMonth()))
                .year(this.checkAndSetPercent(vo.getStationComprehensiveEfficiencyYear())).build();  // 电站综合效率
        dayReportDetailEntityList.add(psComprehensiveEfficient);

        TDayReportDetailEntity siteDepleteRate = TDayReportDetailEntity.builder().indexCode("site_deplete_rate").unit("%")
                .dayReportId(mainId)
                .day(this.checkAndSetPercent(vo.getStationElectricityRateDay()))
                .month(this.checkAndSetPercent(vo.getStationElectricityRateMonth()))
                .year(this.checkAndSetPercent(vo.getStationElectricityRateYear()))
                .total(this.checkAndSetPercent(vo.getStationElectricityRateAll())).build();  // 站用电率
        dayReportDetailEntityList.add(siteDepleteRate);

        TDayReportDetailEntity batteryStorageDepleteRate = TDayReportDetailEntity.builder().indexCode("battery_storage_deplete_rate").unit("%")
                .dayReportId(mainId)
                .day(this.checkAndSetPercent(vo.getBatteryLossRateDay()))
                .month(this.checkAndSetPercent(vo.getBatteryLossRateMonth()))
                .year(this.checkAndSetPercent(vo.getBatteryLossRateYear())).build();  // 电池储能耗损率
        dayReportDetailEntityList.add(batteryStorageDepleteRate);

        TDayReportDetailEntity bpdDepleteRate = TDayReportDetailEntity.builder().indexCode("bpd_deplete_rate").unit("%")
                .dayReportId(mainId)
                .day(this.checkAndSetPercent(vo.getTransformerLossRateDay()))
                .month(this.checkAndSetPercent(vo.getTransformerLossRateMonth()))
                .year(this.checkAndSetPercent(vo.getTransformerLossRateYear())).build();  // 变配电耗损率
        dayReportDetailEntityList.add(bpdDepleteRate);

        TDayReportDetailEntity operationHour = TDayReportDetailEntity.builder().indexCode("operation_hour").unit("h")
                .dayReportId(mainId)
                .day(this.checkAndSet(vo.getRunningHoursDay()))
                .month(this.checkAndSet(vo.getRunningHoursMonth()))
                .year(this.checkAndSet(vo.getRunningHoursYear())).build();  // 运行利用小时数
        dayReportDetailEntityList.add(operationHour);

        TDayReportDetailEntity equalUtilizeEfficient = TDayReportDetailEntity.builder().indexCode("equal_utilize_efficient").unit("%")
                .dayReportId(mainId)
                .day(this.checkAndSetPercent(vo.getEquivalentUtilizationCoefficientDay()))
                .month(this.checkAndSetPercent(vo.getEquivalentUtilizationCoefficientMonth()))
                .year(this.checkAndSetPercent(vo.getEquivalentUtilizationCoefficientYear())).build();  // 等效利用小时数
        dayReportDetailEntityList.add(equalUtilizeEfficient);

        TDayReportDetailEntity swPrice = TDayReportDetailEntity.builder().indexCode("sw_price").unit("元/kWh")
                .dayReportId(mainId)
                .day(this.checkAndSet(req.getNetPowerPriceDay()))
                .month(this.checkAndSet(req.getNetPowerPriceDay()))
                .year(null).build();  // 上网电价
        dayReportDetailEntityList.add(swPrice);

        TDayReportDetailEntity xwPrice = TDayReportDetailEntity.builder().indexCode("xw_price").unit("元/kWh")
                .dayReportId(mainId)
                .day(this.checkAndSet(req.getOffNetPowerPriceDay()))
                .month(this.checkAndSet(req.getOffNetPowerPriceMonth()))
                .year(null).build();  // 下网电价
        dayReportDetailEntityList.add(xwPrice);

        TDayReportDetailEntity pcsLossRate = TDayReportDetailEntity.builder().indexCode("pcs_loss_rate").unit("%")
                .dayReportId(mainId)
                .day(this.checkAndSetPercent(vo.getPcsLossRateDay()))
                .month(this.checkAndSetPercent(vo.getPcsLossRateMonth()))
                .year(this.checkAndSetPercent(vo.getPcsLossRateYear()))
                .total(this.checkAndSetPercent(vo.getPcsLossRateTotal())).build();  // PCS损耗电率
        dayReportDetailEntityList.add(pcsLossRate);

        TDayReportDetailEntity mainLossRate = TDayReportDetailEntity.builder().indexCode("main_loss_rate").unit("%")
                .dayReportId(mainId)
                .day(this.checkAndSetPercent(vo.getMainLossRateDay()))
                .month(this.checkAndSetPercent(vo.getMainLossRateMonth()))
                .year(this.checkAndSetPercent(vo.getMainLossRateYear()))
                .total(this.checkAndSetPercent(vo.getMainLossRateTotal())).build();  // 主变损耗电率
        dayReportDetailEntityList.add(mainLossRate);

        return dayReportDetailEntityList;
    }

    // 封装月报表结果数据
    public List<TMonthReportDetailEntity> wrapMonthData(Long mainId, EssExcelResultVO vo, EssExcelParamReq req) {

        List<TMonthReportDetailEntity> monthReportDetailEntityList = new ArrayList<>();
        TMonthReportDetailEntity swPower = TMonthReportDetailEntity.builder().indexCode("sw_power").unit("万kWh")
                .monthReportId(mainId)
                .month(this.checkAndSet(req.getNetPowerMonth()))
                .year(this.checkAndSet(req.getNetPowerYear())).build();  // 上网电量
        monthReportDetailEntityList.add(swPower);

        TMonthReportDetailEntity xwPower = TMonthReportDetailEntity.builder().indexCode("xw_power").unit("万kWh")
                .monthReportId(mainId)
                .month(this.checkAndSet(req.getOffNetPowerMonth()))
                .year(this.checkAndSet(req.getOffNetPowerYear())).build();  // 下网电量
        monthReportDetailEntityList.add(xwPower);

        TMonthReportDetailEntity cdXwPower = TMonthReportDetailEntity.builder().indexCode("cd_xw_power").unit("万kWh")
                .monthReportId(mainId)
                .month(this.checkAndSet(req.getChargeOffNetPowerMonth()))
                .year(this.checkAndSet(req.getChargeOffNetPowerYear())).build();  // 充电时下网电量
        monthReportDetailEntityList.add(cdXwPower);

        TMonthReportDetailEntity storageUnitSwPower = TMonthReportDetailEntity.builder().indexCode("storage_unit_sw_power").unit("万kWh")
                .monthReportId(mainId)
                .month(this.checkAndSet(req.getBatteryNetPowerMonth()))
                .year(this.checkAndSet(req.getBatteryNetPowerYear())).build();  // 储能单元上网电量
        monthReportDetailEntityList.add(storageUnitSwPower);

        TMonthReportDetailEntity storageUnitXwPower = TMonthReportDetailEntity.builder().indexCode("storage_unit_xw_power").unit("万kWh")
                .monthReportId(mainId)
                .month(this.checkAndSet(req.getBatteryOffNetPowerMonth()))
                .year(this.checkAndSet(req.getBatteryOffNetPowerYear())).build();  // 储能单元下网电量
        monthReportDetailEntityList.add(storageUnitXwPower);

        TMonthReportDetailEntity standbyPower = TMonthReportDetailEntity.builder().indexCode("standby_power").unit("万kWh")
                .monthReportId(mainId)
                .month(this.checkAndSet(vo.getStandbyElectricityMonth()))
                .year(this.checkAndSet(vo.getStandbyElectricityYear())).build();  // 待机期间站用电量
        monthReportDetailEntityList.add(standbyPower);

        TMonthReportDetailEntity standbyHourPower = TMonthReportDetailEntity.builder().indexCode("standby_hour_power").unit("万kWh")
                .monthReportId(mainId)
                .month(this.checkAndSet(vo.getStandbyHourElectricityMonth()))
                .year(this.checkAndSet(vo.getStandbyHourElectricityYear())).build();  // 待机期间小时消耗站用电量
        monthReportDetailEntityList.add(standbyHourPower);

        TMonthReportDetailEntity cfConvertEfficient = TMonthReportDetailEntity.builder().indexCode("cf_convert_efficient").unit("%")
                .monthReportId(mainId)
                .month(this.checkAndSetPercent(vo.getEfficiencyMonth()))
                .year(this.checkAndSetPercent(vo.getEfficiencyYear())).build();  // 充放电转换效率（电科院）
        monthReportDetailEntityList.add(cfConvertEfficient);

        TMonthReportDetailEntity allDepletePower = TMonthReportDetailEntity.builder().indexCode("all_deplete_power").unit("万kWh")
                .monthReportId(mainId)
                .month(this.checkAndSet(vo.getTotalElectricityMonth()))
                .year(this.checkAndSet(vo.getTotalElectricityYear())).build();  // 全场综合耗电量
        monthReportDetailEntityList.add(allDepletePower);

        TMonthReportDetailEntity siteDepletePower = TMonthReportDetailEntity.builder().indexCode("site_deplete_power").unit("万kWh")
                .monthReportId(mainId)
                .month(this.checkAndSet(vo.getSiteElectricityMonth()))
                .year(this.checkAndSet(vo.getSiteElectricityYear())).build();  // 站用电量
        monthReportDetailEntityList.add(siteDepletePower);

        TMonthReportDetailEntity pcsChargePower = TMonthReportDetailEntity.builder().indexCode("pcs_charge_power").unit("万kWh")
                .monthReportId(mainId)
                .month(this.checkAndSet(req.getPcsChargePowerMonth()))
                .year(this.checkAndSet(req.getPcsChargePowerYear())).build();  // PCS充电电量
        monthReportDetailEntityList.add(pcsChargePower);

        TMonthReportDetailEntity pcsDischargePower = TMonthReportDetailEntity.builder().indexCode("pcs_discharge_power").unit("万kWh")
                .monthReportId(mainId)
                .month(this.checkAndSet(req.getPcsDischargePowerMonth()))
                .year(this.checkAndSet(req.getPcsDischargePowerYear())).build();  // PCS放电电量
        monthReportDetailEntityList.add(pcsDischargePower);

        TMonthReportDetailEntity storageDepletePower = TMonthReportDetailEntity.builder().indexCode("storage_deplete_power").unit("万kWh")
                .monthReportId(mainId)
                .month(this.checkAndSet(vo.getBatteryLossMonth()))
                .year(this.checkAndSet(vo.getBatteryLossYear())).build();  // 电池储能损耗电量
        monthReportDetailEntityList.add(storageDepletePower);

        TMonthReportDetailEntity bpdDepletePower = TMonthReportDetailEntity.builder().indexCode("bpd_deplete_power").unit("万kWh")
                .monthReportId(mainId)
                .month(this.checkAndSet(vo.getTransformerLossMonth()))
                .year(this.checkAndSet(vo.getTransformerLossYear())).build();  // 变配电损耗电量
        monthReportDetailEntityList.add(bpdDepletePower);

        TMonthReportDetailEntity batteryChargePower = TMonthReportDetailEntity.builder().indexCode("battery_charge_power").unit("万kWh")
                .monthReportId(mainId)
                .month(this.checkAndSet(req.getBatteryChargePowerMonth()))
                .year(this.checkAndSet(req.getBatteryChargePowerYear()))
                .total(this.checkAndSet(req.getBatteryChargePowerAll())).build();  // 电池充电电量
        monthReportDetailEntityList.add(batteryChargePower);

        TMonthReportDetailEntity batteryDischargePower = TMonthReportDetailEntity.builder().indexCode("battery_discharge_power").unit("万kWh")
                .monthReportId(mainId)
                .month(this.checkAndSet(req.getBatteryDischargePowerMonth()))
                .year(this.checkAndSet(req.getBatteryDischargePowerYear()))
                .total(this.checkAndSet(req.getBatteryDischargePowerAll())).build();  // 电池放电电量
        monthReportDetailEntityList.add(batteryDischargePower);

        TMonthReportDetailEntity zeroSecurityTransformer = TMonthReportDetailEntity.builder().indexCode("zero_security_transformer").unit("万kWh")
                .monthReportId(mainId)
                .month(this.checkAndSet(req.getSecurityPowerMonth()))
                .year(this.checkAndSet(req.getSecurityPowerYear())).build();  // 0号保安变电量
        monthReportDetailEntityList.add(zeroSecurityTransformer);

        TMonthReportDetailEntity bwdAverageChargePower = TMonthReportDetailEntity.builder().indexCode("bwd_average_charge_power").unit("MW")
                .monthReportId(mainId)
                .month(this.checkAndSet(vo.getAverageChargingPowerMonth()))
                .year(this.checkAndSet(vo.getAverageChargingPowerYear())).build();  // 并网点充电平均功率
        monthReportDetailEntityList.add(bwdAverageChargePower);

        TMonthReportDetailEntity bwdAverageDisChargePower = TMonthReportDetailEntity.builder().indexCode("bwd_average_discharge_power").unit("MW")
                .monthReportId(mainId)
                .month(this.checkAndSet(vo.getAverageDischargingPowerMonth()))
                .year(this.checkAndSet(vo.getAverageDischargingPowerYear())).build();  // 并网点放电电平均功率
        monthReportDetailEntityList.add(bwdAverageDisChargePower);

        TMonthReportDetailEntity batteryConvertEfficient = TMonthReportDetailEntity.builder().indexCode("battery_convert_efficient").unit("%")
                .monthReportId(mainId)
                .month(this.checkAndSetPercent(vo.getBatteryEfficiencyMonth()))
                .year(this.checkAndSetPercent(vo.getBatteryEfficiencyYear())).build();  // 电池充、放电转换效率
        monthReportDetailEntityList.add(batteryConvertEfficient);

        TMonthReportDetailEntity pcsConvertEfficient = TMonthReportDetailEntity.builder().indexCode("pcs_convert_efficient").unit("%")
                .monthReportId(mainId)
                .month(this.checkAndSetPercent(vo.getPcsEfficiencyMonth()))
                .year(this.checkAndSetPercent(vo.getPcsEfficiencyYear())).build();  // PCS储能充、放电效率
        monthReportDetailEntityList.add(pcsConvertEfficient);

        TMonthReportDetailEntity jlChargeEfficient = TMonthReportDetailEntity.builder().indexCode("jl_charge_efficient").unit("%")
                .monthReportId(mainId)
                .month(this.checkAndSetPercent(vo.getAcChargeEfficiencyMonth()))
                .year(this.checkAndSetPercent(vo.getAcChargeEfficiencyYear())).build();  // 交流侧充电效率
        monthReportDetailEntityList.add(jlChargeEfficient);

        TMonthReportDetailEntity chargeConvertEfficient = TMonthReportDetailEntity.builder().indexCode("charge_convert_efficient").unit("%")
                .monthReportId(mainId)
                .month(this.checkAndSetPercent(vo.getChargeEfficiencyMonth()))
                .year(this.checkAndSetPercent(vo.getChargeEfficiencyYear())).build();  // 充电过程转换效率
        monthReportDetailEntityList.add(chargeConvertEfficient);

        TMonthReportDetailEntity jlDischargeEfficient = TMonthReportDetailEntity.builder().indexCode("jl_discharge_efficient").unit("%")
                .monthReportId(mainId)
                .month(this.checkAndSetPercent(vo.getAcDischargeEfficiencyMonth()))
                .year(this.checkAndSetPercent(vo.getAcDischargeEfficiencyYear())).build();  // 交流侧放电效率
        monthReportDetailEntityList.add(jlDischargeEfficient);

        TMonthReportDetailEntity dischargeConvertEfficient = TMonthReportDetailEntity.builder().indexCode("discharge_convert_efficient").unit("%")
                .monthReportId(mainId)
                .month(this.checkAndSetPercent(vo.getDischargeEfficiencyMonth()))
                .year(this.checkAndSetPercent(vo.getDischargeEfficiencyYear())).build();  // 放电过程转换效率
        monthReportDetailEntityList.add(dischargeConvertEfficient);

        TMonthReportDetailEntity cfCycleEfficient = TMonthReportDetailEntity.builder().indexCode("cf_cycle_efficient").unit("%")
                .monthReportId(mainId)
                .month(this.checkAndSetPercent(vo.getCycleEfficiencyMonth()))
                .year(this.checkAndSetPercent(vo.getCycleEfficiencyYear())).build();  // 充放电周期效率（储能合同）
        monthReportDetailEntityList.add(cfCycleEfficient);

        TMonthReportDetailEntity psLoadRate = TMonthReportDetailEntity.builder().indexCode("ps_load_rate").unit("%")
                .monthReportId(mainId)
                .month(this.checkAndSetPercent(vo.getStationLoadRateMonth()))
                .year(this.checkAndSetPercent(vo.getStationLoadRateYear())).build();  // 电站负荷率
        monthReportDetailEntityList.add(psLoadRate);

        TMonthReportDetailEntity psComprehensiveEfficient = TMonthReportDetailEntity.builder().indexCode("ps_comprehensive_efficient").unit("%")
                .monthReportId(mainId)
                .month(this.checkAndSetPercent(vo.getStationComprehensiveEfficiencyMonth()))
                .year(this.checkAndSetPercent(vo.getStationComprehensiveEfficiencyYear())).build();  // 电站综合效率
        monthReportDetailEntityList.add(psComprehensiveEfficient);

        TMonthReportDetailEntity siteDepleteRate = TMonthReportDetailEntity.builder().indexCode("site_deplete_rate").unit("%")
                .monthReportId(mainId)
                .month(this.checkAndSetPercent(vo.getStationElectricityRateMonth()))
                .year(this.checkAndSetPercent(vo.getStationElectricityRateYear())).build();  // 站用电率
        monthReportDetailEntityList.add(siteDepleteRate);

        TMonthReportDetailEntity batteryStorageDepleteRate = TMonthReportDetailEntity.builder().indexCode("battery_storage_deplete_rate").unit("%")
                .monthReportId(mainId)
                .month(this.checkAndSetPercent(vo.getBatteryLossRateMonth()))
                .year(this.checkAndSetPercent(vo.getBatteryLossRateYear())).build();  // 电池储能耗损率
        monthReportDetailEntityList.add(batteryStorageDepleteRate);

        TMonthReportDetailEntity bpdDepleteRate = TMonthReportDetailEntity.builder().indexCode("bpd_deplete_rate").unit("%")
                .monthReportId(mainId)
                .month(this.checkAndSetPercent(vo.getTransformerLossRateMonth()))
                .year(this.checkAndSetPercent(vo.getTransformerLossRateYear())).build();  // 变配电耗损率
        monthReportDetailEntityList.add(bpdDepleteRate);

        TMonthReportDetailEntity chargeNum = TMonthReportDetailEntity.builder().indexCode("charge_num").unit("次")
                .monthReportId(mainId)
                .month(this.checkAndSet(req.getChargeTimesMonth()))
                .year(this.checkAndSet(req.getChargeTimesYear())).build();  // 充电次数
        monthReportDetailEntityList.add(chargeNum);

        TMonthReportDetailEntity dischargeNum = TMonthReportDetailEntity.builder().indexCode("discharge_num").unit("次")
                .monthReportId(mainId)
                .month(this.checkAndSet(req.getDischargeTimesMonth()))
                .year(this.checkAndSet(req.getDischargeTimesYear())).build();  // 放电次数
        monthReportDetailEntityList.add(dischargeNum);

        TMonthReportDetailEntity equalCfNum = TMonthReportDetailEntity.builder().indexCode("equal_cf_num").unit("次")
                .monthReportId(mainId)
                .month(this.checkAndSet(vo.getEquivalentChargeMonth()))
                .year(this.checkAndSet(vo.getEquivalentChargeYear())).build();  // 等效充放电次数
        monthReportDetailEntityList.add(equalCfNum);

        TMonthReportDetailEntity shutdownHour = TMonthReportDetailEntity.builder().indexCode("shutdown_hour").unit("h")
                .monthReportId(mainId)
                .month(this.checkAndSet(req.getStopHoursMonth()))
                .year(this.checkAndSet(req.getStopHoursYear())).build();  // 当月电站停机小时数
        monthReportDetailEntityList.add(shutdownHour);

        TMonthReportDetailEntity standbyHour = TMonthReportDetailEntity.builder().indexCode("standby_hour").unit("h")
                .monthReportId(mainId)
                .month(this.checkAndSet(req.getStandbyHoursMonth()))
                .year(this.checkAndSet(req.getStandbyHoursYear())).build();  // 当月电站待机小时数
        monthReportDetailEntityList.add(standbyHour);

        TMonthReportDetailEntity chargeSumHour = TMonthReportDetailEntity.builder().indexCode("charge_sum_hour").unit("h")
                .monthReportId(mainId)
                .month(this.checkAndSet(req.getChargeTimeSumMonth()))
                .year(this.checkAndSet(req.getChargeTimeSumYear())).build();  // 充电总时间（充电运行小时）
        monthReportDetailEntityList.add(chargeSumHour);

        TMonthReportDetailEntity dischargeSumHour = TMonthReportDetailEntity.builder().indexCode("discharge_sum_hour").unit("h")
                .monthReportId(mainId)
                .month(this.checkAndSet(req.getDischargeTimeSumMonth()))
                .year(this.checkAndSet(req.getDischargeTimeSumYear())).build();  // 放电总时间（充电运行小时）
        monthReportDetailEntityList.add(dischargeSumHour);

        TMonthReportDetailEntity operateHour = TMonthReportDetailEntity.builder().indexCode("operate_hour").unit("h")
                .monthReportId(mainId)
                .month(this.checkAndSet(vo.getRunningHoursTotalMonth()))
                .year(this.checkAndSet(vo.getRunningHoursTotalYear())).build();  // 运行小时
        monthReportDetailEntityList.add(operateHour);

        TMonthReportDetailEntity chargeHour = TMonthReportDetailEntity.builder().indexCode("charge_hour").unit("h")
                .monthReportId(mainId)
                .month(this.checkAndSet(vo.getChargingUtilizationHoursMonth()))
                .year(this.checkAndSet(vo.getChargingUtilizationHoursYear())).build();  // 充电利用小时数
        monthReportDetailEntityList.add(chargeHour);

        TMonthReportDetailEntity dischargeHour = TMonthReportDetailEntity.builder().indexCode("discharge_hour").unit("h")
                .monthReportId(mainId)
                .month(this.checkAndSet(vo.getDischargingUtilizationHoursMonth()))
                .year(this.checkAndSet(vo.getDischargingUtilizationHoursYear())).build();  // 放电利用小时数
        monthReportDetailEntityList.add(dischargeHour);

        TMonthReportDetailEntity utilizeHour = TMonthReportDetailEntity.builder().indexCode("utilize_hour").unit("h")
                .monthReportId(mainId)
                .month(this.checkAndSet(vo.getUtilizationHoursMonth()))
                .year(this.checkAndSet(vo.getUtilizationHoursYear())).build();  // 利用小时
        monthReportDetailEntityList.add(utilizeHour);

        TMonthReportDetailEntity utilizeCoefficient = TMonthReportDetailEntity.builder().indexCode("utilize_coefficient").unit("%")
                .monthReportId(mainId)
                .month(this.checkAndSetPercent(vo.getUtilizationCoefficientMonth()))
                .year(this.checkAndSetPercent(vo.getUtilizationCoefficientYear())).build();  // 利用系数
        monthReportDetailEntityList.add(utilizeCoefficient);

        TMonthReportDetailEntity utilizationIndex = TMonthReportDetailEntity.builder().indexCode("utilization_index").unit("%")
                .monthReportId(mainId)
                .month(this.checkAndSetPercent(vo.getUtilizationRateIndexMonth()))
                .year(this.checkAndSetPercent(vo.getUtilizationRateIndexYear())).build();  // 利用率指数
        monthReportDetailEntityList.add(utilizationIndex);

        TMonthReportDetailEntity planOutageCoefficient = TMonthReportDetailEntity.builder().indexCode("plan_outage_coefficient").unit("%")
                .monthReportId(mainId)
                .month(this.checkAndSetPercent(vo.getPlanStopCoefficientMonth()))
                .year(this.checkAndSetPercent(vo.getPlanStopCoefficientYear())).build();  // 计划停运系数
        monthReportDetailEntityList.add(planOutageCoefficient);

        TMonthReportDetailEntity avePlanOutageHour = TMonthReportDetailEntity.builder().indexCode("ave_plan_shutdown_hour").unit("h")
                .monthReportId(mainId)
                .month(this.checkAndSet(vo.getAveragePlanStopHoursMonth()))
                .year(this.checkAndSet(vo.getAveragePlanStopHoursYear())).build();  // 平均计划停运小时
        monthReportDetailEntityList.add(avePlanOutageHour);

        TMonthReportDetailEntity unPlanOutageCoefficient = TMonthReportDetailEntity.builder().indexCode("un_plan_outage_coefficient").unit("%")
                .monthReportId(mainId)
                .month(this.checkAndSetPercent(vo.getNonPlanStopCoefficientMonth()))
                .year(this.checkAndSetPercent(vo.getNonPlanStopCoefficientYear())).build();  // 非计划停运系数
        monthReportDetailEntityList.add(unPlanOutageCoefficient);

        TMonthReportDetailEntity aveUnPlanOutageHour = TMonthReportDetailEntity.builder().indexCode("ave_un_plan_shutdown_hour").unit("h")
                .monthReportId(mainId)
                .month(this.checkAndSet(vo.getAverageNonPlanStopHoursMonth()))
                .year(this.checkAndSet(vo.getAverageNonPlanStopHoursYear())).build();  // 平均非计划停运小时
        monthReportDetailEntityList.add(aveUnPlanOutageHour);

        TMonthReportDetailEntity availableHour = TMonthReportDetailEntity.builder().indexCode("available_hour").unit("h")
                .monthReportId(mainId)
                .month(this.checkAndSet(vo.getAvailableHoursMonth()))
                .year(this.checkAndSet(vo.getAvailableHoursYear())).build();  // 可用小时
        monthReportDetailEntityList.add(availableHour);

        TMonthReportDetailEntity availableCoefficient = TMonthReportDetailEntity.builder().indexCode("available_coefficient").unit("%")
                .monthReportId(mainId)
                .month(this.checkAndSetPercent(vo.getAvailableCoefficientMonth()))
                .year(this.checkAndSetPercent(vo.getAvailableCoefficientYear())).build();  // 可用系数
        monthReportDetailEntityList.add(availableCoefficient);

        TMonthReportDetailEntity spareHour = TMonthReportDetailEntity.builder().indexCode("spare_hour").unit("h")
                .monthReportId(mainId)
                .month(this.checkAndSet(vo.getStandbyHoursMonth()))
                .year(this.checkAndSet(vo.getStandbyHoursYear())).build();  // 备用小时
        monthReportDetailEntityList.add(spareHour);

        TMonthReportDetailEntity spareCoefficient = TMonthReportDetailEntity.builder().indexCode("spare_coefficient").unit("%")
                .monthReportId(mainId)
                .month(this.checkAndSetPercent(vo.getStandbyCoefficientMonth()))
                .year(this.checkAndSetPercent(vo.getStandbyCoefficientYear())).build();  // 备用系数
        monthReportDetailEntityList.add(spareCoefficient);

        TMonthReportDetailEntity allSocMax = TMonthReportDetailEntity.builder().indexCode("all_soc_max").unit("%")
                .monthReportId(mainId)
                .month(this.checkAndSetPercent(req.getSocUpperLimit()))
                .year(this.checkAndSetPercent(req.getSocUpperLimit())).build();  // 整站SOC设定上限值
        monthReportDetailEntityList.add(allSocMax);

        TMonthReportDetailEntity allSocMin = TMonthReportDetailEntity.builder().indexCode("all_soc_min").unit("%")
                .monthReportId(mainId)
                .month(this.checkAndSetPercent(req.getSocLowerLimit()))
                .year(this.checkAndSetPercent(req.getSocLowerLimit())).build();  // 整站SOC设定下限值
        monthReportDetailEntityList.add(allSocMin);

        TMonthReportDetailEntity cellVoltageMax = TMonthReportDetailEntity.builder().indexCode("cell_voltage_max").unit("V")
                .monthReportId(mainId)
                .month(this.checkAndSet(req.getVoltageUpperLimit()))
                .year(this.checkAndSet(req.getVoltageUpperLimit())).build();  // 电芯电压设定上限值
        monthReportDetailEntityList.add(cellVoltageMax);

        TMonthReportDetailEntity cellVoltageMin = TMonthReportDetailEntity.builder().indexCode("cell_voltage_min").unit("V")
                .monthReportId(mainId)
                .month(this.checkAndSet(req.getVoltageLowerLimit()))
                .year(this.checkAndSet(req.getVoltageLowerLimit())).build();  // 电芯电压设定下限值
        monthReportDetailEntityList.add(cellVoltageMin);

        TMonthReportDetailEntity unCfBatteryMax = TMonthReportDetailEntity.builder().indexCode("un_cf_battery_max").unit("mV")
                .monthReportId(mainId)
                .month(this.checkAndSet(req.getMaxCellVoltageMonth()))
                .year(this.checkAndSet(req.getMaxCellVoltageYear())).build();  // 非充放电状态单体电池最大压差
        monthReportDetailEntityList.add(unCfBatteryMax);

        TMonthReportDetailEntity unCfGroupMax = TMonthReportDetailEntity.builder().indexCode("un_cf_group_max").unit("V")
                .monthReportId(mainId)
                .month(this.checkAndSet(req.getMaxClusterVoltageMonth()))
                .year(this.checkAndSet(req.getMaxClusterVoltageYear())).build();  // 非充放电状态簇间最大压差
        monthReportDetailEntityList.add(unCfGroupMax);

        TMonthReportDetailEntity swPrice = TMonthReportDetailEntity.builder().indexCode("sw_price").unit("元/kWh")
                .monthReportId(mainId)
                .month(this.checkAndSet(req.getNetPowerPriceDay()))
                .year(null).build();  // 上网电价
        monthReportDetailEntityList.add(swPrice);

        TMonthReportDetailEntity xwPrice = TMonthReportDetailEntity.builder().indexCode("xw_price").unit("元/kWh")
                .monthReportId(mainId)
                .month(this.checkAndSet(req.getOffNetPowerPriceMonth()))
                .year(null).build();  // 下网电价
        monthReportDetailEntityList.add(xwPrice);

        TMonthReportDetailEntity pcsLossRate = TMonthReportDetailEntity.builder().indexCode("pcs_loss_rate").unit("%")
                .monthReportId(mainId)
                .month(this.checkAndSetPercent(vo.getPcsLossRateMonth()))
                .year(this.checkAndSetPercent(vo.getPcsLossRateYear()))
                .total(this.checkAndSetPercent(vo.getPcsLossRateTotal())).build();  // PCS损耗电率
        monthReportDetailEntityList.add(pcsLossRate);

        TMonthReportDetailEntity mainLossRate = TMonthReportDetailEntity.builder().indexCode("main_loss_rate").unit("%")
                .monthReportId(mainId)
                .month(this.checkAndSetPercent(vo.getMainLossRateMonth()))
                .year(this.checkAndSetPercent(vo.getMainLossRateYear()))
                .total(this.checkAndSetPercent(vo.getMainLossRateTotal())).build();  // 主变损耗电率
        monthReportDetailEntityList.add(mainLossRate);

        return monthReportDetailEntityList;
    }

    // 检查并赋值 常规
    public <T> BigDecimal checkAndSet(T checkValue) {
        if (checkValue == null) {
            return null;
        }
        return new BigDecimal(String.valueOf(checkValue)).setScale(4, RoundingMode.HALF_UP);
    }

    // 检查并赋值 百分比
    public <T> BigDecimal checkAndSetPercent(T checkValue) {
        if (checkValue == null) {
            return null;
        }
        return new BigDecimal(String.valueOf(checkValue)).multiply(BigDecimal.valueOf(100)).setScale(2, RoundingMode.HALF_UP);
    }

}
