<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.gcl.psmis.monitor.applet.dao.reform.ReformDao">


    <select id="getReformOrderList" resultType="com.gcl.psmis.framework.common.resp.reform.ReformOrderResp">
        SELECT
        ps.ps_code,
        u2.`name` as userName,
        acc.acceptance_no,
        acc.acceptance_type,
        CASE
        WHEN ord.reform_no IS NULL THEN
        1 ELSE 2
        END AS tag,
        ord.`status`,
        CASE
        WHEN ord.`status` = 301 THEN
        '整改方案'
        WHEN ord.`status` = 302 THEN
        '待整改'
        WHEN ord.`status` = 303 THEN
        '技术审核'
        WHEN ord.`status` = 304 THEN
        '质量审核'
        WHEN ord.`status` = 305 THEN
        '整改完成'
        END                                              statusLabel,
        CASE
        WHEN TIMESTAMPDIFF(SECOND, IFNULL(ord.tech_start_time, NOW()), IFNULL(ord.finish_time, NOW()))  >   60*60*24*30 THEN
        '超时'
        ELSE
        '未超时'
        END AS reformOverTime,
        CONCAT( r.province_name, r.city_name, r.area_name, ps.address ) AS psAddress,
        u1.F_RealName as acceptanceUser,
        acc.acceptance_end_time,
        ps.lat,
        ps.lng,
        acc.create_time
        FROM
        t_power_station ps
        INNER JOIN t_reform_acceptance acc ON ps.id = acc.ps_id
        LEFT JOIN t_reform_order ord ON ord.source_no = acc.acceptance_no
        LEFT JOIN t_region r ON r.id = ps.region_id
        LEFT JOIN base_user u1 ON u1.F_Id = acc.acceptance_user_id
        LEFT JOIN t_user u2 ON u2.id = ps.user_id
        <where>
            acc.del_flag = 0 and acc.cancel_flag = 0
            <if test="req.acceptanceType != null ">
                AND acc.acceptance_type = #{req.acceptanceType}
            </if>
            <if test="req.tag != null and  req.tag == 1">
                AND ord.reform_no is null
            </if>
            <if test="req.tag != null and req.tag == 2">
                AND ord.reform_no is not null
            </if>
            <if test="req.checkState!=null and req.checkState!=''">
                and acc.acceptance_status = #{req.checkState}
            </if>

            <if test="req.userId!=null and req.userId!=''">
                and acc.acceptance_user_id = #{req.userId}
            </if>
            <if test="req.keyWord!=null and req.keyWord!=''">
                and ( ps.ps_code like concat( '%', #{req.keyWord}, '%')
                or u1.F_RealName like concat( '%', #{req.keyWord}, '%')
                or u2.`name` like concat( '%', #{req.keyWord}, '%'))
            </if>
            <choose>
                <when test='req.reformOverTimeStatus == "0"'>
                    and TIMESTAMPDIFF(SECOND, IFNULL(ord.tech_start_time, NOW()), IFNULL(ord.finish_time, NOW())) &lt;= 60*60*24*30
                </when>
                <when test='req.reformOverTimeStatus == "1"'>
                    and TIMESTAMPDIFF(SECOND, IFNULL(ord.tech_start_time, NOW()), IFNULL(ord.finish_time, NOW())) &gt; 60*60*24*30
                </when>
            </choose>
        </where>
        order by acc.create_time desc
    </select>
    <select id="getReformOrderBaseInfo"
            resultType="com.gcl.psmis.framework.common.resp.reform.ReformOrderBaseResp">
        SELECT acc.acceptance_no,
               ps.ps_code,
               u2.name                                           as userName,
               com.operation_short_name                                    as dev,
               CONCAT(r.province_name, r.city_name, r.area_name) AS psAddress,
               GROUP_CONCAT(roof_type SEPARATOR ',')             as roof,
               u1.F_RealName                                     as acceptanceUser,
               acc.create_time,
               acc.acceptance_start_time,
               CASE
                   WHEN ord.reform_no IS NULL THEN
                       1 ELSE 2
                   END AS tag,
               CASE

                   WHEN ord.reform_no IS NULL THEN
                       '通过'
                   ELSE '不通过'
                   END                                           AS acceptanceStatus,
               acc.acceptance_over_time,
               ROUND(TIME_TO_SEC(TIMEDIFF(acc.acceptance_over_time, acc.acceptance_start_time)) / 3600,
                     2)                                          AS subtractTime,
               ord.reform_no,
               ord.`status`,
               CASE
                   WHEN ord.`status` = 301 THEN
                       '整改方案'
                   WHEN ord.`status` = 302 THEN
                       '待整改'
                   WHEN ord.`status` = 303 THEN
                       '技术审核'
                   WHEN ord.`status` = 304 THEN
                       '质量审核'
                   WHEN ord.`status` = 305 THEN
                       '整改完成'
                   END                                              statusLabel,
               acc.acceptance_end_time,
               acc.scene_remark,
               ord.tech_start_time
        FROM t_reform_acceptance acc
                 LEFT JOIN t_reform_order ord ON ord.source_no = acc.acceptance_no
                 LEFT JOIN t_power_station ps ON ps.id = acc.ps_id
                 LEFT JOIN t_operation com ON com.operation_code = ps.service_org_code
                 LEFT JOIN t_region r ON r.id = ps.region_id
                 LEFT JOIN base_user u1 ON u1.F_Id = acc.acceptance_user_id
                 LEFT JOIN t_user u2 ON u2.id = ps.user_id
                 LEFT JOIN t_ps_vs_roof roof ON roof.ps_id = ps.id
        WHERE acc.acceptance_no = #{acceptanceNo}
        GROUP BY ps.id
    </select>

    <select id="getReformCheckStandard"
            resultType="com.gcl.psmis.framework.common.resp.reform.ReformCheckStandardResp">
        SELECT cs.*,
               ci.check_result,
               ci.check_describe,
               ci.id as checkItemId
        FROM `t_reform_check_item` ci
                 LEFT JOIN t_reform_check_standard cs ON ci.disqualification_no = cs.disqualification_no
        WHERE ci.acceptance_no = #{acceptanceno}
    </select>

    <select id="getAcceptanceDetail" resultType="com.gcl.psmis.framework.common.resp.reform.AcceptanceDetailVO">
        select t2.ps_code,
               t1.acceptance_no,
               t3.name username,
               t1.acceptance_status,
               t1.acceptance_type,
               t2.address,
               t2.id   ps_id
        from t_reform_acceptance t1
                 left join t_power_station t2 on t1.ps_id = t2.id
                 left join t_user t3 on t2.user_id = t3.id
        where t1.acceptance_no = #{acceptanceNo}

    </select>

    <select id="findTecAccount" resultType="java.lang.String">
        SELECT u.F_Account
        FROM base_role r
                 INNER JOIN base_userrelation ur ON ur.F_ObjectId = r.F_Id
                 INNER JOIN base_user u ON ur.F_UserId = u.F_Id
        WHERE r.F_EnCode = 'pro.tec'
          AND ur.F_ObjectType = 'Role'
          AND u.F_Id IN (
            SELECT F_UserId
            FROM base_userrelation ur
            WHERE ur.F_ObjectId IN (
                SELECT bo.F_Id
                FROM t_power_station ps
                         LEFT JOIN base_organize bo ON bo.F_EnCode = ps.province_company_code
                WHERE id = #{psId}
            )
        )
    </select>

    <select id="getItemCnt" resultType="java.lang.Integer">
        select
        count(*)
        from
        t_reform_check_standard t1
        where
        t1.del_flag = 0
        and
        t1.level = #{level}
        and
        t1.type = #{type}
        and
        t1.standard_id = #{standardId}
        <if test="titleList != null and titleList.size > 0">
            and t1.title_id in
            <foreach collection="titleList" item="item" open="(" close=")" separator=",">
                #{item}
            </foreach>
        </if>
    </select>
</mapper>