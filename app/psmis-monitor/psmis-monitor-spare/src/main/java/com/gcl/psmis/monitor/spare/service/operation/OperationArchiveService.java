package com.gcl.psmis.monitor.spare.service.operation;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollStreamUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONUtil;
import com.baomidou.dynamic.datasource.annotation.DSTransactional;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.gcl.framework.data.dto.UserDTO;
import com.gcl.framework.data.holder.CurrentUserHolder;
import com.gcl.psmis.framework.common.exception.BussinessException;
import com.gcl.psmis.framework.common.req.archive.*;
import com.gcl.psmis.framework.common.resp.inspection.InspectionResp;
import com.gcl.psmis.framework.common.vo.archive.*;
import com.gcl.psmis.framework.common.vo.spare.operation.OperationConfigVO;
import com.gcl.psmis.framework.common.vo.spare.operation.OperationCreditVO;
import com.gcl.psmis.framework.common.vo.spare.operation.OperationPersonVO;
import com.gcl.psmis.framework.dict.util.ExpandBeanUtils;
import com.gcl.psmis.framework.gencode.enums.GenRuleCode;
import com.gcl.psmis.framework.gencode.util.GenCodeUtil;
import com.gcl.psmis.framework.mbg.entity.*;
import com.gcl.psmis.framework.mbg.service.*;
import com.gcl.psmis.manager.dao.WorkerBillDao;
import com.gcl.psmis.monitor.spare.dao.operation.OperationArchiveDao;
import jnpf.util.StringUtil;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.time.LocalDate;
import java.util.*;
import java.util.function.Function;
import java.util.regex.Pattern;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2024年10月10日
 */
@Service
@Slf4j
@RequiredArgsConstructor
public class OperationArchiveService {

    private final OperationArchiveDao operationArchiveDao;
    private final TOperationService tOperationService;
    private final TOperationFileService tOperationFileService;
    private final TDictClassService tDictClassService;
    private final TDictService tDictService;
    private final TOperationChangeRecordService tOperationChangeRecordService;
    private final TDevOpsCreditService tDevOpsCreditService;
    private final TOperationContractService tOperationContractService;
    private final TSpareDevOpsWhService tSpareDevOpsWhService;
    private final GenCodeUtil genCodeUtil;
    private final TOperationAreaService tOperationAreaService;
    private final TOperationAreaChangeRecordService tOperationAreaChangeRecordService;
    private final TPowerStationService tPowerStationService;
    private final BaseOrganizeService baseOrganizeService;
    private final TOperationRechargeRecordService tOperationRechargeRecordService;
    private final OperationUserService operationUserService;
    private final TSpareStockOrderService tSpareStockOrderService;
    private final TSpareReturnService tSpareReturnService;
    private final BaseUserService baseUserService;
    private final TNewInspectionOrderService tNewInspectionOrderService;
    private final TDevOpsGdFillService tDevOpsGdFillService;
    private final TGdFillXjDetailService tGdFillXjDetailService;
    private final WorkerBillDao workerBillDao;
    private final BaseRoleService baseRoleService;


    /**
     * 运维商档案分页列表
     */
    public IPage<ArchivePageVO> archivePageList(OperationArchivePageReq req) {

        Page<ArchivePageVO> page = new Page<>(req.getPageNum(), req.getPageSize());
        // 运维商只能看自己
        UserDTO userDTO = CurrentUserHolder.getUserDTOThreadLocal();
        // 判断当前用户组织是否数据运维商集合下面 是 则认为运维商
        if (null != userDTO && StringUtil.isNotBlank(userDTO.getOrganizeId())) {
            BaseOrganizeEntity baseOrganizeEntity = baseOrganizeService.getById(userDTO.getOrganizeId());
            if (null != baseOrganizeEntity) {
                BaseOrganizeEntity parantBaseOrganizeEntity = baseOrganizeService.getById(baseOrganizeEntity.getFParentid());
                if (null != parantBaseOrganizeEntity && (StringUtil.equals(parantBaseOrganizeEntity.getFEncode(), "server.list") || StringUtil.equals(parantBaseOrganizeEntity.getFEncode(), "server.all"))) {
                    req.setServerCode(baseOrganizeEntity.getFEncode());
                }
            }
        }

        IPage<ArchivePageVO> result = operationArchiveDao.archivePageList(page, req);

        if (req.getPageType() == 1 && CollUtil.isNotEmpty(result.getRecords())) {
            // 运维商新增分页列表,写入各个类型的状态 (注意 操作类型为保存时,即便档案基本信息已经完整,此时也只可编辑 不可创建)
            for (ArchivePageVO temp : result.getRecords()) {
                AgentCreateOpVO agentCreateOpVO = this.agentCreateOp(temp.getMaintenanceCompanyCode(), 0);
                temp.setBaseInfo(agentCreateOpVO.getBaseAllInfo());
                temp.setYwArea(agentCreateOpVO.getYwArea());
                temp.setDeposit(agentCreateOpVO.getDeposit());
                temp.setYwContract(agentCreateOpVO.getYwContract());
                temp.setCreateOrEdit(agentCreateOpVO.getBaseAllInfo() * temp.getOpStatus());
                temp.setCreateFlag(agentCreateOpVO.getConfigCreate() * temp.getOpStatus());
            }
        }
        ExpandBeanUtils.convert(result, result);
        return result;
    }

    /**
     * 运维商&&运维商-详情
     * type 0运维商、1代理商
     */
    public BothDetailVO bothDetail(String code, Integer type) {

        BothDetailVO result;
        Long id = null;
        TOperationEntity tOperationEntity = tOperationService.lambdaQuery().eq(TOperationEntity::getDelFlag, 0).eq(TOperationEntity::getOperationCode, code).one();
        if (tOperationEntity == null) {
            throw new BussinessException("无法查询代理商基本信息,请核实代理商Code是否正确!");
        }
        id = tOperationEntity.getId();
        //  运维商详情
        result = operationArchiveDao.operationDetail(code);
        if (id != null) {
            // 查询附件相关信息
            result.setBusinessLicensePic(this.operationFile(id, 2, 6));
            result.setIdCardPic(this.operationFile(id, 2, 7));
            result.setBasePic(this.operationFile(id, 2, 8));
            result.setInsurancePic(this.operationFile(id, 2, 1));
            result.setPersonIdCardPic(this.operationFile(id, 2, 2));
            result.setSpecialOperationCertificatePic(this.operationFile(id, 2, 3));
            result.setWorkPic(this.operationFile(id, 2, 4));
            result.setLowVoltagePic(this.operationFile(id, 2, 5));
            result.setOperationCarPic(this.operationFile(id, 2, 10));
            result.setWarehousePic(this.operationFile(id, 2, 11));
            result.setSitePic(this.operationFile(id, 2, 12));
            result.setDecorationPic(this.operationFile(id, 2, 13));
        }
        ExpandBeanUtils.convert(result, result);
        return result;
    }

    /**
     * 运维商&&代理商-变更类型
     */
    public List<ChangeTypeVO> changeType(Integer type) {
        // 查询所有变更类型
        TDictClassEntity tDictClassEntity = tDictClassService.lambdaQuery().eq(TDictClassEntity::getDelFlag, 0).eq(TDictClassEntity::getDictClassCode, "file_change_type").one();
        List<TDictEntity> dictEntityList = tDictService.lambdaQuery().eq(TDictEntity::getDelFlag, 0).eq(TDictEntity::getDictClassId, tDictClassEntity.getId()).list();
        List<ChangeTypeVO> result = new ArrayList<>();
        for (TDictEntity dictEntity : dictEntityList) {
            if (dictEntity.getDictCode().equals("1") && type == 1) {
                // 只有运维商类型才能变更 基础信息
                continue;
            }
            result.add(new ChangeTypeVO(dictEntity.getDictCode(), dictEntity.getDictName()));
        }
        return result;
    }

    /**
     * 运维商&&代理商-变更
     */
    public void change(OperationChangeReq req) {

        TOperationEntity tOperationEntity = tOperationService.lambdaQuery().eq(TOperationEntity::getDelFlag, 0).eq(TOperationEntity::getOperationCode, req.getCode()).one();
        if (tOperationEntity == null) {
            throw new BussinessException("无法查询运维商基本信息,请核实运维商Code是否正确!");
        }
        // 获取变更前的信息
        BothDetailVO bothDetailVOBefore = this.bothDetail(req.getCode(), req.getListType());
        String before = JSONUtil.toJsonStr(bothDetailVOBefore);
        Long id = tOperationEntity.getId();

        // 根据类型变更信息
        switch (req.getChangeType()) {
            case 1:
                // 变更基础信息
                tOperationService.lambdaUpdate().eq(TOperationEntity::getOperationCode, req.getCode()).eq(TOperationEntity::getDelFlag, 0).set(TOperationEntity::getOperationShortName, req.getShortName()).update(new TOperationEntity());
                break;
            case 2:
                // 变更结算类型
                tOperationService.lambdaUpdate().eq(TOperationEntity::getOperationCode, req.getCode()).eq(TOperationEntity::getDelFlag, 0).set(TOperationEntity::getSettlementType, req.getSettlementType()).update(new TOperationEntity());
                break;
            case 3:
                // 变更工商信息
                tOperationService.lambdaUpdate().eq(TOperationEntity::getOperationCode, req.getCode()).eq(TOperationEntity::getDelFlag, 0).set(TOperationEntity::getLegalPerson, req.getLegalPerson()).set(TOperationEntity::getIdCard, req.getIdCard()).set(TOperationEntity::getBusinessLicenseCode, req.getBusinessLicenseCode()).set(TOperationEntity::getAccountOpeningBank, req.getAccountOpeningBank()).set(TOperationEntity::getBankAccountCode, req.getBankAccountCode()).set(TOperationEntity::getUnbCode, req.getUnbCode()).set(TOperationEntity::getBusinessAddress, req.getBusinessAddress()).update(new TOperationEntity());
                this.checkInfo(req.getIdCard(), req.getBankAccountCode(), null, req.getUnbCode(), req.getBusinessLicenseCode());
                // 变更附件相关信息（全删、全增）
                // 更新营业执照附件
                this.deleteAndSaveFile(id, 2, 6, req.getBusinessLicensePic());
                // 更新法人身份证附件
                this.deleteAndSaveFile(id, 2, 7, req.getIdCardPic());
                // 更新基础开票附件
                this.deleteAndSaveFile(id, 2, 8, req.getBasePic());
                break;
            case 4:
                // 变更运维人员信息
                tOperationService.lambdaUpdate().eq(TOperationEntity::getOperationCode, req.getCode()).eq(TOperationEntity::getDelFlag, 0).set(TOperationEntity::getOperationPerson, req.getOperationPerson()).set(TOperationEntity::getPersonPhone, req.getPersonPhone()).set(TOperationEntity::getElectricianLicenseCode, req.getElectricianLicenseCode()).set(TOperationEntity::getSpecialOperationCertificateCode, req.getSpecialOperationCertificateCode()).update(new TOperationEntity());
                this.checkInfo(null, null, req.getPersonPhone(), null, null);
                // 变更附件相关信息（全删、全增）
                // 更新保险附件
                this.deleteAndSaveFile(id, 2, 1, req.getInsurancePic());
                // 更新运维人员身份证附件
                this.deleteAndSaveFile(id, 2, 2, req.getPersonIdCardPic());
                // 更新特种作业证照片附件
                this.deleteAndSaveFile(id, 2, 3, req.getSpecialOperationCertificatePic());
                // 更新劳动合同附件
                this.deleteAndSaveFile(id, 2, 4, req.getWorkPic());
                // 更新低压电工证附件
                this.deleteAndSaveFile(id, 2, 5, req.getLowVoltagePic());
                break;
            case 5:
                // 变更其他附件信息
                // 更新运维车辆附件
                this.deleteAndSaveFile(id, 2, 10, req.getOperationCarPic());
                // 更新仓库照片附件
                this.deleteAndSaveFile(id, 2, 11, req.getWarehousePic());
                // 更新场地及门头照片附件
                this.deleteAndSaveFile(id, 2, 12, req.getSitePic());
                // 更新承装修资质附件
                this.deleteAndSaveFile(id, 2, 13, req.getDecorationPic());
                break;
            default:
                break;
        }

        // 获取变更后的信息
        BothDetailVO bothDetailVOAfter = this.bothDetail(req.getCode(), req.getListType());
        String after = JSONUtil.toJsonStr(bothDetailVOAfter);
        // 保存变更记录
        TOperationChangeRecordEntity tOperationChangeRecord = TOperationChangeRecordEntity.builder().operationCode(tOperationEntity.getOperationCode()).changeTime(new Date()).operatorName(CurrentUserHolder.getUserDTOThreadLocal().getRealName()).operatorCode(CurrentUserHolder.getUserDTOThreadLocal().getEmpNo()).changeType(req.getChangeType()).beforeChange(before).afterChange(after).build();
        tOperationChangeRecordService.save(tOperationChangeRecord);
    }

    /**
     * 查询附件的基本信息
     */
    public List<String> operationFile(Long id, Integer businessType, Integer fileType) {

        List<TOperationFileEntity> urlList = tOperationFileService.lambdaQuery().eq(TOperationFileEntity::getDelFlag, 0).eq(TOperationFileEntity::getRelationId, id).eq(TOperationFileEntity::getBusinessType, businessType).eq(TOperationFileEntity::getFileType, fileType).list();
        if (CollUtil.isEmpty(urlList)) {
            return null;
        }
        return urlList.stream().map(TOperationFileEntity::getFileUrl).collect(Collectors.toList());
    }

    /**
     * 删除&&保存附件信息
     * 先删除、后保存
     */
    public void deleteAndSaveFile(Long id, Integer businessType, Integer fileType, List<String> urlList) {

        if (CollUtil.isEmpty(urlList)) {
            return;
        }
        tOperationFileService.remove(new LambdaQueryWrapper<TOperationFileEntity>().eq(TOperationFileEntity::getDelFlag, 0).eq(TOperationFileEntity::getRelationId, id).eq(TOperationFileEntity::getBusinessType, businessType).eq(TOperationFileEntity::getFileType, fileType));

        List<TOperationFileEntity> resultList = new ArrayList<>();
        for (String url : urlList) {
            TOperationFileEntity tOperationFileEntity = TOperationFileEntity.builder().relationId(id).businessType(businessType).fileType(fileType).fileUrl(url).build();
            resultList.add(tOperationFileEntity);
        }
        if (CollUtil.isNotEmpty(resultList)) {
            tOperationFileService.saveBatch(resultList);
        }
    }

    /**
     * 代理商档案-分页列表
     */
    public IPage<AgentPageVO> agentPageList(OperationAgentPageReq req) {
        Page<AgentPageVO> page = new Page<>(req.getPageNum(), req.getPageSize());
        IPage<AgentPageVO> result = operationArchiveDao.agentPageList(page, req);

        if (CollUtil.isEmpty(result.getRecords())) {
            // 复写代理商的状态,和鑫阳光保持一致
            List<XygAgentStatusVO> xygAgentStatusVOS = operationArchiveDao.xygAgentStatusList();
            if (CollUtil.isNotEmpty(xygAgentStatusVOS)) {
                Map<String, String> map = CollStreamUtil.toMap(xygAgentStatusVOS, XygAgentStatusVO::getAgentCode, XygAgentStatusVO::getMaintenanceCompanyStatusName);
                for (AgentPageVO agentPageVO : result.getRecords()) {
                    agentPageVO.setMaintenanceCompanyStatusName(map.get(agentPageVO.getAgentCode()));
                }
            }
        }
        return result;
    }

    /**
     * 代理商档案-创建运维商
     * type 0运维商、1代理商
     */
    public AgentCreateOpVO agentCreateOp(String code, Integer type) {

        AgentCreateOpVO result = new AgentCreateOpVO();
        // 查询代理商是否已经维护运维商
        TOperationEntity tOperationEntity = operationArchiveDao.getOperationInfo(code, type);

        if (tOperationEntity == null) {
            // 代理商尚未变更任何信息,所有信息均缺失
            return result;
        }
        // 获取当前代理商Code对应的详情结果
        BothDetailVO bothDetailVO = this.bothDetail(code, type);
        // 判断基本信息是否完善
        result = this.isBaseInfoComplete(bothDetailVO);

        // 判断运维区域信息是否完善
        result.setYwArea(tOperationEntity.getRegionStatus() == 1 ? 1 : 0);

        // 判断运维合同信息是否完善(先补全合同的运维商Code,再判断当前运维商是否存在有效的合同)
        this.scanContract(tOperationEntity.getOperationName(), tOperationEntity.getOperationCode());
        List<TOperationContractEntity> contractList = tOperationContractService.lambdaQuery().eq(TOperationContractEntity::getDelFlag, 0).eq(TOperationContractEntity::getOperationCode, code).le(TOperationContractEntity::getStartDate, new Date()).ge(TOperationContractEntity::getEndDate, new Date()).list();
        if (CollUtil.isNotEmpty(contractList)) {
            // 当前运维商存在有效的合同,更新运维商主表的合同状态
            result.setYwContract(1);
            tOperationService.lambdaUpdate().eq(TOperationEntity::getDelFlag, 0).eq(TOperationEntity::getOperationCode, code).set(TOperationEntity::getContractStatus, 1).update();
            // 更新合同的开始、结束时间
            if (CollectionUtil.isNotEmpty(contractList)) {
                // 获取最早签署合同
                TOperationContractEntity contractSign = contractList.stream().max(Comparator.comparing(TOperationContractEntity::getSignDate, Comparator.nullsLast(Comparator.naturalOrder()))).get();
                // 根据签署时间更新运维商的开始、结束时间
                tOperationService.update(new LambdaUpdateWrapper<TOperationEntity>().set(TOperationEntity::getOperationTimeStart, contractSign.getStartDate()).set(TOperationEntity::getOperationTimeEnd, contractSign.getEndDate()).eq(TOperationEntity::getOperationCode, code));
            }
        } else {
            tOperationService.lambdaUpdate().eq(TOperationEntity::getDelFlag, 0).eq(TOperationEntity::getOperationCode, code).set(TOperationEntity::getContractStatus, 2).update();
        }
        // 判断是否已经充值保证金
        TDevOpsCreditEntity opsCredit = tDevOpsCreditService.lambdaQuery().eq(TDevOpsCreditEntity::getDelFlag, 0).eq(TDevOpsCreditEntity::getDevOpsCode, code).one();
        BigDecimal creditNum = opsCredit == null ? null : opsCredit.getCreditInitial().setScale(0, RoundingMode.HALF_UP);
        result.setDeposit((creditNum != null && !creditNum.equals(BigDecimal.ZERO)) ? 1 : 0);

        result.setConfigCreate(result.getBaseAllInfo() * result.getYwArea() * result.getDeposit() * result.getYwContract());

        result.setOPName(tOperationEntity.getOperationName());

        return result;
    }

    /**
     * 判断基本信息是否已经完善
     */
    public AgentCreateOpVO isBaseInfoComplete(BothDetailVO bothDetailVO) {
        // 判断基础信息是否全部维护
        int ywName = StrUtil.isBlank(bothDetailVO.getName()) ? 0 : 1;
        int ywShortName = StrUtil.isBlank(bothDetailVO.getShortName()) ? 0 : 1;
        int ywCode = StrUtil.isBlank(bothDetailVO.getCode()) ? 0 : 1;
        int rateCode = StrUtil.isBlank(bothDetailVO.getTaxNumber()) ? 0 : 1;
        int ywType = bothDetailVO.getType() == null ? 0 : 1;
        int devType = bothDetailVO.getDevType() == null ? 0 : 1;
        int psType = bothDetailVO.getType() == null ? 0 : 1;
        int provinceCompanyCode = 1;   // 公司类型为开发时,所属省公司才必填
        if (bothDetailVO.getDevType() != null && bothDetailVO.getDevType() == 1 && StrUtil.isBlank(bothDetailVO.getProvinceCompanyCode())) {
            provinceCompanyCode = 0;
        }
        int baseInfo = ywName * ywShortName * ywCode * rateCode * ywType * devType * provinceCompanyCode * psType;

        // 判断运维结算信息是否全部维护
        // 如果dev_type 为开发 1 时,去掉校验
        int ywSettle = 1;
        if (bothDetailVO.getDevType() != null && bothDetailVO.getDevType() == 0 && StrUtil.isBlank(bothDetailVO.getSettlementType())) {
            ywSettle = 0;
        }
        // 判断仓库信息是否全部维护
        int ckName = StrUtil.isBlank(bothDetailVO.getWarehouseName()) ? 0 : 1;
        int ckCode = StrUtil.isBlank(bothDetailVO.getWarehouseCode()) ? 0 : 1;
        int ckAddress = StrUtil.isBlank(bothDetailVO.getWarehouseAddress()) ? 0 : 1;
        int whInfo = ckName * ckCode * ckAddress;

        // 判断工商信息是否全部维护
        int legalPerson = StrUtil.isBlank(bothDetailVO.getLegalPerson()) ? 0 : 1;
        int idCard = StrUtil.isBlank(bothDetailVO.getIdCard()) ? 0 : 1;
        int businessLicenseCode = StrUtil.isBlank(bothDetailVO.getBusinessLicenseCode()) ? 0 : 1;
        int accountOpeningBank = StrUtil.isBlank(bothDetailVO.getAccountOpeningBank()) ? 0 : 1;
        int bankAccountCode = StrUtil.isBlank(bothDetailVO.getBankAccountCode()) ? 0 : 1;
        int unbCode = StrUtil.isBlank(bothDetailVO.getUnbCode()) ? 0 : 1;
        int businessAddress = StrUtil.isBlank(bothDetailVO.getBusinessAddress()) ? 0 : 1;
        int zzPic = CollUtil.isEmpty(bothDetailVO.getBusinessLicensePic()) ? 0 : 1;
        int frPic = CollUtil.isEmpty(bothDetailVO.getIdCardPic()) ? 0 : 1;
        int basePic = CollUtil.isEmpty(bothDetailVO.getBasePic()) ? 0 : 1;
        int gsInfo = legalPerson * idCard * businessLicenseCode * accountOpeningBank * bankAccountCode * unbCode * businessAddress * zzPic * frPic * basePic;

        // 判断运维人员信息是否全部维护
        int operationPerson = StrUtil.isBlank(bothDetailVO.getOperationPerson()) ? 0 : 1;
        int phone = StrUtil.isBlank(bothDetailVO.getPersonPhone()) ? 0 : 1;
        int electricianLicenseCode = StrUtil.isBlank(bothDetailVO.getElectricianLicenseCode()) ? 0 : 1;
        int specialOperationCertificateCode = StrUtil.isBlank(bothDetailVO.getSpecialOperationCertificateCode()) ? 0 : 1;
        int insurancePic = CollUtil.isEmpty(bothDetailVO.getInsurancePic()) ? 0 : 1;
        int personIdCardPic = CollUtil.isEmpty(bothDetailVO.getPersonIdCardPic()) ? 0 : 1;
        int specialOperationCertificatePic = CollUtil.isEmpty(bothDetailVO.getSpecialOperationCertificatePic()) ? 0 : 1;
        int workPic = CollUtil.isEmpty(bothDetailVO.getWorkPic()) ? 0 : 1;
        int lowVoltagePic = CollUtil.isEmpty(bothDetailVO.getLowVoltagePic()) ? 0 : 1;
        int ywPerson = operationPerson * phone * electricianLicenseCode * specialOperationCertificateCode * insurancePic * personIdCardPic * specialOperationCertificatePic * workPic * lowVoltagePic;

        // 判断其他附件信息是否全部维护
        int car = CollUtil.isEmpty(bothDetailVO.getOperationCarPic()) ? 0 : 1;
        int warehousePic = CollUtil.isEmpty(bothDetailVO.getWarehousePic()) ? 0 : 1;
        int sitePic = CollUtil.isEmpty(bothDetailVO.getSitePic()) ? 0 : 1;
        int decorationPic = CollUtil.isEmpty(bothDetailVO.getDecorationPic()) ? 0 : 1;
        int otherInfo = car * warehousePic * sitePic * decorationPic;

        // 基础大类的最终结果
        int baseAllInfo = baseInfo * ywSettle * whInfo * gsInfo * ywPerson * otherInfo;

        // 封装结果
        return AgentCreateOpVO.builder().baseAllInfo(baseAllInfo).baseInfo(baseInfo).ywSettle(ywSettle).whInfo(whInfo).gsInfo(gsInfo).ywPerson(ywPerson).otherInfo(otherInfo).ywArea(0).deposit(0).ywContract(0).configCreate(0).build();
    }

    /**
     * 代理商档案-确认创建
     */
    public void configCreateOp(String code) {

        TOperationEntity tOperationEntity = tOperationService.lambdaQuery().eq(TOperationEntity::getDelFlag, 0).eq(TOperationEntity::getOperationCode, code).one();
        if (tOperationEntity == null) {
            throw new BussinessException("无法正常创建运维商,请核实代理商Code是否正确!");
        }
        // 判断是否可以创建运维商
        AgentCreateOpVO agentCreateOpVO = this.agentCreateOp(code, 1);
        if (agentCreateOpVO.getConfigCreate() != 1) {
            throw new BussinessException("运维商相关信息尚未完善,请全部完善后再进行创建!");
        }
        // 校验数据格式是否正确
        this.checkInfo(tOperationEntity.getIdCard(), tOperationEntity.getBankAccountCode(), tOperationEntity.getPersonPhone(), tOperationEntity.getUnbCode(), tOperationEntity.getBusinessLicenseCode());
        // 正常创建时,更新运维商主表数据
        tOperationService.lambdaUpdate().eq(TOperationEntity::getDelFlag, 0).eq(TOperationEntity::getOperationCode, code).set(TOperationEntity::getOperationStatus, 1).set(TOperationEntity::getOperationType, 3).set(TOperationEntity::getCreateByName, CurrentUserHolder.getUserDTOThreadLocal().getRealName()).update();
        // 正常创建运维商时,更新t_power_station
        this.updatePowerStation(tOperationEntity);
    }

    /**
     * 运维商新建-删除
     */
    public void createOpDelete(String code) {

        TOperationEntity tOperationEntity = tOperationService.lambdaQuery().eq(TOperationEntity::getDelFlag, 0).eq(TOperationEntity::getOperationCode, code).one();
        if (tOperationEntity == null) {
            throw new BussinessException("无法删除运维商,请核实运维商Code是否正确!");
        }
        // 删除 运维商主表
        tOperationService.lambdaUpdate().eq(TOperationEntity::getDelFlag, 0).eq(TOperationEntity::getOperationCode, code).remove();
        // 删除 运维商关联的附件信息
        tOperationFileService.lambdaUpdate().eq(TOperationFileEntity::getRelationId, tOperationEntity.getId()).eq(TOperationFileEntity::getBusinessType, 2).remove();
        // 删除 运维商关联的运维商区域
        tOperationAreaService.lambdaUpdate().eq(TOperationAreaEntity::getOperationCode, code).remove();
        // 删除 运维商的充值记录
        tOperationRechargeRecordService.lambdaUpdate().eq(TOperationRechargeRecordEntity::getMaintenanceCompanyCode, code).remove();
        // 删除 运维商的额度表
        tDevOpsCreditService.lambdaUpdate().eq(TDevOpsCreditEntity::getDevOpsCode, code).remove();
        // 删除 运维商的仓库表
        tSpareDevOpsWhService.lambdaUpdate().eq(TSpareDevOpsWhEntity::getDevOpsCode, code).remove();
        // 清空合同表的运维商Code
        tOperationContractService.lambdaUpdate().eq(TOperationContractEntity::getOperationName, tOperationEntity.getOperationName()).set(TOperationContractEntity::getOperationCode, null).update();
    }

    /**
     * 运维商新建-创建
     */
    public CreateOpAddVO createOpConfig(String code) {

        CreateOpAddVO result = new CreateOpAddVO();
        TOperationEntity tOperationEntity = tOperationService.lambdaQuery().eq(TOperationEntity::getDelFlag, 0).eq(TOperationEntity::getOperationCode, code).one();
        if (tOperationEntity == null) {
            throw new BussinessException("无法正常创建运维商,请核实运维商Code是否正确!");
        }
        AgentCreateOpVO agentCreateOpVO = this.agentCreateOp(code, 0);
        if (agentCreateOpVO.getConfigCreate() * tOperationEntity.getOpStatus() == 1) {

            // 运维商基本信息全部完善
            tOperationService.lambdaUpdate().eq(TOperationEntity::getDelFlag, 0).eq(TOperationEntity::getOperationCode, code).set(TOperationEntity::getOperationStatus, 1).update();
            // 正常创建运维商时,更新t_power_station
            this.updatePowerStation(tOperationEntity);
        } else {
            // 运维商基本信息未完善,无法创建
            result.setBaseAllInfo(agentCreateOpVO.getBaseAllInfo());
            result.setYwArea(agentCreateOpVO.getYwArea());
            result.setDeposit(agentCreateOpVO.getDeposit());
            result.setYwContract(agentCreateOpVO.getYwContract());
        }
        return result;
    }

    /**
     * 根据运维商名称,扫描t_operation_contract表,写入运维商Code
     */
    public void scanContract(String name, String code) {

        tOperationContractService.lambdaUpdate().eq(TOperationContractEntity::getDelFlag, 0).eq(TOperationContractEntity::getOperationName, name).isNull(TOperationContractEntity::getOperationCode).set(TOperationContractEntity::getOperationCode, code).update();

    }

    /**
     * 运维商新建-新增运维商
     */
    public String createOpAdd(AddReq req) {

        if (StrUtil.isNotBlank(req.getName())) {
            // 判断该运维商的名称是否已经存在(用名称做唯一校验)
            List<TOperationEntity> listCheck = tOperationService.lambdaQuery().eq(TOperationEntity::getDelFlag, 0).eq(TOperationEntity::getOperationName, req.getName()).list();
            if (CollUtil.isNotEmpty(listCheck)) {
                throw new BussinessException("该运维商名称已经存在,无法正常创建运维商,请重新命名!");
            }
        }
        if (StrUtil.isNotBlank(req.getRateCode())) {
            // 判断该运维商的名称是否已经存在(用税号做唯一校验)
            List<TOperationEntity> listCheck = tOperationService.lambdaQuery().eq(TOperationEntity::getDelFlag, 0).eq(TOperationEntity::getTaxCode, req.getRateCode()).list();
            if (CollUtil.isNotEmpty(listCheck)) {
                throw new BussinessException("该运维商税号已经存在,无法正常创建运维商,请重新核对税号!");
            }
        }
        // 生成运维商Code
        String opCode = genCodeUtil.getSysCodeByCodeRule(GenRuleCode.Archive);
        // 生成仓库Code(无论省市区是否已经选择,只要第一次点击保存或提交后,此运维商绑定的仓库Code就已经最终确定了)
        String whCode = genCodeUtil.getSysCodeByCodeRule(GenRuleCode.Warehouse);

        // 保存主表数据
        TOperationEntity tOperationEntity = TOperationEntity.builder().operationCode(opCode).operationName(req.getName()).operationStatus(4)     // 创建中
                .operationShortName(req.getShortName()).taxCode(req.getRateCode()).operationType(1).devType(req.getDevType()).psType(req.getPsType()).provinceCompanyCode(req.getProvinceCompanyCode()).settlementType(req.getSettlementType()).legalPerson(req.getLegalPerson()).idCard(req.getIdCard()).businessLicenseCode(req.getBusinessLicenseCode()).accountOpeningBank(req.getAccountOpeningBank()).bankAccountCode(req.getBankAccountCode()).unbCode(req.getUnbCode()).businessAddress(req.getBusinessAddress()).operationPerson(req.getOperationPerson()).personPhone(req.getPersonPhone()).electricianLicenseCode(req.getElectricianLicenseCode()).specialOperationCertificateCode(req.getSpecialOperationCertificateCode()).opStatus(req.getOperationType()).build();
        tOperationService.save(tOperationEntity);

        // 保存仓库表数据
        TSpareDevOpsWhEntity tSpareDevOpsWhEntity = TSpareDevOpsWhEntity.builder()
                .devOpsCode(opCode)
                .devOpsName(req.getName())
                .warehouseCode(whCode)
                .warehouseName(req.getShortName() + "-" + "售后仓")
                .provinceId(req.getProvinceId())
                .provinceName(req.getProvinceName())
                .cityId(req.getCityId())
                .cityName(req.getCityName())
                .regionId(req.getRegionId())
                .regionName(req.getRegionName())
                .build();
        tSpareDevOpsWhService.save(tSpareDevOpsWhEntity);

        // 保存文件数据
        Long id = tOperationEntity.getId();
        // 更新营业执照附件
        this.deleteAndSaveFile(id, 2, 6, req.getBusinessLicensePic());
        // 更新法人身份证附件
        this.deleteAndSaveFile(id, 2, 7, req.getIdCardPic());
        // 更新基础开票附件
        this.deleteAndSaveFile(id, 2, 8, req.getBasePic());
        // 更新保险附件
        this.deleteAndSaveFile(id, 2, 1, req.getInsurancePic());
        // 更新运维人员身份证附件
        this.deleteAndSaveFile(id, 2, 2, req.getPersonIdCardPic());
        // 更新特种作业证照片附件
        this.deleteAndSaveFile(id, 2, 3, req.getSpecialOperationCertificatePic());
        // 更新劳动合同附件
        this.deleteAndSaveFile(id, 2, 4, req.getWorkPic());
        // 更新低压电工证附件
        this.deleteAndSaveFile(id, 2, 5, req.getLowVoltagePic());
        // 更新运维车辆附件
        this.deleteAndSaveFile(id, 2, 10, req.getOperationCarPic());
        // 更新仓库照片附件
        this.deleteAndSaveFile(id, 2, 11, req.getWarehousePic());
        // 更新场地及门头照片附件
        this.deleteAndSaveFile(id, 2, 12, req.getSitePic());
        // 更新承装修资质附件
        this.deleteAndSaveFile(id, 2, 13, req.getDecorationPic());

        if (req.getOperationType() == 1) {
            // 提交时,校验数据是否完全
            BothDetailVO bothDetailVO = this.bothDetail(opCode, 0);
            AgentCreateOpVO baseInfoComplete = this.isBaseInfoComplete(bothDetailVO);
            if (baseInfoComplete.getBaseAllInfo() != 1) {
                throw new BussinessException("运维商基础信息尚未完善,请全部完善后再点击提交!");
            }
            // 校验数据格式是否正确
            this.checkInfo(req.getIdCard(), req.getBankAccountCode(), req.getPersonPhone(), req.getUnbCode(), req.getBusinessLicenseCode());
            // 判断是否需要往信用额度增加一条数据
            TDevOpsCreditEntity tDevOpsCreditEntity = tDevOpsCreditService.lambdaQuery().eq(TDevOpsCreditEntity::getDelFlag, 0).eq(TDevOpsCreditEntity::getDevOpsCode, opCode).one();
            if (tDevOpsCreditEntity == null) {
                // 信用记录表增加一条数据 t_dev_ops_credit
                TDevOpsCreditEntity tDevOpsCreditEntityAdd = TDevOpsCreditEntity.builder().devOpsCode(opCode).devOpsName(req.getName()).build();
                tDevOpsCreditService.save(tDevOpsCreditEntityAdd);
            }
        }
        return opCode;
    }

    /**
     * 运维商新建-编辑
     */
    public void createOpEdit(EditReq req) {

        // 查询运维商
        TOperationEntity tOperationEntity = tOperationService.lambdaQuery().eq(TOperationEntity::getDelFlag, 0).eq(TOperationEntity::getOperationCode, req.getOpCode()).one();
        if (tOperationEntity == null) {
            throw new BussinessException("无法编辑运维商,请核实运维商Code是否正确!");
        }

        // 更新 t_dev_ops_credit 运维商名称(当修改名称时,要同步修改额度表的名称)
        tDevOpsCreditService.lambdaUpdate().eq(TDevOpsCreditEntity::getDelFlag, 0).eq(TDevOpsCreditEntity::getDevOpsCode, req.getOpCode()).set(TDevOpsCreditEntity::getDevOpsName, req.getName()).update();

        // 更新主表数据
        tOperationService.lambdaUpdate().eq(TOperationEntity::getDelFlag, 0).eq(TOperationEntity::getOperationCode, req.getOpCode()).set(TOperationEntity::getOperationName, req.getName()).set(TOperationEntity::getOperationShortName, req.getShortName()).set(TOperationEntity::getTaxCode, req.getTaxNumber()).set(TOperationEntity::getDevType, req.getDevType()).set(TOperationEntity::getProvinceCompanyCode, req.getProvinceCompanyCode()).set(TOperationEntity::getPsType, req.getPsType()).set(TOperationEntity::getSettlementType, req.getSettlementType()).set(TOperationEntity::getLegalPerson, req.getLegalPerson()).set(TOperationEntity::getIdCard, req.getIdCard()).set(TOperationEntity::getBusinessLicenseCode, req.getBusinessLicenseCode()).set(TOperationEntity::getAccountOpeningBank, req.getAccountOpeningBank()).set(TOperationEntity::getBankAccountCode, req.getBankAccountCode()).set(TOperationEntity::getUnbCode, req.getUnbCode()).set(TOperationEntity::getBusinessAddress, req.getBusinessAddress()).set(TOperationEntity::getOperationPerson, req.getOperationPerson()).set(TOperationEntity::getPersonPhone, req.getPersonPhone()).set(TOperationEntity::getElectricianLicenseCode, req.getElectricianLicenseCode()).set(TOperationEntity::getSpecialOperationCertificateCode, req.getSpecialOperationCertificateCode()).set(TOperationEntity::getOpStatus, req.getOperationType()).update();

        // 更新仓库表数据
        tSpareDevOpsWhService.lambdaUpdate().eq(TSpareDevOpsWhEntity::getDelFlag, 0).eq(TSpareDevOpsWhEntity::getWarehouseCode, req.getWarehouseCode()).eq(TSpareDevOpsWhEntity::getDevOpsCode, req.getOpCode()).set(TSpareDevOpsWhEntity::getProvinceId, req.getProvinceId()).set(TSpareDevOpsWhEntity::getProvinceName, req.getProvinceName()).set(TSpareDevOpsWhEntity::getCityId, req.getCityId()).set(TSpareDevOpsWhEntity::getCityName, req.getCityName()).set(TSpareDevOpsWhEntity::getRegionId, req.getRegionId()).set(TSpareDevOpsWhEntity::getRegionName, req.getRegionName()).update();
        // 保存文件数据
        Long id = tOperationEntity.getId();
        // 更新营业执照附件
        this.deleteAndSaveFile(id, 2, 6, req.getBusinessLicensePic());
        // 更新法人身份证附件
        this.deleteAndSaveFile(id, 2, 7, req.getIdCardPic());
        // 更新基础开票附件
        this.deleteAndSaveFile(id, 2, 8, req.getBasePic());
        // 更新保险附件
        this.deleteAndSaveFile(id, 2, 1, req.getInsurancePic());
        // 更新运维人员身份证附件
        this.deleteAndSaveFile(id, 2, 2, req.getPersonIdCardPic());
        // 更新特种作业证照片附件
        this.deleteAndSaveFile(id, 2, 3, req.getSpecialOperationCertificatePic());
        // 更新劳动合同附件
        this.deleteAndSaveFile(id, 2, 4, req.getWorkPic());
        // 更新低压电工证附件
        this.deleteAndSaveFile(id, 2, 5, req.getLowVoltagePic());
        // 更新运维车辆附件
        this.deleteAndSaveFile(id, 2, 10, req.getOperationCarPic());
        // 更新仓库照片附件
        this.deleteAndSaveFile(id, 2, 11, req.getWarehousePic());
        // 更新场地及门头照片附件
        this.deleteAndSaveFile(id, 2, 12, req.getSitePic());
        // 更新承装修资质附件
        this.deleteAndSaveFile(id, 2, 13, req.getDecorationPic());

        if (req.getOperationType() == 1) {
            // 提交时,校验数据是否完全
            BothDetailVO bothDetailVO = this.bothDetail(req.getOpCode(), 0);
            AgentCreateOpVO baseInfoComplete = this.isBaseInfoComplete(bothDetailVO);
            if (baseInfoComplete.getBaseAllInfo() != 1) {
                throw new BussinessException("运维商基础信息尚未完善,请全部完善后再点击提交!");
            }
            // 判断是否需要往信用额度增加一条数据
            TDevOpsCreditEntity tDevOpsCreditEntity = tDevOpsCreditService.lambdaQuery().eq(TDevOpsCreditEntity::getDelFlag, 0).eq(TDevOpsCreditEntity::getDevOpsCode, req.getOpCode()).one();
            if (tDevOpsCreditEntity == null) {
                // 信用记录表增加一条数据 t_dev_ops_credit
                TDevOpsCreditEntity tDevOpsCreditEntityAdd = TDevOpsCreditEntity.builder().devOpsCode(req.getOpCode()).devOpsName(req.getName()).build();
                tDevOpsCreditService.save(tDevOpsCreditEntityAdd);
            }

            // 校验数据格式是否正确
            this.checkInfo(req.getIdCard(), req.getBankAccountCode(), req.getPersonPhone(), req.getUnbCode(), req.getBusinessLicenseCode());
        }
    }

    /**
     * 运维商区域-列表
     */
    public List<AreaListVO> opAreaList(String code, Integer type) {

        TOperationEntity operationEntity = tOperationService.lambdaQuery().eq(TOperationEntity::getDelFlag, 0).eq(TOperationEntity::getOperationCode, code).one();
        if (operationEntity == null) {
            if (type == 0) {
                throw new BussinessException("此代理商尚未维护运维商基本信息,无法维护运维商区域");
            } else {
                return new ArrayList<>();
            }
        }
        return operationArchiveDao.opAreaList(code);
    }

    /**
     * 运维商区域-删除
     */
    public void opAreaDelete(String code, Long regionId, String relationAgent) {

        List<TOperationAreaEntity> list = tOperationAreaService.lambdaQuery().eq(TOperationAreaEntity::getDelFlag, 0).eq(TOperationAreaEntity::getOperationCode, code).eq(TOperationAreaEntity::getRegionId, regionId).list();
        if (CollUtil.isNotEmpty(list)) {
            // 更新运维商区域表
            tOperationAreaService.lambdaUpdate()
                    .eq(TOperationAreaEntity::getDelFlag, 0).eq(TOperationAreaEntity::getOperationCode, code)
                    .eq(TOperationAreaEntity::getRegionId, regionId).remove();
            // 更新运维商主表区域状态
            List<TOperationAreaEntity> listAll = tOperationAreaService.lambdaQuery()
                    .eq(TOperationAreaEntity::getDelFlag, 0).eq(TOperationAreaEntity::getOperationCode, code).list();
            tOperationService.lambdaUpdate().eq(TOperationEntity::getDelFlag, 0)
                    .eq(TOperationEntity::getOperationCode, code).set(TOperationEntity::getRegionStatus, CollUtil.isNotEmpty(listAll) ? 1 : 2).update();
            // 添加变更记录
            TOperationAreaChangeRecordEntity tOperationAreaChangeRecordEntity = TOperationAreaChangeRecordEntity.builder()
                    .operationCode(code).provinceId(list.get(0).getProvinceId()).provinceName(list.get(0).getProvinceName())
                    .cityId(list.get(0).getCityId()).cityName(list.get(0).getCityName()).regionId(regionId)
                    .regionName(list.get(0).getRegionName()).serverOrgName(relationAgent).changeType(2).build();
            tOperationAreaChangeRecordService.save(tOperationAreaChangeRecordEntity);

            // 查询当前运维商的状态,如果是非创建中,则同步更新t_power_station主表运维商信息
            // this.areaPowerStation(list);
        }
    }

    /**
     * 运维商区域-新增
     */
    public void opAreaAdd(AreaAddReq req) {

        if (CollUtil.isNotEmpty(req.getAgentCodeList())) {
            // 查询所有的已经维护的代理商,以省市区+代理商Code进行分组
            Map<String, TOperationAreaEntity> areaMap = new HashMap<>();
            List<TOperationAreaEntity> areaList = tOperationAreaService.lambdaQuery().eq(TOperationAreaEntity::getDelFlag, 0).list();
            if (CollUtil.isNotEmpty(areaList)) {
                areaMap = areaList.stream().collect(Collectors.toMap(item -> item.getServerOrgCode() + "_" + item.getRegionId(), Function.identity()));
            }
            List<TOperationAreaEntity> result = new ArrayList<>();
            for (String agentCode : req.getAgentCodeList()) {
                // 校验区域是否已存在
                for (AreaAddReq.regionData region : req.getRegionInfo()) {
                    String key = agentCode + "_" + region.getRegionId();
                    if (areaMap.containsKey(key)) {
                        throw new BussinessException("此区域的代理商已经被运维商关联,请重新选择！");
                    } else {
                        // 新增运维商区域
                        TOperationAreaEntity tOperationAreaEntity = TOperationAreaEntity.builder()
                                .operationCode(req.getOpCode()).serverOrgCode(agentCode).provinceId(req.getProvinceId())
                                .provinceName(req.getProvinceName()).cityId(req.getCityId()).cityName(req.getCityName())
                                .regionId(region.getRegionId()).regionName(region.getRegionName()).build();
                        result.add(tOperationAreaEntity);
                    }
                }
            }
            if (CollUtil.isNotEmpty(result)) {
                // 新增运维商区域
                tOperationAreaService.saveBatch(result);
                // 更新主表区域状态
                tOperationService.lambdaUpdate().eq(TOperationEntity::getDelFlag, 0)
                        .eq(TOperationEntity::getOperationCode, req.getOpCode()).set(TOperationEntity::getRegionStatus, 1).update();
                // 添加变更记录
                for (AreaAddReq.regionData region : req.getRegionInfo()) {
                    TOperationAreaChangeRecordEntity tOperationAreaChangeRecordEntity = TOperationAreaChangeRecordEntity.builder()
                            .operationCode(req.getOpCode()).provinceId(req.getProvinceId()).provinceName(req.getProvinceName())
                            .cityId(req.getCityId()).cityName(req.getCityName()).regionId(region.getRegionId()).regionName(region.getRegionName())
                            .serverOrgName(req.getRelationAgent()).changeType(1).build();
                    tOperationAreaChangeRecordService.save(tOperationAreaChangeRecordEntity);
                }
                // 查询当前运维商的状态,如果是非创建中,则同步更新t_power_station主表运维商信息
                this.areaPowerStation(result);
            }
        }
    }

    /**
     * 变更记录-档案变更记录列表
     */
    public IPage<ChangeArchivePageVO> changeArchivePageList(ChangeArchiveReq req) {

        Page<ChangeArchivePageVO> page = new Page<>(req.getPageNum(), req.getPageSize());
        IPage<ChangeArchivePageVO> result = operationArchiveDao.changeArchivePageList(page, req);
        ExpandBeanUtils.convert(result, result);
        return result;
    }


    /**
     * 变更记录-档案变更记录详情
     */
    public ArchiveChangeVO changeArchiveDetail(Long id) {

        TOperationChangeRecordEntity record = tOperationChangeRecordService.lambdaQuery().eq(TOperationChangeRecordEntity::getDelFlag, 0).eq(TOperationChangeRecordEntity::getId, id).one();
        if (record == null) {
            throw new BussinessException("无法查询变更记录详情!");
        }
        //变更前
        BothDetailVO beforeVO = JSONUtil.toBean(record.getBeforeChange(), BothDetailVO.class);
        // 变更后
        BothDetailVO afterVO = JSONUtil.toBean(record.getAfterChange(), BothDetailVO.class);

        return ArchiveChangeVO.builder().before(beforeVO).after(afterVO).build();

    }

    /**
     * 变更记录-区域变更记录列表
     */
    public IPage<ChangeAreaPageVO> changeAreaPageList(ChangeAreaReq req) {

        Page<ChangeAreaPageVO> page = new Page<>(req.getPageNum(), req.getPageSize());
        return operationArchiveDao.changeAreaPageList(page, req);
    }

    /**
     * 正常创建运维商时,更新t_power_station
     */
    private void updatePowerStation(TOperationEntity tOperationEntity) {
        List<TOperationAreaEntity> areaList = tOperationAreaService.lambdaQuery()
                .eq(TOperationAreaEntity::getDelFlag, 0)
                .eq(TOperationAreaEntity::getOperationCode, tOperationEntity.getOperationCode())
                .list();
        for (TOperationAreaEntity area : areaList) {
            // 更新并网时间大于一年的电站
            this.updatePsOperation(tOperationEntity, area);
        }
    }

    /**
     * 已经创建成功的运维商在维护运维商区域时,也需要更新电站主表
     * 新增、删除运维商区域时,更新t_power_station
     */
    private void areaPowerStation(List<TOperationAreaEntity> areaList) {

        String opCode = areaList.get(0).getOperationCode();
        TOperationEntity one = tOperationService.lambdaQuery()
                .eq(TOperationEntity::getDelFlag, 0)
                .eq(TOperationEntity::getOperationCode, opCode)
                .one();
        if (one.getOperationStatus() != 4) {
            for (TOperationAreaEntity area : areaList) {
                // 新增运维商区域,前提是并网时间大于一年
                this.updatePsOperation(one, area);
            }
        }
    }

    // 更新并网时间大于一年电站运维商信息
    private void updatePsOperation(TOperationEntity one, TOperationAreaEntity area) {
        // 新增运维商区域
        //一年外并网电站
        List<TPowerStationEntity> psList = tPowerStationService.lambdaQuery()
                .eq(TPowerStationEntity::getDelFlag, 0).eq(TPowerStationEntity::getRegionId, area.getRegionId())
                .eq(TPowerStationEntity::getDevType, one.getDevType()).eq(TPowerStationEntity::getServiceOrgCode, area.getServerOrgCode())
                .le(TPowerStationEntity::getPowerCheckTime, DateUtil.offsetMonth(DateUtil.date(), -12))
                .eq(TPowerStationEntity::getPsType, 1).eq(TPowerStationEntity::getArFlag, 1).list();

        //校验代理商是否暂停运维
        TOperationEntity agent = tOperationService.getOne(Wrappers.<TOperationEntity>lambdaQuery().eq(TOperationEntity::getDelFlag, 0)
                .eq(TOperationEntity::getOperationCode, area.getServerOrgCode()));
        if (Objects.nonNull(agent) && agent.getPauseFlag() == 1) {
            //一年内并网电站且代理商还是暂停运维的电站
            List<TPowerStationEntity> neiPsList = tPowerStationService.lambdaQuery()
                    .eq(TPowerStationEntity::getDelFlag, 0).eq(TPowerStationEntity::getRegionId, area.getRegionId())
                    .eq(TPowerStationEntity::getDevType, one.getDevType()).eq(TPowerStationEntity::getServiceOrgCode, area.getServerOrgCode())
                    .gt(TPowerStationEntity::getPowerCheckTime, DateUtil.offsetMonth(DateUtil.date(), -12))
                    .eq(TPowerStationEntity::getPsType, 1).eq(TPowerStationEntity::getArFlag, 1).list();
            if (CollUtil.isNotEmpty(neiPsList)) {
                psList.addAll(neiPsList);
            }
        }
        if (CollUtil.isEmpty(psList)) {
            return;
        }
        psList.forEach(ps -> ps.setOperationCode(one.getOperationCode()));
        tPowerStationService.updateBatchById(psList);
        //更新电站对应的巡检工单(未完成)运维商
        List<String> psCodes = psList.stream().map(TPowerStationEntity::getPsCode).collect(Collectors.toList());
        List<TNewInspectionOrderEntity> inspectionOrder = tNewInspectionOrderService.list(Wrappers.<TNewInspectionOrderEntity>lambdaQuery()
                .in(TNewInspectionOrderEntity::getPsCode, psCodes).eq(TNewInspectionOrderEntity::getDelFlag, 0).eq(TNewInspectionOrderEntity::getInsStatus, 0));
        List<TNewInspectionOrderEntity> addData = new ArrayList<>();
        if (CollUtil.isNotEmpty(inspectionOrder)) {
            inspectionOrder.forEach(inspection -> inspection.setInspectionPerson("yw" + one.getOperationCode()));
            addData.addAll(inspectionOrder);
        }
        if (CollUtil.isNotEmpty(addData)) {
            tNewInspectionOrderService.saveOrUpdateBatch(addData);
        }
    }

    /**
     * 校验身份证长度(18位)
     * 校验银行卡号长度(13-19位、全数字)
     * 校验手机格式(11)位
     * 联行号(12位)
     * 营业执照(15、18位)
     */
    private void checkInfo(String idCard, String bankCard, String mobile, String unionBank, String businessLicense) {
        if (StrUtil.isNotBlank(idCard) && idCard.length() != 18) {
            throw new BussinessException("身份证号长度不正确!");
        }
        /*if ((StrUtil.isNotBlank(bankCard) && (bankCard.length() < 13 || bankCard.length() > 20 || !bankCard.matches("\\d+")))) {
            throw new BussinessException("银行卡号长度或格式不正确!");
        }*/
        if ((StrUtil.isNotBlank(mobile) && (mobile.length() != 11 || !mobile.matches("\\d+")))) {
            throw new BussinessException("手机号格式不正确!");
        }
        if ((StrUtil.isNotBlank(unionBank) && unionBank.length() != 12)) {
            throw new BussinessException("联行号格式不正确!");
        }
        if ((StrUtil.isNotBlank(businessLicense) && (businessLicense.length() != 15 && businessLicense.length() != 18))) {
            throw new BussinessException("营业执照格式不正确!");
        }
    }

    /**
     * 根据代理商获取用户相关数量
     *
     * @param id 代理商id
     * @return Integer
     */
    public Integer getUserCnt(Long id) {
        int cnt = 0;
        //查询代理商数据
        TOperationEntity operation = tOperationService.getById(id);
        if (Objects.isNull(operation)) {
            throw new BussinessException("代理商不存在!");
        }
        //查询代理商相关的人员
        OperationConfigVO operationConfigVO = operationUserService.operationConfig(2, operation.getOperationCode());
        if (Objects.isNull(operationConfigVO) || (null == operationConfigVO.getOperationAdminVO()) && CollectionUtil.isEmpty(operationConfigVO.getOperationPersonList())) {
            //为空直接返回
            return cnt;
        }
        cnt = 1;
        List<OperationPersonVO> operationPersonList = operationConfigVO.getOperationPersonList();
        if (Objects.nonNull(operationPersonList)) {
            //下属人员存在返回1+size
            cnt = cnt + operationPersonList.size();
        }
        return cnt;
    }

    /**
     * 代理商暂停运维时校验
     *
     * @param id 代理商id
     * @return
     */
    public AgentPauseResultVO checkPause(Long id) {
        AgentPauseResultVO result = new AgentPauseResultVO();
        // 查询代理商信息
        TOperationEntity operation = tOperationService.getById(id);
        if (Objects.isNull(operation)) {
            throw new BussinessException("代理商不存在!");
        }
        //运维工单
        Integer operationCnt = operationArchiveDao.getOperationFlag(operation.getOperationCode());
        if (operationCnt == 0) {
            result.setOperationFlag(true);
        }
        //备件申请
        long applyCnt = tSpareStockOrderService.count(Wrappers.<TSpareStockOrderEntity>lambdaQuery().notIn(TSpareStockOrderEntity::getOrderStatus, 4, 5).eq(TSpareStockOrderEntity::getMaintenanceCompanyCode, operation.getOperationCode()));
        if (applyCnt == 0) {
            result.setApplyFlag(true);
        }
        //备件退还
        long returnCnt = tSpareReturnService.count(Wrappers.<TSpareReturnEntity>lambdaQuery().eq(TSpareReturnEntity::getSeverOrgCode, operation.getOperationCode()).ne(TSpareReturnEntity::getReturnStatus, 4));
        if (returnCnt == 0) {
            result.setReturnFlag(true);
        }
        //售后仓物料
        Integer whCnt = operationArchiveDao.getWhFlag(operation.getOperationCode());
        if (Objects.isNull(whCnt) || whCnt == 0) {
            result.setWhFlag(true);
        }
        //额度占用
        OperationCreditVO operationCreditVO = operationArchiveDao.getAmountFlag(operation.getOperationCode());
        if (operationCreditVO.getCreditConsume().compareTo(BigDecimal.ZERO) == 0 && operationCreditVO.getTemporaryConsume().compareTo(BigDecimal.ZERO) == 0) {
            result.setAmountFlag(true);
        }
        return result;
    }

    /**
     * 暂停运维代理商
     *
     * @param id 代理商id
     */
    @DSTransactional(rollbackFor = Exception.class)
    public void pauseAgent(Long id) {
        //获取代理商数据
        TOperationEntity operation = tOperationService.getById(id);
        if (Objects.isNull(operation)) {
            throw new BussinessException("代理商不存在!");
        }
        if (operation.getPauseFlag() == 1) {
            throw new BussinessException("该代理商已暂停运维,不可重复暂停运维!");
        }
        //更新t_operation表 为暂停状态
        operation.setPauseFlag(1);
        // 获取代理商下所有用户
        List<String> userCodes = this.getUserCnt(operation.getOperationCode());

        List<TPowerStationEntity> psList = tPowerStationService.list(Wrappers.<TPowerStationEntity>lambdaQuery()
                .eq(TPowerStationEntity::getDelFlag, 0).eq(TPowerStationEntity::getArFlag, 1).eq(TPowerStationEntity::getOperationCode, operation.getOperationCode()));
        if (CollUtil.isNotEmpty(psList)) {
            //电站切代理商市此代理商,更新运维商为电站对应区域的运维商
            Set<Long> regionIds = CollStreamUtil.toSet(psList, TPowerStationEntity::getRegionId);
            List<TOperationAreaEntity> areaList = tOperationAreaService.list(Wrappers.<TOperationAreaEntity>lambdaQuery()
                    .eq(TOperationAreaEntity::getDelFlag, 0).in(TOperationAreaEntity::getRegionId, regionIds));
            if (CollUtil.isNotEmpty(areaList)) {
                Map<Long, Map<String, List<TOperationAreaEntity>>> areaMap = CollStreamUtil.groupBy2Key(areaList, TOperationAreaEntity::getRegionId, TOperationAreaEntity::getServerOrgCode);
                //根据区域获取对应的运维商编码
                for (TPowerStationEntity ps : psList) {
                    Map<String, List<TOperationAreaEntity>> regionMap = areaMap.get(ps.getRegionId());
                    if (CollUtil.isEmpty(regionMap)) {
                        continue;
                    }
                    List<TOperationAreaEntity> serviceMap = regionMap.get(ps.getServiceOrgCode());
                    if (CollUtil.isNotEmpty(serviceMap)) {
                        //不存在置为null
                        ps.setOperationCode(serviceMap.get(0).getOperationCode());
                    }
                }
            }
        }
        //更新代理商
        tOperationService.updateById(operation);
        //更新并网一年内的电站运维商
        tPowerStationService.updateBatchById(psList);
        //禁用相关人员
        if (CollUtil.isNotEmpty(userCodes)) {
            baseUserService.lambdaUpdate()
                    .set(BaseUserEntity::getFEnabledmark, 0)
                    .set(BaseUserEntity::getFDescription, "因暂停运维,禁用此账号")
                    .in(BaseUserEntity::getFAccount, userCodes)
                    .update();
        }
    }

    public OperationResultVO checkBack(Long id) {
        OperationResultVO result = new OperationResultVO();
        // 查询代理商信息
        TOperationEntity operation = tOperationService.getById(id);
        if (Objects.isNull(operation)) {
            throw new BussinessException("代理商不存在!");
        }
        //公用代理商暂停运维校验
        AgentPauseResultVO agentPauseResultVO = this.checkPause(id);
        BeanUtil.copyProperties(agentPauseResultVO, result);
        //运维商区域标识
        List<AreaListVO> areaListVOS = operationArchiveDao.opAreaList(operation.getOperationCode());
        if (CollUtil.isEmpty(areaListVOS)) {
            result.setOperationAreaFlag(true);
        }
        //巡检工单校验
        List<TPowerStationEntity> psList = tPowerStationService.list(Wrappers.<TPowerStationEntity>lambdaQuery()
                .eq(TPowerStationEntity::getDelFlag, 0).eq(TPowerStationEntity::getOperationCode, operation.getOperationCode()));
        if (CollUtil.isEmpty(psList)) {
            result.setInspectionFlag(true);
            return result;
        }
        List<TNewInspectionOrderEntity> inspectionOrders = tNewInspectionOrderService.list(Wrappers.<TNewInspectionOrderEntity>lambdaQuery()
                .eq(TNewInspectionOrderEntity::getDelFlag, 0)
                .in(TNewInspectionOrderEntity::getPsCode, CollStreamUtil.toList(psList, TPowerStationEntity::getPsCode))
                .in(TNewInspectionOrderEntity::getInsStatus, 1, 2));
        //判断巡检中,待审核数量是否为0
        if (CollUtil.isEmpty(inspectionOrders)) {
            result.setInspectionFlag(true);
        }
        return result;
    }

    public OperationOrderCntVO orderCnt(Long id) {
        // 查询运维商信息
        TOperationEntity operation = tOperationService.getById(id);
        if (Objects.isNull(operation)) {
            throw new BussinessException("运维商不存在!");
        }
        //根据运维商查询当年度的巡检工单
        List<TNewInspectionOrderEntity> inspectionOrders = tNewInspectionOrderService.list(Wrappers.<TNewInspectionOrderEntity>lambdaQuery().
                eq(TNewInspectionOrderEntity::getDelFlag, 0).eq(TNewInspectionOrderEntity::getInspectionPerson, String.format("yw%s", operation.getOperationCode())));
        if (CollUtil.isEmpty(inspectionOrders)) {
            return new OperationOrderCntVO(0, 0);
        }
        return OperationOrderCntVO.builder().
                allCnt(inspectionOrders.size())
                .finishCnt(Math.toIntExact(inspectionOrders.stream().filter(inspectionOrder -> inspectionOrder.getInsStatus() == 3).count()))
                .build();
    }

    @DSTransactional(rollbackFor = Exception.class)
    public void backConfirm(Long id) {
        //获取运维商数据
        TOperationEntity operation = tOperationService.getById(id);
        if (Objects.isNull(operation)) {
            throw new BussinessException("运维商不存在!");
        }
        if (2 == operation.getOperationStatus()) {
            throw new BussinessException("该运维商已退商,不可重复退商!");
        }
        //更改运维商的状态
        operation.setOperationStatus(2);
        //查询代理商相关的人员
        List<String> userCodes = this.getUserCnt(operation.getOperationCode());
        //获取所有未完成的巡检单
        List<InspectionResp> inspectionOrders = workerBillDao.getNotFinishInspection(operation.getOperationCode());
        List<TGdFillXjDetailEntity> xjDetail = new ArrayList<>();
        if (CollUtil.isNotEmpty(inspectionOrders)) {
            LocalDate now = LocalDate.now();
            //季度
            int quarter = (now.getMonthValue() - 1) / 3 + 1;
            //根据运维商查询电站数量
            //获取所有电站
            List<TPowerStationEntity> powerStationList = tPowerStationService.lambdaQuery()
                    .eq(TPowerStationEntity::getDelFlag, 0)
                    .eq(TPowerStationEntity::getOperationCode, operation.getOperationCode())
                    .eq(TPowerStationEntity::getPsType, 1).list();
            //计算巡检单考核费用(按照季度对单价进行折算扣费,举例,若运维商在2月退商,则生成所有未完成电站巡检单的巡检扣费单,扣费单价为50*1/4,扣费总额扣费单价*未完成巡检工单数量若4月退商则扣费单价为50*2/4)
            //计算扣费单价
            BigDecimal price = BigDecimal.valueOf(50).multiply(BigDecimal.valueOf(quarter).multiply(new BigDecimal("-1")).divide(BigDecimal.valueOf(4)));
            //生成巡检扣费工单
            TDevOpsGdFillEntity build = TDevOpsGdFillEntity.builder()
                    .psNum(powerStationList.size()).checkFee(price.multiply(BigDecimal.valueOf(inspectionOrders.size())))
                    .devOpsCode(operation.getOperationCode()).devOpsName(operation.getOperationName())
                    .fillDate(now.getYear() + "Q" + quarter).gdStatus(-1).fillNo(genCodeUtil.getSysCodeByCodeRule(GenRuleCode.FG))
                    .gdNum(inspectionOrders.size()).fillType(1).actualCheckFee(price.multiply(BigDecimal.valueOf(inspectionOrders.size())))
                    .build();
            tDevOpsGdFillService.save(build);
            //封装巡检工单明细
            inspectionOrders.forEach(inspectionOrder -> xjDetail.add(TGdFillXjDetailEntity.builder()
                    .checkFee(price).fillId(build.getId()).fillNo(build.getFillNo()).inspectionCode(inspectionOrder.getInspectionCode())
                    .psCode(inspectionOrder.getPsCode()).userName(inspectionOrder.getUserName()).address(inspectionOrder.getAddress()).status(0)
                    .build()));
        }
        //更新运维商->退商
        tOperationService.updateById(operation);
        //添加巡检工单详细
        if (CollUtil.isNotEmpty(xjDetail)) {
            tGdFillXjDetailService.saveBatch(xjDetail);
        }
        //禁用相关人员
        if (CollUtil.isNotEmpty(userCodes)) {
            baseUserService.lambdaUpdate()
                    .set(BaseUserEntity::getFEnabledmark, 0)
                    .set(BaseUserEntity::getFDescription, "因退商,禁用此账号")
                    .in(BaseUserEntity::getFAccount, userCodes)
                    .update();
        }
    }

    // 抽取禁用的运维人员
    public List<String> getUserCnt(String operationCode) {
        List<String> userCodes = new ArrayList<>();
        //查询代理商相关的人员
        OperationConfigVO operationConfigVO = operationUserService.operationConfig(2, operationCode);
        if (Objects.nonNull(operationConfigVO)) {
            if (Objects.nonNull(operationConfigVO.getOperationAdminVO())) {
                userCodes.add(operationConfigVO.getOperationAdminVO().getAccount());
            }
            BaseRoleEntity role = baseRoleService.getOne(new LambdaQueryWrapper<BaseRoleEntity>().eq(BaseRoleEntity::getFEncode, "operation"));
            String roleId = role.getFId();
            Pattern pattern = Pattern.compile("(^|,)" + Pattern.quote(roleId) + "(,|$)");
            if (CollUtil.isNotEmpty(operationConfigVO.getOperationPersonList())) {
                List<String> collect = operationConfigVO.getOperationPersonList().stream()
                        .filter(item -> pattern.matcher(item.getRoleName()).find()) // 精确匹配 roleId 只可以更新包含运维人员数据查看角色
                        .map(OperationPersonVO::getAccount)
                        .collect(Collectors.toList());
                userCodes.addAll(collect);
            }
        }
        return userCodes;
    }

    /**
     * 新增区域前获取电站,巡检工单
     *
     * @param req
     * @return
     */
    public OpAreaCheckVo opAreaCheck(AreaAddReq req) {
        //查询区域/代理商下的电站
        List<TPowerStationEntity> psList = tPowerStationService.list(Wrappers.<TPowerStationEntity>lambdaQuery()
                .eq(TPowerStationEntity::getDelFlag, 0).in(TPowerStationEntity::getRegionId, CollStreamUtil.toSet(req.getRegionInfo(), AreaAddReq.regionData::getRegionId))
                .le(TPowerStationEntity::getPowerCheckTime, DateUtil.offsetMonth(DateUtil.date(), -12))
                .eq(TPowerStationEntity::getDevType, 0).eq(TPowerStationEntity::getPsType, 1)
                .in(CollUtil.isNotEmpty(req.getAgentCodeList()), TPowerStationEntity::getServiceOrgCode, req.getAgentCodeList()));
        if (CollUtil.isEmpty(psList)) {
            return OpAreaCheckVo.builder()
                    .orderCnt(0)
                    .psCnt(0)
                    .build();
        }
        //根据电站编号查询待巡检的巡检单
        List<TNewInspectionOrderEntity> inspectionOrders = tNewInspectionOrderService.list(Wrappers.<TNewInspectionOrderEntity>lambdaQuery()
                .eq(TNewInspectionOrderEntity::getDelFlag, 0).eq(TNewInspectionOrderEntity::getInsStatus, 0)
                .in(TNewInspectionOrderEntity::getPsCode, psList.stream().map(TPowerStationEntity::getPsCode).collect(Collectors.toList())));
        if (CollUtil.isEmpty(inspectionOrders)) {
            return OpAreaCheckVo.builder()
                    .orderCnt(0)
                    .psCnt(psList.size())
                    .build();
        }
        return OpAreaCheckVo.builder()
                .orderCnt(inspectionOrders.size())
                .psCnt(psList.size())
                .build();
    }
}
