<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.gcl.psmis.monitor.dao.insurance.InsuranceClaimsDao">

    <select id="getPage" resultType="com.gcl.psmis.framework.common.vo.insurance.CompanyPayoutPageVO">
        SELECT
        io.id id,
        io.case_num caseNum,
        io.maintenance_company_name maintenanceCompanyName,
        io.province_company_name provinceCompanyName,
        io.business_person businessPerson,
        io.create_by_name createByName,
        ioc.submit_time submitTime,
        psref.count stationNum,
        ocref.amount
        FROM
        t_insurance_order io
        LEFT JOIN t_insurance_order_loss_assessment ioa ON io.id = ioa.insurance_order_id
        LEFT JOIN t_insurance_order_compensation ioc ON io.id = ioc.insurance_order_id
        LEFT JOIN ( SELECT insurance_order_id orderId, count(*) count FROM t_insurance_order_vs_ps WHERE del_flag = 0
        GROUP BY insurance_order_id ) psref ON psref.orderId = io.id
        LEFT JOIN ( SELECT insurance_order_id orderId, SUM( amount ) amount FROM
        t_insurance_order_compensation WHERE del_flag = 0 GROUP BY insurance_order_id ) ocref ON ocref.orderId = io.id
        WHERE
        io.submit_status = 1 AND ioa.submit_status = 1 and ioc.submit_status = 1 and io.pay_submit_status != 1
        <if test="req.caseNum != null and req.caseNum != ''">
            AND io.case_num = #{req.caseNum}
        </if>
        <if test="req.createTimeBegin != null">
            AND ioc.submit_time >= #{req.createTimeBegin}
        </if>
        <if test="req.createTimeEnd != null">
            AND ioc.submit_time &lt;= #{req.createTimeEnd}
        </if>
        <if test="req.maintenanceCompanyId != null and req.maintenanceCompanyId.size() > 0">
            AND io.maintenance_company_id IN
            <foreach collection="req.maintenanceCompanyId" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
        <if test="req.orderIds != null and req.orderIds.size() > 0">
            AND io.id IN
            <foreach collection="req.orderIds" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
        <if test="req.provinceCompanyId != null and req.provinceCompanyId.size() > 0">
            AND io.province_company_id IN
            <foreach collection="req.provinceCompanyId" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
        GROUP BY io.id
        ORDER BY ioa.submit_time DESC
    </select>

</mapper>
