<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.gcl.psmis.monitor.dao.powerstation.statement.PsStatementDao">

    <select id="psList" resultType="com.gcl.psmis.framework.common.resp.statement.PsStatementResp">
        select
        ps.id,
        tr.province_name,
        ps.province_company_name,
        ps.abb_name,
        ps.sun_powertime psTime,
        ps.all_power_time allTime,
        ps.capacity/1000000 psCapacity,
        case
        when #{psStatementReq.pickMonth} = 1 then sp.jan
        when #{psStatementReq.pickMonth} = 2 then sp.feb
        when #{psStatementReq.pickMonth} = 3 then sp.mar
        when #{psStatementReq.pickMonth} = 4 then sp.apr
        when #{psStatementReq.pickMonth} = 5 then sp.may
        when #{psStatementReq.pickMonth} = 6 then sp.jun
        when #{psStatementReq.pickMonth} = 7 then sp.jul
        when #{psStatementReq.pickMonth} = 8 then sp.aug
        when #{psStatementReq.pickMonth} = 9 then sp.sep
        when #{psStatementReq.pickMonth} = 10 then sp.oct
        when #{psStatementReq.pickMonth} = 11 then sp.nov
        when #{psStatementReq.pickMonth} = 12 then sp.dece
        end hourBudget
        from t_power_station ps
        left join t_scene_parameter sp on ps.ps_code=sp.ps_code and sp.del_flag=0 and sp.pick_year =
        #{psStatementReq.pickYear}
        left join t_region tr on ps.province_id=tr.id
        <where>
            ps.del_flag = 0
            and ps.ar_flag = 1
            and ps.ps_type = 2
            <if test="psStatementReq.abbName!=null and psStatementReq.abbName!=''">
                and ps.abb_name like concat ('%',#{psStatementReq.abbName},'%')
            </if>
            <if test="psStatementReq.owne!=null and psStatementReq.owne!=''">
                and ps.owne like concat ('%',#{psStatementReq.owne},'%')
            </if>
            <if test="psStatementReq.provinceIds!=null and psStatementReq.provinceIds.size()>0">
                and ps.province_id in
                <foreach collection="psStatementReq.provinceIds" index="index" item="item" open="(" separator=","
                         close=")">
                    #{item}
                </foreach>
            </if>
            <if test="psStatementReq.provinceCompanyCodes!=null and psStatementReq.provinceCompanyCodes.size()>0">
                and ps.province_company_code in
                <foreach collection="psStatementReq.provinceCompanyCodes" index="index" item="item" open="("
                         separator="," close=")">
                    #{item}
                </foreach>
            </if>
        </where>

    </select>

    <select id="psYearList" resultType="com.gcl.psmis.framework.common.resp.statement.PsStatementResp">
        select
        ps.id,
        tr.province_name,
        ps.province_company_name,
        ps.abb_name,
        ps.sun_powertime psTime,
        ps.all_power_time allTime,
        ps.capacity/1000000 psCapacity,
        (${psStatementReq.s}) hourBudget
        from t_power_station ps
        left join t_scene_parameter sp on ps.ps_code=sp.ps_code and sp.del_flag=0 and sp.pick_year =
        #{psStatementReq.pickYear}
        left join t_region tr on ps.province_id=tr.id
        <where>
            ps.del_flag = 0
            and ps.ar_flag = 1
            and ps.ps_type = 2
            <if test="psStatementReq.abbName!=null and psStatementReq.abbName!=''">
                and ps.abb_name like concat ('%',#{psStatementReq.abbName},'%')
            </if>
            <if test="psStatementReq.owne!=null and psStatementReq.owne!=''">
                and ps.owne like concat ('%',#{psStatementReq.owne},'%')
            </if>
            <if test="psStatementReq.provinceIds!=null and psStatementReq.provinceIds.size()>0">
                and ps.province_id in
                <foreach collection="psStatementReq.provinceIds" index="index" item="item" open="(" separator=","
                         close=")">
                    #{item}
                </foreach>
            </if>
            <if test="psStatementReq.provinceCompanyCodes!=null and psStatementReq.provinceCompanyCodes.size()>0">
                and ps.province_company_code in
                <foreach collection="psStatementReq.provinceCompanyCodes" index="index" item="item" open="("
                         separator="," close=")">
                    #{item}
                </foreach>
            </if>
        </where>
    </select>

    <select id="provinceList" resultType="com.gcl.psmis.framework.common.resp.statement.ProvinceStatementResp">
        SELECT
        ps.province_id,
        tr.province_name
        FROM
        t_power_station ps
        LEFT JOIN t_region tr ON ps.province_id = tr.id
        <where>
            ps.del_flag = 0
            AND ps.ar_flag = 1
            AND ps.ps_type = 2
            <if test="psStatementReq.provinceIds!=null and psStatementReq.provinceIds.size()>0">
                and ps.province_id in
                <foreach collection="psStatementReq.provinceIds" index="index" item="item" open="(" separator=","
                         close=")">
                    #{item}
                </foreach>
            </if>
            <if test="psStatementReq.provinceCompanyCodes!=null and psStatementReq.provinceCompanyCodes.size()>0">
                and ps.province_company_code in
                <foreach collection="psStatementReq.provinceCompanyCodes" index="index" item="item" open="("
                         separator="," close=")">
                    #{item}
                </foreach>
            </if>
        </where>
        GROUP BY
        ps.province_id,
        tr.province_name
        ORDER BY
        ps.province_id
    </select>

    <select id="findProvince" resultType="com.gcl.psmis.framework.common.resp.statement.ProvinceStatementResp">
        SELECT
        sum(ps.capacity/1000000) psCapacity,
        GROUP_CONCAT(ps.id SEPARATOR ',') AS psIds,
        sum(( ps.capacity*sp.${s} ))/sum( ps.capacity) hourBudget,
        sum(ps.capacity*(sp.${s})/10000000) powerBudget
        FROM t_power_station ps
        LEFT JOIN t_scene_parameter sp ON ps.ps_code = sp.ps_code AND sp.del_flag = 0 and sp.pick_year = #{pickYear}
        <where>
            ps.del_flag = 0
            AND ps.ar_flag = 1
            AND ps.ps_type = 2
            <if test="provinceId!=null and provinceId!=''">
                and ps.province_id = #{provinceId}
            </if>
            <if test="psTime!=null and psTime!=''">
                and ps.all_power_time &lt; #{psTime}
            </if>
        </where>
    </select>

    <select id="findProvinceCapacity" resultType="java.math.BigDecimal">
        SELECT
        sum(ps.capacity/1000000) psCapacity
        FROM t_power_station ps
        <where>
            ps.del_flag = 0
            AND ps.ar_flag = 1
            AND ps.ps_type = 2
            <if test="provinceId!=null and provinceId!=''">
                and ps.province_id = #{provinceId}
            </if>
            <if test="provinceCompanyCode!=null and provinceCompanyCode!=''">
                and ps.province_company_code = #{provinceCompanyCode}
            </if>

            <if test="psTime!=null and psTime!=''">
                and ps.all_power_time &lt; #{psTime}
            </if>
        </where>
    </select>
    <select id="companyList" resultType="com.gcl.psmis.framework.common.resp.statement.ProvinceStatementResp">
        SELECT
        ps.province_id,
        tr.province_name,
        ps.province_company_code,
        ps.province_company_name
        FROM
        t_power_station ps
        LEFT JOIN t_region tr ON ps.province_id = tr.id
        <where>
            ps.del_flag = 0
            AND ps.ar_flag = 1
            AND ps.ps_type = 2
            <if test="psStatementReq.provinceIds!=null and psStatementReq.provinceIds.size()>0">
                and ps.province_id in
                <foreach collection="psStatementReq.provinceIds" index="index" item="item" open="(" separator=","
                         close=")">
                    #{item}
                </foreach>
            </if>
            <if test="psStatementReq.provinceCompanyCodes!=null and psStatementReq.provinceCompanyCodes.size()>0">
                and ps.province_company_code in
                <foreach collection="psStatementReq.provinceCompanyCodes" index="index" item="item" open="("
                         separator="," close=")">
                    #{item}
                </foreach>
            </if>
        </where>
        GROUP BY
        ps.province_id,
        tr.province_name,
        ps.province_company_code,
        ps.province_company_name
        ORDER BY
        ps.province_company_code
    </select>
    <select id="findCompany" resultType="com.gcl.psmis.framework.common.resp.statement.ProvinceStatementResp">
        SELECT
        sum(ps.capacity/1000000) psCapacity,
        GROUP_CONCAT(ps.id SEPARATOR ',') AS psIds,
        sum((sp.${s})) hourBudget ,
        sum(ps.capacity*(sp.${s})/10000000) powerBudget
        FROM t_power_station ps
        LEFT JOIN t_scene_parameter sp ON ps.ps_code = sp.ps_code AND sp.del_flag = 0 and sp.pick_year = #{pickYear}
        <where>
            ps.del_flag = 0
            AND ps.ar_flag = 1
            AND ps.ps_type = 2
            <if test="provinceCompanyCode!=null and provinceCompanyCode!=''">
                and ps.province_company_code = #{provinceCompanyCode}
            </if>
            <if test="psTime!=null and psTime!=''">
                and ps.all_power_time &lt; #{psTime}
            </if>
        </where>
    </select>
    <select id="findCompanyCapacity" resultType="java.lang.Double">
        SELECT
        sum(ps.capacity/1000000) psCapacity
        FROM t_power_station ps
        <where>
            ps.del_flag = 0
            AND ps.ar_flag = 1
            AND ps.ps_type = 2
            <if test="provinceCompanyCode!=null and provinceCompanyCode!=''">
                and ps.province_company_code = #{provinceCompanyCode}
            </if>
            <if test="psTime!=null and psTime!=''">
                and ps.all_power_time &lt; #{psTime}
            </if>
        </where>
    </select>

    <select id="findProvinceYear"
            resultType="com.gcl.psmis.framework.common.resp.statement.ProvinceStatementResp">
        SELECT
        sum( ps.capacity / 1000000 ) psCapacity,
        GROUP_CONCAT( ps.id SEPARATOR ',' ) AS psIds,
        sum((
        sp.jan + sp.feb + sp.mar + sp.apr + sp.may + sp.jun + sp.jul + sp.aug + sp.sep + sp.oct + sp.nov + sp.dece
        )) hourBudget,
        sum(
        ps.capacity *(
        sp.jan + sp.feb + sp.mar + sp.apr + sp.may + sp.jun + sp.jul + sp.aug + sp.sep + sp.oct + sp.nov + sp.dece
        )/ 1000000
        ) powerBudget
        FROM t_power_station ps
        LEFT JOIN t_scene_parameter sp ON ps.ps_code = sp.ps_code AND sp.del_flag = 0 and sp.pick_year = #{pickYear}
        <where>
            ps.del_flag = 0
            AND ps.ar_flag = 1
            AND ps.ps_type = 2
            <if test="provinceId!=null and provinceId!=''">
                and ps.province_id = #{provinceId}
            </if>
            <if test="psTime!=null and psTime!=''">
                and ps.all_power_time &lt; #{psTime}
            </if>
        </where>
    </select>

    <select id="findProvinceYearNow"
            resultType="com.gcl.psmis.framework.common.resp.statement.ProvinceStatementResp">
        SELECT
        sum(ps.capacity/1000000) psCapacity,
        GROUP_CONCAT(ps.id SEPARATOR ',') AS psIds,
        sum((${s})) hourBudget ,
        sum(ps.capacity*(${s})/10000000) powerBudget
        FROM t_power_station ps
        LEFT JOIN t_scene_parameter sp ON ps.ps_code = sp.ps_code AND sp.del_flag = 0 and sp.pick_year = #{pickYear}
        <where>
            ps.del_flag = 0
            AND ps.ar_flag = 1
            AND ps.ps_type = 2
            <if test="provinceId!=null and provinceId!=''">
                and ps.province_id = #{provinceId}
            </if>
            <if test="psTime!=null and psTime!=''">
                and ps.all_power_time &lt; #{psTime}
            </if>
        </where>
    </select>

    <select id="findProvinceYearById"
            resultType="com.gcl.psmis.framework.common.resp.statement.ProvinceStatementResp">
        SELECT
        sum(ps.capacity/1000000) psCapacity,
        GROUP_CONCAT(ps.id SEPARATOR ',') AS psIds,
        sum(( ps.capacity*(sp.${s} )))/sum( ps.capacity) hourBudget,
        sum(ps.capacity*(${s})/10000000) powerBudget
        FROM t_power_station ps
        LEFT JOIN t_scene_parameter sp ON ps.ps_code = sp.ps_code AND sp.del_flag = 0 and sp.pick_year = #{year}
        <where>
            ps.del_flag = 0
            AND ps.ar_flag = 1
            AND ps.ps_type = 2
            <if test="id!=null and id!=''">
                and ps.id = #{id}
            </if>
        </where>
    </select>

    <select id="findProvinceYearById2"
            resultType="com.gcl.psmis.framework.common.resp.statement.ProvinceStatementResp">
        SELECT
        ps.capacity/1000000 psCapacity,
        sum( ps.capacity * sp.scene_ele )/ sum( ps.capacity ) hourBudget,
        sum( ps.capacity * sp.scene_ele / 10000000 ) powerBudget
        FROM t_power_station ps
        LEFT JOIN t_scene_parameter sp ON ps.ps_code = sp.ps_code AND sp.del_flag = 0
        <where>
            ps.del_flag = 0
            AND ps.ar_flag = 1
            AND ps.ps_type = 2
            AND sp.del_flag = 0
            <if test="psStatementReq.psId!=null ">
                and ps.id = #{psStatementReq.psId}
            </if>
            <if test="psStatementReq.provinceCompanyCode!=null ">
                and ps.province_company_code = #{psStatementReq.provinceCompanyCode}
            </if>
            <if test="psStatementReq.startTime!=null ">
                and sp.scene_month &gt;= #{psStatementReq.startTime}
            </if>
            <if test="psStatementReq.endTime!=null ">
                and sp.scene_month &lt; #{psStatementReq.endTime}
            </if>
        </where>
        group by ps.ps_code
    </select>

    <select id="psList2" resultType="com.gcl.psmis.framework.common.resp.statement.PsStatementResp">
        select
        ps.id,
        ps.ps_code,
        tr.province_name,
        ps.province_company_name,
        ps.abb_name,
        ps.sun_powertime psTime,
        ps.all_power_time allTime,
        ps.capacity/1000000 psCapacity
        from t_power_station ps
        left join t_region tr on ps.province_id=tr.id
        <where>
            ps.del_flag = 0
            and ps.ar_flag = 1
            and ps.ps_type = 2
            <if test="psStatementReq.abbName!=null and psStatementReq.abbName!=''">
                and ps.abb_name like concat ('%',#{psStatementReq.abbName},'%')
            </if>
            <if test="psStatementReq.owne!=null and psStatementReq.owne!=''">
                and ps.owne like concat ('%',#{psStatementReq.owne},'%')
            </if>
            <if test="psStatementReq.provinceIds!=null and psStatementReq.provinceIds.size()>0">
                and ps.province_id in
                <foreach collection="psStatementReq.provinceIds" index="index" item="item" open="(" separator=","
                         close=")">
                    #{item}
                </foreach>
            </if>
            <if test="psStatementReq.provinceCompanyCodes!=null and psStatementReq.provinceCompanyCodes.size()>0">
                and ps.province_company_code in
                <foreach collection="psStatementReq.provinceCompanyCodes" index="index" item="item" open="("
                         separator="," close=")">
                    #{item}
                </foreach>
            </if>
        </where>
    </select>

    <select id="findProvince2" resultType="com.gcl.psmis.framework.common.resp.statement.ProvinceStatementResp">
        SELECT
        sum( ps.capacity / 1000000 ) psCapacity,
        GROUP_CONCAT(ps.id SEPARATOR ',') AS psIds,
        sum( ps.capacity * sp.scene_ele )/ sum( ps.capacity ) hourBudget,
        sum( ps.capacity * sp.scene_ele / 10000000 ) powerBudget
        FROM
        t_power_station ps
        LEFT JOIN t_scene_parameter sp ON ps.ps_code = sp.ps_code
        <where>
            ps.del_flag = 0
            AND ps.ar_flag = 1
            AND ps.ps_type = 2
            AND (sp.del_flag = 0 or sp.del_flag is null)
            and ps.all_power_time is not null
            <if test="psStatementReq.provinceId!=null ">
                and ps.province_id = #{psStatementReq.provinceId}
            </if>
            <if test="psStatementReq.provinceCompanyCode!=null ">
                and ps.province_company_code = #{psStatementReq.provinceCompanyCode}
            </if>
            <if test="psStatementReq.psTime!=null">
                and ps.all_power_time &lt; #{psStatementReq.endTime}
            </if>
            <if test="psStatementReq.startTime!=null ">
                and sp.scene_month &gt;= #{psStatementReq.startTime}
            </if>
            <if test="psStatementReq.endTime!=null ">
                and sp.scene_month &lt; #{psStatementReq.endTime}
            </if>
        </where>
    </select>

    <select id="psYearList2" resultType="com.gcl.psmis.framework.common.resp.statement.PsStatementResp">
        select
        ps.id,
        ps.ps_code,
        tr.province_name,
        ps.province_company_name,
        ps.abb_name,
        ps.sun_powertime psTime,
        ps.all_power_time allTime,
        ps.capacity / 1000000 psCapacity
        from t_power_station ps
        left join t_region tr on ps.province_id=tr.id
        <where>
            ps.del_flag = 0
            and ps.ar_flag = 1
            and ps.ps_type = 2
            <if test="psStatementReq.abbName!=null and psStatementReq.abbName!=''">
                and ps.abb_name like concat ('%',#{psStatementReq.abbName},'%')
            </if>
            <if test="psStatementReq.owne!=null and psStatementReq.owne!=''">
                and ps.owne like concat ('%',#{psStatementReq.owne},'%')
            </if>
            <if test="psStatementReq.provinceIds!=null and psStatementReq.provinceIds.size()>0">
                and ps.province_id in
                <foreach collection="psStatementReq.provinceIds" index="index" item="item" open="(" separator=","
                         close=")">
                    #{item}
                </foreach>
            </if>
            <if test="psStatementReq.provinceCompanyCodes!=null and psStatementReq.provinceCompanyCodes.size()>0">
                and ps.province_company_code in
                <foreach collection="psStatementReq.provinceCompanyCodes" index="index" item="item" open="("
                         separator="," close=")">
                    #{item}
                </foreach>
            </if>
        </where>
    </select>
    <select id="getCompanyPsList" resultType="com.gcl.psmis.framework.mbg.entity.TPowerStationEntity">
        SELECT
        a.*
        FROM
        (
        SELECT
        *,
        DATE_FORMAT( DATE_ADD( all_power_time, INTERVAL 1 MONTH ), '%Y-%m-01' ) AS nextMonth
        FROM
        `t_power_station`
        WHERE
        all_power_time IS NOT NULL
        AND ps_type = 2
        AND ar_flag = 1
        AND del_flag = 0
        ) a
        <where>
            <!-- <if test="startTime!=null ">
                 and a.nextMonth &gt;= #{startTime}
             </if>-->
            <if test="endTime!=null ">
                and a.nextMonth &lt; #{endTime}
            </if>
            <if test="provinceCompanyCode!=null and provinceCompanyCode!=''">
                AND a.province_company_code = #{provinceCompanyCode}
            </if>
            <if test="provinceId!=null">
                AND a.province_id = #{provinceId}
            </if>
        </where>

    </select>


</mapper>