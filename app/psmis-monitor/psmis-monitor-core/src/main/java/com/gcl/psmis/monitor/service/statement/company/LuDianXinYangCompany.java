package com.gcl.psmis.monitor.service.statement.company;

import com.gcl.framework.data.dto.UserDTO;
import com.gcl.framework.data.holder.CurrentUserHolder;
import com.gcl.psmis.framework.common.constant.enums.statement.PsStatementRequirementEnum;
import com.gcl.psmis.framework.common.req.statement.StatementLeadReq;
import com.gcl.psmis.framework.common.vo.statement.StatementVO;
import com.gcl.psmis.framework.common.vo.statement.company.LuDianXinYangVO;
import com.gcl.psmis.framework.common.vo.statement.company.WeiHaiXinTengYueVO;
import com.gcl.psmis.framework.export.executor.ExportExecutor;
import com.gcl.psmis.framework.export.task.ExportTask;
import com.gcl.psmis.framework.export.util.poi.MultiSheetExcelTool;
import com.gcl.psmis.framework.gencode.enums.GenRuleCode;
import com.gcl.psmis.framework.gencode.util.GenCodeUtil;
import com.gcl.psmis.framework.mbg.entity.TPsStatementEntity;
import com.gcl.psmis.monitor.service.statement.AllCompanyTemplateStatementService;
import lombok.extern.log4j.Log4j2;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.io.InputStream;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.ArrayList;
import java.util.List;

/**
 * project: LuDianXinYangCompany
 * Powered 2024-06-12 14:39:47
 * 鲁甸鑫阳新能源有限公司
 * <AUTHOR>
 * @version 1.0
 * @since 1.8
 */
@Log4j2
@Service
public class LuDianXinYangCompany implements AllCompanyTemplateStatementService {

    @Autowired
    private GenCodeUtil genCodeUtil;


    @Override
    public List<TPsStatementEntity> getStatementMessage(InputStream inputStream, StatementLeadReq statementLeadReq) {
        log.info("鲁甸鑫阳新能源有限公司开始导入");
        try {
            List<StatementVO> list = new ArrayList<>();
            List<LuDianXinYangVO> vos = MultiSheetExcelTool.ImportBuilder().importExcel(LuDianXinYangVO.class,inputStream);
            //将导入的数据存储起来,以便后续能下载到原版
            ArrayList<StatementVO> statementVOS = new ArrayList<>();
            for (LuDianXinYangVO vo : vos) {
                if (vo.getCompanyName()!=null && !vo.getCompanyName().isEmpty()){
                    StatementVO statementVO = new StatementVO();
                    statementVO.setAccountNumber(vo.getAccountNumber());
                    statementVO.setPowerTime(vo.getPowerTime());
                    statementVO.setElectricQuantity(vo.getElectricQuantity());
                    statementVO.setYesTaxMoney(vo.getYesTaxMoney());
                    statementVO.setNoTaxMoney(vo.getNoTaxMoney());
                    statementVO.setTaxDiff(vo.getTaxDiff());
                    statementVO.setTaxAret(vo.getTaxAret().replace("%", ""));
                    statementVO.setPowerCompanyName(statementLeadReq.getPowerCompany());
                    statementVO.setProjectCompanyName("鲁甸鑫阳新能源有限公司");
                    list.add(statementVO);
                }else {
                    break;
                }
            }
            List<TPsStatementEntity> tPsStatementEntities = getPsStatement(statementVOS);


            return tPsStatementEntities;
        }catch (Exception e) {
            log.error("鲁甸鑫阳新能源有限公司导入错误:"+e.getMessage());
            return null;
        }
    }

    public List<TPsStatementEntity> getPsStatement(List<StatementVO> filteredList) {

        String code = genCodeUtil.getSysCodeByCodeRule(GenRuleCode.STATEMENT_CODE);//同一批结算单导入号

        String statementMonth = FinalStatementCommonService.getStatementMonth();
        List<TPsStatementEntity> list = new ArrayList<>();
        //当前操作人信息
        UserDTO dto = CurrentUserHolder.getUserDTOThreadLocal();

        int index = 1; // 初始化索引变量
        for (StatementVO statementVO : filteredList) {
            TPsStatementEntity statement = new TPsStatementEntity();
            BeanUtils.copyProperties(statementVO,statement);
            
            statement.setStatementMonth(statementMonth);
            statement.setUnitName("千瓦时");//单位
            statement.setItemName("*发电*太阳能发电");//项目名称
            statement.setTaxAret(new BigDecimal(statementVO.getTaxAret()).divide(new BigDecimal(100)));//默认税率13%  小数存库0.13
            statement.setStatementBatch(code);//对应批次结算编号
            statement.setStatementCode(code+String.format("%04d", index));//结算编号
            index++;
            statement.setElectricQuantity(new BigDecimal(statementVO.getElectricQuantity()));
            statement.setProjectCompany(statementVO.getProjectCompanyName());//项目公司code
            statement.setPowerCompany(statementVO.getPowerCompanyName());//发电公司code
            statement.setStatementNo(dto.getEmpNo());//结算人编号
            statement.setStatementMan(dto.getRealName());//结算人名
            statement.setPowerTime(statementVO.getPowerTime().substring(0,4)+"/"+statementVO.getPowerTime().substring(4));//电费年月
            if (new BigDecimal(statementVO.getElectricQuantity()).compareTo(BigDecimal.ZERO) > 0){
                statement.setElectricQuantity(new BigDecimal(statementVO.getElectricQuantity()));//上网电量
                statement.setYesTaxMoney(new BigDecimal(statementVO.getYesTaxMoney()));//含税金额
                statement.setTaxMoney(statement.getYesTaxMoney().divide(statement.getElectricQuantity(),4,RoundingMode.HALF_UP));//含税电价
                statement.setTaxDiff(new BigDecimal(statementVO.getTaxDiff()));//税额
                statement.setNoTaxMoney(new BigDecimal(statementVO.getNoTaxMoney()));//不含税金额
                statement.setNoTaxPrice(statement.getNoTaxMoney().divide(statement.getElectricQuantity(),4, RoundingMode.HALF_UP));//不含税电价
            }else {
                statement.setElectricQuantity(BigDecimal.ZERO);
                statement.setTaxMoney(BigDecimal.ZERO);
                statement.setYesTaxMoney(BigDecimal.ZERO);
                statement.setTaxDiff(BigDecimal.ZERO);
                statement.setNoTaxMoney(BigDecimal.ZERO);
                statement.setNoTaxPrice(BigDecimal.ZERO);
            }

            statement.setInvoiceState(1);//未开票
            list.add(statement);
        }
        return list;
    }

    @Override
    public String getStrategyKey() {
        return "39-650608";
    }//云南电网有限责任公司昭通鲁甸供电局-鲁甸鑫阳新能源有限公司

}
