package com.gcl.psmis.monitor.dao.patrol;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.gcl.psmis.framework.common.req.patol.EquipmentListReq;
import com.gcl.psmis.framework.common.req.patol.EquipmentReq;
import com.gcl.psmis.framework.common.req.patol.RequireReq;
import com.gcl.psmis.framework.common.resp.patrol.EquipmentInspectionResp;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * project: EquipmentInspectionDao
 * Powered 2023-08-23 15:49:06
 *
 * <AUTHOR>
 * @version 1.0
 * @since 1.8
 */
@Mapper
public interface EquipmentInspectionDao {


    //编辑
    void modify(@Param("req") EquipmentReq req);

    //查询列表/详情
    IPage<EquipmentInspectionResp> queryPage(@Param("page") Page<EquipmentInspectionResp> page, @Param("req") EquipmentListReq req);

    //修改状态
    void modifyStatus(@Param("id") Integer id, @Param("status") Integer status);

    //查询检查要求
    List<String> getRequires(@Param("id") Integer id);
}
