package com.gcl.psmis.monitor.dao.inverter;

import com.gcl.psmis.framework.common.resp.inverter.InverterResp;
import com.gcl.psmis.framework.common.resp.inverter.InverterSnVO;
import com.gcl.psmis.framework.common.vo.InverterNumberVO;
import com.gcl.psmis.framework.mbg.entity.TInverterNumberEntity;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * project: InverterDataDao
 * Powered 2023-09-18 10:28:59
 *
 * <AUTHOR>
 * @version 1.0
 * @since 1.8
 */
@Mapper
public interface InverterDataDao {
    List<InverterResp> findInverter();

    InverterNumberVO findInverterNumber(@Param("sn") String sn);

    List<InverterSnVO> getInverterSn(@Param("sn") String sn);

    String findSn(@Param("sn") String sn);

    TInverterNumberEntity findBySn(@Param("sn") String sn);
}
