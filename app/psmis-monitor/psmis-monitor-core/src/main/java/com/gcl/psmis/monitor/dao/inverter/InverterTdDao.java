package com.gcl.psmis.monitor.dao.inverter;

import com.baomidou.dynamic.datasource.annotation.DS;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.gcl.psmis.framework.common.req.inverter.InverterReq;
import com.gcl.psmis.framework.common.resp.dealer.DealerResp;
import com.gcl.psmis.framework.common.resp.inverter.InverterInquireResp;
import com.gcl.psmis.framework.common.resp.inverter.InverterResp;
import com.gcl.psmis.framework.common.vo.InverterBrandVO;
import com.gcl.psmis.framework.common.vo.InverterNumVo;
import com.gcl.psmis.framework.common.vo.InverterNumberVO;
import com.gcl.psmis.framework.common.vo.ProtocolVO;
import com.gcl.psmis.framework.common.vo.sun.SceneVO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.math.BigDecimal;
import java.util.List;

/**
 * project: StatisticsDao
 * Powered 2023-07-19 10:58:08
 *
 * <AUTHOR>
 * @version 1.0
 * @since 1.8
 */
@Mapper
public interface InverterTdDao {

    @DS("td")
    InverterNumberVO findGroupList(@Param("now") String now, @Param("lastHourTime") String lastHourTime, @Param("sn") String sn);

    @DS("td")
    InverterNumberVO findGroupLine(@Param("now") String now, @Param("lastHourTime") String lastHourTime, @Param("sn") String sn);

    @DS("td")
    InverterNumberVO findMpptList(@Param("now") String now, @Param("lastHourTime") String lastHourTime, @Param("sn") String sn);

    @DS("td")
    InverterNumberVO findMpptLine(@Param("now") String now, @Param("lastHourTime") String lastHourTime, @Param("sn") String sn);
}
