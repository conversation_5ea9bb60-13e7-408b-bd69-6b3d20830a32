package com.gcl.psmis.monitor.dao.patrol;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.gcl.psmis.framework.common.req.patrol.PatrolProjectReq;
import com.gcl.psmis.framework.common.resp.patrol.PatrolProjectResp;
import com.gcl.psmis.framework.common.vo.patrol.PatrolPlanOrderVO;
import com.gcl.psmis.framework.common.vo.patrol.PatrolPlanVo;
import com.gcl.psmis.framework.common.vo.patrol.PatrolProjectVO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.Date;
import java.util.List;

/**
 * project: PatrolProjectDao
 * Powered 2023-08-30 14:10:16
 *
 * <AUTHOR>
 * @version 1.0
 * @since 1.8
 */
@Mapper
public interface PatrolProjectDao {
    IPage<PatrolProjectResp> getProjectInquire(@Param("patrolProjectReq") PatrolProjectReq patrolProjectReq, @Param("page") Page<PatrolProjectResp> page);

    Integer updateState(@Param("id") Long id, @Param("enable") Integer enable, @Param("updateUserId") String updateUserId, @Param("updateUserName") String updateUserName, @Param("updateTime") Date updateTime);

    PatrolProjectResp getProjectDetail(@Param("id") Long id);

    Integer updateProject(@Param("id") Long id, @Param("remark") String remark);

    void addProject(PatrolProjectVO patrolProjectVO);

    List<PatrolPlanVo> findPatrolPlanList(@Param("serviceCompanyCode") String serviceCompanyCode);

    void addInspectionOrder(PatrolProjectDao patrolProjectDao);

    List<Long> findPs(@Param("qisScenariosId") Long qisScenariosId);

    Long findInspectionOrder();

    List<Long> findDevice(@Param("psId") Long psId, @Param("qisScenariosId") Long qisScenariosId );


    List<PatrolPlanOrderVO> findExamine(@Param("deviceIds") List<Long> deviceIds, @Param("psType") Integer psType);

    Integer findPsType(@Param("qisScenariosId") Long qisScenariosId);

    Integer updatePatrolPlan(@Param("qisScenariosId") Long qisScenariosId);

    Integer findPlanId(@Param("id") Long id);

    Integer findPlanNum(@Param("planId") Integer planId);

    void updateStatePlan(@Param("planId") Integer planId);

    void updatePlan(@Param("qisScenariosId") Long qisScenariosId);

    Integer findPlanEnable(@Param("id") Long id);
}
