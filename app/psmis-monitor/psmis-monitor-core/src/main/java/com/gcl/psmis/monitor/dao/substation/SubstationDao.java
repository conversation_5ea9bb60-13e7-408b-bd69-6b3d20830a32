package com.gcl.psmis.monitor.dao.substation;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.gcl.psmis.framework.common.req.substaion.SubstationReq;
import com.gcl.psmis.framework.common.resp.substation.SubArea;
import com.gcl.psmis.framework.common.resp.substation.SubstationBaseResp;
import com.gcl.psmis.framework.common.resp.substation.SubstationResp;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <AUTHOR>
 * @ClassName SubstationDao
 * @description: TODO
 * @date 2024年06月26日
 * @version: 1.0
 */
@Mapper
public interface SubstationDao {


    IPage<SubstationResp> subStationList(@Param("param") Page<SubstationResp> page,@Param("req") SubstationReq substationReq);

    List<String> getAddressByRegionId(@Param("req") List<Long> regionIds);

    List<SubArea> getSubstationAddress(@Param("param")String code);
}
