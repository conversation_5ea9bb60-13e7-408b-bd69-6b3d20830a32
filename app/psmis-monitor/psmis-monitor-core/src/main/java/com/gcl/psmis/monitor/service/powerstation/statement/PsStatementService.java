package com.gcl.psmis.monitor.service.powerstation.statement;

import cn.hutool.core.collection.CollStreamUtil;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.date.DateField;
import cn.hutool.core.date.DateTime;
import cn.hutool.core.date.DateUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.gcl.psmis.framework.common.exception.BussinessException;
import com.gcl.psmis.framework.common.req.statement.PsStatementReq;
import com.gcl.psmis.framework.common.resp.statement.ProvinceStatementResp;
import com.gcl.psmis.framework.common.resp.statement.PsStatementResp;
import com.gcl.psmis.framework.mbg.entity.TPowerStationEntity;
import com.gcl.psmis.framework.mbg.entity.TSceneParameterEntity;
import com.gcl.psmis.framework.mbg.service.TPowerStationService;
import com.gcl.psmis.framework.mbg.service.TSceneParameterService;
import com.gcl.psmis.framework.taos.domain.entity.PsIotDayStInverterEntity;
import com.gcl.psmis.framework.taos.wrapper.TdWrappers;
import com.gcl.psmis.monitor.dao.powerstation.statement.PsStatementDao;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.*;
import java.util.stream.Collectors;

import static java.math.BigDecimal.valueOf;

/**
 * project: PsStatementService
 * Powered 2024-03-06 15:11:44
 *
 * <AUTHOR>
 * @version 1.0
 * @since 1.8
 */
@Service
@Slf4j
public class PsStatementService {

    @Autowired
    private PsStatementDao psStatementDao;

    @Autowired
    private TPowerStationService tPowerStationService;

    @Autowired
    private TSceneParameterService sceneParameterService;

    public IPage<PsStatementResp> psList(PsStatementReq psStatementReq) {
        Date now = new Date();
        if (DateUtil.dayOfMonth(now) == 1) {
            throw new BussinessException("只能统计到昨天  今天为1号无法统计该月");
        }
        Page<PsStatementResp> page = new Page<>(psStatementReq.getPageNum(), psStatementReq.getPageSize());
        //得到查询年份
        psStatementReq.setPickYear(DateUtil.year(psStatementReq.getPsTime()));
        //得到查询月份
        psStatementReq.setPickMonth(DateUtil.month(psStatementReq.getPsTime()) + 1);
        IPage<PsStatementResp> iPage = psStatementDao.psList(page, psStatementReq);
        List<Long> ids = new ArrayList<>();
        if (iPage.getRecords() == null || iPage.getRecords().size() < 1) {
            return iPage;
        }
        int year = DateUtil.year(psStatementReq.getPsTime());
        psStatementReq.setPickYear(year);
        //计算出月份   用来匹配电站对应月份的理论小时数
        int month = DateUtil.month(psStatementReq.getPsTime()) + 1;
        Double ratio = 1.0;
        if (DateUtil.isSameMonth(psStatementReq.getPsTime(), now)) {
            int dayOfMonth = DateUtil.dayOfMonth(now) - 1;// 获取当前是几号并减一   只统计到昨天
            int days = DateUtil.lengthOfMonth(month, DateUtil.isLeapYear(year));// 获取当前月有多少天
            ratio = (double) dayOfMonth / (double) days;
        }
        for (PsStatementResp record : iPage.getRecords()) {
            if (record.getHourBudget() != null) {
                record.setHourBudget(record.getHourBudget().multiply(new BigDecimal(ratio)));
            }
            if (record.getPowerBudget() != null) {
                record.setPowerBudget(record.getPowerBudget().multiply(new BigDecimal(ratio)));
            }
            if (record.getHourBudget() != null && record.getPsCapacity() != null) {
                record.setPowerBudget(record.getHourBudget().multiply(record.getPsCapacity()).divide(new BigDecimal(10), 2, RoundingMode.HALF_UP));
            }
            ids.add(record.getId());
        }
        Date lastTime = DateUtil.offsetMonth(psStatementReq.getPsTime(), 1);
        List<PsIotDayStInverterEntity> entityList = TdWrappers.lambdaQuery(PsIotDayStInverterEntity.class)
                .in(PsIotDayStInverterEntity::getPsId, ids)
                .ge(PsIotDayStInverterEntity::getTs, DateUtil.format(psStatementReq.getPsTime(), "yyyy-MM-dd HH:mm:ss"))
                .lt(PsIotDayStInverterEntity::getTs, DateUtil.format(lastTime, "yyyy-MM-dd HH:mm:ss"))
                .groupBy(PsIotDayStInverterEntity::getPsId);
        if (entityList == null) {
            return iPage;
        } else {
            for (PsStatementResp record : iPage.getRecords()) {
                for (PsIotDayStInverterEntity entity : entityList) {
                    if (String.valueOf(record.getId()).equals(entity.getPsId())) {
                        record.setPowerPractical(new BigDecimal(entity.getEtd()).divide(new BigDecimal(10000), 2, RoundingMode.HALF_UP));
                        if (new BigDecimal(entity.getEtd()).compareTo(BigDecimal.ZERO) > 0
                                && record.getPsCapacity().compareTo(BigDecimal.ZERO) > 0) {
                            record.setHourPractical(new BigDecimal(entity.getEtd()).multiply(new BigDecimal(0.001)).divide(record.getPsCapacity(), 2, RoundingMode.HALF_UP));
                        }
                        if (record.getHourPractical() != null && record.getHourPractical().compareTo(BigDecimal.ZERO) > 0
                                && record.getHourBudget() != null && record.getHourBudget().compareTo(BigDecimal.ZERO) > 0) {
                            record.setPercentage(record.getHourPractical().multiply(valueOf(100)).divide(record.getHourBudget(), 2, RoundingMode.HALF_UP));
                        }
                        break;
                    }
                }
            }
            return iPage;
        }
    }

    public IPage<PsStatementResp> psList2(PsStatementReq psStatementReq) {
        Date now = new Date();
        if (DateUtil.dayOfMonth(now) == 1) {
            throw new BussinessException("只能统计到昨天  今天为1号无法统计该月");
        }
        Page<PsStatementResp> page = new Page<>(psStatementReq.getPageNum(), psStatementReq.getPageSize());
        // 查询当前月份时间
        DateTime startTime = DateUtil.beginOfMonth(psStatementReq.getPsTime());
        DateTime endTime = DateUtil.endOfMonth(psStatementReq.getPsTime());
        psStatementReq.setStartTime(startTime);
        psStatementReq.setEndTime(endTime);
        // 工商业理论小时数查询
        List<TSceneParameterEntity> list = sceneParameterService.list(new LambdaQueryWrapper<TSceneParameterEntity>()
                .eq(TSceneParameterEntity::getPsType, 2)
                .ge(TSceneParameterEntity::getSceneMonth, startTime)
                .le(TSceneParameterEntity::getSceneMonth, endTime));
        // key :ps_code  value:月份对应理论小数据
        Map<String, BigDecimal> sceneMap = new HashMap<>();
        if (CollectionUtil.isNotEmpty(list)) {
            sceneMap = CollStreamUtil.toMap(list, TSceneParameterEntity::getPsCode, TSceneParameterEntity::getSceneEle);
        }
        IPage<PsStatementResp> iPage = psStatementDao.psList2(page, psStatementReq);
        if (CollectionUtil.isEmpty(iPage.getRecords())) {
            return iPage;
        }
        // 查询电站对应发电量
        List<Long> ids = iPage.getRecords().stream().map(s -> s.getId()).collect(Collectors.toList());
        Date lastTime = DateUtil.offsetMonth(psStatementReq.getPsTime(), 1);
        List<PsIotDayStInverterEntity> entityList = TdWrappers.lambdaQuery(PsIotDayStInverterEntity.class)
                .in(PsIotDayStInverterEntity::getPsId, ids)
                .ge(PsIotDayStInverterEntity::getTs, DateUtil.format(psStatementReq.getPsTime(), "yyyy-MM-dd HH:mm:ss"))
                .lt(PsIotDayStInverterEntity::getTs, DateUtil.format(lastTime, "yyyy-MM-dd HH:mm:ss"))
                .groupBy(PsIotDayStInverterEntity::getPsId);
        // key:psId value:etd
        Map<String, String> psEtdMap = new HashMap<>();
        if (CollectionUtil.isNotEmpty(entityList)) {
            psEtdMap = CollStreamUtil.toMap(entityList, PsIotDayStInverterEntity::getPsId, PsIotDayStInverterEntity::getEtd);
        }
        BigDecimal ratio = BigDecimal.ONE;
        // 当前月拆分到天
        if (DateUtil.isSameMonth(psStatementReq.getPsTime(), now)) {
            int dayOfMonth = DateUtil.dayOfMonth(now) - 1;// 获取当前是几号并减一   只统计到昨天
            Integer day = DateUtil.lengthOfMonth(DateUtil.month(psStatementReq.getPsTime()) + 1, DateUtil.isLeapYear(DateUtil.year(psStatementReq.getPsTime())));
            ratio = new BigDecimal(dayOfMonth).divide(new BigDecimal(day), 4, BigDecimal.ROUND_HALF_UP);
        }
        for (PsStatementResp record : iPage.getRecords()) {
            // 理论小时数
            BigDecimal hourBudget = sceneMap.get(record.getPsCode());
            if (hourBudget != null) {
                record.setHourBudget(hourBudget);
                record.setHourBudget(hourBudget.multiply(ratio));
            }
            if (record.getPowerBudget() != null) {
                record.setPowerBudget(record.getPowerBudget().multiply(ratio));
            }
            if (record.getHourBudget() != null && record.getPsCapacity() != null) {
                record.setPowerBudget(record.getHourBudget().multiply(record.getPsCapacity()).divide(new BigDecimal(10), 2, RoundingMode.HALF_UP));
            }
            // 电站对应发电量
            String etd = psEtdMap.get(record.getId().toString());
            if (StringUtils.isNotBlank(etd)) {
                record.setPowerPractical(new BigDecimal(etd).divide(new BigDecimal(10000), 2, RoundingMode.HALF_UP));
                if (new BigDecimal(etd).compareTo(BigDecimal.ZERO) > 0
                        && record.getPsCapacity().compareTo(BigDecimal.ZERO) > 0) {
                    record.setHourPractical(new BigDecimal(etd).multiply(new BigDecimal(0.001)).divide(record.getPsCapacity(), 2, RoundingMode.HALF_UP));
                }
            }
            if (record.getHourPractical() != null && record.getHourPractical().compareTo(BigDecimal.ZERO) > 0
                    && record.getHourBudget() != null && record.getHourBudget().compareTo(BigDecimal.ZERO) > 0) {
                record.setPercentage(record.getHourPractical().multiply(valueOf(100)).divide(record.getHourBudget(), 2, RoundingMode.HALF_UP));
            }
        }
        return iPage;
       /* if (entityList == null) {
            return iPage;
        } else {
            for (PsStatementResp record : iPage.getRecords()) {
                for (PsIotDayStInverterEntity entity : entityList) {
                    if (String.valueOf(record.getId()).equals(entity.getPsId())) {
                        record.setPowerPractical(new BigDecimal(entity.getEtd()).divide(new BigDecimal(10000), 2, RoundingMode.HALF_UP));
                        if (new BigDecimal(entity.getEtd()).compareTo(BigDecimal.ZERO) > 0
                                && record.getPsCapacity().compareTo(BigDecimal.ZERO) > 0) {
                            record.setHourPractical(new BigDecimal(entity.getEtd()).multiply(new BigDecimal(0.001)).divide(record.getPsCapacity(), 2, RoundingMode.HALF_UP));
                        }
                        if (record.getHourPractical() != null && record.getHourPractical().compareTo(BigDecimal.ZERO) > 0
                                && record.getHourBudget() != null && record.getHourBudget().compareTo(BigDecimal.ZERO) > 0) {
                            record.setPercentage(record.getHourPractical().multiply(valueOf(100)).divide(record.getHourBudget(), 2, RoundingMode.HALF_UP));
                        }
                        break;
                    }
                }
            }
            return iPage;
        }*/
    }


    private static final Map<Integer, String> monthMap = new HashMap<>();
    private static final Map<Integer, String> yearMap = new HashMap<>();
    private static final Map<Integer, String> yearMapByPs = new HashMap<>();

    static {
        monthMap.put(1, "jan");
        monthMap.put(2, "feb");
        monthMap.put(3, "mar");
        monthMap.put(4, "apr");
        monthMap.put(5, "may");
        monthMap.put(6, "jun");
        monthMap.put(7, "jul");
        monthMap.put(8, "aug");
        monthMap.put(9, "sep");
        monthMap.put(10, "oct");
        monthMap.put(11, "nov");
        monthMap.put(12, "dece");
    }

    static {
        yearMap.put(2, "jan");
        yearMap.put(3, "jan + feb");
        yearMap.put(4, "jan + feb + mar");
        yearMap.put(5, "jan + feb + mar + apr");
        yearMap.put(6, "jan + feb + mar + apr + may");
        yearMap.put(7, "jan + feb + mar + apr + may + jun");
        yearMap.put(8, "jan + feb + mar + apr + may + jun + jul ");
        yearMap.put(9, "jan + feb + mar + apr + may + jun + jul + aug");
        yearMap.put(10, "jan + feb + mar + apr + may + jun + jul + aug + sep");
        yearMap.put(11, "jan + feb + mar + apr + may + jun + jul + aug + sep + oct");
        yearMap.put(12, "jan + feb + mar + apr + may + jun + jul + aug + sep + oct + nov");
        yearMap.put(13, "jan + feb + mar + apr + may + jun + jul + aug + sep + oct + nov + dece");
    }

    static {
        yearMapByPs.put(12, "dece");
        yearMapByPs.put(11, "nov + dece");
        yearMapByPs.put(10, "oct + nov + dece");
        yearMapByPs.put(9, "sep + oct + nov + dece");
        yearMapByPs.put(8, "aug + sep + oct + nov + dece");
        yearMapByPs.put(7, "jul + aug + sep + oct + nov + dece");
        yearMapByPs.put(6, "jun + jul + aug + sep + oct + nov + dece");
        yearMapByPs.put(5, "may + jun + jul + aug + sep + oct + nov + dece");
        yearMapByPs.put(4, "apr + may + jun + jul + aug + sep + oct + nov + dece");
        yearMapByPs.put(3, "mar + apr + may + jun + jul + aug + sep + oct + nov + dece");
        yearMapByPs.put(2, "feb + mar + apr + may + jun + jul + aug + sep + oct + nov + dece");
        yearMapByPs.put(1, "jan + feb + mar + apr + may + jun + jul + aug + sep + oct + nov + dece");
    }

    public static String getMonth(int month) {
        return monthMap.getOrDefault(month, "Invalid month");
    }

    public static String getYear(int month) {
        if (month == 13) {
            return yearMap.getOrDefault(month, "Invalid year");
        }
        String invalidYear = yearMap.getOrDefault(month, "Invalid year");
        String invalidMonth = monthMap.getOrDefault(month, "Invalid month");
        return invalidYear + "+" + invalidMonth;
    }

    public static String getYearByPs(int month) {
        if (month > 12) {
            month = 12;
        }
        return yearMapByPs.getOrDefault(month, "Invalid year");
    }

    public static String getTime(String s1, String s2) {
        Set<String> set1 = new HashSet<>(Arrays.asList(s1.split(" \\+ ")));
        Set<String> set2 = new HashSet<>(Arrays.asList(s2.split(" \\+ ")));
        // 找到两个集合的交集
        set1.retainAll(set2);
        // 如果交集不为空，则输出交集中的元素
        if (!set1.isEmpty()) {
            return set1.iterator().next();
        } else {
            return null;
        }
    }


    public IPage<ProvinceStatementResp> provinceList(PsStatementReq psStatementReq) {
        Page<ProvinceStatementResp> page = new Page<>(psStatementReq.getPageNum(), psStatementReq.getPageSize());
        //计算出年份   用来匹配电站对应年份的理论小时数
        int year = DateUtil.year(psStatementReq.getPsTime());
        psStatementReq.setPickYear(year);
        //计算出月份   用来匹配电站对应月份的理论小时数
        int month = DateUtil.month(psStatementReq.getPsTime()) + 1;
        String s = getMonth(month);
        //查出省份
        IPage<ProvinceStatementResp> iPage = psStatementDao.provinceList(page, psStatementReq);
        String psTime = DateUtil.format(psStatementReq.getPsTime(), "yyyy/MM/dd HH:mm:ss");
        Date date = DateUtil.date();
        int dayOfMonth = DateUtil.dayOfMonth(date) - 1;// 获取当前是几号并减一   只统计到昨天
        int days = DateUtil.lengthOfMonth(month, DateUtil.isLeapYear(year));// 获取当前月有多少天
        BigDecimal ratio = new BigDecimal(1);
        if (new BigDecimal(dayOfMonth).compareTo(BigDecimal.ZERO) > 0
                && new BigDecimal(days).compareTo(BigDecimal.ZERO) > 0) {
            ratio = new BigDecimal(dayOfMonth).divide(new BigDecimal(days), 4, RoundingMode.HALF_UP);
        }
        for (ProvinceStatementResp record : iPage.getRecords()) {
            ProvinceStatementResp resp;
            Date now = new Date();
            if (DateUtil.isSameMonth(psStatementReq.getPsTime(), now)) {
                if (DateUtil.dayOfMonth(now) == 1) {
                    throw new BussinessException("只能统计到昨天  今天为1号无法统计该月");
                }
                resp = psStatementDao.findProvince(record.getProvinceId(), psTime, psStatementReq.getPickYear(), s);
                if (resp.getHourBudget() != null) {
                    resp.setHourBudget(resp.getHourBudget().multiply(ratio));
                }
                if (resp.getPowerBudget() != null) {
                    resp.setPowerBudget(resp.getPowerBudget().multiply(ratio));
                }
            } else {
                resp = psStatementDao.findProvince(record.getProvinceId(), psTime, psStatementReq.getPickYear(), s);
            }
            //省下的电站装机容量总和
            //Double allCapacity = psStatementDao.findProvinceCapacity(record.getProvinceId(),DateUtil.format(psStatementReq.getPsTime(),"yyyy-MM-dd HH:mm:ss"));
            if (resp != null && resp.getPsCapacity() != null) {
                record.setPsCapacity(resp.getPsCapacity());
            }
            List<Long> ids = null;
            if (resp != null) {
                if (resp.getHourBudget() != null) {
                    record.setHourBudget(resp.getHourBudget());
                }
                if (resp.getPowerBudget() != null) {
                    record.setPowerBudget(resp.getPowerBudget());
                }
                Date lastTime = DateUtil.offsetMonth(psStatementReq.getPsTime(), 1);
                if (resp.getPsIds() != null) {
                    ids = Arrays.stream(resp.getPsIds().split(","))
                            .map(Long::parseLong)
                            .collect(Collectors.toList());
                    List<PsIotDayStInverterEntity> entityList = TdWrappers.lambdaQuery(PsIotDayStInverterEntity.class)
                            .in(PsIotDayStInverterEntity::getPsId, ids)
                            .ge(PsIotDayStInverterEntity::getTs, DateUtil.format(psStatementReq.getPsTime(), "yyyy-MM-dd HH:mm:ss"))
                            .lt(PsIotDayStInverterEntity::getTs, DateUtil.format(lastTime, "yyyy-MM-dd HH:mm:ss"))
                            .groupBy(PsIotDayStInverterEntity::getPsId);
                    BigDecimal eto = BigDecimal.valueOf(0.0);
                    BigDecimal hto = BigDecimal.valueOf(0.0);
                    if (entityList != null) {
                        for (PsIotDayStInverterEntity entity : entityList) {
                            if (entity != null && entity.getEtd() != null) {
                                eto = new BigDecimal(entity.getEtd()).add(eto);
                                if (new BigDecimal(entity.getEtd()).compareTo(BigDecimal.ZERO) > 0
                                        && record.getPsCapacity().compareTo(BigDecimal.ZERO) > 0) {
                                    hto = new BigDecimal(entity.getEtd()).divide(record.getPsCapacity(), 2, RoundingMode.HALF_UP).add(hto);
                                }
                            }
                        }
                        record.setPowerPractical(eto.divide(new BigDecimal(10000), 2, RoundingMode.HALF_UP));
                        record.setHourPractical(hto.multiply(new BigDecimal(0.001)));
                        if (record.getHourPractical() != null && record.getHourBudget() != null
                                && record.getHourPractical().compareTo(BigDecimal.ZERO) != 0
                                && record.getHourBudget().compareTo(BigDecimal.ZERO) != 0) {
                            record.setPercentage(record.getHourPractical().multiply(valueOf(100)).divide(record.getHourBudget(), 2, RoundingMode.HALF_UP));
                        }
                    }
                }
            }
        }
        return iPage;
    }

    public IPage<ProvinceStatementResp> provinceList2(PsStatementReq psStatementReq) {
        Page<ProvinceStatementResp> page = new Page<>(psStatementReq.getPageNum(), psStatementReq.getPageSize());
        //查出省份
        IPage<ProvinceStatementResp> iPage = psStatementDao.provinceList(page, psStatementReq);
        DateTime startTime = DateUtil.beginOfMonth(psStatementReq.getPsTime());
        DateTime endTime = DateUtil.endOfMonth(psStatementReq.getPsTime());
        psStatementReq.setStartTime(startTime);
        psStatementReq.setEndTime(endTime);
        for (ProvinceStatementResp record : iPage.getRecords()) {
            psStatementReq.setProvinceId(record.getProvinceId());
            // 查询当前省份下所有电站统计
            ProvinceStatementResp resp = psStatementDao.findProvince2(psStatementReq);
            //省下的电站装机容量总和
            //Double allCapacity = psStatementDao.findProvinceCapacity(record.getProvinceId(),DateUtil.format(psStatementReq.getPsTime(),"yyyy-MM-dd HH:mm:ss"));
            if (resp != null && resp.getPsCapacity() != null) {
                record.setPsCapacity(resp.getPsCapacity());
            }
            List<Long> ids = null;
            if (resp != null) {
                if (resp.getHourBudget() != null) {
                    record.setHourBudget(resp.getHourBudget());
                }
                if (resp.getPowerBudget() != null) {
                    record.setPowerBudget(resp.getPowerBudget());
                }
                Date lastTime = DateUtil.offsetMonth(psStatementReq.getPsTime(), 1);
                if (resp.getPsIds() != null) {
                    ids = Arrays.stream(resp.getPsIds().split(","))
                            .map(Long::parseLong)
                            .collect(Collectors.toList());
                    List<PsIotDayStInverterEntity> entityList = TdWrappers.lambdaQuery(PsIotDayStInverterEntity.class)
                            .in(PsIotDayStInverterEntity::getPsId, ids)
                            .ge(PsIotDayStInverterEntity::getTs, DateUtil.format(psStatementReq.getPsTime(), "yyyy-MM-dd HH:mm:ss"))
                            .lt(PsIotDayStInverterEntity::getTs, DateUtil.format(lastTime, "yyyy-MM-dd HH:mm:ss"))
                            .groupBy(PsIotDayStInverterEntity::getPsId);
                    BigDecimal eto = BigDecimal.ZERO;
                    BigDecimal hto = BigDecimal.ZERO;
                    if (entityList != null) {
                        for (PsIotDayStInverterEntity entity : entityList) {
                            if (entity != null && entity.getEtd() != null) {
                                eto = new BigDecimal(entity.getEtd()).add(eto);
                                if (new BigDecimal(entity.getEtd()).compareTo(BigDecimal.ZERO) > 0
                                        && record.getPsCapacity().compareTo(BigDecimal.ZERO) > 0) {
                                    hto = new BigDecimal(entity.getEtd()).divide(record.getPsCapacity(), 10, RoundingMode.HALF_UP).add(hto);
                                }
                            }
                        }
                        record.setPowerPractical(eto.divide(new BigDecimal(10000), 2, RoundingMode.HALF_UP));
                        record.setHourPractical(hto.multiply(new BigDecimal(0.001)));
                        if (record.getHourPractical() != null && record.getHourBudget() != null
                                && record.getHourPractical().compareTo(BigDecimal.ZERO) != 0
                                && record.getHourBudget().compareTo(BigDecimal.ZERO) != 0) {
                            record.setPercentage(record.getHourPractical().multiply(valueOf(100)).divide(record.getHourBudget(), 2, RoundingMode.HALF_UP));
                        }
                    }
                }
            }
        }
        return iPage;
    }

    public IPage<ProvinceStatementResp> companyList(PsStatementReq psStatementReq) {
        Page<ProvinceStatementResp> page = new Page<>(psStatementReq.getPageNum(), psStatementReq.getPageSize());
        //计算出年份   用来匹配电站对应年份的理论小时数
        int year = DateUtil.year(psStatementReq.getPsTime());
        psStatementReq.setPickYear(year);
        //计算出月份   用来匹配电站对应月份的理论小时数
        int month = DateUtil.month(psStatementReq.getPsTime()) + 1;
        String s = getMonth(month);
        IPage<ProvinceStatementResp> iPage = psStatementDao.companyList(page, psStatementReq);
        String psTime = DateUtil.format(psStatementReq.getPsTime(), "yyyy/MM/dd HH:mm:ss");
        for (ProvinceStatementResp record : iPage.getRecords()) {
            ProvinceStatementResp resp;
            Date now = new Date();
            //判断是否是当前月   是的话需要算出几号,然后判断当前
            if (DateUtil.isSameMonth(psStatementReq.getPsTime(), now)) {
                if (DateUtil.dayOfMonth(now) == 1) {
                    throw new BussinessException("只能统计到昨天  今天为1号无法统计该月");
                }
                resp = psStatementDao.findProvince(record.getProvinceId(), psTime, psStatementReq.getPickYear(), s);
                Date date = DateUtil.date();
                int dayOfMonth = DateUtil.dayOfMonth(date) - 1;// 获取当前是几号并减一   只统计到昨天
                int days = DateUtil.lengthOfMonth(month, DateUtil.isLeapYear(year));// 获取当前月有多少天
                BigDecimal ratio = new BigDecimal(1);
                if (new BigDecimal(dayOfMonth).compareTo(BigDecimal.ZERO) > 0
                        && new BigDecimal(days).compareTo(BigDecimal.ZERO) > 0) {
                    ratio = new BigDecimal(dayOfMonth).divide(new BigDecimal(days), 4, RoundingMode.HALF_UP);
                }
                if (resp.getHourBudget() != null) {
                    resp.setHourBudget(resp.getHourBudget().multiply(ratio));
                }
                if (resp.getPowerBudget() != null) {
                    resp.setPowerBudget(resp.getPowerBudget().multiply(ratio));
                }
            } else {
                resp = psStatementDao.findProvince(record.getProvinceId(), psTime, psStatementReq.getPickYear(), s);
            }
            //省下的电站装机容量总和
            //Double allCapacity = psStatementDao.findProvinceCapacity(record.getProvinceId(),DateUtil.format(psStatementReq.getPsTime(),"yyyy-MM-dd HH:mm:ss"));
            if (resp != null && resp.getPsCapacity() != null) {
                record.setPsCapacity(resp.getPsCapacity());
            }
            List<Long> ids = null;
            if (resp != null) {
                if (resp.getHourBudget() != null) {
                    record.setHourBudget(resp.getHourBudget());
                }
                if (resp.getPowerBudget() != null) {
                    record.setPowerBudget(resp.getPowerBudget());
                }
                Date lastTime = DateUtil.offsetMonth(psStatementReq.getPsTime(), 1);
                if (resp.getPsIds() != null) {
                    ids = Arrays.stream(resp.getPsIds().split(","))
                            .map(Long::parseLong)
                            .collect(Collectors.toList());
                    List<PsIotDayStInverterEntity> entityList = TdWrappers.lambdaQuery(PsIotDayStInverterEntity.class)
                            .in(PsIotDayStInverterEntity::getPsId, ids)
                            .ge(PsIotDayStInverterEntity::getTs, DateUtil.format(psStatementReq.getPsTime(), "yyyy-MM-dd HH:mm:ss"))
                            .lt(PsIotDayStInverterEntity::getTs, DateUtil.format(lastTime, "yyyy-MM-dd HH:mm:ss"))
                            .groupBy(PsIotDayStInverterEntity::getPsId);
                    BigDecimal eto = BigDecimal.valueOf(0.0);
                    BigDecimal hto = BigDecimal.valueOf(0.0);
                    if (entityList != null) {
                        for (PsIotDayStInverterEntity entity : entityList) {
                            if (entity != null && entity.getEtd() != null) {
                                eto = new BigDecimal(entity.getEtd()).add(eto);
                                if (new BigDecimal(entity.getEtd()).compareTo(BigDecimal.ZERO) > 0 &&
                                        record.getPsCapacity() != null && record.getPsCapacity().compareTo(BigDecimal.ZERO) > 0) {
                                    hto = new BigDecimal(entity.getEtd()).divide(record.getPsCapacity(), 2, RoundingMode.HALF_UP).add(hto);
                                }
                            }
                        }
                        record.setPowerPractical(eto.divide(new BigDecimal(10000), 2, RoundingMode.HALF_UP));
                        record.setHourPractical(hto.multiply(new BigDecimal(0.001)));
                        if (record.getHourPractical() != null && record.getHourBudget() != null
                                && record.getHourPractical().compareTo(BigDecimal.ZERO) != 0
                                && record.getHourBudget().compareTo(BigDecimal.ZERO) != 0) {
                            record.setPercentage(record.getHourPractical().multiply(valueOf(100)).divide(record.getHourBudget(), 2, RoundingMode.HALF_UP));
                        }
                    }
                }
            }
        }
        return iPage;
    }

    public IPage<ProvinceStatementResp> companyList2(PsStatementReq psStatementReq) {
        Page<ProvinceStatementResp> page = new Page<>(psStatementReq.getPageNum(), psStatementReq.getPageSize());
        // 获取不同省份
        IPage<ProvinceStatementResp> iPage = psStatementDao.companyList(page, psStatementReq);

        DateTime startTime = DateUtil.beginOfMonth(psStatementReq.getPsTime());
        DateTime endTime = DateUtil.endOfMonth(psStatementReq.getPsTime());
        psStatementReq.setStartTime(startTime);
        psStatementReq.setEndTime(endTime);
        for (ProvinceStatementResp record : iPage.getRecords()) {
            psStatementReq.setProvinceId(record.getProvinceId());
            psStatementReq.setProvinceCompanyCode(record.getProvinceCompanyCode());
            ProvinceStatementResp resp = psStatementDao.findProvince2(psStatementReq);
            //省下的电站装机容量总和
            BigDecimal allCapacity = psStatementDao.findProvinceCapacity(record.getProvinceId(),DateUtil.format(psStatementReq.getPsTime(),"yyyy-MM-dd HH:mm:ss"),psStatementReq.getProvinceCompanyCode());
            if (resp != null && resp.getPsCapacity() != null) {
                record.setPsCapacity(allCapacity);
            }
            List<Long> ids = null;
            if (resp != null) {
                if (resp.getHourBudget() != null) {
                    record.setHourBudget(resp.getHourBudget());
                }
                if (resp.getPowerBudget() != null) {
                    record.setPowerBudget(resp.getPowerBudget());
                }
                Date lastTime = DateUtil.offsetMonth(psStatementReq.getPsTime(), 1);
                if (resp.getPsIds() != null) {
                    ids = Arrays.stream(resp.getPsIds().split(","))
                            .map(Long::parseLong)
                            .collect(Collectors.toList());
                    List<PsIotDayStInverterEntity> entityList = TdWrappers.lambdaQuery(PsIotDayStInverterEntity.class)
                            .in(PsIotDayStInverterEntity::getPsId, ids)
                            .ge(PsIotDayStInverterEntity::getTs, DateUtil.format(psStatementReq.getPsTime(), "yyyy-MM-dd HH:mm:ss"))
                            .lt(PsIotDayStInverterEntity::getTs, DateUtil.format(lastTime, "yyyy-MM-dd HH:mm:ss"))
                            .groupBy(PsIotDayStInverterEntity::getPsId);
                    BigDecimal eto = BigDecimal.valueOf(0.0);
                    BigDecimal hto = BigDecimal.valueOf(0.0);
                    if (entityList != null) {
                        for (PsIotDayStInverterEntity entity : entityList) {
                            if (entity != null && entity.getEtd() != null) {
                                eto = new BigDecimal(entity.getEtd()).add(eto);
                                if (new BigDecimal(entity.getEtd()).compareTo(BigDecimal.ZERO) > 0 &&
                                        record.getPsCapacity() != null && record.getPsCapacity().compareTo(BigDecimal.ZERO) > 0) {
                                    hto = new BigDecimal(entity.getEtd()).divide(record.getPsCapacity(), 2, RoundingMode.HALF_UP).add(hto);
                                }
                            }
                        }
                        record.setPowerPractical(eto.divide(new BigDecimal(10000), 2, RoundingMode.HALF_UP));
                        record.setHourPractical(hto.multiply(new BigDecimal(0.001)));
                        if (record.getHourPractical() != null && record.getHourBudget() != null
                                && record.getHourPractical().compareTo(BigDecimal.ZERO) != 0
                                && record.getHourBudget().compareTo(BigDecimal.ZERO) != 0) {
                            record.setPercentage(record.getHourPractical().multiply(valueOf(100)).divide(record.getHourBudget(), 2, RoundingMode.HALF_UP));
                        }
                    }
                }
            }
        }
        return iPage;
    }

    public IPage<PsStatementResp> psYearList(PsStatementReq psStatementReq) {
        Page<PsStatementResp> page = new Page<>(psStatementReq.getPageNum(), psStatementReq.getPageSize());
        psStatementReq.setPickYear(DateUtil.year(psStatementReq.getPsTime()));
        psStatementReq.setPickMonth(DateUtil.month(psStatementReq.getPsTime()) + 1);
        //判断报表年份是否是本年
        int year = DateUtil.year(psStatementReq.getPsTime());
        int month = DateUtil.month(psStatementReq.getPsTime()) + 1;
        Date now = new Date();
        int nowYear = DateUtil.year(now);
        String psTime = DateUtil.format(psStatementReq.getPsTime(), "yyyy/MM/dd HH:mm:ss");
        Date lastTime;
        String yearTime;
        if (year == nowYear) {
            lastTime = DateUtil.offsetDay(now, -1);//今年的当前日期-1天
            month = DateUtil.month(now) + 1;//今年当前月份
            int dayOfMonth = DateUtil.dayOfMonth(now) - 1;// 获取当前是几号并减一   只统计到昨天
            if (dayOfMonth < 1) {//小于1说明现在的日期是1号  只统计到当前时间月份-1
                if (month == 1) {
                    throw new BussinessException("今天为1月1号,无法统计今年报表");
                }
                month -= 1;
                dayOfMonth = 0;
            }
            if (dayOfMonth != 0) {
                int days = DateUtil.lengthOfMonth(month, DateUtil.isLeapYear(year));// 获取当前月有多少天
                double i = (double) dayOfMonth / (double) days;
                yearTime = getYear(month);
                yearTime = yearTime + "*" + i;
            } else {
                yearTime = getYear(month);
            }
            psStatementReq.setS(yearTime);
        } else {
            psStatementReq.setS("sp.jan+sp.feb+sp.mar+sp.apr+sp.may+sp.jun+sp.jul+sp.aug+sp.sep+sp.oct+sp.nov+sp.dece");
            lastTime = DateUtil.offset(psStatementReq.getPsTime(), DateField.YEAR, 1);
        }
        //求出电站及装机容量
        IPage<PsStatementResp> iPage = psStatementDao.psYearList(page, psStatementReq);

        if (iPage.getRecords() == null || iPage.getRecords().size() < 1) {
            return iPage;
        }
        List<Long> ids = new ArrayList<>();
        for (PsStatementResp record : iPage.getRecords()) {
            if (record != null && record.getHourBudget() != null && record.getPsCapacity() != null) {
                record.setPowerBudget(record.getHourBudget().multiply(record.getPsCapacity()).divide(new BigDecimal(10), 2, RoundingMode.HALF_UP));
            }
            if (record != null) {
                ids.add(record.getId());
            }
        }
        List<PsIotDayStInverterEntity> entityList = TdWrappers.lambdaQuery(PsIotDayStInverterEntity.class)
                .in(PsIotDayStInverterEntity::getPsId, ids)
                .ge(PsIotDayStInverterEntity::getTs, DateUtil.format(psStatementReq.getPsTime(), "yyyy-MM-dd HH:mm:ss"))
                .lt(PsIotDayStInverterEntity::getTs, DateUtil.format(lastTime, "yyyy-MM-dd HH:mm:ss"))
                .groupBy(PsIotDayStInverterEntity::getPsId);
        if (entityList == null) {
            return iPage;
        } else {
            for (PsStatementResp record : iPage.getRecords()) {
                for (PsIotDayStInverterEntity entity : entityList) {
                    if (String.valueOf(record.getId()).equals(entity.getPsId())) {
                        if (new BigDecimal(entity.getEtd()).compareTo(BigDecimal.ZERO) > 0
                                && record.getPsCapacity().compareTo(BigDecimal.ZERO) > 0) {
                            record.setHourPractical(new BigDecimal(entity.getEtd()).multiply(new BigDecimal(0.001)).divide(record.getPsCapacity(), 2, RoundingMode.HALF_UP));
                        }
                        record.setPowerPractical(new BigDecimal(entity.getEtd()).divide(new BigDecimal(10000), 2, RoundingMode.HALF_UP));
                        if (record.getHourPractical() != null && record.getHourPractical().compareTo(BigDecimal.ZERO) > 0
                                && record.getHourBudget() != null && record.getHourBudget().compareTo(BigDecimal.ZERO) > 0) {
                            record.setPercentage(record.getHourPractical().multiply(valueOf(100)).divide(record.getHourBudget(), 2, RoundingMode.HALF_UP));
                        }
                        break;
                    }
                }
            }
            return iPage;
        }
    }

    public IPage<PsStatementResp> psYearList2(PsStatementReq psStatementReq) {
        Page<PsStatementResp> page = new Page<>(psStatementReq.getPageNum(), psStatementReq.getPageSize());
      /*  psStatementReq.setPickYear(DateUtil.year(psStatementReq.getPsTime()));
        psStatementReq.setPickMonth(DateUtil.month(psStatementReq.getPsTime()) + 1);*/
        int year = DateUtil.year(psStatementReq.getPsTime());
        psStatementReq.setPickYear(year);
        Date now = new Date();
        int nowYear = DateUtil.year(now);
        DateTime startTime = DateUtil.beginOfYear(psStatementReq.getPsTime());
        DateTime endTime = DateUtil.endOfYear(psStatementReq.getPsTime());
        psStatementReq.setStartTime(startTime);
        psStatementReq.setEndTime(endTime);
        //求出电站及装机容量
        IPage<PsStatementResp> iPage = psStatementDao.psYearList2(page, psStatementReq);
        if (CollectionUtil.isEmpty(iPage.getRecords())) {
            return iPage;
        }
        // 查询电站对应发电量
        List<Long> ids = iPage.getRecords().stream().map(s -> s.getId()).collect(Collectors.toList());
        List<PsIotDayStInverterEntity> entityList = TdWrappers.lambdaQuery(PsIotDayStInverterEntity.class)
                .in(PsIotDayStInverterEntity::getPsId, ids)
                .ge(PsIotDayStInverterEntity::getTs, startTime.toDateStr())
                .lt(PsIotDayStInverterEntity::getTs, endTime.toDateStr())
                .groupBy(PsIotDayStInverterEntity::getPsId);
        // key:psId value:etd
        Map<String, String> psEtdMap = new HashMap<>();
        if (CollectionUtil.isNotEmpty(entityList)) {
            psEtdMap = CollStreamUtil.toMap(entityList, PsIotDayStInverterEntity::getPsId, PsIotDayStInverterEntity::getEtd);
        }
        for (PsStatementResp record : iPage.getRecords()) {
            // 年理论小时数
            BigDecimal powerBudget = new BigDecimal(0);//预算发电量
            if (year == nowYear) {
                // 同一年 当前月折算到天  年初到当前时间前一个月完整数据+当前月折算到天
                List<TSceneParameterEntity> list = sceneParameterService.list(new LambdaQueryWrapper<TSceneParameterEntity>()
                        .eq(TSceneParameterEntity::getPsCode, record.getPsCode())
                        .ge(TSceneParameterEntity::getSceneMonth, psStatementReq.getPsTime())
                        .le(TSceneParameterEntity::getSceneMonth, DateUtil.endOfMonth(DateUtil.date()))
                        .orderByAsc(TSceneParameterEntity::getSceneMonth));
                // 获取当前月份天数
                Integer days = DateUtil.lengthOfMonth(DateUtil.month(DateUtil.date()) + 1, DateUtil.isLeapYear(DateUtil.date().year()));
                // 完整月数据
                BigDecimal reduce = list.stream().filter(s -> !s.getSceneMonth().equals(DateUtil.beginOfMonth(DateUtil.date()))).map(TSceneParameterEntity::getSceneEle).reduce(BigDecimal.ZERO, BigDecimal::add);
                // 最后一个月数据
                BigDecimal nowScene = list.stream().filter(s -> s.getSceneMonth().equals(DateUtil.beginOfMonth(DateUtil.date()))).map(TSceneParameterEntity::getSceneEle).reduce(BigDecimal.ZERO, BigDecimal::add);
                Integer dayOfMonth = DateUtil.dayOfMonth(now) - 1;
                // 1号不统计数据
                if (dayOfMonth > 1) {
                    // 当月理论小时数/月天数*昨天到月初天数
                    BigDecimal multiply = nowScene.divide(new BigDecimal(days), 4, BigDecimal.ROUND_HALF_UP).multiply(new BigDecimal(dayOfMonth));
                    reduce = reduce.add(multiply);
                    // 理论小时数*装机容量 = 理论发电量 mw->wkw   *10
                    BigDecimal theoryEtd = record.getPsCapacity().multiply(reduce).multiply(new BigDecimal(10));

                    record.setHourBudget(reduce);
                    powerBudget = powerBudget.add(theoryEtd);
                }
            } else {
                // 不同年 直接取整年数据
                List<TSceneParameterEntity> list = sceneParameterService.list(new LambdaQueryWrapper<TSceneParameterEntity>()
                        .eq(TSceneParameterEntity::getPsCode, record.getPsCode())
                        .ge(TSceneParameterEntity::getSceneMonth, psStatementReq.getPsTime())
                        .le(TSceneParameterEntity::getSceneMonth, DateUtil.endOfYear(psStatementReq.getPsTime()))
                        .orderByAsc(TSceneParameterEntity::getSceneMonth));
                BigDecimal reduce = list.stream().map(TSceneParameterEntity::getSceneEle).reduce(BigDecimal.ZERO, BigDecimal::add);
                // 理论小时数
                record.setHourBudget(reduce);
                // 理论小时数*装机容量 = 理论发电量 mw->wkw   *10
                BigDecimal theoryEtd = record.getPsCapacity().multiply(reduce).divide(new BigDecimal(10000000));
                powerBudget = powerBudget.add(theoryEtd);
            }
            // 预算发电量(万kWh)
            record.setPowerBudget(powerBudget);
            if (record != null && record.getHourBudget() != null && record.getPsCapacity() != null) {
                record.setPowerBudget(record.getHourBudget().multiply(record.getPsCapacity()).divide(new BigDecimal(10), 2, RoundingMode.HALF_UP));
            }
            // 电站对应发电量 kwh-->wkwh /10000
            String etd = psEtdMap.get(record.getId().toString());
            // 实际发电量
            BigDecimal powerPractical = BigDecimal.ZERO;
            if (StringUtils.isNotBlank(etd)) {
                powerPractical = new BigDecimal(etd).divide(new BigDecimal(10000));
                record.setPowerPractical(powerPractical);
            }
            // 实际发电量
            if (StringUtils.isNotBlank(etd)) {
                if (new BigDecimal(etd).compareTo(BigDecimal.ZERO) > 0
                        && record.getPsCapacity().compareTo(BigDecimal.ZERO) > 0) {
                    //实际发电量 wkwh/mw 要*10
                    record.setHourPractical(powerPractical.divide(record.getPsCapacity(), 4, RoundingMode.HALF_UP).multiply(new BigDecimal(10)).setScale(2, BigDecimal.ROUND_HALF_UP));
                }
            }
            if (record.getHourPractical() != null && record.getHourPractical().compareTo(BigDecimal.ZERO) > 0
                    && record.getHourBudget() != null && record.getHourBudget().compareTo(BigDecimal.ZERO) > 0) {
                record.setPercentage(record.getHourPractical().multiply(valueOf(100)).divide(record.getHourBudget(), 2, RoundingMode.HALF_UP));
            }

        }
        return iPage;
    }


    public IPage<ProvinceStatementResp> provinceYearList(PsStatementReq psStatementReq) {
        Page<ProvinceStatementResp> page = new Page<>(psStatementReq.getPageNum(), psStatementReq.getPageSize());
        //查出省份
        IPage<ProvinceStatementResp> iPage = psStatementDao.provinceList(page, psStatementReq);
        //计算出年份   用来匹配电站对应年份的理论小时数
        int year = DateUtil.year(psStatementReq.getPsTime());
        psStatementReq.setPickYear(year);
        //计算出月份   用来匹配电站对应月份的理论小时数
        int month = DateUtil.month(psStatementReq.getPsTime()) + 1;

        Date now = new Date();
        int nowYear = DateUtil.year(now);
        String psTime = DateUtil.format(psStatementReq.getPsTime(), "yyyy/MM/dd HH:mm:ss");
        Date lastTime;
        if (year == nowYear) {
            lastTime = DateUtil.offsetDay(now, -1);//当前日期的昨天
            month = DateUtil.month(now) + 1;//当前日期月份
            int dayOfMonth = DateUtil.dayOfMonth(now) - 1;// 获取当前是几号并减一   只统计到昨天
            if (dayOfMonth < 1) {//小于1说明现在的日期是1号  只统计到当前时间月份-1
                if (month == 1) {
                    throw new BussinessException("今天为1月1号,无法统计今年报表");
                }
                month -= 1;
                dayOfMonth = 0;
            }
            String yearTime;
            double i = 1.0;
            if (dayOfMonth != 0) {
                int days = DateUtil.lengthOfMonth(month, DateUtil.isLeapYear(year));// 获取当前月有多少天
                i = (double) dayOfMonth / (double) days;
                yearTime = getYear(month);
                yearTime = yearTime + "*" + i;
            } else {
                yearTime = getYear(month);
            }
            for (ProvinceStatementResp record : iPage.getRecords()) {
                //找出对应省份下的电站集合
                List<TPowerStationEntity> list = tPowerStationService.list(new LambdaQueryWrapper<TPowerStationEntity>()
                        .eq(TPowerStationEntity::getPsType, 2)
                        .eq(TPowerStationEntity::getArFlag, 1)
                        .eq(TPowerStationEntity::getDelFlag, 0)
                        .isNotNull(TPowerStationEntity::getAllPowerTime)
                        .eq(TPowerStationEntity::getProvinceId, record.getProvinceId()));
                List<PsIotDayStInverterEntity> entityLists = new ArrayList<PsIotDayStInverterEntity>();
                if (list != null) {
                    BigDecimal hourBudget = new BigDecimal(0);//预算小时
                    BigDecimal powerBudget = new BigDecimal(0);//预算发电量
                    BigDecimal psCapacity = new BigDecimal(0);//装机容量
                    //将对应省份下的电站的实际发电量求出,并且通过装机容量赛选满足
                    for (TPowerStationEntity entity : list) {
                        if (entity.getAllPowerTime().compareTo(psStatementReq.getPsTime()) >= 0 && DateUtil.month(entity.getAllPowerTime()) + 1 < DateUtil.month(now)) {
                            continue;
                        }
                        if (DateUtil.year(entity.getAllPowerTime()) < DateUtil.year(now)) {
                            yearTime = getYear(month) + "*" + i;
                        } else {
                            String invalidYear = yearMap.getOrDefault(month, "Invalid year");
                            String yearByPs = getYearByPs(DateUtil.month(entity.getAllPowerTime()) + 2);
                            String time = getTime(invalidYear, yearByPs);
                            if (time != null) {
                                String invalidMonth = monthMap.getOrDefault(month, "Invalid month");
                                yearTime = time + "+" + invalidMonth;
                            } else {
                                yearTime = monthMap.getOrDefault(month, "Invalid month");
                            }
                        }
                        String dateString = DateUtil.format(DateUtil.beginOfMonth(DateUtil.offsetMonth(entity.getAllPowerTime(), 1)), "yyyy-MM-dd HH:mm:ss");
                        //电站对应的发电量
                        List<PsIotDayStInverterEntity> entityList = TdWrappers.lambdaQuery(PsIotDayStInverterEntity.class)
                                .eq(PsIotDayStInverterEntity::getPsId, entity.getId())
                                .ge(PsIotDayStInverterEntity::getTs, dateString)
                                .ge(PsIotDayStInverterEntity::getTs, DateUtil.format(psStatementReq.getPsTime(), "yyyy-MM-dd HH:mm:ss"))
                                .lt(PsIotDayStInverterEntity::getTs, DateUtil.format(lastTime, "yyyy-MM-dd HH:mm:ss"))
                                .groupBy(PsIotDayStInverterEntity::getPsId);
                        entityLists.addAll(entityList);
                        ProvinceStatementResp resp = psStatementDao.findProvinceYearById(entity.getId(), yearTime, year);
                        if (resp != null && resp.getHourBudget() != null && resp.getPsCapacity() != null) {
                            hourBudget = resp.getHourBudget().multiply(resp.getPsCapacity()).add(hourBudget);
                        }
                        if (resp != null && resp.getHourBudget() != null) {
                            powerBudget = resp.getPowerBudget().add(powerBudget);
                        }
                        if (resp != null && resp.getPsCapacity() != null) {
                            psCapacity = resp.getPsCapacity().add(psCapacity);
                        }
                    }
                    record.setPsCapacity(psCapacity);
                    if (hourBudget.compareTo(BigDecimal.ZERO) > 0) {
                        record.setHourBudget(hourBudget.divide(psCapacity, 2, RoundingMode.HALF_UP));
                    }
                    record.setPowerBudget(powerBudget);
                    double powerPractical = 0.0;//实际发电量
                    //求出装机容量总和及发电量总和
                    for (PsIotDayStInverterEntity entityList : entityLists) {
                        if (entityList.getEtd() != null) {
                            powerPractical += Double.parseDouble(entityList.getEtd());
                        }
                    }
                    record.setPowerPractical(new BigDecimal(powerPractical).divide(new BigDecimal(10000), 2, RoundingMode.HALF_UP));
                    if (record.getPowerPractical() != null && record.getPowerPractical().compareTo(BigDecimal.ZERO) > 0 && record.getPsCapacity() != null && record.getPsCapacity().compareTo(BigDecimal.ZERO) > 0) {
                        record.setHourPractical(record.getPowerPractical().multiply(new BigDecimal(10)).divide(record.getPsCapacity(), 2, RoundingMode.HALF_UP));
                    }
                    if (record.getHourPractical() != null && record.getHourBudget() != null && record.getHourPractical().compareTo(BigDecimal.ZERO) > 0) {
                        record.setPercentage(record.getHourPractical().multiply(new BigDecimal(100)).divide(record.getHourBudget(), 2, RoundingMode.HALF_UP));
                    }
                }
            }
            return iPage;
        } else {
            lastTime = DateUtil.offsetMonth(psStatementReq.getPsTime(), 12);//得到明年的时间
            String yearTime = getYear(13);
            for (ProvinceStatementResp record : iPage.getRecords()) {
                //找出对应省份下的电站集合
                List<TPowerStationEntity> list = tPowerStationService.list(new LambdaQueryWrapper<TPowerStationEntity>()
                        .eq(TPowerStationEntity::getPsType, 2)
                        .eq(TPowerStationEntity::getArFlag, 1)
                        .eq(TPowerStationEntity::getDelFlag, 0)
                        .isNotNull(TPowerStationEntity::getAllPowerTime)
                        .eq(TPowerStationEntity::getProvinceId, record.getProvinceId()));
                List<PsIotDayStInverterEntity> entityLists = new ArrayList<PsIotDayStInverterEntity>();
                if (list != null) {
                    BigDecimal hourBudget = new BigDecimal(0);//预算小时
                    BigDecimal powerBudget = new BigDecimal(0);//预算发电量
                    BigDecimal psCapacity = new BigDecimal(0);//装机容量
                    //将对应省份下的电站的实际发电量求出,并且通过装机容量赛选满足
                    for (TPowerStationEntity entity : list) {
                        if (entity.getAllPowerTime().compareTo(psStatementReq.getPsTime()) > 0 && entity.getAllPowerTime().compareTo(lastTime) > 0) {
                            continue;
                        }
                        int i = DateUtil.month(entity.getAllPowerTime()) + 2;
                        if (DateUtil.year(entity.getAllPowerTime()) == DateUtil.year(psStatementReq.getPsTime())) {
                            yearTime = getYearByPs(i);
                        } else {
                            yearTime = getYearByPs(1);
                        }
                        String dateString = DateUtil.format(DateUtil.beginOfMonth(DateUtil.offsetMonth(entity.getAllPowerTime(), 1)), "yyyy-MM-dd HH:mm:ss");
                        //电站对应的发电量
                        List<PsIotDayStInverterEntity> entityList = TdWrappers.lambdaQuery(PsIotDayStInverterEntity.class)
                                .eq(PsIotDayStInverterEntity::getPsId, entity.getId())
                                .ge(PsIotDayStInverterEntity::getTs, dateString)
                                .ge(PsIotDayStInverterEntity::getTs, DateUtil.format(psStatementReq.getPsTime(), "yyyy-MM-dd HH:mm:ss"))
                                .lt(PsIotDayStInverterEntity::getTs, DateUtil.format(lastTime, "yyyy-MM-dd HH:mm:ss"))
                                .groupBy(PsIotDayStInverterEntity::getPsId);
                        entityLists.addAll(entityList);
                        ProvinceStatementResp resp = psStatementDao.findProvinceYearById(entity.getId(), yearTime, year);
                        if (resp != null && resp.getHourBudget() != null && resp.getPsCapacity() != null) {
                            hourBudget = resp.getHourBudget().multiply(resp.getPsCapacity()).add(hourBudget);
                        }
                        if (resp != null && resp.getHourBudget() != null) {
                            powerBudget = resp.getPowerBudget().add(powerBudget);
                        }
                        if (resp != null && resp.getPsCapacity() != null) {
                            psCapacity = resp.getPsCapacity().add(psCapacity);
                        }
                    }
                    record.setPsCapacity(psCapacity);
                    if (hourBudget.compareTo(BigDecimal.ZERO) > 0) {
                        record.setHourBudget(hourBudget.divide(psCapacity, 2, RoundingMode.HALF_UP));
                    }
                    record.setPowerBudget(powerBudget);
                    double powerPractical = 0.0;//实际发电量
                    //求出装机容量总和及发电量总和
                    for (PsIotDayStInverterEntity entityList : entityLists) {
                        if (entityList.getEtd() != null) {
                            powerPractical += Double.parseDouble(entityList.getEtd());
                        }
                    }
                    record.setPowerPractical(new BigDecimal(powerPractical).divide(new BigDecimal(10000), 2, RoundingMode.HALF_UP));
                    if (record.getPowerPractical() != null && record.getPowerPractical().compareTo(BigDecimal.ZERO) > 0 && record.getPsCapacity() != null && record.getPsCapacity().compareTo(BigDecimal.ZERO) > 0) {
                        record.setHourPractical(record.getPowerPractical().multiply(new BigDecimal(10)).divide(record.getPsCapacity(), 2, RoundingMode.HALF_UP));
                    }
                    if (record.getHourPractical() != null && record.getHourBudget() != null && record.getHourPractical().compareTo(BigDecimal.ZERO) > 0) {
                        record.setPercentage(record.getHourPractical().multiply(new BigDecimal(100)).divide(record.getHourBudget(), 2, RoundingMode.HALF_UP));
                    }
                }
            }
            return iPage;
        }
    }

    public IPage<ProvinceStatementResp> provinceYearList2(PsStatementReq psStatementReq) {
        Page<ProvinceStatementResp> page = new Page<>(psStatementReq.getPageNum(), psStatementReq.getPageSize());
        //查出省份
        IPage<ProvinceStatementResp> iPage = psStatementDao.provinceList(page, psStatementReq);

        DateTime startTime = DateUtil.beginOfYear(psStatementReq.getPsTime());
        DateTime endTime = DateUtil.endOfYear(psStatementReq.getPsTime());
        psStatementReq.setStartTime(startTime);
        psStatementReq.setEndTime(endTime);

        int year = DateUtil.year(psStatementReq.getPsTime());
        psStatementReq.setPickYear(year);
        Date now = new Date();
        int nowYear = DateUtil.year(now);
        for (ProvinceStatementResp record : iPage.getRecords()) {
            //找出对应省份下的电站集合
            List<TPowerStationEntity> psList = tPowerStationService.list(new LambdaQueryWrapper<TPowerStationEntity>()
                    .eq(TPowerStationEntity::getPsType, 2)
                    .eq(TPowerStationEntity::getArFlag, 1)
                    .eq(TPowerStationEntity::getDelFlag, 0)
                    .isNotNull(TPowerStationEntity::getAllPowerTime)
                    .eq(TPowerStationEntity::getProvinceId, record.getProvinceId()));
            if (CollectionUtil.isEmpty(psList)) {
                continue;
            }
            // 查询电站发电量
            List<Long> ids = psList.stream().map(TPowerStationEntity::getId).collect(Collectors.toList());
            List<PsIotDayStInverterEntity> iotDayStInverterEntities = TdWrappers.lambdaQuery(PsIotDayStInverterEntity.class)
                    .in(PsIotDayStInverterEntity::getPsId, ids)
                    .ge(PsIotDayStInverterEntity::getTs, DateUtil.format(psStatementReq.getPsTime(), "yyyy-MM-dd HH:mm:ss"))
                    .lt(PsIotDayStInverterEntity::getTs, DateUtil.format(DateUtil.endOfYear(psStatementReq.getPsTime()), "yyyy-MM-dd HH:mm:ss"))
                    .groupBy(PsIotDayStInverterEntity::getPsId);
            BigDecimal powerPractical = BigDecimal.ZERO;
            if (CollectionUtil.isNotEmpty(iotDayStInverterEntities)) {
                powerPractical = iotDayStInverterEntities.stream().map(s -> new BigDecimal(s.getEtd())).reduce(BigDecimal.ZERO, BigDecimal::add).divide(new BigDecimal(10000), 2, RoundingMode.HALF_UP);
                // 实际发电量(万kWh)
                record.setPowerPractical(powerPractical.setScale(2,BigDecimal.ROUND_HALF_UP));
            }
            BigDecimal powerBudget = new BigDecimal(0);//预算发电量
            //装机容量
            BigDecimal psCapacity = new BigDecimal(psList.stream().mapToInt(TPowerStationEntity::getCapacity).sum()).divide(new BigDecimal("1000000"));
            record.setPsCapacity(psCapacity);
            //将对应省份下的电站的实际发电量求出,并且通过装机容量赛选满足
            for (TPowerStationEntity entity : psList) {
                psStatementReq.setPsId(entity.getId());
                psStatementReq.setProvinceCompanyCode(record.getProvinceCompanyCode());
                if (year == nowYear) {
                    // 同一年 当前月折算到天  年初到当前时间前一个月完整数据+当前月折算到天
                    List<TSceneParameterEntity> list = sceneParameterService.list(new LambdaQueryWrapper<TSceneParameterEntity>()
                            .eq(TSceneParameterEntity::getPsCode, entity.getPsCode())
                            .ge(TSceneParameterEntity::getSceneMonth, psStatementReq.getPsTime())
                            .le(TSceneParameterEntity::getSceneMonth, DateUtil.endOfMonth(DateUtil.date()))
                            .orderByAsc(TSceneParameterEntity::getSceneMonth));
                    // 获取当前月份天数
                    Integer days = DateUtil.lengthOfMonth(DateUtil.month(DateUtil.date()) + 1, DateUtil.isLeapYear(DateUtil.date().year()));
                    // 完整月数据
                    BigDecimal reduce = list.stream().filter(s -> !s.getSceneMonth().equals(DateUtil.beginOfMonth(DateUtil.date()))).map(TSceneParameterEntity::getSceneEle).reduce(BigDecimal.ZERO, BigDecimal::add);
                    // 最后一个月数据
                    BigDecimal nowScene = list.stream().filter(s -> s.getSceneMonth().equals(DateUtil.beginOfMonth(DateUtil.date()))).map(TSceneParameterEntity::getSceneEle).reduce(BigDecimal.ZERO, BigDecimal::add);
                    Integer dayOfMonth = DateUtil.dayOfMonth(now) - 1;
                    // 1号不统计数据
                    if (dayOfMonth > 1) {
                        // 当月理论小时数/月天数*昨天到月初天数
                        BigDecimal multiply = nowScene.divide(new BigDecimal(days), 4, BigDecimal.ROUND_HALF_UP).multiply(new BigDecimal(dayOfMonth));
                        reduce = reduce.add(multiply);
                        // 理论小时数*装机容量 = 理论发电量 w->wkw   / 10000000
                        BigDecimal theoryEtd = new BigDecimal(entity.getCapacity()).multiply(reduce).divide(new BigDecimal(10000000));


                        powerBudget = powerBudget.add(theoryEtd);
                    }
                } else {
                    // 不同年 直接取整年数据
                    List<TSceneParameterEntity> list = sceneParameterService.list(new LambdaQueryWrapper<TSceneParameterEntity>()
                            .eq(TSceneParameterEntity::getPsCode, entity.getPsCode())
                            .ge(TSceneParameterEntity::getSceneMonth, psStatementReq.getPsTime())
                            .le(TSceneParameterEntity::getSceneMonth, DateUtil.endOfYear(psStatementReq.getPsTime()))
                            .orderByAsc(TSceneParameterEntity::getSceneMonth));
                    BigDecimal reduce = list.stream().map(TSceneParameterEntity::getSceneEle).reduce(BigDecimal.ZERO, BigDecimal::add);
                    // 理论小时数
                    record.setHourBudget(reduce);
                    // 理论小时数*装机容量 = 理论发电量 mw->wkw   *10
                    BigDecimal theoryEtd = new BigDecimal(entity.getCapacity()).multiply(reduce).divide(new BigDecimal(10000000));
                    powerBudget = powerBudget.add(theoryEtd);

                }
            }
            // 预算小时数 = 各电站理论发电量/总装机容量 wkw/mw --》*10
            record.setHourBudget(powerBudget.divide(psCapacity, 4, RoundingMode.HALF_UP).multiply(new BigDecimal(10)).setScale(2, BigDecimal.ROUND_HALF_UP));

            // 预算发电量(万kWh)
            record.setPowerBudget(powerBudget);


            if (powerPractical != null && powerPractical.compareTo(BigDecimal.ZERO) > 0 && record.getPsCapacity() != null && record.getPsCapacity().compareTo(BigDecimal.ZERO) > 0) {
                record.setHourPractical(powerPractical.multiply(new BigDecimal(10)).divide(record.getPsCapacity(), 2, RoundingMode.HALF_UP));
            }
            if (record.getHourPractical() != null && record.getHourBudget() != null && record.getHourPractical().compareTo(BigDecimal.ZERO) > 0 && record.getHourBudget().compareTo(BigDecimal.ZERO) > 0) {
                record.setPercentage(record.getHourPractical().multiply(new BigDecimal(100)).divide(record.getHourBudget(), 2, RoundingMode.HALF_UP));
            }


      /*      List<PsIotDayStInverterEntity> entityLists = new ArrayList<PsIotDayStInverterEntity>();
            if (list != null) {
                BigDecimal hourBudget = new BigDecimal(0);//预算小时
                BigDecimal powerBudget = new BigDecimal(0);//预算发电量
                BigDecimal psCapacity = new BigDecimal(0);//装机容量
                //将对应省份下的电站的实际发电量求出,并且通过装机容量赛选满足
                for (TPowerStationEntity entity : list) {
                    String dateString = DateUtil.format(DateUtil.beginOfMonth(DateUtil.offsetMonth(entity.getAllPowerTime(), 1)), "yyyy-MM-dd HH:mm:ss");
                    //电站对应的发电量
                    List<PsIotDayStInverterEntity> entityList = TdWrappers.lambdaQuery(PsIotDayStInverterEntity.class)
                            .eq(PsIotDayStInverterEntity::getPsId, entity.getId())
                            .ge(PsIotDayStInverterEntity::getTs, dateString)
                            .lt(PsIotDayStInverterEntity::getTs, endTime.toDateStr())
                            .groupBy(PsIotDayStInverterEntity::getPsId);
                    entityLists.addAll(entityList);
                    psStatementReq.setPsId(entity.getId());
                    ProvinceStatementResp resp = psStatementDao.findProvinceYearById2(psStatementReq);
                    if (null == resp) {
                        continue;
                    }
                    resp.setPsCapacity(new BigDecimal(entity.getCapacity()).divide(new BigDecimal(1000000)));
                    if (resp != null && resp.getHourBudget() != null && resp.getPsCapacity() != null) {
                        hourBudget = resp.getHourBudget().multiply(resp.getPsCapacity()).add(hourBudget);
                    }
                    if (resp != null && resp.getHourBudget() != null) {
                        powerBudget = resp.getPowerBudget().add(powerBudget);
                    }
                    if (resp != null && resp.getPsCapacity() != null) {
                        psCapacity = resp.getPsCapacity().add(psCapacity);
                    }
                }
                record.setPsCapacity(psCapacity);
                if (hourBudget.compareTo(BigDecimal.ZERO) > 0) {
                    record.setHourBudget(hourBudget.divide(psCapacity, 2, RoundingMode.HALF_UP));
                }
                record.setPowerBudget(powerBudget);
                double powerPractical = 0.0;//实际发电量
                //求出装机容量总和及发电量总和
                for (PsIotDayStInverterEntity entityList : entityLists) {
                    if (entityList.getEtd() != null) {
                        powerPractical += Double.parseDouble(entityList.getEtd());
                    }
                }
                record.setPowerPractical(new BigDecimal(powerPractical).divide(new BigDecimal(10000), 2, RoundingMode.HALF_UP));
                if (record.getPowerPractical() != null && record.getPowerPractical().compareTo(BigDecimal.ZERO) > 0 && record.getPsCapacity() != null && record.getPsCapacity().compareTo(BigDecimal.ZERO) > 0) {
                    record.setHourPractical(record.getPowerPractical().multiply(new BigDecimal(10)).divide(record.getPsCapacity(), 2, RoundingMode.HALF_UP));
                }
                if (record.getHourPractical() != null && record.getHourBudget() != null && record.getHourPractical().compareTo(BigDecimal.ZERO) > 0) {
                    record.setPercentage(record.getHourPractical().multiply(new BigDecimal(100)).divide(record.getHourBudget(), 2, RoundingMode.HALF_UP));
                }
            }*/
        }
        return iPage;

    }


    public IPage<ProvinceStatementResp> companyYearList(PsStatementReq psStatementReq) {
        Page<ProvinceStatementResp> page = new Page<>(psStatementReq.getPageNum(), psStatementReq.getPageSize());
        //查出省份
        IPage<ProvinceStatementResp> iPage = psStatementDao.companyList(page, psStatementReq);
        //计算出年份   用来匹配电站对应年份的理论小时数
        int year = DateUtil.year(psStatementReq.getPsTime());
        psStatementReq.setPickYear(year);
        //计算出月份   用来匹配电站对应月份的理论小时数
        int month = DateUtil.month(psStatementReq.getPsTime()) + 1;
        Date now = new Date();
        int nowYear = DateUtil.year(now);
        String psTime = DateUtil.format(psStatementReq.getPsTime(), "yyyy/MM/dd HH:mm:ss");
        Date lastTime;
        if (year == nowYear) {
            lastTime = DateUtil.offsetDay(now, -1);//当前日期的昨天
            month = DateUtil.month(now) + 1;//当前日期月份
            int dayOfMonth = DateUtil.dayOfMonth(now) - 1;// 获取当前是几号并减一   只统计到昨天
            if (dayOfMonth < 1) {//小于1说明现在的日期是1号  只统计到当前时间月份-1
                if (month == 1) {
                    throw new BussinessException("今天为1月1号,无法统计今年报表");
                }
                month -= 1;
                dayOfMonth = 0;
            }
            String yearTime;
            double i = 1.0;
            if (dayOfMonth != 0) {
                int days = DateUtil.lengthOfMonth(month, DateUtil.isLeapYear(year));// 获取当前月有多少天
                i = (double) dayOfMonth / (double) days;
                yearTime = getYear(month);
                yearTime = yearTime + "*" + i;
            } else {
                yearTime = getYear(month);
            }
            for (ProvinceStatementResp record : iPage.getRecords()) {
                //找出对应省份下的电站集合
                List<TPowerStationEntity> list = tPowerStationService.list(new LambdaQueryWrapper<TPowerStationEntity>()
                        .eq(TPowerStationEntity::getPsType, 2)
                        .eq(TPowerStationEntity::getArFlag, 1)
                        .eq(TPowerStationEntity::getDelFlag, 0)
                        .isNotNull(TPowerStationEntity::getAllPowerTime)
                        .eq(TPowerStationEntity::getProvinceCompanyCode, record.getProvinceCompanyCode()));
                List<PsIotDayStInverterEntity> entityLists = new ArrayList<PsIotDayStInverterEntity>();
                if (list != null) {
                    BigDecimal hourBudget = new BigDecimal(0);//预算小时
                    BigDecimal powerBudget = new BigDecimal(0);//预算发电量
                    BigDecimal psCapacity = new BigDecimal(0);//装机容量
                    //将对应省份下的电站的实际发电量求出,并且通过装机容量赛选满足
                    for (TPowerStationEntity entity : list) {
                        if (entity.getAllPowerTime().compareTo(psStatementReq.getPsTime()) >= 0 && DateUtil.month(entity.getAllPowerTime()) + 1 < DateUtil.month(now)) {
                            continue;
                        }
                        if (DateUtil.year(entity.getAllPowerTime()) < DateUtil.year(now)) {
                            yearTime = getYear(month) + "*" + i;
                        } else {
                            String invalidYear = yearMap.getOrDefault(month, "Invalid year");
                            String yearByPs = getYearByPs(DateUtil.month(entity.getAllPowerTime()) + 2);
                            String time = getTime(invalidYear, yearByPs);
                            if (time != null) {
                                String invalidMonth = monthMap.getOrDefault(month, "Invalid month");
                                yearTime = time + "+" + invalidMonth;
                            } else {
                                yearTime = monthMap.getOrDefault(month, "Invalid month");
                            }
                        }
                        //全容并网后的下一个月
                        String dateString = DateUtil.format(DateUtil.beginOfMonth(DateUtil.offsetMonth(entity.getAllPowerTime(), 1)), "yyyy-MM-dd HH:mm:ss");
                        //电站对应的发电量
                        List<PsIotDayStInverterEntity> entityList = TdWrappers.lambdaQuery(PsIotDayStInverterEntity.class)
                                .eq(PsIotDayStInverterEntity::getPsId, entity.getId())
                                .ge(PsIotDayStInverterEntity::getTs, dateString)
                                .ge(PsIotDayStInverterEntity::getTs, DateUtil.format(psStatementReq.getPsTime(), "yyyy-MM-dd HH:mm:ss"))
                                .lt(PsIotDayStInverterEntity::getTs, DateUtil.format(lastTime, "yyyy-MM-dd HH:mm:ss"))
                                .groupBy(PsIotDayStInverterEntity::getPsId);
                        entityLists.addAll(entityList);
                        ProvinceStatementResp resp = psStatementDao.findProvinceYearById(entity.getId(), yearTime, year);
                        if (resp != null && resp.getHourBudget() != null && resp.getPsCapacity() != null) {
                            hourBudget = resp.getHourBudget().multiply(resp.getPsCapacity()).add(hourBudget);
                        }
                        if (resp != null && resp.getHourBudget() != null) {
                            powerBudget = resp.getPowerBudget().add(powerBudget);
                        }
                        if (resp != null && resp.getPsCapacity() != null) {
                            psCapacity = resp.getPsCapacity().add(psCapacity);
                        }
                    }
                    record.setPsCapacity(psCapacity);
                    if (hourBudget.compareTo(BigDecimal.ZERO) > 0) {
                        record.setHourBudget(hourBudget.divide(psCapacity, 2, RoundingMode.HALF_UP));
                    }
                    record.setPowerBudget(powerBudget);
                    double powerPractical = 0.0;//实际发电量
                    //求出装机容量总和及发电量总和
                    for (PsIotDayStInverterEntity entityList : entityLists) {
                        if (entityList.getEtd() != null) {
                            powerPractical += Double.parseDouble(entityList.getEtd());
                        }
                    }
                    record.setPowerPractical(new BigDecimal(powerPractical).divide(new BigDecimal(10000), 2, RoundingMode.HALF_UP));
                    if (record.getPowerPractical() != null && record.getPowerPractical().compareTo(BigDecimal.ZERO) > 0 && record.getPsCapacity() != null && record.getPsCapacity().compareTo(BigDecimal.ZERO) > 0) {
                        record.setHourPractical(record.getPowerPractical().multiply(new BigDecimal(10)).divide(record.getPsCapacity(), 2, RoundingMode.HALF_UP));
                    }
                    if (record.getHourPractical() != null && record.getHourBudget() != null && record.getHourPractical().compareTo(BigDecimal.ZERO) > 0) {
                        record.setPercentage(record.getHourPractical().multiply(new BigDecimal(100)).divide(record.getHourBudget(), 2, RoundingMode.HALF_UP));
                    }
                }
            }
            return iPage;
        } else {
            lastTime = DateUtil.offsetMonth(psStatementReq.getPsTime(), 12);//得到明年的时间
            String yearTime = getYear(13);
            for (ProvinceStatementResp record : iPage.getRecords()) {
                //找出对应省份下的电站集合
                List<TPowerStationEntity> list = tPowerStationService.list(new LambdaQueryWrapper<TPowerStationEntity>()
                        .eq(TPowerStationEntity::getPsType, 2)
                        .eq(TPowerStationEntity::getArFlag, 1)
                        .eq(TPowerStationEntity::getDelFlag, 0)
                        .isNotNull(TPowerStationEntity::getAllPowerTime)
                        .eq(TPowerStationEntity::getProvinceCompanyCode, record.getProvinceCompanyCode()));
                List<PsIotDayStInverterEntity> entityLists = new ArrayList<PsIotDayStInverterEntity>();
                if (list != null) {
                    BigDecimal hourBudget = new BigDecimal(0);//预算小时
                    BigDecimal powerBudget = new BigDecimal(0);//预算发电量
                    BigDecimal psCapacity = new BigDecimal(0);//装机容量
                    //将对应省份下的电站的实际发电量求出,并且通过装机容量赛选满足
                    for (TPowerStationEntity entity : list) {
                        if (entity.getAllPowerTime().compareTo(psStatementReq.getPsTime()) > 0 && entity.getAllPowerTime().compareTo(lastTime) > 0) {
                            continue;
                        }
                        int i = DateUtil.month(entity.getAllPowerTime()) + 2;
                        if (DateUtil.year(entity.getAllPowerTime()) == DateUtil.year(psStatementReq.getPsTime())) {
                            yearTime = getYearByPs(i);
                        } else {
                            yearTime = getYearByPs(1);
                        }
                        String dateString = DateUtil.format(DateUtil.beginOfMonth(DateUtil.offsetMonth(entity.getAllPowerTime(), 1)), "yyyy-MM-dd HH:mm:ss");
                        //电站对应的发电量
                        List<PsIotDayStInverterEntity> entityList = TdWrappers.lambdaQuery(PsIotDayStInverterEntity.class)
                                .eq(PsIotDayStInverterEntity::getPsId, entity.getId())
                                .ge(PsIotDayStInverterEntity::getTs, dateString)
                                .ge(PsIotDayStInverterEntity::getTs, DateUtil.format(psStatementReq.getPsTime(), "yyyy-MM-dd HH:mm:ss"))
                                .lt(PsIotDayStInverterEntity::getTs, DateUtil.format(lastTime, "yyyy-MM-dd HH:mm:ss"))
                                .groupBy(PsIotDayStInverterEntity::getPsId);
                        entityLists.addAll(entityList);
                        ProvinceStatementResp resp = psStatementDao.findProvinceYearById(entity.getId(), yearTime, year);
                        if (resp != null && resp.getHourBudget() != null && resp.getPsCapacity() != null) {
                            hourBudget = resp.getHourBudget().multiply(resp.getPsCapacity()).add(hourBudget);
                        }
                        if (resp != null && resp.getHourBudget() != null) {
                            powerBudget = resp.getPowerBudget().add(powerBudget);
                        }
                        if (resp != null && resp.getPsCapacity() != null) {
                            psCapacity = resp.getPsCapacity().add(psCapacity);
                        }
                    }
                    record.setPsCapacity(psCapacity);
                    if (hourBudget.compareTo(BigDecimal.ZERO) > 0) {
                        record.setHourBudget(hourBudget.divide(psCapacity, 2, RoundingMode.HALF_UP));
                    }
                    record.setPowerBudget(powerBudget);
                    double powerPractical = 0.0;//实际发电量
                    //求出装机容量总和及发电量总和
                    for (PsIotDayStInverterEntity entityList : entityLists) {
                        if (entityList.getEtd() != null) {
                            powerPractical += Double.parseDouble(entityList.getEtd());
                        }
                    }
                    record.setPowerPractical(new BigDecimal(powerPractical).divide(new BigDecimal(10000), 2, RoundingMode.HALF_UP));
                    if (record.getPowerPractical() != null && record.getPowerPractical().compareTo(BigDecimal.ZERO) > 0 && record.getPsCapacity() != null && record.getPsCapacity().compareTo(BigDecimal.ZERO) > 0) {
                        record.setHourPractical(record.getPowerPractical().multiply(new BigDecimal(10)).divide(record.getPsCapacity(), 2, RoundingMode.HALF_UP));
                    }
                    if (record.getHourPractical() != null && record.getHourBudget() != null && record.getHourPractical().compareTo(BigDecimal.ZERO) > 0) {
                        record.setPercentage(record.getHourPractical().multiply(new BigDecimal(100)).divide(record.getHourBudget(), 2, RoundingMode.HALF_UP));
                    }
                }
            }
            return iPage;
        }
    }

    public IPage<ProvinceStatementResp> companyYearList2(PsStatementReq psStatementReq) {
        Page<ProvinceStatementResp> page = new Page<>(psStatementReq.getPageNum(), psStatementReq.getPageSize());
        //查出省份
        IPage<ProvinceStatementResp> iPage = psStatementDao.companyList(page, psStatementReq);
        DateTime endTime = DateUtil.endOfYear(psStatementReq.getPsTime());
        int year = DateUtil.year(psStatementReq.getPsTime());
        psStatementReq.setPickYear(year);
        Date now = new Date();
        int nowYear = DateUtil.year(now);
        // 开始事件
        DateTime startTime = null;
        psStatementReq.setStartTime(startTime);
        psStatementReq.setEndTime(endTime);
        for (ProvinceStatementResp record : iPage.getRecords()) {
            //找出对应省份下的电站集合
            List<TPowerStationEntity> psList = new ArrayList<>();
            if (year == nowYear) {
                // 同年 全融并网时间下个月到当前日期前一天
                psList = psStatementDao.getCompanyPsList(psStatementReq.getPsTime(), DateUtil.offsetDay(now, -1), record.getProvinceCompanyCode(), record.getProvinceId());
            } else {
                // 不同年 全融并网时间大于查询时间年初到年底
                psList = psStatementDao.getCompanyPsList(psStatementReq.getPsTime(), DateUtil.endOfYear(psStatementReq.getPsTime()), record.getProvinceCompanyCode(), record.getProvinceId());
            }
            List<PsIotDayStInverterEntity> entityLists = new ArrayList<PsIotDayStInverterEntity>();
            if (psList != null) {
                // 查询电站发电量
                List<Long> ids = psList.stream().map(TPowerStationEntity::getId).collect(Collectors.toList());
                List<PsIotDayStInverterEntity> iotDayStInverterEntities = TdWrappers.lambdaQuery(PsIotDayStInverterEntity.class)
                        .in(PsIotDayStInverterEntity::getPsId, ids)
                        .ge(PsIotDayStInverterEntity::getTs, DateUtil.format(psStatementReq.getPsTime(), "yyyy-MM-dd HH:mm:ss"))
                        .lt(PsIotDayStInverterEntity::getTs, DateUtil.format(DateUtil.endOfYear(psStatementReq.getPsTime()), "yyyy-MM-dd HH:mm:ss"))
                        .groupBy(PsIotDayStInverterEntity::getPsId);
                BigDecimal powerPractical = BigDecimal.ZERO;
                if (CollectionUtil.isNotEmpty(iotDayStInverterEntities)) {
                    powerPractical = iotDayStInverterEntities.stream().map(s -> new BigDecimal(s.getEtd())).reduce(BigDecimal.ZERO, BigDecimal::add).divide(new BigDecimal(10000));
                    // 实际发电量(万kWh)
                    record.setPowerPractical(powerPractical.setScale(2, RoundingMode.HALF_UP));
                }
                BigDecimal powerBudget = new BigDecimal(0);//预算发电量
                //装机容量
                BigDecimal psCapacity = new BigDecimal(psList.stream().mapToInt(TPowerStationEntity::getCapacity).sum()).divide(new BigDecimal("1000000"));
                record.setPsCapacity(psCapacity);
                //将对应省份下的电站的实际发电量求出,并且通过装机容量赛选满足
                for (TPowerStationEntity entity : psList) {
                    psStatementReq.setPsId(entity.getId());
                    psStatementReq.setProvinceCompanyCode(record.getProvinceCompanyCode());
                    if (year == nowYear) {
                        // 同一年 当前月折算到天  年初到当前时间前一个月完整数据+当前月折算到天
                        List<TSceneParameterEntity> list = sceneParameterService.list(new LambdaQueryWrapper<TSceneParameterEntity>()
                                .eq(TSceneParameterEntity::getPsCode, entity.getPsCode())
                                .ge(TSceneParameterEntity::getSceneMonth, psStatementReq.getPsTime())
                                .le(TSceneParameterEntity::getSceneMonth, DateUtil.endOfMonth(DateUtil.date()))
                                .orderByAsc(TSceneParameterEntity::getSceneMonth));
                        // 获取当前月份天数
                        Integer days = DateUtil.lengthOfMonth(DateUtil.month(DateUtil.date()) + 1, DateUtil.isLeapYear(DateUtil.date().year()));
                        // 完整月数据
                        BigDecimal reduce = list.stream().filter(s -> !s.getSceneMonth().equals(DateUtil.beginOfMonth(DateUtil.date()))).map(TSceneParameterEntity::getSceneEle).reduce(BigDecimal.ZERO, BigDecimal::add);
                        // 最后一个月数据
                        BigDecimal nowScene = list.stream().filter(s -> s.getSceneMonth().equals(DateUtil.beginOfMonth(DateUtil.date()))).map(TSceneParameterEntity::getSceneEle).reduce(BigDecimal.ZERO, BigDecimal::add);
                        Integer dayOfMonth = DateUtil.dayOfMonth(now) - 1;
                        // 1号不统计数据
                        if (dayOfMonth > 1) {
                            // 当月理论小时数/月天数*昨天到月初天数
                            BigDecimal multiply = nowScene.divide(new BigDecimal(days), 4, BigDecimal.ROUND_HALF_UP).multiply(new BigDecimal(dayOfMonth));
                            reduce = reduce.add(multiply);
                            // 理论小时数*装机容量 = 理论发电量 w->wkw   / 10000000
                            BigDecimal theoryEtd = new BigDecimal(entity.getCapacity()).multiply(reduce).divide(new BigDecimal(10000000));


                            powerBudget = powerBudget.add(theoryEtd);
                        }
                    } else {
                        // 不同年 直接取整年数据
                        List<TSceneParameterEntity> list = sceneParameterService.list(new LambdaQueryWrapper<TSceneParameterEntity>()
                                .eq(TSceneParameterEntity::getPsCode, entity.getPsCode())
                                .ge(TSceneParameterEntity::getSceneMonth, psStatementReq.getPsTime())
                                .le(TSceneParameterEntity::getSceneMonth, DateUtil.endOfYear(psStatementReq.getPsTime()))
                                .orderByAsc(TSceneParameterEntity::getSceneMonth));
                        BigDecimal reduce = list.stream().map(TSceneParameterEntity::getSceneEle).reduce(BigDecimal.ZERO, BigDecimal::add);
                        // 理论小时数
                        record.setHourBudget(reduce);
                        // 理论小时数*装机容量 = 理论发电量 mw->wkw   *10
                        BigDecimal theoryEtd = new BigDecimal(entity.getCapacity()).multiply(reduce).divide(new BigDecimal(10000000));
                        powerBudget = powerBudget.add(theoryEtd);

                    }
                }
                // 预算小时数 = 各电站理论发电量/总装机容量
                record.setHourBudget(powerBudget.divide(psCapacity, 4, RoundingMode.HALF_UP).multiply(new BigDecimal(10)).setScale(2, BigDecimal.ROUND_HALF_UP));

                // 预算发电量(万kWh)
                record.setPowerBudget(powerBudget);


                if (powerPractical != null && powerPractical.compareTo(BigDecimal.ZERO) > 0 && record.getPsCapacity() != null && record.getPsCapacity().compareTo(BigDecimal.ZERO) > 0) {
                    record.setHourPractical(powerPractical.multiply(new BigDecimal(10)).divide(record.getPsCapacity(), 2, RoundingMode.HALF_UP));
                }
                if (record.getHourPractical() != null && record.getHourBudget() != null && record.getHourPractical().compareTo(BigDecimal.ZERO) > 0 && record.getHourBudget().compareTo(BigDecimal.ZERO) > 0) {
                    record.setPercentage(record.getHourPractical().multiply(new BigDecimal(100)).divide(record.getHourBudget(), 2, RoundingMode.HALF_UP));
                }
            }
        }
        return iPage;


    }
}
