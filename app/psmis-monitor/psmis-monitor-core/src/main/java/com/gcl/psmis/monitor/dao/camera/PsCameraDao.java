package com.gcl.psmis.monitor.dao.camera;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.gcl.psmis.framework.common.req.camera.BusinessPsReq;
import com.gcl.psmis.framework.common.req.camera.PsCameraReq;
import com.gcl.psmis.framework.common.resp.camera.CameraResp;
import com.gcl.psmis.framework.common.vo.ps.BusinessPsVO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <AUTHOR>
 */
@Mapper
public interface PsCameraDao {
    /**
     * 查询已配置摄像头工商业电站分页列表
     * @param req 入参
     * @param page 分页
     * @return
     */
    Page<BusinessPsVO> getBusinessPs(@Param("req") BusinessPsReq req, @Param("page") Page<BusinessPsVO> page);

    /**
     * 查询已配置摄像头和电站关系列表
     * @param req 入参
     * @return
     */
    List<CameraResp> getCameras(@Param("req") PsCameraReq req);
}
