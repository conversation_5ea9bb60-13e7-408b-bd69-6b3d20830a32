package com.gcl.psmis.monitor.dao.invoice;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.gcl.psmis.framework.common.dto.invoice.ImportInvoiceDTO;
import com.gcl.psmis.framework.common.dto.invoice.QueryInvoiceDTO;
import com.gcl.psmis.framework.common.dto.invoice.QueryOffsetDTO;
import com.gcl.psmis.framework.common.req.invoice.CancelInvoiceQueryReq;
import com.gcl.psmis.framework.common.req.invoice.InvoiceHisExpReq;
import com.gcl.psmis.framework.common.req.invoice.InvoiceHisReq;
import com.gcl.psmis.framework.common.req.invoice.InvoiceManagerReq;
import com.gcl.psmis.framework.common.resp.invoice.*;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <AUTHOR> @Description
 * @Date 2024/5/16
 * @Param
 **/
@Mapper
public interface InvoiceDao {


    IPage<CancelInvoiceVo> getCancelInvoiceList(@Param("req") CancelInvoiceQueryReq req, @Param("page") Page<CancelInvoiceVo> page);

    IPage<InvoiceVo> getInvoiceList(@Param("req") InvoiceManagerReq req, @Param("page") Page<InvoiceVo> page);

    IPage<OffsetDetailVo> getOffsetDetailList(@Param("dto") QueryOffsetDTO dto, @Param("page") Page<OffsetDetailVo> page);

    IPage<InvoiceDetailInfoVo> getInvoiceDetailInfo(@Param("dto") QueryInvoiceDTO dto,@Param("page") Page<InvoiceDetailInfoVo> page);

    IPage<InvoiceHisVo> getInvoiceHisList(@Param("page") Page<InvoiceHisVo> page,@Param("req")  InvoiceHisReq req);

    List<InvoiceHisExpVo> exportInvoiceHis(@Param("req")  InvoiceHisReq req);
    List<BaseDetailInvoiceTableVo> exportBaseInvoiceHis(@Param("req")  InvoiceHisReq req);

    InvoiceVo getDetailRecord(@Param("invoiceDTO")  ImportInvoiceDTO invoiceDTO);

    List<BaseDetailInvoiceVo> exportBaseDetailInvoice(@Param("hisReq") InvoiceHisReq hisReq);

    List<BaseDetailInvoiceVo> exportBaseDetailInvoiceMerge(@Param("hisReq") InvoiceHisReq hisReq);
}
