package com.gcl.psmis.monitor.dao.insurance;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.gcl.psmis.framework.common.req.insurance.InsuranceOrderLossPageReq;
import com.gcl.psmis.framework.common.vo.insurance.CompanyPayoutPageVO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

/**
 * <AUTHOR>
 * @date 2024/8/5 14:30
 */
@Mapper
public interface InsuranceClaimsDao {

    /**
     * 公司赔付分页列表
     */
    IPage<CompanyPayoutPageVO> getPage(@Param("req") InsuranceOrderLossPageReq req, @Param("page") Page<CompanyPayoutPageVO> page);
}
