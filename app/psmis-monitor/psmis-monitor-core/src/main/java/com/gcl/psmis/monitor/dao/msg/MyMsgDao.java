package com.gcl.psmis.monitor.dao.msg;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.gcl.psmis.framework.common.req.msg.FlowListReq;
import com.gcl.psmis.framework.common.req.msg.MyMsgReq;
import com.gcl.psmis.framework.common.req.msg.NodeMsgConfigReq;
import com.gcl.psmis.framework.common.resp.msg.FlowListResp;
import com.gcl.psmis.framework.common.resp.msg.MyMsgResp;
import com.gcl.psmis.framework.mbg.entity.TDictEntity;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * project: MyMsgDao
 * Powered 2023-12-20 14:12:39
 *
 * <AUTHOR>
 * @version 1.0
 * @since 1.8
 */
@Mapper
public interface MyMsgDao {
    IPage<MyMsgResp> findMyMsg(@Param("page") Page<MyMsgResp> page, @Param("myMsgReq") MyMsgReq myMsgReq);

    String getFlowTaskId(@Param("orderNo") String orderNo);

    List<FlowListResp> getFlowList(@Param("flowListReq") FlowListReq flowListReq);

    List<MyMsgResp> findMyMsgNum(@Param("myMsgReq") MyMsgReq myMsgReq);

    IPage<MyMsgResp> findCopyMe(@Param("page") Page<MyMsgResp> page, @Param("myMsgReq") MyMsgReq myMsgReq);

    List<MyMsgResp> findCopyMeNum(@Param("myMsgReq") MyMsgReq myMsgReq);

    List<NodeMsgConfigReq.NodeMsgConfigInfo> getNodeMsgConfig(@Param("nodeCode")  String nodeCode);

    List<TDictEntity> findDict(@Param("code") String code);
}
