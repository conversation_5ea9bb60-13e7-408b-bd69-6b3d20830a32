package com.gcl.psmis.monitor.dao.alarm;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.gcl.psmis.framework.common.req.alarm.AlarmReq;
import com.gcl.psmis.framework.common.resp.alarm.AlarmResp;
import com.gcl.psmis.framework.common.vo.AlarmRuleVO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

/**
 * project: AlarmRuleDao
 * Powered 2023-08-02 11:17:33
 *
 * <AUTHOR>
 * @version 1.0
 * @since 1.8
 */
@Mapper
public interface AlarmRuleDao {

    IPage<AlarmResp> getRuleInquire(@Param("alarmReq") AlarmReq alarmReq, @Param("page") Page<AlarmResp> page);

    AlarmResp getRuleDetail(@Param("id") Integer id);

    Integer addRule(AlarmRuleVO alarmRuleVO);

    Integer updateState(@Param("id") Integer id,@Param("enable") Integer enable);

    Integer updateAlarm(AlarmResp alarmResp);
}
