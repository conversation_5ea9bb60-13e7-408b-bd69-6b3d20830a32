package com.gcl.psmis.monitor.dao.powerstation.statement;

import cn.hutool.core.date.DateTime;
import cn.hutool.poi.excel.sax.AttributeName;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.gcl.psmis.framework.common.req.statement.PsStatementReq;
import com.gcl.psmis.framework.common.resp.statement.ProvinceStatementResp;
import com.gcl.psmis.framework.common.resp.statement.PsResp;
import com.gcl.psmis.framework.common.resp.statement.PsStatementResp;
import com.gcl.psmis.framework.mbg.entity.TPowerStationEntity;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

/**
 * project: PsStatementDao
 * Powered 2024-03-06 15:12:10
 *
 * <AUTHOR>
 * @version 1.0
 * @since 1.8
 */
@Mapper
public interface PsStatementDao {

    IPage<PsStatementResp> psList(@Param("page") Page<PsStatementResp> page, @Param("psStatementReq") PsStatementReq psStatementReq);

    IPage<PsStatementResp> psList2(@Param("page") Page<PsStatementResp> page, @Param("psStatementReq") PsStatementReq psStatementReq);

    IPage<PsStatementResp> psYearList(@Param("page") Page<PsStatementResp> page, @Param("psStatementReq") PsStatementReq psStatementReq);


    IPage<PsStatementResp> psYearList2(@Param("page") Page<PsStatementResp> page, @Param("psStatementReq") PsStatementReq psStatementReq);

    IPage<ProvinceStatementResp> provinceList(@Param("page") Page<ProvinceStatementResp> page, @Param("psStatementReq") PsStatementReq psStatementReq);

    ProvinceStatementResp findProvince(@Param("provinceId") Long provinceId, @Param("psTime") String psTime, @Param("pickYear") Integer pickYear, @Param("s") String s);

    ProvinceStatementResp findProvince2(@Param("psStatementReq") PsStatementReq psStatementReq);


    BigDecimal findProvinceCapacity(@Param("provinceId") Long provinceId, @Param("psTime") String psTime, @Param("provinceCompanyCode") String provinceCompanyCode);

    IPage<ProvinceStatementResp> companyList(@Param("page") Page<ProvinceStatementResp> page, @Param("psStatementReq") PsStatementReq psStatementReq);

    ProvinceStatementResp findCompany(@Param("provinceCompanyCode") String provinceCompanyCode, @Param("psTime") String psTime, @Param("pickYear") Integer pickYear, @Param("s") String s);

    Double findCompanyCapacity(@Param("provinceCompanyCode") String provinceCompanyCode, @Param("psTime") String psTime);

    ProvinceStatementResp findProvinceYear(@Param("provinceId") Long provinceId, @Param("psTime") String psTime, @Param("pickYear") Integer pickYear);

    ProvinceStatementResp findProvinceYearNow(@Param("provinceId") Long provinceId, @Param("psTime") String psTime, @Param("pickYear") Integer pickYear, @Param("s") String yearTime);


    ProvinceStatementResp findProvinceYearById(@Param("id") Long id, @Param("s") String yearTime, @Param("year") int year);

    ProvinceStatementResp findProvinceYearById2(@Param("psStatementReq") PsStatementReq psStatementReq);

    List<TPowerStationEntity> getCompanyPsList(@Param("startTime")Date startTime,@Param("endTime") Date endTime,@Param("provinceCompanyCode") String provinceCompanyCode,@Param("provinceId") Long provinceId);
}
