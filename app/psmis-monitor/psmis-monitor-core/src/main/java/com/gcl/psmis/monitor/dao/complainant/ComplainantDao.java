package com.gcl.psmis.monitor.dao.complainant;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.gcl.psmis.framework.common.req.complainant.ComplainantListReq;
import com.gcl.psmis.framework.common.vo.complainant.ComplainantListVO;
import com.gcl.psmis.framework.mbg.entity.TGdFillDetailEntity;
import com.gcl.psmis.framework.mbg.entity.TOperationComplainantEntity;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

@Mapper
public interface ComplainantDao {
    IPage<ComplainantListVO> getComplainantList(Page<ComplainantListVO> page, @Param("params") ComplainantListReq req);

    TGdFillDetailEntity getFillDetailByOperationNo(@Param("operationNo") String operationNo);

    List<TOperationComplainantEntity> getCount();
}
