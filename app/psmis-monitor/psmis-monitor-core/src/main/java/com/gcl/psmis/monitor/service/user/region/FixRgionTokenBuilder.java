package com.gcl.psmis.monitor.service.user.region;

import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.List;

/**
 *固定两级region
 */
@Component
public class FixRgionTokenBuilder implements ITokenBuilder{

    @Override
    public boolean suport(String regionName) {
        return true;
    }

    @Override
    public List<String> buildToken(String regionName) {
        List<String> result = new ArrayList<>();
        int index = regionName.indexOf("省");
        if(index<=0){
            index = regionName.indexOf("自治区")+2;
        }
        if(index<=0){
            index = regionName.indexOf("市");
        }


        index = index +1;
        String level1 = regionName.substring(0,index);
        String level2 = regionName.substring(index);

        result.add(level1);
        result.add(level2);

        return result;
    }

}
