package com.gcl.psmis.monitor.dao.inverter;

import com.gcl.psmis.framework.common.req.inverter.InverterPositionReq;
import com.gcl.psmis.framework.common.req.inverter.InverterRegionReq;
import com.gcl.psmis.framework.common.resp.inverter.InverterRankResp;
import com.gcl.psmis.framework.common.resp.inverter.RegionInverterVO;
import com.gcl.psmis.framework.common.vo.InverterPsMessageVO;
import com.gcl.psmis.framework.common.vo.RegionVO;
import com.gcl.psmis.framework.common.vo.Regionn;
import com.gcl.psmis.framework.common.vo.inverter.RegionIdVO;
import com.gcl.psmis.framework.common.vo.patrol.PsMessageVO;
import com.gcl.psmis.framework.taos.domain.entity.PsIotStInverterEntity;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * project: InverterRankDao
 * Powered 2023-09-11 14:20:42
 *
 * <AUTHOR>
 * @version 1.0
 * @since 1.8
 */
@Mapper
public interface InverterRankDao {


    List<Regionn> patrolByPsType(InverterRegionReq inverterRegionReq);

    List<InverterPsMessageVO> showInverter();

    List<Regionn> patrolByPsTypeCity(@Param("id") Long id,InverterRegionReq inverterRegionReq);

    List<String> findInverterSn();

    List<InverterRankResp> inquireInverter(@Param("currentFormatted") String currentFormatted, @Param("fiveMinutesAgoFormatted") String fiveMinutesAgoFormatted);

    List<PsMessageVO> showPs(InverterRegionReq inverterRegionReq);

    List<Regionn> patrolByPsTypeDistrictId(@Param("id") Long id, InverterRegionReq inverterRegionReq);

    List<Regionn> patrolByPsTypeTownId(@Param("id") Long id, InverterRegionReq inverterRegionReq);

    List<Regionn> getProvinces();

    List<Regionn> getCitiesByProvinceId(@Param("id") Integer id);

    List<Regionn> getDistrictsByCityId(@Param("id") Integer id);

    Integer findCapacity(@Param("sn") String s);

    String findAddress(@Param("sn") String s);

    List<String> findInventerAllSn(@Param("params") InverterPositionReq params);

    RegionIdVO findRegionIdVO(@Param("sn") String sn);

    List<RegionVO> getInverterProvinces(@Param("psType") Integer psType);

    List<RegionVO> getCities(@Param("id") Integer id, @Param("psType") Integer psType);

    List<RegionVO> getDistricts(@Param("id") Integer id, @Param("psType") Integer psType);

    List<RegionInverterVO> findRegion(InverterRegionReq inverterRegionReq);

    List<RegionInverterVO> findInverter(InverterRegionReq inverterRegionReq);

    List<RegionInverterVO> findRegionCity(InverterRegionReq inverterRegionReq);

    List<RegionInverterVO> findRegionArea(InverterRegionReq inverterRegionReq);

    List<RegionInverterVO> findRegionTown(InverterRegionReq inverterRegionReq);

    List<RegionIdVO> findRegionIdVOs(@Param("snLists") List<String> snLists);

    List<PsIotStInverterEntity> findPower( @Param("currentFormatted") String currentFormatted, @Param("fiveMinutesAgoFormatted") String fiveMinutesAgoFormatted);
}
