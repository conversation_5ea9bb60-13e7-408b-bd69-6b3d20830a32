package com.gcl.psmis.monitor.dao.insurance;

import com.baomidou.dynamic.datasource.annotation.DS;
import com.gcl.psmis.framework.common.vo.insurance.AgentVO;
import com.gcl.psmis.framework.common.vo.insurance.InsurancePsDetailVO;
import com.gcl.psmis.framework.common.vo.insurance.ProCompanyVO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <AUTHOR>
 */
@Mapper
public interface InsuranceOrderDao {

    @DS("xyg")
    List<InsurancePsDetailVO> getPsDetail(@Param("psCodes") List<String> psCodes);

    @DS("xyg")
    List<ProCompanyVO> getCompany();

    @DS("xyg")
    List<AgentVO> getAgent();
}
