package com.gcl.psmis.monitor.dao.maintenance;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.gcl.psmis.framework.common.req.maintenance.BreakdownReq;
import com.gcl.psmis.framework.common.req.maintenance.InefficientReq;
import com.gcl.psmis.framework.common.resp.maintenance.BreakdownResp;
import com.gcl.psmis.framework.common.resp.maintenance.InefficientResp;
import com.gcl.psmis.framework.taos.domain.entity.PsIotAlarmStInverterEntity;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;
import java.util.Set;

@Mapper
public interface BreakdownDao {
    IPage<BreakdownResp> breakdownList(@Param("page") Page<PsIotAlarmStInverterEntity> page, @Param("breakdownReq") BreakdownReq breakdownReq);

    List<BreakdownResp> getRespById(@Param("idList") Set<Long> idList);

    List<BreakdownResp> getAllSn(@Param("breakdownReq") BreakdownReq breakdownReq);
}
