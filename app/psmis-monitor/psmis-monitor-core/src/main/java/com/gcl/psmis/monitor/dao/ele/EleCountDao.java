package com.gcl.psmis.monitor.dao.ele;

import com.baomidou.mybatisplus.annotation.InterceptorIgnore;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.gcl.psmis.framework.common.dto.ele.SceneRoofDTO;
import com.gcl.psmis.framework.common.dto.ele.TheoryHoursByProvinceDTO;
import com.gcl.psmis.framework.common.dto.ele.TheoryHoursDTO;
import com.gcl.psmis.framework.common.req.ele.EleCountByProvinceReq;
import com.gcl.psmis.framework.common.req.ele.EleCountReq;
import com.gcl.psmis.framework.common.resp.ele.*;
import com.gcl.psmis.framework.mbg.entity.TSceneParameterEntity;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @ClassName EleCountDao
 * @description: TODO
 * @date 2023年11月15日
 * @version: 1.0
 */
@Mapper
public interface EleCountDao {

    /**
     * @param eleCountReq
     * @param page
     * @return
     */
    IPage<EleMonthCountResp> eleMonthStatistics(@Param("eleCountReq") EleCountReq eleCountReq, @Param("page") Page<EleMonthCountResp> page);

    List<Long> eleMonthStatisticsIds(@Param("eleCountReq") EleCountReq eleCountReq);

    /**
     * @param eleCountReq
     * @param page
     * @return
     */
    IPage<EleQuarterCountResp> eleQuarterStatistics(@Param("eleCountReq") EleCountReq eleCountReq, @Param("page") Page<EleQuarterCountResp> page);
    List<Long> eleQuarterStatisticsIds(@Param("eleCountReq") EleCountReq eleCountReq);

    /**
     * @param eleCountReq
     * @param page
     * @return
     */
    IPage<EleYearCountResp> eleYearStatistics(@Param("eleCountReq") EleCountReq eleCountReq, @Param("page") Page<EleYearCountResp> page);
    List<Long> eleYearStatisticsIds(@Param("eleCountReq") EleCountReq eleCountReq);

    /**
     * @param eleCountReq
     * @return
     */
    List<TheoryHoursDTO> getTheoryHours(@Param("eleCountReq") EleCountReq eleCountReq);

    /**
     * @param eleCountReq
     * @param page
     * @return
     */
    IPage<EleWeekCountResp> eleWeekStatistics(@Param("eleCountReq") EleCountReq eleCountReq, @Param("page") Page<EleWeekCountResp> page);

    List<Long> eleWeekStatisticsIds(@Param("eleCountReq") EleCountReq eleCountReq);

    /**
     * @param startTime
     * @param endTime
     */
    List<TheoryHoursDTO> eleHoursByDay(@Param("startTime") String startTime, @Param("endTime") String endTime);

    /**
     * @param startTime
     * @param endTime
     */
    @InterceptorIgnore(tenantLine = "true")
    List<TheoryHoursDTO> eleHoursByDay2(@Param("startTime") Date startTime, @Param("endTime") Date endTime);

    /**
     * @param eleCountReq
     * @param page
     * @return
     */
    IPage<EleCountByProvinceResp> provinceStatistics(@Param("eleCountReq") EleCountByProvinceReq eleCountReq, @Param("page") Page<EleCountByProvinceResp> page);

    /**
     * 分页获取省报表数据
     * @param page 分页
     * @param req 请求参数
     * @return page
     */
    IPage<EleCountByProvinceResp> provincePage(@Param("page") IPage<EleCountByProvinceResp> page, @Param("req") EleCountByProvinceReq req);

    List<EleCountByProvinceResp> provinceStatisticsNoPage(@Param("eleCountReq") EleCountByProvinceReq eleCountReq);

    List<TheoryHoursDTO> getTheoryHours2(@Param("eleCountReq") EleCountReq eleCountReq);

    @InterceptorIgnore(tenantLine = "true")
    List<SceneRoofDTO> listGroupByScenceCode();


    /**
     * 获取省报表合计数据
     *
     * @param req 请求参数
     * @return EleCountByProvinceResp
     */
    EleCountByProvinceResp provincePageSum(@Param("req") EleCountByProvinceReq req);

    List<PsRoofResp> getPsRoofInfo(@Param("psId") Long psId);
}
