package com.gcl.psmis.monitor.dao.sys;

import com.baomidou.dynamic.datasource.annotation.DS;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.gcl.psmis.framework.common.req.sys.DataScopeReq;
import com.gcl.psmis.framework.common.vo.powerstation.PsUserVO;
import com.gcl.psmis.framework.common.vo.sys.UserAreaVO;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 数据权限配置相关
 *
 * <AUTHOR>
 */
public interface DataScopeDao {

    List<UserAreaVO> listUserArea(DataScopeReq req);

    String findOrgIdByUserId(String userId);

    List<PsUserVO> getSelectedPs(DataScopeReq req);

    Page<PsUserVO> getNoSelectedPs(@Param("req") DataScopeReq req, @Param("page") Page<PsUserVO> page);

    String getOrgTreeStr(String userId);

    String findOrgTypeById(String orgId);
}
