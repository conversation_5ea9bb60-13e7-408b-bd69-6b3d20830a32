package com.gcl.psmis.monitor.service.fr;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.date.DatePattern;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import com.baomidou.dynamic.datasource.annotation.DSTransactional;
import com.gcl.framework.data.dto.UserDTO;
import com.gcl.framework.data.holder.CurrentUserHolder;
import com.gcl.psmis.framework.common.ResponseResult;
import com.gcl.psmis.framework.common.constant.CommonResultCode;
import com.gcl.psmis.framework.common.dto.nkbi.FillFlowAuditPersonDTO;
import com.gcl.psmis.framework.common.dto.nkbi.FillFlowDTO;
import com.gcl.psmis.framework.common.dto.nkbi.FlowCompanyDTO;
import com.gcl.psmis.framework.common.exception.BussinessException;
import com.gcl.psmis.framework.gencode.enums.GenRuleCode;
import com.gcl.psmis.framework.gencode.util.GenCodeUtil;
import com.gcl.psmis.framework.mbg.entity.TFillingFlowEntity;
import com.gcl.psmis.framework.mbg.entity.TFlowVsBusinessEntity;
import com.gcl.psmis.framework.mbg.service.TFlowVsBusinessService;
import com.gcl.psmis.workflow.api.WorkFlowRpcService;
import com.gcl.psmis.workflow.constant.ProcessKeyEnum;
import com.gcl.psmis.workflow.constant.task.FlowNodeEnum;
import com.gcl.psmis.workflow.constant.task.SingleWorkFlowEnum;
import com.gcl.psmis.workflow.constant.task.SumWorkFlowEnum;
import com.gcl.psmis.workflow.domain.dto.FlowBizDTO;
import com.gcl.psmis.workflow.domain.req.ProcessCompleteReq;
import com.gcl.psmis.workflow.domain.req.StartProcessInstanceReq;
import com.xxl.job.core.context.XxlJobHelper;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;


/**
 * <AUTHOR>
 * @date 2022/05/09
 * @description 人资单体填报流程发起
 */
@Service
@Slf4j
@RequiredArgsConstructor
public class FrWorkFlowService {
    @Autowired
    private WorkFlowRpcService workFlowRpcService;
    @Autowired
    private TFlowVsBusinessService tFlowVsBusinessService;
    @Autowired
    private FrFillingFlowService frFillingFlowService;
    @Autowired
    private GenCodeUtil genCodeUtil;
    @Autowired
    private FrDataSumService frDataSumService;

    /**
     * 启动流程方法
     * 该方法根据提供的公司代码、组织类型、指数类型和操作者信息，启动一个财务汇报流程
     *
     * @param fillCompanyCode 填报公司代码，标识启动流程的公司
     * @param sumCompanyCode  汇总公司代码，可能与填报公司代码不同
     * @param sumOrgType      汇总组织类型，定义了组织的结构
     * @param indexType       指数类型，1代表计划，2代表实际，影响流程的类型和时间范围
     * @param yearMonthly
     */
    @DSTransactional
    public void flowStart(String fillCompanyCode, String sumCompanyCode, String sumOrgType, Integer indexType, String yearMonthly) {

        // 根据指数类型确定流程名称
        String flowName = indexType.equals(1) ? "汇总上报计划流程" : "汇总上报实际流程";

        // 记录流程初始化开始
        logInitProcessStart(flowName);

        // 获取流程适用的年月，计划流程使用下月，实际流程使用上月
        if (StrUtil.isBlank(yearMonthly)) {
            if (indexType.equals(2)) {
                yearMonthly = DateUtil.format(DateUtil.lastMonth(), DatePattern.NORM_MONTH_PATTERN);
            } else {
                yearMonthly = DateUtil.format(DateUtil.nextMonth(), DatePattern.NORM_MONTH_PATTERN);
            }
        }
        // 检查中转表数据是否同步完成
        if (!frFillingFlowService.verifySync(yearMonthly)) {
            logErrorAndThrowException("中转表数据未同步完成，请稍后重试");
        }

        // 获取单体填报流程公司数据
        List<FlowCompanyDTO> singleFlows = frFillingFlowService.getFlowCompanyDTOS(1);

        // 将单体流程数据按填报公司代码分组
        Map<String, List<FlowCompanyDTO>> singleFlowMap = new HashMap<>();
        if (CollUtil.isNotEmpty(singleFlows)) {
            singleFlowMap = singleFlows.stream().collect(Collectors.groupingBy(FlowCompanyDTO::getSumCompanyCode));
        }

        // 创建并配置公司DTO对象
        FlowCompanyDTO companyDTO = frFillingFlowService.getFlowCompanyDTO(sumCompanyCode, fillCompanyCode, sumOrgType, 2);
        if (StrUtil.isBlank(companyDTO.getFillCompanyCode())) {
            logErrorAndThrowException("未找到对应的填报公司");
        }
        // 获取填报公司审核人员信息
        List<FillFlowAuditPersonDTO> auditPerson = frFillingFlowService.getAuditPersons(sumCompanyCode, fillCompanyCode, sumOrgType, 2);
        companyDTO.setAuditPerson(auditPerson);

        // 启动流程
        startFlow(companyDTO, 2, ProcessKeyEnum.SUM_FILLING_FLOW, indexType, yearMonthly, singleFlowMap, flowName);

        // 记录流程完成
        logProcessCompletion(flowName);
    }

    /**
     * 发起流程
     *
     * @param companyDTO     公司数据传输对象，包含填报公司和汇总公司的代码
     * @param processKeyEnum 流程关键字枚举，用于标识不同的流程
     * @param indexType      指标类型
     * @param yearMonthly    年月字符串，用于指定流程的时间范围
     * @param singleFlowMap
     * @param flowName
     */
    public void startFlow(FlowCompanyDTO companyDTO, int flowType, ProcessKeyEnum processKeyEnum, int indexType
            , String yearMonthly, Map<String, List<FlowCompanyDTO>> singleFlowMap, String flowName) {

        // 校验当月流程是否已经发起
        checkDuplication(companyDTO, flowType, indexType, yearMonthly, flowName);

        // 如果是汇总流程， 校验单体填报是否完成 没有完成直接跳过
//        if (flowType == 2 && !frFillingFlowService.checkSingleFlowStatus(companyDTO, singleFlowMap, indexType, yearMonthly)) {
//            throw new BussinessException("未完成单体填报，请先完成单体填报");
//        }

        // 1、获取填报公司code
        String sumCompanyCode = companyDTO.getSumCompanyCode();
        if (flowType == 2) {
            sumCompanyCode = companyDTO.getFillCompanyCode();
        }
        // 3 、根据流程公司数据  获取所有填报公司详细数据
        List<FillFlowDTO> fillFlowDTOS = frFillingFlowService.getFillFlowByCompanyCode(companyDTO.getFillCompanyCode(),
                sumCompanyCode, yearMonthly, flowType, companyDTO.getSumOrgType(),companyDTO.getSpecFlag());


        // 如果没有数据，则跳过该流程
        if (CollUtil.isEmpty(fillFlowDTOS)) {
            throw new BussinessException("未查询到填报公司" + companyDTO.getFillCompanyCode() + "指标数据，请检查流程配置");
//            if (!checkDataIntegrity(companyDTO, flowType, yearMonthly)) {
//                throw new BussinessException("未查询到填报公司" + companyDTO.getFillCompanyCode() + "指标数据，请检查流程配置");
//            }
        }
        String persons = getPersons(companyDTO);
        // 汇总状态
        Integer sumStatus = flowType == 1 ? 1 : null;

        //  获取当前用户信息
        UserDTO userDTO = CurrentUserHolder.getUserDTOThreadLocal();
        String flowNo = getFlowNo(processKeyEnum.name());
        // 流程实体主数据
        TFillingFlowEntity build = frFillingFlowService.getTFillingFlowEntity(companyDTO, flowType, processKeyEnum, indexType, yearMonthly, sumStatus, userDTO, flowNo);
        // 发起流程
        startWorkFlow(processKeyEnum, build, persons, userDTO, flowName);

        //保存流程业务数据
        frFillingFlowService.saveFlowData(build, fillFlowDTOS, companyDTO.getFillCompanyCode());
        // log
        XxlJobHelper.log(String.format("%s创建单体上报计划流程成功!", build.getFlowNo()));
    }

    /**
     * 校验当月流程是否已经发起
     *
     * @param companyDTO
     * @param flowType
     * @param indexType
     * @param yearMonthly
     * @param flowName
     */
    private void checkDuplication(FlowCompanyDTO companyDTO, int flowType, int indexType, String yearMonthly, String flowName) {
        List<TFillingFlowEntity> tFillingFlowEntities = frFillingFlowService.getTFillingFlowEntities(yearMonthly, flowType, indexType,
                companyDTO.getFillCompanyCode(), companyDTO.getSumCompanyCode(), companyDTO.getSumOrgType());

        if (CollUtil.isNotEmpty(tFillingFlowEntities)) {
            throw new BussinessException(String.format("当月流程已经发起，%s创建" + flowName + "失败!", companyDTO.getFillCompanyCode()));
        }
    }


    /**
     * 获取审核人员
     *
     * @param companyDTO
     * @return
     */
    private String getPersons(FlowCompanyDTO companyDTO) {
        String persons = "";
        List<FillFlowAuditPersonDTO> auditPerson = companyDTO.getAuditPerson();
        if (CollUtil.isEmpty(auditPerson)) {
            return persons;
        }
        List<String> auditCodes = auditPerson.stream().filter(audit -> audit.getNodeCode().equals(1))
                .map(FillFlowAuditPersonDTO::getAuditCode).collect(Collectors.toList());
        if (CollUtil.isNotEmpty(auditCodes)) {
            persons = StrUtil.join(",", auditCodes);
        }
        return persons;
    }


    /**
     * 校验数据完整性
     *
     * @param companyDTO
     * @param flowType
     * @param yearMonthly
     * @return
     */
    private boolean checkDataIntegrity(FlowCompanyDTO companyDTO, int flowType, String yearMonthly) {
        if (flowType == 1) {
            XxlJobHelper.log("未查询到填报公司" + companyDTO.getFillCompanyCode() + "详细数据，请检查流程配置");
            return false;
        } else {
            // 校验单体填报是否有数据，如果没有，跳过
            int count = frFillingFlowService.getCountByYearMonthlyAndParentCodeAndSumOrgType(yearMonthly,
                    companyDTO.getFillCompanyCode(), companyDTO.getSumOrgType());
            if (count == 0) {
                XxlJobHelper.log("未查询到填报公司" + companyDTO.getFillCompanyCode() + "详细数据，请检查流程配置");
                return false;
            }
        }
        return true;
    }

    /**
     * 获取流程编号
     *
     * @param name
     * @return
     */
    private String getFlowNo(String name) {
        String ruleCode = genCodeUtil.getSysCodeByCodeRule(GenRuleCode.FILLING_FLOW);
        return StrUtil.concat(true, name, "_", ruleCode);
    }

    /**
     * 记录错误日志并抛出异常
     */
    private void logErrorAndThrowException(String message) {
        XxlJobHelper.log(message);
        throw new BussinessException(message);
    }

    /**
     * 记录初始化流程开始日志
     */
    private void logInitProcessStart(String flowName) {
        XxlJobHelper.log("中转表数据同步完成，开始发起" + flowName);
    }

    /**
     * 记录流程完成日志
     */
    private void logProcessCompletion(String flowName) {
        XxlJobHelper.log(flowName + "发起完成");
    }


    /**
     * 发起流程
     *
     * @param processKeyEnum
     * @param build
     * @param persons
     * @param operator
     * @param flowName
     */
    public void startWorkFlow(ProcessKeyEnum processKeyEnum, TFillingFlowEntity build, String persons, UserDTO operator, String flowName) {
        StartProcessInstanceReq req = new StartProcessInstanceReq();
        // 发起流程
        req.setAuthenticatedAccount(operator.getEmpNo());
        req.setProcessKey(processKeyEnum);
        req.setBusinessKey(build.getFlowNo());
        Map<String, Object> var = new HashMap<>();
        var.put("person", persons);
        var.put("operatorAccount", operator.getEmpNo());
        var.put("operatorName", operator.getRealName());
        req.setVariables(var);
        ResponseResult defectResult = workFlowRpcService.start(req);
        if (defectResult.getCode() != CommonResultCode.SUCCESS.code()) {
            throw new BussinessException("开启" + flowName + "失败!");
        }
    }

    /**
     * 数据刷新
     *
     * @param fillCompanyCode
     * @param sumCompanyCode
     * @param sumOrgType
     * @param yearMonthly
     * @param flowType
     * @param refreshType
     * @param levelType
     */
    public void flowRefresh(String fillCompanyCode, String sumCompanyCode, String sumOrgType, String yearMonthly, Integer flowType
            , Integer refreshType, String levelType) {
        frFillingFlowService.flowRefresh(fillCompanyCode, sumCompanyCode, sumOrgType, yearMonthly, flowType, refreshType, levelType);
    }

    /**
     * 提交流程
     *
     * @param orderNo
     */
    @DSTransactional
    public void flowSubmit(String orderNo) {
        UserDTO userDTO = CurrentUserHolder.getUserDTOThreadLocal();
        // 待提交->提交 (撤回)——>特殊节点--> 流程结束。
        TFillingFlowEntity tFillingFlowEntity = frFillingFlowService.getFlowByFlowNo(orderNo);
        if (tFillingFlowEntity == null) {
            throw new BussinessException("无表单信息！");
        }
        // 流程名称
        String flowName = tFillingFlowEntity.getFlowName() ;

        // 获取下个节点code
        FlowNodeEnum flowNodeEnum = getFlowNodeEnum(tFillingFlowEntity);

        TFlowVsBusinessEntity one = tFlowVsBusinessService.lambdaQuery().eq(TFlowVsBusinessEntity::getBusinessKey, orderNo).one();
        if (one == null) {
            throw new BussinessException("无当前工单流程信息！");
        }
        // 校验流程状态
        checkFlowStatus(one, userDTO, flowNodeEnum);

        ProcessCompleteReq completeReq = new ProcessCompleteReq();
        completeReq.setProcessInstanceId(one.getProcessInstanceId());
        HashMap<String, Object> map = new HashMap<>();
        completeReq.setFlowNodeEnum(flowNodeEnum);
        // 获取当前节点code审核人
        String person = getPersonsByNodeCode(flowNodeEnum, tFillingFlowEntity);
        map.put("person", person);
        map.put("operatorAccount", userDTO.getEmpNo());
        map.put("operatorName", userDTO.getRealName());
        completeReq.setVariables(map);
        completeReq.setOperatorAccount(userDTO.getEmpNo());
        completeReq.setOperatorName(userDTO.getRealName());
        ResponseResult res = workFlowRpcService.processComplete(completeReq);
        if (res.getCode() != CommonResultCode.SUCCESS.code()) {
            throw new BussinessException(flowName + flowNodeEnum.getDesc() + "提交流程失败!");
        }

        // 更新流程状态
        frFillingFlowService.updateFlowStatus(tFillingFlowEntity, flowNodeEnum, userDTO, null);

        // 更新实际流程中的计划数据
        frFillingFlowService.updatePlanData(tFillingFlowEntity);
    }

    /**
     * 获取审核人
     *
     * @param flowNodeEnum
     * @param tFillingFlowEntity
     * @return
     */
    private String getPersonsByNodeCode(FlowNodeEnum flowNodeEnum, TFillingFlowEntity tFillingFlowEntity) {
        String person = "";
        if (tFillingFlowEntity.getFlowType().equals(1)) {
            person = getSingleAuditPerson(flowNodeEnum, tFillingFlowEntity);
        } else {
            person = getSumAuditPerson(flowNodeEnum, tFillingFlowEntity);
        }
        return person;
    }


    /**
     * 审核
     *
     * @param orderNo
     * @param choose
     */
    @DSTransactional
    public void flowAudit(String orderNo, String choose) {
        UserDTO userDTO = CurrentUserHolder.getUserDTOThreadLocal();
        TFillingFlowEntity tFillingFlowEntity = frFillingFlowService.getFlowByFlowNo(orderNo);
        if (tFillingFlowEntity == null) {
            throw new BussinessException("无表单信息！");
        }
        // 获取下个节点code
        FlowNodeEnum flowNodeEnum = getFlowNodeEnum(tFillingFlowEntity);
        // 待提交->提交 (撤回)——>特殊节点--> 流程结束。
        TFlowVsBusinessEntity one = tFlowVsBusinessService.lambdaQuery().eq(TFlowVsBusinessEntity::getBusinessKey, orderNo).one();
        if (one == null) {
            throw new BussinessException("无当前工单流程信息！");
        }
        // 流程名称
        String flowName = tFillingFlowEntity.getFlowName() ;

        // 校验流程状态
        checkFlowStatus(one, userDTO, flowNodeEnum);
        ProcessCompleteReq completeReq = new ProcessCompleteReq();
        completeReq.setProcessInstanceId(one.getProcessInstanceId());
        HashMap<String, Object> map = new HashMap<>();

        // 获取当前节点code审核人
        String person = getPersonsByNodeCode(flowNodeEnum, tFillingFlowEntity);
        completeReq.setFlowNodeEnum(flowNodeEnum);
        map.put("person", person);
        map.put("choose", choose);
        map.put("operatorAccount", userDTO.getEmpNo());
        map.put("operatorName", userDTO.getRealName());
        completeReq.setVariables(map);
        completeReq.setOperatorAccount(userDTO.getEmpNo());
        completeReq.setOperatorName(userDTO.getRealName());
        ResponseResult res = workFlowRpcService.processComplete(completeReq);
        if (res.getCode() != CommonResultCode.SUCCESS.code()) {
            throw new BussinessException(flowName + flowNodeEnum.getDesc() + "提交流程失败!");
        }
        frFillingFlowService.updateFlowStatus(tFillingFlowEntity, flowNodeEnum, userDTO, choose);

    }

    /**
     * 获取流程节点枚举
     *
     * @param tFillingFlowEntity
     * @return
     */
    public FlowNodeEnum getFlowNodeEnum(TFillingFlowEntity tFillingFlowEntity) {
        Integer flowStatus = tFillingFlowEntity.getFlowStatus();
        Integer flowType = tFillingFlowEntity.getFlowType();
        if (flowType.equals(1)) {
            return getSingleFlowNodeEnum(flowStatus);
        } else if (flowType.equals(2)) {
            return getSumFlowNodeEnum(flowStatus);
        } else {
            throw new BussinessException("流程类型异常！");
        }
    }

    private FlowNodeEnum getSumFlowNodeEnum(Integer flowStatus) {
        // 1-未提交，2-已提交，3-归档，4-已提交撤回,5-流程结束
        if (flowStatus.equals(1)) {
            return SumWorkFlowEnum.ACTIVITY_SUM_FILLING_01;
        } else if (flowStatus.equals(2)) {
            return SumWorkFlowEnum.ACTIVITY_SUM_FILLING_02;
        } else if (flowStatus.equals(3)) {
            return SumWorkFlowEnum.ACTIVITY_SUM_FILLING_03;
        } else if (flowStatus.equals(4)) {
            return SumWorkFlowEnum.ACTIVITY_SUM_FILLING_01;
        }
        return null;
    }

    private FlowNodeEnum getSingleFlowNodeEnum(Integer flowStatus) {
        // 1-未提交，2-已提交，3-归档，4-已提交撤回,5-流程结束
        if (flowStatus.equals(1)) {
            return SingleWorkFlowEnum.ACTIVITY_SINGLE_FILLING_01;
        } else if (flowStatus.equals(2)) {
            return SingleWorkFlowEnum.ACTIVITY_SINGLE_FILLING_02;
        } else if (flowStatus.equals(3)) {
            return SingleWorkFlowEnum.ACTIVITY_SINGLE_FILLING_03;
        } else if (flowStatus.equals(4)) {
            return SingleWorkFlowEnum.ACTIVITY_SINGLE_FILLING_01;
        }
        return null;
    }

    public void checkFlowStatus(TFlowVsBusinessEntity one, UserDTO userDTO, FlowNodeEnum flowNodeEnum) {
        FlowBizDTO flowBizDTO = new FlowBizDTO();
        flowBizDTO.setProcInsId(one.getProcessInstanceId());
        flowBizDTO.setUserNo(userDTO.getEmpNo());
        ResponseResult responseResult = workFlowRpcService.queryTaskByUser(flowBizDTO);
        if (responseResult.getCode() != CommonResultCode.SUCCESS.code()) {
            throw new BussinessException("当前人员无此流程节点代办任务,请确认！");
        }
        JSONObject jsonObj = JSONUtil.parseObj(responseResult.getData());
        String code = jsonObj.getStr("taskDefinitionKey");
        if (!code.equals(flowNodeEnum.getCode())) {
            throw new BussinessException("当前人员的待办节点不对,请确认！");
        }
    }

    /**
     * 汇总流程获取审核人
     *
     * @param flowNodeEnum
     * @param tFillingFlowEntity
     * @return
     */
    public String getSumAuditPerson(FlowNodeEnum flowNodeEnum, TFillingFlowEntity tFillingFlowEntity) {
        String person = "";
        List<String> personList = new ArrayList<>();

        List<FillFlowAuditPersonDTO> auditPerson = frFillingFlowService.getAuditPersons(tFillingFlowEntity.getParentCode(),
                tFillingFlowEntity.getCompanyCode(), tFillingFlowEntity.getSumOrgType(), 2);


        if (flowNodeEnum.equals(SumWorkFlowEnum.ACTIVITY_SUM_FILLING_01) || flowNodeEnum.equals(SumWorkFlowEnum.ACTIVITY_SUM_FILLING_02)) {
            personList = auditPerson.stream().filter(audit -> audit.getNodeCode().equals(1)).map(FillFlowAuditPersonDTO::getAuditCode).collect(Collectors.toList());
        } else if (flowNodeEnum.equals(SumWorkFlowEnum.ACTIVITY_SUM_FILLING_03) || flowNodeEnum.equals(SumWorkFlowEnum.ACTIVITY_SUM_FILLING_04)) {
            personList = auditPerson.stream().filter(audit -> audit.getNodeCode().equals(2)).map(FillFlowAuditPersonDTO::getAuditCode).collect(Collectors.toList());
        }
        if (CollUtil.isNotEmpty(personList)) {
            person = String.join(",", personList);
        }
        return person;
    }

    /**
     * 单独流程获取审核人
     *
     * @param flowNodeEnum
     * @param tFillingFlowEntity
     * @return
     */
    public String getSingleAuditPerson(FlowNodeEnum flowNodeEnum, TFillingFlowEntity tFillingFlowEntity) {
        String person = "";
        List<String> personList = new ArrayList<>();
        List<FillFlowAuditPersonDTO> auditPerson = frFillingFlowService.getAuditPersons(tFillingFlowEntity.getParentCode(),
                tFillingFlowEntity.getCompanyCode(), tFillingFlowEntity.getSumOrgType(), 1);

        if (flowNodeEnum.equals(SingleWorkFlowEnum.ACTIVITY_SINGLE_FILLING_01) || flowNodeEnum.equals(SingleWorkFlowEnum.ACTIVITY_SINGLE_FILLING_02)) {
            personList = auditPerson.stream().filter(audit -> audit.getNodeCode().equals(1)).map(FillFlowAuditPersonDTO::getAuditCode).collect(Collectors.toList());
        } else if (flowNodeEnum.equals(SingleWorkFlowEnum.ACTIVITY_SINGLE_FILLING_03) ) {
            personList = auditPerson.stream().filter(audit -> audit.getNodeCode().equals(2)).map(FillFlowAuditPersonDTO::getAuditCode).collect(Collectors.toList());
        }

        if (CollUtil.isNotEmpty(personList)) {
            return String.join(",", personList);
        }
        return person;
    }

    /**
     * 更新汇集表数据
     *
     * @param orderNo
     */
    public void updateSumFlow(String orderNo) {
        TFillingFlowEntity flowEntity = frFillingFlowService.getFlowByFlowNo(orderNo);
        if (flowEntity != null) {
            // 归档汇总结束 需要将业务数据同步归集表
            frDataSumService.saveFlowDataToDataSum(flowEntity);
        }

    }
}

