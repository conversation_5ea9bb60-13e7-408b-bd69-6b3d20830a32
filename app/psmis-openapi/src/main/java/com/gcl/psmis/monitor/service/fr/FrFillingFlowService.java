package com.gcl.psmis.monitor.service.fr;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.ObjUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.dynamic.datasource.annotation.DS;
import com.gcl.framework.data.dto.UserDTO;
import com.gcl.psmis.framework.common.constant.enums.nkbi.SystemSourceEnum;
import com.gcl.psmis.framework.common.dto.nkbi.FillFlowAuditPersonDTO;
import com.gcl.psmis.framework.common.dto.nkbi.FillFlowDTO;
import com.gcl.psmis.framework.common.dto.nkbi.FlowCompanyDTO;
import com.gcl.psmis.framework.common.dto.nkbi.SourceLogDTO;
import com.gcl.psmis.framework.common.exception.BussinessException;
import com.gcl.psmis.framework.mbg.entity.TFillingFlowDetailEntity;
import com.gcl.psmis.framework.mbg.entity.TFillingFlowEntity;
import com.gcl.psmis.framework.mbg.service.TFillingFlowDetailService;
import com.gcl.psmis.framework.mbg.service.TFillingFlowService;
import com.gcl.psmis.monitor.dao.fr.FrBiDataDao;
import com.gcl.psmis.workflow.constant.ProcessKeyEnum;
import com.gcl.psmis.workflow.constant.task.FlowNodeEnum;
import com.gcl.psmis.workflow.constant.task.SingleWorkFlowEnum;
import com.gcl.psmis.workflow.constant.task.SumWorkFlowEnum;
import com.xxl.job.core.context.XxlJobHelper;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.jetbrains.annotations.NotNull;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.util.*;
import java.util.stream.Collectors;


/**
 * <AUTHOR>
 * @date 2022/05/09
 * @description 人资单体填报流程发起
 */
@Service
@Slf4j
@RequiredArgsConstructor
@DS("hr")
public class FrFillingFlowService {
    @Autowired
    private FrBiDataDao frBiDataDao;
    @Autowired
    private FrDataSumService frDataSumService;
    @Autowired
    private TFillingFlowService fillingFlowService;
    @Autowired
    private TFillingFlowDetailService tFillingFlowDetailService;

    /**
     * 获取流程公司数据列表
     * <p>
     * 此方法用于从数据库中检索流程公司配置信息，并以FlowCompanyDTO对象列表的形式返回
     * 如果没有查询到任何数据，将抛出异常，提示检查流程配置
     *
     * @return FlowCompanyDTO对象列表，包含流程公司配置信息
     * @throws BussinessException 如果未查询到流程公司数据，则抛出此异常
     */
    public @NotNull List<FlowCompanyDTO> getFlowCompanyDTOS(Integer flowId) {
        // 从数据库中获取流程公司配置信息
        List<FlowCompanyDTO> flowCompanyDTOS = frBiDataDao.getFlowCompany(flowId);
        // 检查查询结果是否为空
        if (CollUtil.isEmpty(flowCompanyDTOS)) {
            XxlJobHelper.log("未查询到流程公司数据，请检查流程配置");
            throw new BussinessException("未查询到流程公司数据，请检查流程配置");
        }
        // 返回查询到的流程公司数据列表
        return flowCompanyDTOS;
    }

    /**
     * 判断中转表数据是否同步完成
     *
     * @return 如果数据同步完成则返回true，否则返回false
     */
    public boolean verifySync(String yearMonthly) {
        // 获取当天日期的日志信息
        List<SourceLogDTO> sourceLogDTOList = frBiDataDao.getSourceLogByDateStamp(yearMonthly);
        // 如果日志信息为空，则返回false
        if (CollUtil.isEmpty(sourceLogDTOList)) {
            return false;
        }
        // 如果日志信息数量不为8，则返回false
        if (sourceLogDTOList.size() < 8) {
            return false;
        }
        // 同步状态都为0 则继续执行
        // 将日志信息转换为map，key为系统来源，value为同步状态
        Map<String, String> map = sourceLogDTOList.stream().filter(sourceLogDTO -> !sourceLogDTO.getSystemSource().equals("Transfer"))
                .collect(Collectors.toMap(SourceLogDTO::getSystemSource, SourceLogDTO::getStatus,  (oldValue, newValue) -> newValue));

        // 遍历所有系统来源枚举
        for (SystemSourceEnum typeEnum : SystemSourceEnum.values()) {
            // 如果同步状态不为0，则返回false
            if (!StringUtils.equals(map.get(typeEnum.getCode()), "0")) {
                return false;
            }
        }
        // 所有同步状态都为0，返回true
        return true;
    }

    /**
     * 根据公司编码获取填报流程数据
     *
     * @param fillCompanyCode
     * @param sumCompanyCode
     * @param yearMonthly
     * @param flowType
     * @param sumOrgType
     * @param specFlag
     * @return
     */
    public List<FillFlowDTO> getFillFlowByCompanyCode(String fillCompanyCode, String sumCompanyCode, String yearMonthly, int flowType, String sumOrgType, Integer specFlag) {
        return frBiDataDao.getFillFlowByCompanyCode(fillCompanyCode, sumCompanyCode, yearMonthly, flowType, sumOrgType, specFlag);
    }

    /**
     * 获取流程实体主数据
     *
     * @param companyDTO
     * @param flowType
     * @param processKeyEnum
     * @param indexType
     * @param yearMonthly
     * @param userDTO
     * @param flowNo
     * @return
     */
    public TFillingFlowEntity getTFillingFlowEntity(FlowCompanyDTO companyDTO, int flowType, ProcessKeyEnum processKeyEnum
            , int indexType, String yearMonthly, Integer sumStatus, UserDTO userDTO, String flowNo) {
        TFillingFlowEntity build = TFillingFlowEntity.builder()
                .createByName(userDTO.getRealName())
                .createByNo(userDTO.getEmpNo())
                .updateTime(DateUtil.date())
                .createTime(DateUtil.date())
                .flowNo(flowNo)
                .flowName(processKeyEnum.getDesc())
                .flowType(flowType)
                .indexType(indexType)
                .flowStatus(1)
                .yearMonthly(yearMonthly)
                .companyCode(companyDTO.getFillCompanyCode())
                .parentCode(companyDTO.getSumCompanyCode())
                .sumStatus(sumStatus)
                .sumOrgType(companyDTO.getSumOrgType())
                .build();
        return build;
    }

    /**
     * 校验单体填报是否完成 没有完成直接跳过
     *
     * @param companyDTO
     * @param singleFlowMap
     * @param indexType
     * @param yearMonthly
     * @return
     */
    public boolean checkSingleFlowStatus(FlowCompanyDTO companyDTO, Map<String, List<FlowCompanyDTO>> singleFlowMap, int indexType, String yearMonthly) {

        // 如果流流程公司数据填报层级分类为整体 则跳过验证
        if (companyDTO.getLevelType().equals("ZT")) {
            return true;
        }
        // 获取单体流程公司数据
        List<TFillingFlowEntity> singleFillingFlows = frBiDataDao.getSingleFillingFlows(companyDTO.getFillCompanyCode(), indexType, yearMonthly);
        if (CollUtil.isEmpty(singleFillingFlows)) {
            XxlJobHelper.log("汇总公司【" + companyDTO.getSumCompanyCode() + "】 单体上报流程未完成，跳过汇总上报流程");
            return false;
        }
        // 发起的单体路程数据
        Set<String> singleCompanyCodes = singleFillingFlows.stream().map(TFillingFlowEntity::getCompanyCode).collect(Collectors.toSet());
        List<FlowCompanyDTO> singleFlowDTOS = singleFlowMap.get(companyDTO.getFillCompanyCode());
        // 获取所有单体填报公司详细数据
        if (CollUtil.isEmpty(singleFlowDTOS)) {
            return true;
        }
        // 预设单体流程数据
        Set<String> stringSet = singleFlowDTOS.stream().map(FlowCompanyDTO::getFillCompanyCode).collect(Collectors.toSet());
        if (!singleCompanyCodes.equals(stringSet)) {
            return false;
        }

        return true;
    }

    /**
     * 插入流程业务数据
     *
     * @param flowEntity
     * @param fillFlowDTOS
     * @param fillCompanyCode
     */
    public void saveFlowData(TFillingFlowEntity flowEntity, List<FillFlowDTO> fillFlowDTOS, String fillCompanyCode) {
        // 1 插入如表数据
        flowEntity.insert();

        Long flowId = flowEntity.getId();
        // 单体流程
        if (flowEntity.getFlowType().equals(1)) {
            insertFillingFlowDetail(fillFlowDTOS, flowId, null, flowEntity.getYearMonthly(),flowEntity.getIndexType());
        }
        // 汇总流程
        else {
            insertFillingFlowDetail(fillFlowDTOS, null, flowId, flowEntity.getYearMonthly(), flowEntity.getIndexType());
            Set<String> indexNos = null;
            if (CollUtil.isNotEmpty(fillFlowDTOS)) {
                indexNos = fillFlowDTOS.stream().map(FillFlowDTO::getIndexNo).collect(Collectors.toSet());
            }
            // 更新该区域公司填报状态和汇总流程Id
            updateFillingFlowDetail(flowId, indexNos, flowEntity.getCompanyCode(), flowEntity.getYearMonthly(), flowEntity.getSumOrgType(), flowEntity.getIndexType());

            // 更新单体流程汇总状态
            frBiDataDao.updateSingleFlowSumStatus(fillCompanyCode, flowEntity.getIndexType(), flowEntity.getYearMonthly());

        }
    }

    /**
     * 更新该区域公司填报状态和汇总流程Id
     *
     * @param flowId
     * @param indexNos
     * @param parentCode
     * @param yearMonthly
     * @param sumOrgType
     * @param indexType
     */
    private void updateFillingFlowDetail(Long flowId, Set<String> indexNos, String parentCode, String yearMonthly, String sumOrgType, Integer indexType) {
        frBiDataDao.updateSumFlowId(flowId, indexNos, parentCode, yearMonthly, sumOrgType, indexType);
    }

    /**
     * 插入业务数据
     *
     * @param fillFlowDTOS
     * @param singleFlowId
     * @param yearMonthly
     * @param indexType
     */
    private void insertFillingFlowDetail(List<FillFlowDTO> fillFlowDTOS, Long singleFlowId, Long sumFlowId, String yearMonthly, Integer indexType) {
        if (CollUtil.isEmpty(fillFlowDTOS)) {
            return;
        }
        // 从计划表中获取年度目标、月度分解、月度计划 实际的是
        List<TFillingFlowDetailEntity> planEntities = null;
        if (indexType.equals(2)) { // 获取计划数据
            Integer flowType = singleFlowId == null ? 2 : 1;
            planEntities = frBiDataDao.selectPlanList(yearMonthly, flowType);
        }

        List<TFillingFlowDetailEntity> detailEntities = new ArrayList<>();
        for (FillFlowDTO fillFlowDTO : fillFlowDTOS) {
            BigDecimal yearTarget = Objects.isNull(fillFlowDTO.getYearTarget()) ? BigDecimal.ZERO : fillFlowDTO.getYearTarget();
            BigDecimal monthlyAnalyze = Objects.isNull(fillFlowDTO.getMonthlyAnalyze()) ? BigDecimal.ZERO : fillFlowDTO.getMonthlyAnalyze();
            BigDecimal monthlyPlan = Objects.isNull(fillFlowDTO.getMonthlyPlan()) ? BigDecimal.ZERO : fillFlowDTO.getMonthlyPlan();
            BigDecimal monthlyActual = Objects.isNull(fillFlowDTO.getMonthlyActual()) ? BigDecimal.ZERO : fillFlowDTO.getMonthlyActual();

            // 获取计划数据
            // 年度目标 月度分解 月度计划
            PlanResult result = getPlanData(fillFlowDTO, planEntities, yearTarget, monthlyAnalyze, monthlyPlan,yearMonthly);

            TFillingFlowDetailEntity detailEntity = TFillingFlowDetailEntity.builder()
                    .singleFlowId(singleFlowId)
                    .sumFlowId(sumFlowId)
                    .indexNo(fillFlowDTO.getIndexNo())
                    .indexName(fillFlowDTO.getIndexName())
                    .indexUnit(fillFlowDTO.getIndexUnit())
                    .indexParent(fillFlowDTO.getIndexParent())
                    .levelType(fillFlowDTO.getLevelType())
                    .fillLevel(fillFlowDTO.getFillLevel())
                    .companyCode(fillFlowDTO.getCompanyCode())
                    .parentCode(fillFlowDTO.getParentOrgNo())
                    .yearMonthly(yearMonthly)
                    .yearMonthly(yearMonthly)
                    .yearTarget(result.yearTarget)
                    .monthlyAnalyze(result.monthlyAnalyze)
                    .monthlyPlan(result.monthlyPlan)
                    .monthlyActual(monthlyActual)
                    .planFiledFlag(fillFlowDTO.getPlanFiledFlag())
                    .actualFiledFlag(fillFlowDTO.getActualFiledFlag())
                    .actualValue(fillFlowDTO.getActualValue())
                    .yearTargetSign(fillFlowDTO.getYearTargetSign())
                    .monthActualSign(fillFlowDTO.getMonthActualSign())
                    .monthlyPlanSign(fillFlowDTO.getMonthlyPlanSign())
                    .monthlyAnalyzeSign(fillFlowDTO.getMonthlyAnalyzeSign())
                    .targetSystemSource(fillFlowDTO.getTargetSystemSource())
                    .actualSystemSource(fillFlowDTO.getActualSystemSource())
                    .analyzeSystemSource(fillFlowDTO.getAnalyzeSystemSource())
                    .planSystemSource(fillFlowDTO.getPlanSystemSource())
                    .sumOrgType(fillFlowDTO.getSumOrgType())
                    .createTime(DateUtil.date())
                    .updateTime(DateUtil.date())
                    .yearTargetUpdateTime(DateUtil.date())
                    .monthlyAnalyzeUpdateTime(DateUtil.date())
                    .monthlyPlanUpdateTime(DateUtil.date())
                    .monthlyActualUpdateTime(DateUtil.date())
                    .indexDescribe(fillFlowDTO.getIndexDescribe())
                    .sort(fillFlowDTO.getSort())
                    .configId(fillFlowDTO.getConfigId())
                    .build();
            detailEntities.add(detailEntity);
        }
        // 批量插入明细表
        tFillingFlowDetailService.saveBatch(detailEntities,10000);
    }

    /**
     * 汇总上报流程刷新
     *
     * @param fillCompanyCode
     * @param sumCompanyCode
     * @param sumOrgType
     * @param yearMonthly
     * @param refreshType
     * @param levelType
     */
    public void flowRefresh(String fillCompanyCode, String sumCompanyCode, String sumOrgType, String yearMonthly, Integer flowType, Integer refreshType, String levelType) {
        frBiDataDao.flowRefresh(fillCompanyCode, sumCompanyCode, sumOrgType, yearMonthly, flowType, refreshType, levelType);
    }

    /**
     * 根据工单号获取流程信息
     *
     * @param orderNo
     * @return
     */
    public TFillingFlowEntity getFlowByFlowNo(String orderNo) {
        return frBiDataDao.getFlowByFlowNo(orderNo);
    }


    /**
     * 更新流程状态
     *
     * @param tFillingFlowEntity
     * @param flowNodeEnum
     * @param userDTO
     * @param choose
     */
    public void updateFlowStatus(TFillingFlowEntity tFillingFlowEntity, FlowNodeEnum flowNodeEnum, UserDTO userDTO, String choose) {
        // 单体流程
        if (tFillingFlowEntity.getFlowType().equals(1)) {
            updateSingleFlowStatus(tFillingFlowEntity, flowNodeEnum, userDTO, choose);
        } else {
            // 汇总流程
            updateSumFlowStatus(tFillingFlowEntity, flowNodeEnum, userDTO, choose);
        }

    }

    /**
     * 更新汇总流程状态
     *
     * @param tFillingFlowEntity
     * @param flowNodeEnum
     * @param userDTO
     * @param choose
     */
    public void updateSumFlowStatus(TFillingFlowEntity tFillingFlowEntity, FlowNodeEnum flowNodeEnum, UserDTO userDTO, String choose) {
        //  // 1-未提交，2-已提交，3-归档，4-已提交撤回,5-流程结束
        if (flowNodeEnum.equals(SumWorkFlowEnum.ACTIVITY_SUM_FILLING_01)) {
            frBiDataDao.updateFlowByFlowNo(tFillingFlowEntity.getFlowNo(), 2, userDTO.getEmpNo(), userDTO.getRealName(), DateUtil.date(), DateUtil.date(), null, null);
        } else if (flowNodeEnum.equals(SumWorkFlowEnum.ACTIVITY_SUM_FILLING_02)) {
            if ("1".equals(choose)) {
                frBiDataDao.updateFlowByFlowNo(tFillingFlowEntity.getFlowNo(), 3, null, null, null, DateUtil.date(), userDTO.getEmpNo(), 1);
                // 归档汇总结束 需要将业务数据同步归集表
                frDataSumService.saveFlowDataToDataSum(tFillingFlowEntity);
            } else {
                frBiDataDao.updateFlowByFlowNo(tFillingFlowEntity.getFlowNo(), 4, null, null, null, DateUtil.date(), null, null);
            }
        } else if (flowNodeEnum.equals(SumWorkFlowEnum.ACTIVITY_SUM_FILLING_03)) {
            if ("1".equals(choose)) {
                frBiDataDao.updateFlowByFlowNo(tFillingFlowEntity.getFlowNo(), 5, null, null, null, DateUtil.date(), null, null);
            } else {
                frBiDataDao.updateFlowByFlowNo(tFillingFlowEntity.getFlowNo(), 4, null, null, null, DateUtil.date(), null, null);
            }
        }
    }

    /**
     * 更新单体流程状态
     *
     * @param tFillingFlowEntity
     * @param flowNodeEnum
     * @param userDTO
     * @param choose
     */

    private void updateSingleFlowStatus(TFillingFlowEntity tFillingFlowEntity, FlowNodeEnum flowNodeEnum, UserDTO userDTO, String choose) {
        // 1-未提交，2-已提交，3-归档，4-已提交撤回,5-流程结束
        if (flowNodeEnum.equals(SingleWorkFlowEnum.ACTIVITY_SINGLE_FILLING_01)) {
            frBiDataDao.updateFlowByFlowNo(tFillingFlowEntity.getFlowNo(), 2, userDTO.getEmpNo(), userDTO.getRealName(),
                    DateUtil.date(), DateUtil.date(), null, null);
        } else if (flowNodeEnum.equals(SingleWorkFlowEnum.ACTIVITY_SINGLE_FILLING_02)) {
            if ("1".equals(choose)) {
                frBiDataDao.updateFlowByFlowNo(tFillingFlowEntity.getFlowNo(), 3, null, null, null, DateUtil.date(), userDTO.getEmpNo(), 1);
                // 归档汇总结束 需要将业务数据同步归集表
                frDataSumService.saveFlowDataToDataSum(tFillingFlowEntity);
            } else {
                frBiDataDao.updateFlowByFlowNo(tFillingFlowEntity.getFlowNo(), 4, null, null, null, DateUtil.date(), null, null);
            }
        } else if (flowNodeEnum.equals(SingleWorkFlowEnum.ACTIVITY_SINGLE_FILLING_03)) {
            if ("1".equals(choose)) {
                frBiDataDao.updateFlowByFlowNo(tFillingFlowEntity.getFlowNo(), 5, null, null, null, DateUtil.date(), null, null);
            } else {
                frBiDataDao.updateFlowByFlowNo(tFillingFlowEntity.getFlowNo(), 4, null, null, null, DateUtil.date(), null, null);
            }
        }
    }

    /**
     * 根据汇总上报流程获取数量
     *
     * @param yearMonthly
     * @param fillCompanyCode
     * @param sumOrgType
     * @return
     */
    public int getCountByYearMonthlyAndParentCodeAndSumOrgType(String yearMonthly, String fillCompanyCode, String sumOrgType) {
        return frBiDataDao.getCountByYearMonthlyAndParentCodeAndSumOrgType(yearMonthly, fillCompanyCode, sumOrgType);
    }

    /**
     * 获取汇总上报流程审核人员
     *
     * @param sumCompanyCode
     * @param fillCompanyCode
     * @param sumOrgType
     * @param flowType
     * @return
     */
    public List<FillFlowAuditPersonDTO> getAuditPersons(String sumCompanyCode, String fillCompanyCode, String sumOrgType, Integer flowType) {
        return frBiDataDao.getAuditPersons(sumCompanyCode, fillCompanyCode, sumOrgType, flowType);
    }

    /**
     * 获取汇总上报流程审核人员
     *
     * @param sumCompanyCode
     * @param fillCompanyCode
     * @param sumOrgType
     * @param flowId
     * @return
     */
    public FlowCompanyDTO getFlowCompanyDTO(String sumCompanyCode, String fillCompanyCode, String sumOrgType, Integer flowId) {
        return frBiDataDao.getFlowCompanyDTO(sumCompanyCode, fillCompanyCode, sumOrgType, flowId);
    }

    /**
     * 根据条件获取流程信息
     *
     * @param yearMonthly
     * @param flowType
     * @param indexType
     * @param fillCompanyCode
     * @param sumCompanyCode
     * @param sumOrgType
     * @return
     */
    public List<TFillingFlowEntity> getTFillingFlowEntities(String yearMonthly, Integer flowType, Integer indexType, String fillCompanyCode, String sumCompanyCode, String sumOrgType) {
        return fillingFlowService.lambdaQuery().eq(StrUtil.isNotBlank(yearMonthly), TFillingFlowEntity::getYearMonthly, yearMonthly)
                .eq(flowType != null, TFillingFlowEntity::getFlowType, flowType)
                .eq(indexType != null, TFillingFlowEntity::getIndexType, indexType)
                .eq(StrUtil.isNotBlank(fillCompanyCode), TFillingFlowEntity::getCompanyCode, fillCompanyCode)
                .eq(StrUtil.isNotBlank(sumCompanyCode), TFillingFlowEntity::getParentCode, sumCompanyCode)
                .eq(StrUtil.isNotBlank(sumOrgType), TFillingFlowEntity::getSumOrgType, sumOrgType)
                .list();
    }


    private static @NotNull FrFillingFlowService.PlanResult getPlanData(FillFlowDTO fillFlowDTO, List<TFillingFlowDetailEntity> planEntities, BigDecimal yearTarget, BigDecimal monthlyAnalyze, BigDecimal monthlyPlan, String yearMonthly) {
        // 替换计划数据
        if (CollUtil.isNotEmpty(planEntities)) {
            TFillingFlowDetailEntity planEntity = planEntities.stream().filter(
                            plan -> plan.getYearMonthly().equals(yearMonthly)
                                    && plan.getCompanyCode().equals(fillFlowDTO.getCompanyCode())
                                    && plan.getParentCode().equals(fillFlowDTO.getParentOrgNo())
                                    && plan.getIndexNo().equals(fillFlowDTO.getIndexNo())
                                    && ObjUtil.equals(plan.getIndexParent(), fillFlowDTO.getIndexParent())
                                    && plan.getLevelType().equals(fillFlowDTO.getLevelType())
                                    && plan.getFillLevel().equals(fillFlowDTO.getFillLevel())
                                    && ObjUtil.equals(plan.getSumOrgType(), fillFlowDTO.getSumOrgType()))
                    .findFirst().orElse(null);
            if (planEntity != null) {
                yearTarget = planEntity.getYearTarget();
                monthlyAnalyze = planEntity.getMonthlyAnalyze();
                monthlyPlan = planEntity.getMonthlyPlan();
            }
        }
        PlanResult result = new PlanResult(yearTarget, monthlyAnalyze, monthlyPlan);
        return result;
    }

    /**
     * 更新实际流程计划数据
     * @param tFillingFlowEntity
     */
    public void updatePlanData(TFillingFlowEntity tFillingFlowEntity) {
        // 如果是实际流程  不处理
        if (tFillingFlowEntity.getIndexType().equals(2)){
            return ;
        }

        List<TFillingFlowDetailEntity> planEntities = null;
        if (tFillingFlowEntity.getFlowType().equals(1)) {
            planEntities = tFillingFlowDetailService.lambdaQuery()
                    .eq(TFillingFlowDetailEntity::getSingleFlowId, tFillingFlowEntity.getId())
                    .eq(TFillingFlowDetailEntity::getYearMonthly, tFillingFlowEntity.getYearMonthly())
                    .list();
        }
        if (tFillingFlowEntity.getFlowType().equals(2)) {
            planEntities = tFillingFlowDetailService.lambdaQuery()
                    .eq(TFillingFlowDetailEntity::getSumFlowId, tFillingFlowEntity.getId())
                    .eq(TFillingFlowDetailEntity::getYearMonthly, tFillingFlowEntity.getYearMonthly())
                    .list();
        }

        for (TFillingFlowDetailEntity detailEntity : planEntities) {
            frBiDataDao.updatePlanData(detailEntity.getYearMonthly(),
                    detailEntity.getCompanyCode(),
                    detailEntity.getParentCode(),
                    detailEntity.getIndexNo(),
                    detailEntity.getIndexParent(),
                    detailEntity.getLevelType(),
                    detailEntity.getFillLevel(),
                    detailEntity.getSumOrgType(),
                    detailEntity.getYearTarget(),
                    detailEntity.getMonthlyAnalyze(),
                    detailEntity.getMonthlyPlan());
        }
    }

    private static class PlanResult {
        public final BigDecimal yearTarget;
        public final BigDecimal monthlyAnalyze;
        public final BigDecimal monthlyPlan;

        public PlanResult(BigDecimal yearTarget, BigDecimal monthlyAnalyze, BigDecimal monthlyPlan) {
            this.yearTarget = yearTarget;
            this.monthlyAnalyze = monthlyAnalyze;
            this.monthlyPlan = monthlyPlan;
        }
    }
}

