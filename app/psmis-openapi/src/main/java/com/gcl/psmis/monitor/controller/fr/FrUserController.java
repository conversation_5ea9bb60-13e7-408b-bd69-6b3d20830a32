package com.gcl.psmis.monitor.controller.fr;

import com.gcl.psmis.framework.common.annotation.GetGclApiByToken;
import com.gcl.psmis.monitor.service.fr.FrUserService;
import io.swagger.annotations.ApiOperation;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * <AUTHOR>
 * @ClassName FrUserController
 * @description: TODO
 * @date 2025年04月15日
 * @version: 1.0
 */
@Slf4j
@RestController
@RequestMapping(value = "/fr/user")
@RequiredArgsConstructor
public class FrUserController {
    @Autowired
    private FrUserService frUserService;

    @ApiOperation(value = "报表用户创建")
    @GetGclApiByToken("/createUser")
    public void createUser() {
        frUserService.createUser();
    }

    @ApiOperation(value = "报表用户更新角色")
    @GetGclApiByToken("/updateUserRole")
    public void updateUserRole() {
        frUserService.updateUserRole();
    }

}
