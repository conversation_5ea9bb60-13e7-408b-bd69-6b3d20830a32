spring:
  cloud:
    nacos:
      discovery:
        server-addr: http://10.10.15.120:58848
      config:
        server-addr: http://10.10.15.120:58848
        file-extension: yaml
        namespace: 849c87ac-4ce9-4358-84d6-be7aaef09e1c
        shared-configs:
          - data-id: et-report-datasource-dev.yaml
            group: DEFAULT_GROUP
          - data-id: common-rocketmq-dev.yaml
            group: DEFAULT_GROUP
          - data-id: common-oss-dev.yaml
            group: DEFAULT_GROUP
          - data-id: common-other-dev.yaml
            group: DEFAULT_GROUP
          - data-id: common-redis-dev.yaml
            group: DEFAULT_GROUP
swagger:
  enabled: true
knife4j:
  # 开启增强配置
  enable: true
  basic:
    enable: true
    # Basic认证用户名
    username: gcl
    # Basic认证密码
    password: Twgdh!@#1024
rocketmq:
  group-id: GID_PSMIS_ET_REPORT_DEV