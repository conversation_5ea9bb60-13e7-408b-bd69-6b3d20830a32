package com.gcl.psmis.rmq.service;

import com.gcl.psmis.rmq.dao.overtime.OverTimeDao;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * <AUTHOR>
 * @ClassName OverTimeService
 * @description: TODO
 * @date 2024年01月16日
 * @version: 1.0
 */
@Service
@Slf4j
public class OverTimeService {

    @Autowired
    private OverTimeDao overTimeDao;

   public  Integer getOverTimeMsgCount(String bizNo,String nodeCode){
      return overTimeDao.getOverTimeMsgCount(bizNo,nodeCode);
   }

}
