package com.gcl.psmis.rmq.listener;

import com.gcl.psmis.framework.mbg.entity.*;
import com.gcl.psmis.framework.mbg.service.*;
import com.gcl.psmis.framework.mq.enums.EventTypeEnum;
import com.gcl.psmis.framework.mq.event.TemporaryCreditExpiredEvent;
import com.gcl.psmis.framework.mq.listener.EventListener;

import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;

/**
 * project: TemporaryCreditExpireEventListener
 * <AUTHOR>
 * @version 1.0
 * @since 1.8
 */
@Service
@Slf4j
public class TemporaryCreditExpireEventListener implements EventListener<TemporaryCreditExpiredEvent> {
    @Autowired
    private TDevOpsCreditService tDevOpsCreditService;
    @Autowired
    private TOperationQuotaService tOperationQuotaService;

    @Override
    public EventTypeEnum getEventType() {
        return EventTypeEnum.TEMPORARY_CREDIT_EXPIRED;
    }

    @Override
    public void eventHandler(TemporaryCreditExpiredEvent msg) throws Exception {
        log.info("TemporaryCreditExpireEventListener 接收到消息，代理商/运维商{}的{}临时额度到期,{}", msg.getCode(), msg.getTemporaryCredit(), msg);
        // 更新主表的临时额度、计算扣减后的额度
        BigDecimal temporaryCredit = msg.getTemporaryCredit();
        tDevOpsCreditService.lambdaUpdate()
                .eq(TDevOpsCreditEntity::getDevOpsCode, msg.getCode())
                .setSql("temporary_surplus=temporary_surplus - " + temporaryCredit)
                .setSql("temporary_initial=temporary_initial - " + temporaryCredit).update();
        // 设置临时额度申请单号到期
        tOperationQuotaService.lambdaUpdate()
                .eq(TOperationQuotaEntity::getApplicationCode, msg.getTemporaryCode())
                .set(TOperationQuotaEntity::getIsExpired, 0).update();
    }
}
