package com.gcl.psmis.rmq.service;

import com.gcl.psmis.framework.common.constant.enums.OrderSource;
import com.gcl.psmis.framework.common.constant.enums.OrderType;
import com.gcl.psmis.framework.common.constant.enums.inspection.InspectionOrderStatus;
import com.gcl.psmis.framework.common.resp.ResultInspectionRep;
import com.gcl.psmis.framework.common.util.JacksonHelper;
import com.gcl.psmis.framework.common.vo.patrol.BaseUserVO;
import com.gcl.psmis.framework.common.vo.patrol.PatrolPlanOrderVO;
import com.gcl.psmis.framework.common.vo.patrol.PatrolProjectVO;
import com.gcl.psmis.framework.common.vo.patrol.PlanDeviceExamineVO;
import com.gcl.psmis.framework.gencode.enums.GenRuleCode;
import com.gcl.psmis.framework.gencode.util.GenCodeUtil;
import com.gcl.psmis.framework.mbg.entity.TInspectionOrderDeviceConfigEntity;
import com.gcl.psmis.framework.mbg.entity.TInspectionOrderEntity;
import com.gcl.psmis.framework.mbg.entity.TInspectionOrderFormEntity;
import com.gcl.psmis.framework.mbg.entity.TQisPlanEntity;
import com.gcl.psmis.framework.mbg.service.*;
import com.gcl.psmis.framework.mq.enums.EventTypeEnum;
import com.gcl.psmis.framework.mq.event.CreateSavePatrolProjectEvent;
import com.gcl.psmis.framework.mq.event.OrderOvertimeStatusDelayEvent;
import com.gcl.psmis.framework.mq.producer.MqProducerUtil;
import com.gcl.psmis.framework.workflow.flow.strategy.FlowService;
import com.gcl.psmis.rmq.dao.patrol.PatrolProjectDao;
import com.gcl.psmis.rmq.listener.PatrolProjectEventListener;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;
import java.time.ZoneId;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * project: PatrolProjectEventService
 * Powered 2023-09-12 16:31:11
 *
 * <AUTHOR>
 * @version 1.0
 * @since 1.8
 */
@Service
@Slf4j
public class PatrolProjectEventService {

    @Autowired
    private TQisPlanService tQisPlanService;

    @Autowired
    private PatrolProjectDao patrolProjectDao;

    @Autowired
    private GenCodeUtil genCodeUtil;

    @Autowired
    private TInspectionOrderService tInspectionOrderService;

    @Autowired
    private TInspectionOrderFormService tInspectionOrderFormService;

    @Autowired
    private TInspectionOrderDeviceConfigService tInspectionOrderDeviceConfigService;

    @Autowired
    private FlowService flowService;

    @Autowired
    private BaseUserService baseUserService;

    @Autowired
    private MqProducerUtil mqProducerUtil;

    @Transactional
    public PatrolProjectEventListener.Result getResult(TQisPlanEntity tQisPlanEntity) {
        //查询工单信息
        PatrolProjectVO patrolProjectVO = patrolProjectDao.findPatrolProject(tQisPlanEntity.getId());
        ResultInspectionRep resultInspectionRep = new ResultInspectionRep();
        //工单来源赋值巡检计划
        patrolProjectVO.setOrderSource(OrderSource.PLAN_TASK.getCode());
        //工单类型赋值巡检工单
        patrolProjectVO.setOrderType(OrderType.INSPECTION_ORDER.getCode());
        //查找工单下的电站集合
        List<Long> psIds = patrolProjectDao.findPs(patrolProjectVO.getQisScenariosId());
        Date form = new Date();
        TInspectionOrderEntity temp = new TInspectionOrderEntity();
        //当派发状态为自动派发时
        if (patrolProjectVO.getSendType() == 1) {
            //将派单状态改为待处理
            patrolProjectVO.setOrderStatus(InspectionOrderStatus.WAIT_HANDLE.getCode());
            form = Date.from(LocalDateTime.now().plusDays(patrolProjectVO.getLimitDay()).atZone(ZoneId.systemDefault()).toInstant());
            //根据生成时间+完成时间得到派单结束时间
            temp.setEndTime(form);
            //派单时间
            temp.setDispatchTime(new Date());
        } else if (patrolProjectVO.getSendType() == 2){
            //将手动派发是  派单状态为待派发
            patrolProjectVO.setOrderStatus(InspectionOrderStatus.WAIT_SEND_ORDER.getCode());
        }

        List<TInspectionOrderEntity> inspectionOrderEntities = psIds.stream().map(psId -> {
            TInspectionOrderEntity tInspectionOrderEntity = new TInspectionOrderEntity();
            BeanUtils.copyProperties(temp,tInspectionOrderEntity);
            String no = genCodeUtil.getSysCodeByCodeRule(GenRuleCode.PLAN_CODE);
            patrolProjectVO.setOrderNo(no);
            tInspectionOrderEntity.setOrderNo(patrolProjectVO.getOrderNo());
            tInspectionOrderEntity.setOrderSource(patrolProjectVO.getOrderSource());
            tInspectionOrderEntity.setOrderType(patrolProjectVO.getOrderType());
            tInspectionOrderEntity.setOrderName(patrolProjectVO.getName());
            tInspectionOrderEntity.setLimitDay(patrolProjectVO.getLimitDay());
            tInspectionOrderEntity.setMaintenanceCompany(patrolProjectVO.getServiceCompanyCode());
            tInspectionOrderEntity.setOrderStatus(patrolProjectVO.getOrderStatus());
            tInspectionOrderEntity.setPlanId(patrolProjectVO.getQisScenariosId());
            tInspectionOrderEntity.setPsSignFlag(1);
            tInspectionOrderEntity.setQisId(patrolProjectVO.getId());
            tInspectionOrderEntity.setPsId(psId);
            //查找电站负责人id
            String serviceEmpId = patrolProjectDao.findServiceEmpId(psId);
            if (serviceEmpId!=null){
                BaseUserVO baseUserVO = patrolProjectDao.findBaseUserVO(serviceEmpId);
                tInspectionOrderEntity.setCreateByNo(baseUserVO.getFAccount());
                tInspectionOrderEntity.setCreateByName(baseUserVO.getFRealName());
            }
            tInspectionOrderEntity.setMaintenanceCompany(patrolProjectVO.getServiceCompanyCode());
            tInspectionOrderEntity.setResponsiblePerson(patrolProjectVO.getDutyNo());
            return tInspectionOrderEntity;
        }).collect(Collectors.toList());
        //批量添加巡检工单
        tInspectionOrderService.saveBatch(inspectionOrderEntities);
        List<Long> inspectionOrderId = inspectionOrderEntities.stream().map(TInspectionOrderEntity::getId).collect(Collectors.toList());
        resultInspectionRep.setInspectionOrderId(inspectionOrderId);
        PatrolProjectEventListener.Result result = new PatrolProjectEventListener.Result(patrolProjectVO, resultInspectionRep, psIds,inspectionOrderId, form, inspectionOrderEntities);
        return result;
    }

    @Transactional
    public void extracted(CreateSavePatrolProjectEvent msg, PatrolProjectEventListener.Result result) {
        //获得工单下电站类型
        Integer psType = patrolProjectDao.findPsType(result.patrolProjectVO.getQisScenariosId());
        //工单id
        List<Long> inspectionOrderId = result.inspectionOrderId;
        //Long orderId = patrolProjectDao.findInspectionOrder();
        for (Long aLong : inspectionOrderId) {
            if (result.patrolProjectVO.getSendType() == 1){//如果派发方式是自动
                OrderOvertimeStatusDelayEvent orderMsg = new OrderOvertimeStatusDelayEvent();
                orderMsg.setOrderId(aLong);
                orderMsg.setDispatchTime(msg.getDispatchTime());
                orderMsg.setEndTime(result.form);
                mqProducerUtil.sendDeliverMsg(
                        EventTypeEnum.ORDER_OVERTIME_STATUS_DELAY.getTopic(),
                        EventTypeEnum.ORDER_OVERTIME_STATUS_DELAY.getTag(),
                        JacksonHelper.objectToJson(orderMsg),
                        msg.getDispatchDate());
            }
        }

        for (Long psId : result.psIds) {
            List<TInspectionOrderEntity> inspectionOrderEntities = result.inspectionOrderEntities;
            Long orderid = null;
            for (TInspectionOrderEntity inspectionOrderEntity : inspectionOrderEntities) {
                if (Objects.equals(inspectionOrderEntity.getPsId(), psId)){
                    orderid = inspectionOrderEntity.getId();
                }
            }
            //对应电站下的设备sn
//            List<Long> deviceIds = patrolProjectDao.findDevice(psId, result.patrolProjectVO.getQisScenariosId());
            List<PlanDeviceExamineVO> deviceSns = patrolProjectDao.findDeviceSn(psId, result.patrolProjectVO.getQisScenariosId());
            //该预案选中的所有检查项
            List<PlanDeviceExamineVO> examineIds = patrolProjectDao.findExamineId(result.patrolProjectVO.getQisScenariosId(),psType);
            List<PatrolPlanOrderVO> patrolPlanOrderVOS = new ArrayList<>();
            for (PlanDeviceExamineVO deviceSn : deviceSns) {
                for (PlanDeviceExamineVO aLong : examineIds) {
                    if (deviceSn.getDeviceType().equals(aLong.getDeviceType())){
                        //根据检查项编号查检查要求id
                        List<Long> detailIds = patrolProjectDao.findDetail(aLong.getExamineId());
                        for (Long detailId : detailIds) {
                            PatrolPlanOrderVO patrolPlanOrderVO = new PatrolPlanOrderVO();
                            patrolPlanOrderVO.setDeviceSn(deviceSn.getDeviceSn());
                            patrolPlanOrderVO.setExamineId(aLong.getExamineId());
                            patrolPlanOrderVO.setDetailId(detailId);
                            patrolPlanOrderVOS.add(patrolPlanOrderVO);
                        }
                    }
                }
            }
            //List<PatrolPlanOrderVO> patrolPlanOrderVOS = patrolProjectDao.findExamine(deviceIds, psType);

            Long finalOrderid = orderid;
            List<TInspectionOrderFormEntity> entities = new ArrayList<>();
            for (PatrolPlanOrderVO patrolPlanOrderVO : patrolPlanOrderVOS) {
                TInspectionOrderFormEntity tInspectionOrderFormEntity = new TInspectionOrderFormEntity();
                tInspectionOrderFormEntity.setOrderId(finalOrderid);
                tInspectionOrderFormEntity.setPsId(psId);
                tInspectionOrderFormEntity.setSn(patrolPlanOrderVO.getDeviceSn());
                tInspectionOrderFormEntity.setInspectionItemId(patrolPlanOrderVO.getExamineId());
                tInspectionOrderFormEntity.setInspectionItemRequireId(patrolPlanOrderVO.getDetailId());
                entities.add(tInspectionOrderFormEntity);
            }
            tInspectionOrderFormService.saveBatch(entities);
            List<Long> InspectionOrderFormId = entities.stream().map(TInspectionOrderFormEntity::getId).collect(Collectors.toList());
            result.resultInspectionRep.setInspectionOrderFormId(InspectionOrderFormId);

        }

        for (Long psId : result.psIds) {
            List<TInspectionOrderEntity> inspectionOrderEntities = result.inspectionOrderEntities;
            Long orderid = null;
            for (TInspectionOrderEntity inspectionOrderEntity : inspectionOrderEntities) {
                if (Objects.equals(inspectionOrderEntity.getPsId(), psId)){
                    orderid = inspectionOrderEntity.getId();
                }
            }
            List<String> deviceSns = patrolProjectDao.findDevice(psId, result.patrolProjectVO.getQisScenariosId());
            Long finalOrderid = orderid;
            List<TInspectionOrderDeviceConfigEntity> entities = deviceSns.stream()
                    .map(deviceSn -> {
                        TInspectionOrderDeviceConfigEntity tInspectionOrderDeviceConfigEntity = new TInspectionOrderDeviceConfigEntity();
                        tInspectionOrderDeviceConfigEntity.setOrderId(finalOrderid);
                        tInspectionOrderDeviceConfigEntity.setPsId(psId);
                        tInspectionOrderDeviceConfigEntity.setSn(deviceSn);
                        tInspectionOrderDeviceConfigEntity.setSignFlag(1);
                        tInspectionOrderDeviceConfigEntity.setItemFlag(1);
                        return tInspectionOrderDeviceConfigEntity;
                    })
                    .collect(Collectors.toList());
            tInspectionOrderDeviceConfigService.saveBatch(entities);
            List<Long> InspectionOrderDeviceId = entities.stream().map(TInspectionOrderDeviceConfigEntity::getId).collect(Collectors.toList());
            result.resultInspectionRep.setInspectionOrderDeviceId(InspectionOrderDeviceId);
        }
    }
}
