package com.gcl.psmis.rmq.listener;

import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.gcl.psmis.framework.common.constant.enums.DelFlagEnum;
import com.gcl.psmis.framework.common.constant.enums.inspection.InspectionOrderOvertimeStatus;
import com.gcl.psmis.framework.common.constant.enums.inspection.InspectionOrderStatus;
import com.gcl.psmis.framework.common.util.JacksonHelper;
import com.gcl.psmis.framework.mbg.entity.TInspectionOrderEntity;
import com.gcl.psmis.framework.mbg.service.TInspectionOrderService;
import com.gcl.psmis.framework.mq.enums.EventTypeEnum;
import com.gcl.psmis.framework.mq.event.OrderOvertimeStatusDelayEvent;
import com.gcl.psmis.framework.mq.listener.EventListener;
import com.gcl.psmis.framework.mq.producer.MqProducerUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Date;


/**
* @description: 工单超时状态处理
*/
@Service
@Slf4j
public class OrderOvertimeDelayListener implements EventListener<OrderOvertimeStatusDelayEvent> {

    @Autowired
    private TInspectionOrderService tInspectionOrderService;

    @Autowired
    private MqProducerUtil mqProducerUtil;

    @Override
    public EventTypeEnum getEventType() {
        return EventTypeEnum.ORDER_OVERTIME_STATUS_DELAY;
    }

    @Override
    public void eventHandler(OrderOvertimeStatusDelayEvent msg) {
        log.info("OrderOvertimeDelayListener 接收到消息，" + msg);
        TInspectionOrderEntity tInspectionOrderEntity = tInspectionOrderService.lambdaQuery()
                .eq(TInspectionOrderEntity::getId,msg.getOrderId())
                .eq(TInspectionOrderEntity::getDelFlag, DelFlagEnum.DISABLE)
                .one();
        if(tInspectionOrderEntity==null){
            log.error(String.format("orderId=%s,不存在或已删除",msg.getOrderId()));
            return;
        }

        //工单完成(待验收) 关闭 验收状态,不做超时处理
        if(InspectionOrderStatus.CLOSE.equals(tInspectionOrderEntity.getOrderStatus())
                ||InspectionOrderStatus.CHECKED.equals(tInspectionOrderEntity.getOrderStatus())
            ||InspectionOrderStatus.WAIT_CHECK.equals(tInspectionOrderEntity.getOrderStatus())){
            return;
        }

        Date endTime = tInspectionOrderEntity.getEndTime();
        if(endTime==null){
            return;
        }

        Date now = new Date();

        if((now.after(endTime) || now.equals(endTime)) && InspectionOrderOvertimeStatus.NO.getCode().equals(tInspectionOrderEntity.getOvertimeStatus())){
            tInspectionOrderService.update(
                    new LambdaUpdateWrapper<TInspectionOrderEntity>()
                            .eq(TInspectionOrderEntity::getId,msg.getOrderId())
                            .set(TInspectionOrderEntity::getOvertimeStatus, InspectionOrderOvertimeStatus.YES.getCode())
            );
        }else if(now.before(endTime)){
            Long oneDaySeconds = 24*60*60L;
            Long deliverSeconds = endTime.getTime() - now.getTime();
            deliverSeconds = deliverSeconds / 1000;

            if(deliverSeconds>oneDaySeconds){
                deliverSeconds = oneDaySeconds;
            }

            OrderOvertimeStatusDelayEvent orderOvertimeStatusDelayEvent = new OrderOvertimeStatusDelayEvent();
            orderOvertimeStatusDelayEvent.setOrderId(msg.getOrderId());
            orderOvertimeStatusDelayEvent.setDispatchTime(msg.getDispatchTime());
            orderOvertimeStatusDelayEvent.setEndTime(endTime);

            mqProducerUtil.sendDeliverMsg(
                    EventTypeEnum.ORDER_OVERTIME_STATUS_DELAY.getTopic(),
                    EventTypeEnum.ORDER_OVERTIME_STATUS_DELAY.getTag(),
                    JacksonHelper.objectToJson(orderOvertimeStatusDelayEvent),
                    deliverSeconds);
        }


    }

}
