package com.gcl.psmis.rmq.dao.patrol;

import com.gcl.psmis.framework.common.vo.patrol.BaseUserVO;
import com.gcl.psmis.framework.common.vo.patrol.PatrolPlanOrderVO;
import com.gcl.psmis.framework.common.vo.patrol.PatrolProjectVO;
import com.gcl.psmis.framework.common.vo.patrol.PlanDeviceExamineVO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * project: PatrolProjectDao
 * Powered 2023-09-12 16:33:30
 *
 * <AUTHOR>
 * @version 1.0
 * @since 1.8
 */
@Mapper
public interface PatrolProjectDao {
    PatrolProjectVO findPatrolProject(@Param("id") Long id);

    List<Long> findPs(@Param("qisScenariosId") Long qisScenariosId);

    Integer findPsType(@Param("qisScenariosId") Long qisScenariosId);

    Long findInspectionOrder();

    List<String> findDevice(@Param("psId") Long psId, @Param("qisScenariosId") Long qisScenariosId);

    List<PatrolPlanOrderVO> findExamine(@Param("deviceIds") List<Long> deviceIds, @Param("psType") Integer psType);

    List<PlanDeviceExamineVO> findExamineId(@Param("qisScenariosId") Long qisScenariosId, @Param("psType") Integer psType);

    List<Long> findExamineList(@Param("deviceSn") String deviceSn, @Param("psType") Integer psType);

    List<Long> findDetail(@Param("aLong") Long aLong);

    String findServiceEmpId(@Param("psId") Long psId);

    BaseUserVO findBaseUserVO(@Param("serviceEmpId") String serviceEmpId);

    void deleteOrder(@Param("inspectionOrderId") List<Long> inspectionOrderId);

    List<PlanDeviceExamineVO> findDeviceSn(@Param("psId") Long psId, @Param("qisScenariosId") Long qisScenariosId);
}
