package com.gcl.psmis.rmq.listener;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.date.DateUtil;
import cn.hutool.json.JSONUtil;
import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.gcl.psmis.framework.common.ResponseResult;
import com.gcl.psmis.framework.common.exception.BussinessException;
import com.gcl.psmis.framework.common.req.msg.MessageReq;
import com.gcl.psmis.framework.common.req.msg.SendReq;
import com.gcl.psmis.framework.common.util.JacksonHelper;
import com.gcl.psmis.framework.mbg.entity.*;
import com.gcl.psmis.framework.mbg.service.*;
import com.gcl.psmis.framework.mq.enums.EventTypeEnum;
import com.gcl.psmis.framework.mq.event.QisOvertimeEvent;
import com.gcl.psmis.framework.mq.event.qis.NodeMsgDTO;
import com.gcl.psmis.framework.mq.listener.EventListener;
import com.gcl.psmis.framework.mq.producer.MqProducerUtil;
import com.gcl.psmis.msg.api.SendRpcService;
import com.gcl.psmis.rmq.service.OverTimeService;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Map;

/**
* @className: QisWorkFlowEventListener
* @author: xinan.yuan
* @create: 2024/8/15 14:40
* @description: 
*/
@Service
@Slf4j
public class QisWorkFlowEventListener implements EventListener<QisOvertimeEvent> {

    private static final long SECONDS_OF_A_DAY = 86400L;

    @Autowired
    private MqProducerUtil mqProducerUtil;

    @Autowired
    private SendRpcService sendRpcService;

    @Autowired
    private TBizMsgService bizMsgService;

    @Autowired
    private TMsgTemplateService msgTemplateService;

    @Autowired
    private TNodeMessageConfigService nodeMessageConfigService;

    @Autowired
    private TDefectOrderService defectOrderService;

    @Autowired
    private TInspectionOrderService inspectionOrderService;


    @Autowired
    private OverTimeService overTimeService;
    @Override
    public EventTypeEnum getEventType() {
        return EventTypeEnum.QIS_WORKFLOW;
    }

    @Override
    public void eventHandler(QisOvertimeEvent msg) throws Exception {
        if(null == msg.getCycNum()){
            return;
        }
        if (msg.getCycNum() == 0) {
            NodeMsgDTO nodeMsgDTO = msg.getNodeMsgDTO();

            TMsgTemplateEntity msgTemplateEntity = msgTemplateService.getById(nodeMsgDTO.getMessageTemplateId());
            List<TNodeMessageConfigEntity> nodeMessageConfigEntities = nodeMessageConfigService.list(new LambdaQueryWrapper<TNodeMessageConfigEntity>()
                    .eq(TNodeMessageConfigEntity::getNodeCode, nodeMsgDTO.getNodeCode())
                    .eq(TNodeMessageConfigEntity::getMessageTemplateId, nodeMsgDTO.getMessageTemplateId()));


            Date endTime = null;
            String acceptanceResult = null;
            if(msg.getNodeMsgDTO().getBussNo().startsWith("IW")){
                TInspectionOrderEntity one = inspectionOrderService.getOne(new LambdaQueryWrapper<TInspectionOrderEntity>().eq(TInspectionOrderEntity::getOrderNo, msg.getNodeMsgDTO().getBussNo()));
                if(null == one){
                    return;
                }
                endTime = one.getEndTime();
                acceptanceResult = one.getAcceptanceResult();
            }else if(msg.getNodeMsgDTO().getBussNo().startsWith("DW")){

                TDefectOrderEntity defectOrderEntity = defectOrderService.getOne(new LambdaQueryWrapper<TDefectOrderEntity>().eq(TDefectOrderEntity::getDefectOrderNo, msg.getNodeMsgDTO().getBussNo()));
                if(null == defectOrderEntity){
                    return;


                }
                endTime = defectOrderEntity.getEndTime();
                acceptanceResult = defectOrderEntity.getAcceptanceResult();
            }


            // 验收不再发送消息
            if (null == msgTemplateEntity || CollectionUtil.isEmpty(nodeMessageConfigEntities) || StringUtils.isNotBlank(acceptanceResult)) {
                return;
            }
            //若项目延期，判断延期时间，截止时间内不发送消息,
            if (DateUtil.parseDate(DateUtil.now()).compareTo(endTime) > 0) {
                return;
            }
            // 查看当前节点超时消息，是否到次数上限
           /* List<TBizMsgEntity> list = bizMsgService.list(new LambdaUpdateWrapper<TBizMsgEntity>()
                    .eq(TBizMsgEntity::getBizNo, nodeMsgDTO.getBussNo())
                    .eq(TBizMsgEntity::getNodeCode, nodeMsgDTO.getNodeCode()));*/

            Integer overTimeMsgCount = overTimeService.getOverTimeMsgCount(msg.getNodeMsgDTO().getBussNo(), nodeMsgDTO.getNodeCode());
            for (TNodeMessageConfigEntity nodeMessageConfigEntity : nodeMessageConfigEntities) {
                // 消息条数大于配置次数则不再发送消息
                if (overTimeMsgCount >= nodeMessageConfigEntity.getRemindCount()) {
                    continue;
                }
                MessageReq messageReq = MessageReq.builder().extra(nodeMsgDTO.getBussNo())
                        .receiver(nodeMsgDTO.getAccountList())
                        .variables(nodeMsgDTO.getVariables()).build();
                SendReq sendReq = SendReq.builder().messageTemplateId(nodeMsgDTO.getMessageTemplateId())
                        .messageParam(messageReq).build();
                try {
                    ResponseResult send = sendRpcService.send(sendReq);
                    log.info("消缺工单发送消息：{}", JacksonHelper.objectToJson(send));
                    if (send.getCode() != 200) {
                        throw new BussinessException("消缺工单发送消息失败,入参：" + sendReq.toString());
                    }
                    String messageId = JSONUtil.toList(JSON.toJSONString(send.getData()), Map.class).get(0).get("messageId").toString();
                    // 保存消息信息记录表
                    List<TBizMsgEntity> bizMsgEntities = new ArrayList<>();
                    for (String account : nodeMsgDTO.getAccountList()) {
                        TBizMsgEntity tBizMsgEntity = new TBizMsgEntity();
                        tBizMsgEntity.setBizNo(nodeMsgDTO.getBussNo());
                        tBizMsgEntity.setMsgId(messageId);
                        tBizMsgEntity.setAccount(nodeMsgDTO.getEmpNo());
                        tBizMsgEntity.setMsgType(nodeMsgDTO.getNodeMessageType());
                        tBizMsgEntity.setReceiver(account);
                        tBizMsgEntity.setNodeCode(nodeMsgDTO.getNodeCode());
                        bizMsgEntities.add(tBizMsgEntity);
                    }
                    bizMsgService.saveBatch(bizMsgEntities);
                } catch (Exception e) {
                    e.printStackTrace();
                }
                // 发送下次消息(小时)
                Integer remindInterval = nodeMessageConfigEntity.getRemindInterval();
                Integer seconds = null;
                // 圈数
                Integer cycNum = 0;
                if (remindInterval <= 12) {
                    seconds = remindInterval * 60 * 60;
                } else {
                    cycNum = remindInterval / 12;
                    seconds = (remindInterval % 12) * 60;
                }
                msg.setCycNum(cycNum);
                msg.setSurplusSec(seconds);
                mqProducerUtil.sendDeliverMsg(
                        EventTypeEnum.QIS_WORKFLOW.getTopic(),
                        EventTypeEnum.QIS_WORKFLOW.getTag(),
                        JacksonHelper.objectToJson(msg),
                        (long)seconds);
            }
        } else {
            msg.setCycNum(msg.getCycNum() - 1);
            long seconds = (long) msg.getSurplusSec();
            if (msg.getCycNum() != 0) {
                seconds = SECONDS_OF_A_DAY;
            }
            mqProducerUtil.sendDeliverMsg(
                    EventTypeEnum.QIS_WORKFLOW.getTopic(),
                    EventTypeEnum.QIS_WORKFLOW.getTag(),
                    JacksonHelper.objectToJson(msg)
                    , seconds);
        }
    }
}
