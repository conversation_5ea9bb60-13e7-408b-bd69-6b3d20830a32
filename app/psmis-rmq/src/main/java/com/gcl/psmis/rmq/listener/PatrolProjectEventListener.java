package com.gcl.psmis.rmq.listener;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.exceptions.ExceptionUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.gcl.framework.data.dto.UserDTO;
import com.gcl.framework.data.holder.CurrentUserHolder;
import com.gcl.psmis.framework.common.ResponseResult;
import com.gcl.psmis.framework.common.constant.CommonResultCode;
import com.gcl.psmis.framework.common.constant.enums.DelFlagEnum;
import com.gcl.psmis.framework.common.exception.BussinessException;
import com.gcl.psmis.framework.common.resp.ResultInspectionRep;
import com.gcl.psmis.framework.common.vo.patrol.PatrolProjectVO;
import com.gcl.psmis.framework.gencode.util.GenCodeUtil;
import com.gcl.psmis.framework.mbg.entity.*;
import com.gcl.psmis.framework.mbg.service.*;
import com.gcl.psmis.framework.mq.enums.EventTypeEnum;
import com.gcl.psmis.framework.mq.event.CreateSavePatrolProjectEvent;
import com.gcl.psmis.framework.mq.listener.EventListener;
import com.gcl.psmis.framework.mq.producer.MqProducerUtil;
import com.gcl.psmis.framework.workflow.flow.fegin.YinMaiApi;
import com.gcl.psmis.framework.workflow.util.SendMsgUtil;
import com.gcl.psmis.rmq.dao.patrol.PatrolProjectDao;
import com.gcl.psmis.rmq.service.PatrolProjectEventService;
import com.gcl.psmis.workflow.api.WorkFlowRpcService;
import com.gcl.psmis.workflow.constant.ProcessKeyEnum;
import com.gcl.psmis.workflow.constant.task.QisOrderFlowEnum;
import com.gcl.psmis.workflow.domain.req.ProcessCompleteReq;
import com.gcl.psmis.workflow.domain.req.StartProcessInstanceReq;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * project: PatrolProjectEventListener
 * Powered 2023-09-12 14:44:10
 *
 * <AUTHOR>
 * @version 1.0
 * @since 1.8
 */
@Service
@Slf4j
public class PatrolProjectEventListener implements EventListener<CreateSavePatrolProjectEvent> {

    @Autowired
    private PatrolProjectEventService patrolProjectEventService;

    @Autowired
    private TQisPlanService tQisPlanService;

    @Autowired
    private PatrolProjectDao patrolProjectDao;

    @Autowired
    private TFlowVsBusinessService tFlowVsBusinessService;

    @Autowired
    private BaseUserService baseUserService;

    @Autowired
    private BaseRoleService baseRoleService;

    @Autowired
    private WorkFlowRpcService workFlowRpcService;

    @Autowired
    private TPatrolPlanService patrolPlanService;

    @Override
    public EventTypeEnum getEventType() {
        return EventTypeEnum.PATROL_PROJECT;
    }

    @Override
    public void eventHandler(CreateSavePatrolProjectEvent msg) {
        log.info("OrderOvertimeDelayListener 接收到消息111，" + msg);
        Result result = null;
        try {
            TQisPlanEntity tQisPlanEntity = tQisPlanService.lambdaQuery()
                    .eq(TQisPlanEntity::getId, msg.getQisId())
                    .eq(TQisPlanEntity::getDelFlag, DelFlagEnum.DISABLE)
                    .one();
            if (tQisPlanEntity == null) {
                log.error(String.format("orderId=%s,不存在,或已删除", msg.getQisId()));
                return;
            }

            result = patrolProjectEventService.getResult(tQisPlanEntity);

            // 创建工单 调用工作流
            // 1自动 2手动
            if (2 == result.patrolProjectVO.getSendType()) {
                for (TInspectionOrderEntity inspectionOrderEntity : result.inspectionOrderEntities) {
                    // 发起流程
                    StartProcessInstanceReq req = new StartProcessInstanceReq();
                    req.setAuthenticatedAccount("system");
                    req.setProcessKey(ProcessKeyEnum.QIS_FLOW);
                    req.setBusinessKey(inspectionOrderEntity.getOrderNo());
                    Map<String, Object> var = new HashMap<>();
                    // 获取消缺派单人员列表
//                    UserDTO userDTO = CurrentUserHolder.getUserDTOThreadLocal();
                    BaseRoleEntity baseRoleEntity = baseRoleService.getOne(new LambdaQueryWrapper<BaseRoleEntity>()
                            .eq(BaseRoleEntity::getFEncode, "QisOrder"));
                    if (null == baseRoleEntity) {
                        throw new BussinessException("未创建当前角色，请确认！");
                    }
                    List<BaseUserEntity> userEntities = baseUserService.list(new LambdaQueryWrapper<BaseUserEntity>()
                            .like(BaseUserEntity::getFRoleid, baseRoleEntity.getFId()));
                    if (CollectionUtil.isEmpty(userEntities)) {
                        throw new BussinessException("消缺派单员未配置人员请确认！");
                    }
                    List<String> accounts = userEntities.stream().map(s -> s.getFAccount()).collect(Collectors.toList());
                    var.put("person", accounts);
                    var.put("operatorAccount", "system");
                    var.put("operatorName", "系統");
                    req.setVariables(var);
                    ResponseResult defectResult = workFlowRpcService.start(req);
                    if (defectResult.getCode() != CommonResultCode.SUCCESS.code()) {
                        throw new BussinessException("开启巡检工单流程失败!");
                    }
                }

            } else if (1 == result.patrolProjectVO.getSendType()) {
                for (TInspectionOrderEntity inspectionOrderEntity : result.inspectionOrderEntities) {
                    // 发起流程
                    StartProcessInstanceReq req = new StartProcessInstanceReq();
                    req.setAuthenticatedAccount("system");
                    req.setProcessKey(ProcessKeyEnum.QIS_FLOW);
                    req.setBusinessKey(inspectionOrderEntity.getOrderNo());
                    Map<String, Object> var = new HashMap<>();
                    // 获取消缺派单人员列表
                    // 巡检计划获取派单人
                    BaseUserEntity baseUserEntity = baseUserService.getById(tQisPlanEntity.getDutyNo());


                 /*   UserDTO userDTO = CurrentUserHolder.getUserDTOThreadLocal();*/
                    BaseRoleEntity baseRoleEntity = baseRoleService.getOne(new LambdaQueryWrapper<BaseRoleEntity>()
                            .eq(BaseRoleEntity::getFEncode, "QisOrder"));
                    if (null == baseRoleEntity) {
                        throw new BussinessException("未创建当前角色，请确认！");
                    }
                    List<BaseUserEntity> userEntities = baseUserService.list(new LambdaQueryWrapper<BaseUserEntity>()
                            .like(BaseUserEntity::getFRoleid, baseRoleEntity.getFId()));
                    if (CollectionUtil.isEmpty(userEntities)) {
                        throw new BussinessException("巡检派单员未配置人员请确认！");
                    }
                    List<String> accounts = userEntities.stream().map(s -> s.getFAccount()).collect(Collectors.toList());
                    var.put("person", accounts);
                    var.put("operatorAccount", "system");
                    var.put("operatorName", "系統");
                    req.setVariables(var);
                    ResponseResult defectResult = workFlowRpcService.start(req);
                    if (defectResult.getCode() != CommonResultCode.SUCCESS.code()) {
                        throw new BussinessException("开启巡检工单流程失败!");
                    }
                    // 自动派单
                    ProcessCompleteReq completeReq = new ProcessCompleteReq();
                    TFlowVsBusinessEntity one = tFlowVsBusinessService.getOne(new LambdaQueryWrapper<TFlowVsBusinessEntity>()
                            .eq(TFlowVsBusinessEntity::getBusinessKey, inspectionOrderEntity.getOrderNo()));
                    if (one == null) {
                        throw new BussinessException("当前工单无流程信息，请联系管理员！");
                    }
                    completeReq.setProcessInstanceId(one.getProcessInstanceId());
                    completeReq.setFlowNodeEnum(QisOrderFlowEnum.QIS_ORDER);
                    HashMap<String, Object> map = new HashMap<>();
                    map.put("person", baseUserEntity.getFAccount());
                    map.put("operatorAccount", baseUserEntity.getFAccount());
                    map.put("operatorName", baseUserEntity.getFRealname());
                    completeReq.setVariables(map);
                    completeReq.setOperatorAccount(baseUserEntity.getFAccount());
                    completeReq.setOperatorName(baseUserEntity.getFRealname());
                    ResponseResult responseResult = workFlowRpcService.processComplete(completeReq);
                    if (responseResult.getCode() != CommonResultCode.SUCCESS.code()) {
                        throw new BussinessException("巡检流程派单失败!");
                    }


                }
            }


            /*if (2 == result.patrolProjectVO.getSendType()) {
                for (TInspectionOrderEntity inspectionOrderEntity : result.inspectionOrderEntities) {
                    // 创建工单 调用工作流
                    FlowReq flowReq = new FlowReq();
                    flowReq.setBizCode(inspectionOrderEntity.getOrderNo());
                    flowReq.setId(inspectionOrderEntity.getId());
                    flowReq.setFlowType(FlowTypeEnum.QIS_ORDER.getDesc());
                    try {
                        flowService.start(flowReq);
                        TInspectionOrderEntity tInspectionOrderEntity = tInspectionOrderService.getById(inspectionOrderEntity.getId());
                        sendMsgUtil.sendMsgServer(tInspectionOrderEntity.getOrderNo(), QisFlowEnum.QIS_START.getCode(),tInspectionOrderEntity.getFFlowtaskid(),null,tInspectionOrderEntity.getFFlowid());
                    }catch (Exception e) {
                        tInspectionOrderService.removeById(inspectionOrderEntity.getId());
                    }

                }
            } else if (1 == result.patrolProjectVO.getSendType()) {
                for (TInspectionOrderEntity inspectionOrderEntity : result.inspectionOrderEntities) {
                    // 创建工单 调用工作流
                    FlowReq flowReq = new FlowReq();
                    flowReq.setBizCode(inspectionOrderEntity.getOrderNo());
                    flowReq.setId(inspectionOrderEntity.getId());
                    flowReq.setFlowType(FlowTypeEnum.QIS_ORDER.getDesc());
                    flowReq.setQisPlanFlag(Boolean.TRUE);
                    String flowTaskId = flowService.start(flowReq);
                    TInspectionOrderEntity tInspectionOrderEntity = tInspectionOrderService.getById(inspectionOrderEntity.getId());
                    sendMsgUtil.sendMsgServer(tInspectionOrderEntity.getOrderNo(), QisFlowEnum.QIS_START.getCode(),tInspectionOrderEntity.getFFlowtaskid(),null,tInspectionOrderEntity.getFFlowid());
                    // 自动调用审核通过了
                    // 派单 调用工作流  responsiblePerson 派单人员
                    // 通过账号获取人员id
                    FlowReq auditReq = new FlowReq();
                    auditReq.setBizCode(inspectionOrderEntity.getOrderNo());
                    auditReq.setResponsible_person(result.patrolProjectVO.getDutyNo());
                    auditReq.setId(inspectionOrderEntity.getId());
                    auditReq.setFlowType(FlowTypeEnum.QIS_ORDER.getDesc());
                    auditReq.setFlowTaskId(flowTaskId);
                    auditReq.setQisPlanFlag(Boolean.TRUE);
                    flowService.audit(auditReq);
                    // 发送消息
                    sendMsgUtil.sendMsgServer(tInspectionOrderEntity.getOrderNo(), QisFlowEnum.QIS_ORDER.getCode(),tInspectionOrderEntity.getFFlowtaskid(),null,tInspectionOrderEntity.getFFlowid());
                }
            }*/

            patrolProjectEventService.extracted(msg, result);

        } catch (Exception e) {
            List<Long> inspectionOrderId = null;
            if (result != null) {
                inspectionOrderId = result.inspectionOrderId;
            }
            if (inspectionOrderId != null) {
                patrolProjectDao.deleteOrder(inspectionOrderId);
            }
            log.error("引迈流程失败：" + ExceptionUtil.stacktraceToString(e));
        }

    }


    public static class Result {
        public final PatrolProjectVO patrolProjectVO;
        public final ResultInspectionRep resultInspectionRep;
        public final List<Long> psIds;
        public final List<Long> inspectionOrderId;
        public final Date form;
        public final List<TInspectionOrderEntity> inspectionOrderEntities;

        public Result(PatrolProjectVO patrolProjectVO, ResultInspectionRep resultInspectionRep, List<Long> psIds, List<Long> inspectionOrderId, Date form, List<TInspectionOrderEntity> inspectionOrderEntities) {
            this.patrolProjectVO = patrolProjectVO;
            this.resultInspectionRep = resultInspectionRep;
            this.psIds = psIds;
            this.inspectionOrderId = inspectionOrderId;
            this.form = form;
            this.inspectionOrderEntities = inspectionOrderEntities;
        }
    }

}
