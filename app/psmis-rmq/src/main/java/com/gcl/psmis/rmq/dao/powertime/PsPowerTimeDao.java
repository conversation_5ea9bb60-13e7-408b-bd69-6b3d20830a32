package com.gcl.psmis.rmq.dao.powertime;

import com.baomidou.dynamic.datasource.annotation.DS;
import com.gcl.psmis.framework.taos.domain.entity.PsIotDayStInverterEntity;
import com.gcl.psmis.framework.taos.domain.entity.PsIotStInverterEntity;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

@Mapper
public interface PsPowerTimeDao {

    @DS("td")
    void updatePsIotStInverterIsAfter(@Param("entityList") List<PsIotStInverterEntity> entityList);

    @DS("td")
    void updatePsIotDayStInverterIsAfter(@Param("entityList") List<PsIotDayStInverterEntity> entityList);
}
