package com.gcl.psmis.rmq.listener;

import cn.hutool.core.date.DateUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.gcl.psmis.framework.common.constant.enums.BzBusinessModeEnum;
import com.gcl.psmis.framework.common.constant.enums.BzFinishStatusEnum;
import com.gcl.psmis.framework.common.constant.enums.BzPowerStationTypeEnum;
import com.gcl.psmis.framework.common.constant.enums.DelFlagEnum;
import com.gcl.psmis.framework.common.exception.BussinessException;
import com.gcl.psmis.framework.mbg.entity.*;
import com.gcl.psmis.framework.mbg.service.*;
import com.gcl.psmis.framework.mq.enums.EventTypeEnum;
import com.gcl.psmis.framework.mq.event.CreateSaveFinishEvent;
import com.gcl.psmis.framework.mq.event.bz.InverterVO;
import com.gcl.psmis.framework.mq.listener.EventListener;
import com.gcl.psmis.manager.PowerStationManager;
import com.gcl.psmis.rmq.service.CreateSaveFinishEventService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.util.List;

/**
 * @className: CreateSaveFinishEventListener
 * @author: xinan.yuan
 * @create: 2023/8/4 10:06
 * @description: 完工消息监听
 */
@Service
@Slf4j
public class CreateSaveFinishEventListener implements EventListener<CreateSaveFinishEvent> {

    @Autowired
    private TPowerStationService tPowerStationService;

    @Autowired
    private PowerStationManager powerStationManager;


    @Autowired
    private CreateSaveFinishEventService createSaveFinishEventService;

    @Autowired
    private TOperationService operationService;

    @Autowired
    private SysProvinceService sysProvinceService;

    @Autowired
    private SysCityService sysCityService;

    @Autowired
    private SysAreaService sysAreaService;

    @Autowired
    private SysTownService sysTownService;

    @Autowired
    private TRegionService tRegionService;

    @Autowired
    private BaseOrganizeService baseOrganizeService;

    @Autowired
    private TProjectCompanyService projectCompanyService;

    @Autowired
    private TProvinceCompanyConfigService provinceCompanyConfigService;


    @Override
    public EventTypeEnum getEventType() {
        return EventTypeEnum.CREATE_SAVE_FINISH;
    }


    @Override
    public void eventHandler(CreateSaveFinishEvent msg) {
        if (StringUtils.isBlank(msg.getDeveloperAgentName())) {
            return;
        }
        //psmis保存电站
        //1.判断是更新、新增、删除操作
        if (msg.getIntentionState().equals(BzFinishStatusEnum.DELETED.getStatus())) {//删除
            powerStationManager.removePs(msg.getStationNo());
            return;
        }

        //检验虚拟逆变器与逆变器混合
        if (!preCheckInverterVO(msg.getInverters())) {
            return;
        }

        //判断是否是虚拟电站
        if (StringUtils.isNotBlank(msg.getPowerStationType()) &&
                Integer.valueOf(msg.getPowerStationType()).equals(BzPowerStationTypeEnum.Virtual.code)) {
            powerStationManager.saveOrUpdateVirPs(msg);
            return;
        }

        TPowerStationEntity ps = tPowerStationService.lambdaQuery().eq(TPowerStationEntity::getPsCode, msg.getStationNo()).eq(TPowerStationEntity::getDelFlag, 0).one();

        if (ps == null) {//新增电站
            //构建并更新电站
            ps = this.buildPs(msg);
            powerStationManager.savePs(msg, ps);
            //更新逆变器信息
            createSaveFinishEventService.updateInvertPsInfo();
        } else {//更新电站
            //构建并更新电站
            ps = this.buildPs(msg);
            powerStationManager.updatePs(msg, ps);
            //更新逆变器信息
            createSaveFinishEventService.updateInvertPsInfo();
        }
    }

    /**
     * @param inverterList
     * @return
     * @desc 检验虚拟逆变器与逆变器混合
     * @date 2024/1/23 10:56
     */
    private boolean preCheckInverterVO(List<InverterVO> inverterList) {
        boolean allEndWithX = inverterList.stream()
                .allMatch(inverterVO -> inverterVO.getSn().toLowerCase().endsWith("x"));

        boolean noneEndWithX = inverterList.stream()
                .noneMatch(inverterVO -> inverterVO.getSn().toLowerCase().endsWith("x"));

        if (allEndWithX || noneEndWithX) {
            log.info("虚拟逆变器检验通过！");
            return true;
        }
        log.error("电站不能虚拟逆变器与逆变器混合！");
        return false;
    }

    public TPowerStationEntity buildPs(CreateSaveFinishEvent msg) {

        TOperationEntity tCompanyEntity = operationService.lambdaQuery().eq(TOperationEntity::getOperationCode, msg.getOrgCode()).one();
        if (tCompanyEntity == null) {
            throw new BussinessException("未获取到运维商信息！" + msg.getDeveloperAgentName());
        }

        TPowerStationEntity ps = TPowerStationEntity.builder()
                .psCode(msg.getStationNo())
                .psNumber(msg.getStationNo())
                .lat(new BigDecimal(msg.getLatitude()))
                .lng(new BigDecimal(msg.getLongitude()))
                .address(msg.getProjectAddressDetail())
                .owne(msg.getShowName())
                .completedApprovalTime(msg.getCompletedApprovalTime())
                .epc(msg.getEpcorg())
                .dev(msg.getDeveloperAgentName())
                .install(msg.getDeveloperAgentName())
                .server(msg.getDeveloperAgentName())
                .serviceOrgCode(msg.getOrgCode())
                .operationCode(msg.getOrgCode())
                .provinceCompanyCode(msg.getProvinceCompanyCode())
                .provinceCompanyName(msg.getProvinceCompanyName())
                .empNo(msg.getSalerCode())
                .operationAgent(msg.getOperationAgentName())
                .initTime(DateUtil.date())
                .productName(msg.getProductName())
                .productCode(msg.getProductNo())
                .businessCode(msg.getBusinessMode())
                .businessName(BzBusinessModeEnum.getDescByCode(Integer.valueOf(msg.getBusinessMode())))
                .psType(1)
                .capacity(msg.getCapacity())
                .devType(msg.getDevType())
                .intentno(msg.getIntentNo())
                .filingType(msg.getPowerGridType())
                .projectCompanyCode(msg.getPropertyOrgCode())
                .build();
        if (StringUtils.isBlank(msg.getProjectProvinceCode()) || StringUtils.isBlank(msg.getProjectCityCode())
                || StringUtils.isBlank(msg.getProjectAreaCode())) {
            throw new BussinessException("地区编码不能为空！");
        }
        // 项目公司数据更新
        if (StringUtils.isNotBlank(msg.getPropertyOrgCode())) {
            TProjectCompanyEntity projectCompanyEntity = projectCompanyService.getOne(new LambdaQueryWrapper<TProjectCompanyEntity>()
                    .eq(TProjectCompanyEntity::getCode, msg.getPropertyOrgCode()));
            if (null == projectCompanyEntity) {
                TProjectCompanyEntity.builder()
                        .code(msg.getPropertyOrgCode())
                        .devType(msg.getDevType())
                        .name(msg.getShowName())
                        .build().insert();
            }else{
                if(projectCompanyEntity.getDevType()==null){
                    projectCompanyService.lambdaUpdate().set(TProjectCompanyEntity::getDevType,msg.getDevType())
                            .eq(TProjectCompanyEntity::getId,projectCompanyEntity.getId()).update(new TProjectCompanyEntity());
                }else if(!projectCompanyEntity.getDevType().equals(msg.getDevType())){
                    projectCompanyService.lambdaUpdate().set(TProjectCompanyEntity::getDevType,2)
                            .eq(TProjectCompanyEntity::getId,projectCompanyEntity.getId()).update(new TProjectCompanyEntity());
                }
            }
        }


        //区域信息补充
        //省
        TRegionEntity regionP = tRegionService.getById(msg.getProvinceId());
        if (regionP == null) {
            regionP = new TRegionEntity();
            SysProvinceEntity sysProvince = sysProvinceService.getById(msg.getProvinceId());
            regionP.setId(Long.valueOf(sysProvince.getId()));
            regionP.setName(sysProvince.getName());
            regionP.setLevel(0);
            regionP.setPid(0L);
            regionP.setAdCode(sysProvince.getCode().substring(0, 6));
            regionP.setProvinceId(regionP.getId());
            regionP.setStatus(1);
            regionP.setProvinceName(sysProvince.getName());
            tRegionService.save(regionP);
        }

        ps.setProvinceId(regionP.getId());
        //市
        TRegionEntity regionC = tRegionService.getById(msg.getCityId());
        if (regionC == null) {
            regionC = new TRegionEntity();
            SysCityEntity sysCity = sysCityService.getById(msg.getCityId());
            regionC.setId(Long.valueOf(sysCity.getId()));
            regionC.setName(sysCity.getName());
            regionC.setLevel(1);
            regionC.setPid(regionP.getId());
            regionC.setAdCode(sysCity.getCode().substring(0, 6));
            regionC.setCityId(regionC.getId());
            regionC.setProvinceId(regionP.getId());
            regionC.setStatus(1);
            regionP.setProvinceName(regionP.getName());
            regionC.setCityName(sysCity.getName());
            tRegionService.save(regionC);
        }
        ps.setCityId(regionC.getCityId());
        //区
        TRegionEntity regionR = tRegionService.getById(msg.getAreaId());
        if (regionR == null) {
            regionR = new TRegionEntity();
            SysAreaEntity sysArea = sysAreaService.getById(msg.getAreaId());
            regionR.setId(Long.valueOf(sysArea.getId()));
            regionR.setName(sysArea.getName());
            regionR.setLevel(2);
            regionR.setPid(regionC.getId());
            regionR.setAdCode(sysArea.getCode().substring(0, 6));
            regionR.setCityId(regionC.getId());
            regionR.setProvinceId(regionP.getId());
            regionR.setStatus(1);
            regionR.setProvinceName(regionP.getName());
            regionR.setCityName(regionC.getName());
            regionR.setAreaId(regionR.getId());
            regionR.setAreaName(regionR.getName());
            tRegionService.save(regionR);
        }else {
            log.info("完工更新区id" + msg.getAreaId());
            SysAreaEntity sysArea = sysAreaService.getById(msg.getAreaId());
            tRegionService.lambdaUpdate()
                    .set(TRegionEntity::getProvinceId, regionP.getId())
                    .set(TRegionEntity::getCityId, regionC.getId())
                    .set(TRegionEntity::getAreaId, regionR.getId())
                    .set(TRegionEntity::getName, sysArea.getName())
                    .set(TRegionEntity::getAreaName, sysArea.getName())
                    .eq(TRegionEntity::getId, regionR.getId()).update();
        }
        ps.setRegionId(regionR.getId());

        //镇
        SysTownEntity sysTown = sysTownService.lambdaQuery().eq(SysTownEntity::getId, msg.getTownId())
                .one();
        if(sysTown!=null){
            TRegionEntity regionT = tRegionService.getById(Long.valueOf(sysTown.getId()+sysTown.getCode().substring(0, 9)));
            if (regionT == null && msg.getTownId() != null) {
                regionT = new TRegionEntity();
                log.info("完工新增镇id" + msg.getTownId());
                if (sysTown != null && sysTown.getAreaId() != null) {
                    regionT.setId(Long.valueOf(sysTown.getId()+sysTown.getCode().substring(0, 9)));
                    regionT.setName(sysTown.getName());
                    regionT.setLevel(3);
                    regionT.setPid(Long.valueOf(sysTown.getAreaId()));
                    regionT.setAdCode(sysTown.getCode().substring(0, 9));

                    regionT.setProvinceId(regionP.getId());
                    regionT.setCityId(regionC.getId());
                    regionT.setTownId(regionT.getId());
                    regionT.setAreaId(regionR.getId());

                    regionT.setStatus(1);

                    regionT.setProvinceName(regionP.getName());
                    regionT.setCityName(regionC.getName());
                    regionT.setAreaName(regionR.getName());
                    regionT.setTownName(regionT.getName());
                    tRegionService.save(regionT);
                    ps.setTownId(Long.valueOf(msg.getTownId()));
                }
            }else{
                log.info("完工更新镇id" + msg.getTownId());
                if (sysTown != null && sysTown.getAreaId() != null) {
                    tRegionService.lambdaUpdate()
                            .set(TRegionEntity::getProvinceId,regionP.getId())
                            .set(TRegionEntity::getCityId,regionC.getId())
                            .set(TRegionEntity::getAreaId,regionR.getId())
                            .set(TRegionEntity::getName,sysTown.getName())
                            .set(TRegionEntity::getTownName,sysTown.getName())
                            .eq(TRegionEntity::getId,regionT.getId()).update();
                }

            }
        }



        // 同步省公司配置表(t_province_company_config)
        TProvinceCompanyConfigEntity provinceCompanyConfigEntity = provinceCompanyConfigService.getOne(new LambdaQueryWrapper<TProvinceCompanyConfigEntity>()
                .eq(TProvinceCompanyConfigEntity::getProvinceCompanyCode, msg.getProvinceCompanyCode())
                .eq(TProvinceCompanyConfigEntity::getDelFlag, DelFlagEnum.DISABLE.type));
        if (null == provinceCompanyConfigEntity) {
            BaseOrganizeEntity baseOrganizeEntity = baseOrganizeService.getOne(new LambdaQueryWrapper<BaseOrganizeEntity>()
                    .eq(BaseOrganizeEntity::getFEncode, msg.getProvinceCompanyCode()));

            TProvinceCompanyConfigEntity.builder()
                    .provinceCompanyCode(msg.getProvinceCompanyCode())
                    .xygProvinceCompanyName(msg.getProvinceCompanyName())
                    .ymProvinceCompanyName(null == baseOrganizeEntity ? null : baseOrganizeEntity.getFFullname())
                    .build().insert();
        } else {
            provinceCompanyConfigEntity.setXygProvinceCompanyName(msg.getProvinceCompanyName());
            provinceCompanyConfigEntity.setProvinceCompanyCode(msg.getProvinceCompanyCode());
            provinceCompanyConfigService.updateById(provinceCompanyConfigEntity);
        }
        return ps;
    }

}
