package com.gcl.psmis.rmq;

import com.gcl.psmis.framework.workflow.flow.fegin.YinMaiApi;
import com.gcl.psmis.msg.api.SendRpcService;
import com.gcl.psmis.workflow.api.WorkFlowRpcService;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.boot.context.properties.EnableConfigurationProperties;
import org.springframework.cloud.client.discovery.EnableDiscoveryClient;
import org.springframework.cloud.openfeign.EnableFeignClients;
import org.springframework.context.annotation.ComponentScan;
import org.springframework.context.annotation.ComponentScans;

@SpringBootApplication
@EnableDiscoveryClient
@EnableConfigurationProperties
@EnableFeignClients(clients ={YinMaiApi.class, SendRpcService.class, WorkFlowRpcService.class} )
@ComponentScans(@ComponentScan(basePackages = {"com.gcl.psmis.framework","com.gcl.psmis.manager"}))
public class PsmisRMQApplication {

    public static void main(String[] args) {
        SpringApplication.run(PsmisRMQApplication.class, args);
    }

}
