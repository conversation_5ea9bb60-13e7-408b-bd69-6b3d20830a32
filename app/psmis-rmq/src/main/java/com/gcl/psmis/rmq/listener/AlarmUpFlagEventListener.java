package com.gcl.psmis.rmq.listener;

import com.gcl.psmis.framework.common.constant.enums.AlarmUpFlagEnum;
import com.gcl.psmis.framework.common.util.JacksonHelper;
import com.gcl.psmis.framework.mq.enums.EventTypeEnum;
import com.gcl.psmis.framework.mq.event.alarm.AlarmUpFlagEvent;
import com.gcl.psmis.framework.mq.listener.EventListener;
import com.gcl.psmis.framework.redis.enums.IotRedisKeyEnum;
import com.gcl.psmis.framework.taos.domain.entity.PsIotAlarmStInverterEntity;
import com.gcl.psmis.framework.taos.wrapper.TdWrappers;
import com.gcl.psmis.rmq.dao.alarm.AlarmUpFlagDao;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;

/**
* @className: AlarmUpFlagEventListener
* @author: xinan.yuan
* @create: 2023/8/28 10:53
* @description: 告警置牌消息
*/
@Service
@Slf4j
public class AlarmUpFlagEventListener implements EventListener<AlarmUpFlagEvent> {

    @Resource(name = "redisTemplate2")
    private RedisTemplate<String, Object> redisTemplate2;

    @Autowired
    private AlarmUpFlagDao alarmUpFlagDao;


    @Override
    public EventTypeEnum getEventType() {
        return EventTypeEnum.ALARM_UP_FLAG;
    }

    @Override
    public void eventHandler(AlarmUpFlagEvent msg) {

        log.info("置牌消息 eventHandler:{}", JacksonHelper.objectToJson(msg));
        if(msg.getUpFlag()==null||msg.getUpTime()==null||msg.getAlarmId()==null){
            log.error("置牌消息信息缺失！");
            return;
        }
        String key = String.format(IotRedisKeyEnum.IOT_ALARM_UP_FLAG.getKey(), msg.getSn(), msg.getPsId(), msg.getFaultCode());
        //设置置牌redis
        if(msg.getUpFlag().equals(AlarmUpFlagEnum.UP.getCode())){
            redisTemplate2.opsForValue().set(key, msg.getUpTime());
        }else{
            redisTemplate2.delete(key);
        }

        PsIotAlarmStInverterEntity entity = TdWrappers.lambdaQuery(PsIotAlarmStInverterEntity.class)
                .eq(PsIotAlarmStInverterEntity::getPsId, msg.getPsId())
                .eq(PsIotAlarmStInverterEntity::getAlarmId, msg.getAlarmId())
                .eq(PsIotAlarmStInverterEntity::getSn, msg.getSn())
                .eq(PsIotAlarmStInverterEntity::getFaultCode, msg.getFaultCode())
                .last();
        if (entity != null) {
            entity.setUpFlag(msg.getUpFlag());
            alarmUpFlagDao.updateAlarmUpFlag(entity);
        }

    }
}
