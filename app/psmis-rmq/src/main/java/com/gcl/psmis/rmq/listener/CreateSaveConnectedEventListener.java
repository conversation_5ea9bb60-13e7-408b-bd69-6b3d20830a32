package com.gcl.psmis.rmq.listener;

import cn.hutool.core.exceptions.ExceptionUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.gcl.psmis.framework.common.constant.enums.DelFlagEnum;
import com.gcl.psmis.framework.common.exception.BussinessException;
import com.gcl.psmis.framework.common.util.PsSynchronizationAccountNumberUtils;
import com.gcl.psmis.framework.mbg.entity.TPowerStationEntity;
import com.gcl.psmis.framework.mbg.entity.TPowerStationExtendEntity;
import com.gcl.psmis.framework.mbg.service.TPowerStationExtendService;
import com.gcl.psmis.framework.mbg.service.TPowerStationService;
import com.gcl.psmis.framework.mq.enums.EventTypeEnum;
import com.gcl.psmis.framework.mq.event.CreateSaveConnectedEvent;
import com.gcl.psmis.framework.mq.listener.EventListener;
import com.gcl.psmis.framework.mq.producer.MqProducerUtil;
import com.gcl.psmis.manager.CountPowerManager;
import com.gcl.psmis.manager.SunPowerTimeManager;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * @className: PsPowerTimeEventListener
 * @author: xinan.yuan
 * @create: 2023/8/1 20:44
 * @description: 并网时间过滤
 */
@Service
@Slf4j
public class CreateSaveConnectedEventListener implements EventListener<CreateSaveConnectedEvent> {

    @Autowired
    private TPowerStationService tPowerStationService;

    @Autowired
    private MqProducerUtil mqProducerUtil;

    @Autowired
    private TPowerStationExtendService powerStationExtendService;


    @Autowired
    private CountPowerManager startPowerTimeManager;
    @Autowired
    private SunPowerTimeManager sunPowerTimeManager;


    @Autowired
    private PsSynchronizationAccountNumberUtils psSynchronizationAccountNumberUtils;

    @Override
    public EventTypeEnum getEventType() {
        return EventTypeEnum.CREATE_SAVE_CONNECTED;
    }

    @Override
    public void eventHandler(CreateSaveConnectedEvent msg) {
        if (msg.getStationNo() == null || msg.getEndCheckTime() == null) {
            return;
        }
        TPowerStationEntity ps = tPowerStationService.lambdaQuery().eq(TPowerStationEntity::getPsCode, msg.getStationNo()).one();
        if (ps == null) {
            log.error("电站{}未入库，无法更新并网时间！", msg.getStationNo());
            throw new BussinessException("电站未入库" + msg.getStationNo() + "请稍后重试！");
        }

        tPowerStationService.lambdaUpdate()
                .set(TPowerStationEntity::getPowerTime, msg.getCompletedTime())
                .set(TPowerStationEntity::getPowerTimeApply, msg.getApplyTime())
                .set(TPowerStationEntity::getPowerTimeBack, msg.getReceiptTime())
                .set(TPowerStationEntity::getPowerCheckTime, msg.getEndCheckTime())
                .set(TPowerStationEntity::getStartPowertime,msg.getFirstSendPowerTime())
                .set(TPowerStationEntity::getArFlag, 1)
                .set(TPowerStationEntity::getAccountNumber, msg.getAccountNum())
                .set(TPowerStationEntity::getElecMeter, msg.getAmmeterNo())
                .eq(TPowerStationEntity::getId, ps.getId())
                .update();
        // 同步电站发电户号
        try {
            psSynchronizationAccountNumberUtils.psSynchronizationAccountNumber(msg.getStationNo(),msg.getAccountNum());
        }catch (Exception e){
            log.error("同步电站发电户号失败：电站code:"+msg.getStationNo()+"发电户号："+msg.getAccountNum()+"失败原因"+ ExceptionUtil.stacktraceToString(e));
        }
        // 更新电站拓展表字段
        TPowerStationExtendEntity psExtend = powerStationExtendService.getOne(new LambdaQueryWrapper<TPowerStationExtendEntity>()
                .eq(TPowerStationExtendEntity::getPsId, ps.getId())
                .eq(TPowerStationExtendEntity::getDelFlag, DelFlagEnum.DISABLE.type));
        if (null != psExtend) {
            psExtend.setNetTime(msg.getEndCheckTime());
            psExtend.setStateGridName(msg.getStategridName());
            psExtend.setNetCard(msg.getBankNo());
            powerStationExtendService.updateById(psExtend);
        } else {
            TPowerStationExtendEntity powerStationExtendEntity = new TPowerStationExtendEntity();
            powerStationExtendEntity.setNetTime(msg.getEndCheckTime());
            powerStationExtendEntity.setStateGridName(msg.getStategridName());
            powerStationExtendEntity.setNetCard(msg.getBankNo());
            powerStationExtendEntity.setPsId(ps.getId());
            powerStationExtendService.save(powerStationExtendEntity);
        }


    }

}
