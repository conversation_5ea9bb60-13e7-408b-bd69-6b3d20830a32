<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.gcl.psmis.app.td.task.dao.SyncPowerDao">

    <update id="batchUpdatePsKwMulti">
        <foreach collection="list" item="item" index="index" open="" close="" separator=";">
            update t_ps_kw
            <trim prefix="set" suffixOverrides=",">
                <if test="item.pac !=null">
                    pac=#{item.pac},
                </if>
                <if test="item.pac ==null">
                    pac=null,
                </if>
                <if test="item.etdAfter !=null">
                    etd_after=#{item.etdAfter},
                </if>
                <if test="item.etd !=null">
                    etd=#{item.etd},
                </if>
                <if test="item.etd ==null">
                    etd= null,
                </if>
                <if test="item.etdForecast !=null">
                    etd_forecast=#{item.etdForecast}/100,
                </if>
                <if test="item.etdForecast !=null and item.etdForecast!=0">
                    etd_efficiency=etd/etd_forecast,
                </if>
                <if test="item.emonth !=null">
                    emonth=#{item.emonth},
                </if>
                <if test="item.emonthForecast !=null">
                    emonth_forecast=#{item.emonthForecast}/100,
                </if>
                <if test="item.emonthForecast !=null and item.emonthForecast!=0">
                    emonth_efficiency=emonth/emonth_forecast,
                </if>
                <if test="item.eyear !=null">
                    eyear=#{item.eyear},
                </if>
                <if test="item.eyearForecast !=null">
                    eyear_forecast=#{item.eyearForecast}/100,
                </if>
                <if test="item.eyearForecast !=null and item.eyearForecast!=0">
                    eyear_efficiency=eyear/eyear_forecast,
                </if>
                <if test="item.eto !=null">
                    eto=#{item.eto},
                </if>
                <if test="item.etoForecast !=null">
                    eto_forecast=#{item.etoForecast}/100,
                </if>
                <if test="item.etoForecast !=null and item.etoForecast!=0">
                    eto_efficiency=eto/eto_forecast,
                </if>
                <if test="item.inverterEto !=null">
                    inverter_eto=#{item.inverterEto},
                </if>
                <if test="item.eyesterday !=null">
                    eyesterday=#{item.eyesterday},
                </if>
                <if test="item.eyesterdayForecast !=null">
                    eyesterday_forecast=#{item.eyesterdayForecast}/100,
                </if>
            </trim>
            where ps_id=#{item.psId} and del_flag = 0
        </foreach>
    </update>

    <update id="batchUpdateInverterKwMulti">
        <foreach collection="list" item="item" index="index" open="" close="" separator=";">
            update t_inverter_kw
            <trim prefix="set" suffixOverrides=",">
                <if test="item.etd !=null and item.ts !=null">
                    etd=#{item.etd},
                </if>
                <if test="item.pac !=null and item.ts !=null">
                    pac=#{item.pac},
                </if>
                <if test="item.tmp !=null and item.ts !=null">
                    tmp=#{item.tmp},
                </if>
                <if test="item.ts !=null">
                    ts=#{item.ts},
                </if>
            </trim>
            where inverter_sn=#{item.inverterSn}
        </foreach>
    </update>
    <update id="resetInverterKw">
        update t_inverter_kw
        set etd=null,
            pac=null,
            tmp=null
        where del_flag = 0 `etd` IS NOT NULL and ts &lt;= #{endTime}
    </update>


    <select id="listPsDaysPower" resultType="com.gcl.psmis.framework.common.dto.tdtask.PsPowerDTO">
        select
        sum(`etd`) as `power`,
        ps_id
        from
        ps_iot_day.st_inverter
        <where>
            <if test=' startTime !=null and startTime != "" '>
                and `ts` &gt;= #{startTime}
            </if>
            <if test=' endTime !=null and endTime != "" '>
                and `ts` &lt;= #{endTime}
            </if>
            <if test=' psId !=null and psId != "" '>
                and ps_id= #{psId}
            </if>
            and ps_id is not null and ps_id !='null'
        </where>
        PARTITION BY
        ps_id
    </select>
    <select id="listPsId" resultType="java.lang.Long">
        select ps_id
        from t_ps_kw
        <where>
            del_flag=0
            <if test=' psId !=null and psId != "" '>
                and ps_id= #{psId}
            </if>
        </where>
    </select>


    <select id="listPsLastEtd" resultType="com.gcl.psmis.framework.common.dto.tdtask.PsPowerDTO">
        select sum(etd) as power,sum(pac) pac,ps_id from (select last_row(etd) etd,last_row(pac)
        pac,last_row(ps_station_id) ps_id from ps_iot.st_inverter
        <where>
            <if test="isAfter !=null">
                and is_after = #{isAfter}
            </if>
            <if test=' startTime !=null and startTime != "" '>
                and `ts` &gt;= #{startTime}
            </if>
            <if test=' endTime !=null and endTime != "" '>
                and `ts` &lt;= #{endTime}
            </if>
            and ps_id is not null and ps_id !='null'
        </where>
        PARTITION BY
        tbname)
        PARTITION BY ps_id;
    </select>

    <select id="listInverterSn" resultType="java.lang.String">
        select inverter_sn
        from t_inverter_kw
        where del_flag = 0
    </select>

    <select id="findInverterId" resultType="java.lang.String">
        select inverter_sn
        from t_inverter_kw
        where del_flag = 0
    </select>

    <select id="listInverterPac" resultType="com.gcl.psmis.framework.common.dto.tdtask.InverterDTO">
        select last_row(tmp) tmp, last_row(pac) pac, last_row(sn) InverterSn, last_row(ts) ts
        from iot.st_inverter partition by tbname
    </select>

    <select id="findInverterSn" resultType="java.lang.String">
        select last_row(sn) sn
        from iot.st_inverter partition by sn
    </select>

    <select id="findInverterKw" resultType="com.gcl.psmis.framework.common.dto.tdtask.InverterDTO">
        select last_row(pac) pac,last_row(tmp) tmp,last_row(sn) inverterSn,last_row(etd) etd, last_row(ts) ts from
        iot.st_inverter
        <where>
            <if test=' startTime !=null and startTime != "" '>
                and `ts` &gt;= #{startTime}
            </if>
            <if test=' endTime !=null and endTime != "" '>
                and `ts` &lt;= #{endTime}
            </if>
        </where>
        partition by tbname
    </select>

    <select id="findInverterBySn" resultType="com.gcl.psmis.framework.common.dto.tdtask.InverterDTO">
        select last_row(pac) pac,last_row(tmp) tmp,last_row(sn) inverterSn,last_row(etd) etd, last_row(ts) ts from
        iot.st_inverter
        <where>
            <if test="sn!=null and sn!=''">
                and `sn` = #{sn}
            </if>
            <if test=' startTime !=null and startTime != "" '>
                and `ts` &gt;= #{startTime}
            </if>
            <if test=' endTime !=null and endTime != "" '>
                and `ts` &lt;= #{endTime}
            </if>
        </where>
        partition by tbname
    </select>
</mapper>