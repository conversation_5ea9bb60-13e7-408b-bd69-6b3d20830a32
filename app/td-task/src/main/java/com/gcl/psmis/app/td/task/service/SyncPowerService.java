package com.gcl.psmis.app.td.task.service;

import cn.hutool.core.collection.ListUtil;
import cn.hutool.core.date.DatePattern;
import cn.hutool.core.date.DateTime;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.thread.ThreadUtil;
import com.gcl.psmis.app.td.task.dao.SyncPowerDao;
import com.gcl.psmis.framework.common.dto.tdtask.InverterDTO;
import com.gcl.psmis.framework.common.dto.tdtask.PsPowerDTO;
import com.gcl.psmis.framework.mbg.entity.TPsKwEntity;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.concurrent.CountDownLatch;
import java.util.concurrent.atomic.AtomicReference;
import java.util.function.Function;
import java.util.stream.Collectors;


/**
 * @className: SyncPowerService
 * @author: xinan.yuan
 * @create: 2023/7/30 11:38
 * @description: 同步计算电站发电量到t_ps_kw表
 */
@Service
@Slf4j
public class SyncPowerService {


    @Autowired
    private SyncPowerDao syncPowerDao;


    /**
     * 统计月年总截止到昨日(并网后、并网前+并网后)、并网前总发电量、昨日发电量（每日凌晨执行）
     * 10分钟算完100w
     */
    public void syncYesPsPower(String psId) {

        long start = System.currentTimeMillis();
        System.out.println("start:" + start);
        log.info("start:" + start);

        DateTime nowDate = DateUtil.date();

        String yesterday = DateUtil.endOfDay(DateUtil.offsetDay(nowDate, -1)).toString();

        String startYesterday = DateUtil.beginOfDay(DateUtil.offsetDay(nowDate, -1)).toString();

        //库中已有psId
        List<Long> psIds = syncPowerDao.listPsId(psId);

        //======td 所有电站当月(不分并网前后)
        List<PsPowerDTO> psEmonthDTOS = syncPowerDao.listPsDaysPower(DateUtil.beginOfMonth(nowDate).toString(DatePattern.NORM_DATETIME_PATTERN), yesterday, psId);
        //根据psId聚合
        Map<String, PsPowerDTO> psEmonthMap = psEmonthDTOS.parallelStream().collect(Collectors.toMap(PsPowerDTO::getPsId, Function.identity(), (key1, key2) -> key2));

        //======td 所有电站当年(不分并网前后)
        List<PsPowerDTO> psEyearDTOS = syncPowerDao.listPsDaysPower(DateUtil.beginOfYear(nowDate).toString(DatePattern.NORM_DATETIME_PATTERN), yesterday, psId);
        //根据psId聚合
        Map<String, PsPowerDTO> psEyearMap = psEyearDTOS.parallelStream().collect(Collectors.toMap(PsPowerDTO::getPsId, Function.identity(), (key1, key2) -> key2));

        //======td 所有电站总发电量(不分并网前后)
        List<PsPowerDTO> psEtotalDTOS = syncPowerDao.listPsDaysPower(null, yesterday, psId);
        //根据psId聚合
        Map<String, PsPowerDTO> psEtotalMap = psEtotalDTOS.parallelStream().collect(Collectors.toMap(PsPowerDTO::getPsId, Function.identity(), (key1, key2) -> key2));

        //===========td 昨日发电量(不分并网前后)
        List<PsPowerDTO> eyesterdayDTOS = syncPowerDao.listPsDaysPower(startYesterday, yesterday, psId);
        //根据psId聚合
        Map<String, PsPowerDTO> eyesterdayMap = eyesterdayDTOS.parallelStream().collect(Collectors.toMap(PsPowerDTO::getPsId, Function.identity(), (key1, key2) -> key2));

        List<TPsKwEntity> tPsKwEntityList = psIds.stream().map(psid -> {
            TPsKwEntity tPsKwEntity = new TPsKwEntity();
            tPsKwEntity.setPsId(psid);
            tPsKwEntity.setEmonth(BigDecimal.ZERO);
            if (psEmonthMap != null && psEmonthMap.size() > 0) {
                PsPowerDTO psPowerDTO = psEmonthMap.get(String.valueOf(psid));
                if (psPowerDTO != null) {
                    tPsKwEntity.setEmonth(psPowerDTO.getPower());
                } else {
                    tPsKwEntity.setEmonth(BigDecimal.ZERO);
                }
            }

            tPsKwEntity.setEyear(BigDecimal.ZERO);
            if (psEyearMap != null && psEyearMap.size() > 0) {
                PsPowerDTO psPowerDTO = psEyearMap.get(String.valueOf(psid));
                if (psPowerDTO != null) {
                    tPsKwEntity.setEyear(psPowerDTO.getPower());
                } else {
                    tPsKwEntity.setEyear(BigDecimal.ZERO);
                }

            }
            tPsKwEntity.setEto(BigDecimal.ZERO);
            if (psEtotalMap != null && psEtotalMap.size() > 0) {
                PsPowerDTO psPowerDTO = psEtotalMap.get(String.valueOf(psid));
                if (psPowerDTO != null) {
                    tPsKwEntity.setEto(psPowerDTO.getPower());
                } else {
                    tPsKwEntity.setEto(BigDecimal.ZERO);
                }
            }
            tPsKwEntity.setEyesterday(BigDecimal.ZERO);
            if (eyesterdayMap != null && eyesterdayMap.size() > 0) {
                PsPowerDTO psPowerDTO = eyesterdayMap.get(String.valueOf(psid));
                if (psPowerDTO != null) {
                    tPsKwEntity.setEyesterday(psPowerDTO.getPower());
                } else {
                    tPsKwEntity.setEyesterday(BigDecimal.ZERO);
                }
            }
            return tPsKwEntity;
        }).collect(Collectors.toList());

        //更新数据表
        this.updatePsList(tPsKwEntityList);

        long end = System.currentTimeMillis();
        System.out.println("end:" + end);
        System.out.println("sql 运行时间" + (end - start) / 1000 + "s");
        log.info("end:" + end);
        log.info("sql 运行时间" + (end - start) / 1000 + "s");

    }

    /**
     * 批量更新发电量表
     *
     * @param tPsKwEntityList
     */
    private void updatePsList(List<TPsKwEntity> tPsKwEntityList) {
        if (tPsKwEntityList.size() < 10000) {
            syncPowerDao.batchUpdatePsKwMulti(tPsKwEntityList);
        } else {
            List<List<TPsKwEntity>> split = ListUtil.split(tPsKwEntityList, 10000);
            split.parallelStream().forEach(tPsKwEntities -> {
                syncPowerDao.batchUpdatePsKwMulti(tPsKwEntities);
            });
        }
    }


    /**
     * @param
     * @return
     * @desc 批量同步今日发电量、今日能效、实时功率(每5分钟)
     * @date 2023/7/30 20:30
     */
    public void syncTodayPsPower(String psId) {
        long start = System.currentTimeMillis();
        System.out.println("start:" + start);
        log.info("start:" + start);

        DateTime nowDate = DateUtil.date();
        //库中已有psId
        List<Long> psIds = syncPowerDao.listPsId(psId);
        //初始化线程数量
        CountDownLatch countDownLatch = ThreadUtil.newCountDownLatch(2);

        AtomicReference<Map<String, PsPowerDTO>> psEdayMap = new AtomicReference<>();
        ThreadUtil.execute(() -> {
            //======td 所有电站每天最新etd
            List<PsPowerDTO> psEdayDTOS = syncPowerDao.listPsLastEtd(DateUtil.beginOfDay(nowDate).toString(DatePattern.NORM_DATETIME_PATTERN), DateUtil.endOfDay(nowDate).toString(DatePattern.NORM_DATETIME_PATTERN), null);
            //根据psId聚合
            psEdayMap.set(psEdayDTOS.parallelStream().collect(Collectors.toMap(PsPowerDTO::getPsId, Function.identity(), (key1, key2) -> key2)));

            //调用线程计数器-1
            countDownLatch.countDown();
        });


        AtomicReference<Map<String, PsPowerDTO>> psPacMap = new AtomicReference<>();
        ThreadUtil.execute(() -> {
            //======td 所有电站每天最新pac
            //获取近15分钟功率
            List<PsPowerDTO> psPacDTOS = syncPowerDao.listPsLastEtd(DateUtil.offsetMinute(nowDate, -15).toString(DatePattern.NORM_DATETIME_PATTERN), DateUtil.endOfDay(nowDate).toString(DatePattern.NORM_DATETIME_PATTERN), null);
            //根据psId聚合
            psPacMap.set(psPacDTOS.parallelStream().collect(Collectors.toMap(PsPowerDTO::getPsId, Function.identity(), (key1, key2) -> key2)));

            //调用线程计数器-1
            countDownLatch.countDown();
        });

        //唤醒主线程
        try {
            countDownLatch.await();
        } catch (InterruptedException e) {
            e.printStackTrace();
        }

        //数据合并
        List<TPsKwEntity> tPsKwEntityList = psIds.stream().map(psid -> {

            TPsKwEntity tPsKwEntity = new TPsKwEntity();
            tPsKwEntity.setPsId(psid);
            setPsEday(psEdayMap, psid, tPsKwEntity, -1);
            setPsEday(psPacMap, psid, tPsKwEntity, 3);
            return tPsKwEntity;
        }).collect(Collectors.toList());

        //更新数据表
        this.updatePsList(tPsKwEntityList);

        long end = System.currentTimeMillis();
        System.out.println("end:" + end);
        System.out.println("sql 运行时间" + (end - start) / 1000 + "s");
        log.info("end:" + end);
        log.info("sql 运行时间" + (end - start) / 1000 + "s");
    }

    /**
     * 赋值当天发电量、当天预测发电量
     *
     * @param psEdayMap
     * @param psId
     * @param edayType  0 预测其他 1预测天合 2预测资方 3 实时功率 -1 实际发电量  4 并网后当日
     */
    private void setPsEday(AtomicReference<Map<String, PsPowerDTO>> psEdayMap, Long psId, TPsKwEntity tPsKwEntity, Integer edayType) {
        //先赋0
        if (edayType.equals(-1)) {
            tPsKwEntity.setEtd(null);
        } else if (edayType.equals(3)) {
            tPsKwEntity.setPac(null);
        } else if (edayType.equals(4)) {
            tPsKwEntity.setEtdAfter(BigDecimal.ZERO);
        }
        if (psEdayMap != null && psEdayMap.get() != null && psEdayMap.get().size() > 0) {
            PsPowerDTO psEdayDTO = psEdayMap.get().get(String.valueOf(psId));
            if (psEdayDTO != null) {
                if (edayType.equals(-1)) {
                    tPsKwEntity.setEtd(psEdayDTO.getPower());
                } else if (edayType.equals(3)) {
                    tPsKwEntity.setPac(psEdayDTO.getPac());
                } else if (edayType.equals(4)) {
                    tPsKwEntity.setEtdAfter(psEdayDTO.getPower());
                }
            }
        }
    }

    public void syncTodayInverter(String sn) {
        long start = System.currentTimeMillis();
        System.out.println("start:" + start);
        log.info("start:" + start);

        LocalDateTime currentTime = LocalDateTime.now();
//        List<String> stringList = syncPowerDao.findInverterId();
        // 获取当前时间前五分钟的时间
        LocalDateTime fiveMinutesAgo = currentTime.minusMinutes(7);
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");
        String currentFormatted = currentTime.format(formatter);
        String fiveMinutesAgoFormatted = fiveMinutesAgo.format(formatter);
        List<InverterDTO> inverterDTOs = new ArrayList<>();
        if (sn!=null && sn.length()>0){
            inverterDTOs = syncPowerDao.findInverterBySn(fiveMinutesAgoFormatted,currentFormatted,sn);
        }else {
            inverterDTOs = syncPowerDao.findInverterKw(fiveMinutesAgoFormatted,currentFormatted );
        }



        //更新数据表
        this.updateInverterList(inverterDTOs);

        syncPowerDao.resetInverterKw(DateUtil.beginOfDay(DateUtil.date()).toString());

        long end = System.currentTimeMillis();
        System.out.println("end:" + end);
        System.out.println("sql 运行时间" + (end - start) / 1000 + "s");
        log.info("end:" + end);
        log.info("sql 运行时间" + (end - start) / 1000 + "s");
    }

    private void updateInverterList(List<InverterDTO> inverterDTOList) {
        if (inverterDTOList.size() == 0) {

        } else if (inverterDTOList.size() < 10000) {
            syncPowerDao.batchUpdateInverterKwMulti(inverterDTOList);
        } else {
            List<List<InverterDTO>> split = ListUtil.split(inverterDTOList, 10000);
            split.parallelStream().forEach(tInverterKwEntities -> {
                syncPowerDao.batchUpdateInverterKwMulti(tInverterKwEntities);
            });
        }
    }

}
