package com.gcl.psmis.app.td.task.dao;

import com.baomidou.dynamic.datasource.annotation.DS;
import com.gcl.psmis.framework.common.dto.tdtask.InverterDTO;
import com.gcl.psmis.framework.common.dto.tdtask.PsPowerDTO;
import com.gcl.psmis.framework.mbg.entity.TPsKwEntity;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Set;

/**
* @className: SyncPowerDao
* @author: xinan.yuan
* @create: 2023/7/30 11:46
* @description:
*/
@Repository
public interface SyncPowerDao {


    /**
     * @desc 每个电站一段时间天发电量之和（截止到昨日）
     * @date 2023/7/30 11:51
     * @param startTime 开始时间
     * @param endTime 结束时间
     * @return {@link List< PsPowerDTO>}
     */
    @DS("td")
    List<PsPowerDTO> listPsDaysPower(@Param("startTime")String startTime, @Param("endTime")String endTime,@Param("psId")String psId);

    @DS("psmis")
    List<Long> listPsId(@Param("psId")String psId);

    @DS("psmis")
    void batchUpdatePsKwMulti(@Param("list") List<TPsKwEntity> tPsKwEntityList);

    @DS("td")
    List<PsPowerDTO> listPsLastEtd(@Param("startTime")String startTime,@Param("endTime")String endTime,@Param("isAfter") Integer isAfter);

    @DS("psmis")
    List<String> listInverterSn();

    @DS("psmis")
    List<String> findInverterId();


    @DS("td")
    List<InverterDTO> listInverterPac();

    @DS("psmis")
    void batchUpdateInverterKwMulti(@Param("list") List<InverterDTO> inverterDTOList);

    @DS("td")
    List<String> findInverterSn();

    @DS("td")
    List<InverterDTO> findInverterKw(@Param("startTime")String startTime,@Param("endTime")String endTime);

    @DS("td")
    List<InverterDTO> findInverterBySn(@Param("startTime")String startTime,@Param("endTime")String endTime, @Param("sn") String sn);


    @DS("psmis")
    void resetInverterKw(@Param("endTime")String endTime);

}
