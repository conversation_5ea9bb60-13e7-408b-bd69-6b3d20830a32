package com.gcl.psmis.ant.task.dao;

import com.baomidou.dynamic.datasource.annotation.DS;
import com.gcl.psmis.framework.common.dto.ant.AntBaseDTO;
import com.gcl.psmis.framework.common.dto.ant.DeviceInfoDTO;
import com.gcl.psmis.framework.common.dto.ant.VenueDTO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;
import java.util.Set;

/**
 * <AUTHOR>
 */
@Mapper
public interface DeviceInfoDao {

    /**
     * 查询智维逆变器数据
     *
     * @param psCodes         电站编号
     * @param zwProjectIdList 智维项目公司编码
     * @return List
     */
    List<DeviceInfoDTO> queryInverterInfo(@Param("psCodes") List<String> psCodes, @Param("zwProjectIdList") List<Integer> zwProjectIdList);

    /**
     * 查询所属地id
     *
     * @param psCodeSet 电站编码
     * @return List
     */
    @DS("xyg")
    List<VenueDTO> venueData(@Param("psCodeSet") Set<String> psCodeSet);

    @DS("xyg")
    AntBaseDTO queryCompanyByOrderId(@Param("orderId") Long orderId);
}
