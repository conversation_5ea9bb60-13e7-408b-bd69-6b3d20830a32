package com.gcl.crontask.test;


import com.gcl.psmis.cron.task.CronTaskApplication;
import com.gcl.psmis.cron.task.service.nkbi.HRWorkFlowService;
import com.gcl.psmis.framework.gencode.enums.GenRuleCode;
import com.gcl.psmis.framework.gencode.util.GenCodeUtil;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.ActiveProfiles;
import org.springframework.test.context.junit4.SpringRunner;

@RunWith(SpringRunner.class)
@SpringBootTest(classes = CronTaskApplication.class)
@ActiveProfiles("dev")
public class FillingFlowTest {
    @Autowired
    private HRWorkFlowService fillService;
    @Autowired
    private GenCodeUtil genCodeUtil;
    @Test
    public void initSinglePlanFillWorkflowTest() {

        fillService.initSinglePlanFillWorkflow("2025-04");

    }

    @Test
    public void initSingleActualFillWorkflowTest() {
        fillService.initSingleActualFillWorkflow("2025-04");

    }


//    @Test
//    public void initSumPlanFillWorkflowTest() {
//
//        fillService.initSumPlanFillWorkflow("2025-01");
//
//    }
//
//    @Test
//    public void initSumActualFillWorkflowTest() {
//        fillService.initSumActualFillWorkflow("2025-01");
//
//    }

}
