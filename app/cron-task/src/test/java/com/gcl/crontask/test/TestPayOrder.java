package com.gcl.crontask.test;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.NumberUtil;
import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import com.alibaba.fastjson.JSON;
import com.baomidou.dynamic.datasource.annotation.DSTransactional;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.gcl.psmis.cron.task.CronTaskApplication;
import com.gcl.psmis.cron.task.dao.payorder.PowerPayOrderDetailDao;
import com.gcl.psmis.cron.task.jobhandler.PowerPayOrderPaymentHandler;
import com.gcl.psmis.cron.task.service.ICBCDetailService;
import com.gcl.psmis.cron.task.service.bill.WorkerBillService;
import com.gcl.psmis.cron.task.service.payorder.*;
import com.gcl.psmis.framework.common.dto.IcbcPojo.*;
import com.gcl.psmis.framework.common.vo.payorder.PowerPayOrderVO;
import com.gcl.psmis.framework.mbg.entity.*;
import com.gcl.psmis.framework.mbg.service.*;
import com.gcl.psmis.manager.service.PayOrderGenerateService;
import com.gcl.psmis.manager.service.PowerPayOrderManager;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.poi.xssf.usermodel.XSSFCell;
import org.apache.poi.xssf.usermodel.XSSFRow;
import org.apache.poi.xssf.usermodel.XSSFSheet;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;
import org.dromara.pdf.pdfbox.core.base.Document;
import org.dromara.pdf.pdfbox.core.ext.extractor.DocumentExtractor;
import org.dromara.pdf.pdfbox.handler.PdfHandler;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.ActiveProfiles;
import org.springframework.test.context.junit4.SpringRunner;

import java.io.ByteArrayInputStream;
import java.io.FileInputStream;
import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.stream.Collectors;

/**
 * @Author: sunjiaxing
 * @CreateTime: 2024-03-21  15:37
 * @Description: TODO
 */
@RunWith(SpringRunner.class)
@SpringBootTest(classes = CronTaskApplication.class)
@ActiveProfiles("dev")
public class TestPayOrder {

    @Autowired
    PowerPayOrderDetailService powerPayOrderDetailService;

    @Autowired
    PowerPayOrderPaymentHandler powerPayOrderPaymentHandler;

    @Autowired
    private PaymentService paymentService;

    @Autowired
    private OrgRuleService orgRuleService;

    @Autowired
    private ICBCDetailService icbcDetailService;

    @Value("${sms.enable:0}")
    private Integer smsEnable;

    @Autowired
    WorkerBillService workerBillService;

    @Autowired
    ICBCService icbcService;

    @Test
    public void test() {
        try {
            powerPayOrderDetailService.getPayOrderDetail();
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    @Test
    public void test2() {
        powerPayOrderPaymentHandler.getECApproveResultAndPayment();
    }

    @Test
    public void test3() {
        System.out.println(smsEnable);
    }

    @Test
    public void test4() {
        paymentService.fillBankNoAndBankName();
    }

    @Test
    public void test5() {
        try {
            paymentService.recoverData20240510_457941();
        } catch (Exception e) {
            e.printStackTrace();
        }

    }

    @Test
    public void test6() {
        try {
            paymentService.recoverData20240511_458026();
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    @Test
    public void test7() {
        paymentService.paymentResultCheck();
    }

    @Test
    public void test8() {
        powerPayOrderDetailService.getPayOrderDetailAll();
    }

    @Test
    public void tesd9() {
        paymentService.paymentResultCheckSecond();
    }

    @Test
    public void test10() {
        paymentService.paymentReturn();
    }

    @Test
    public void test11() {
        orgRuleService.initOrgRule();
    }

    @Autowired
    BankInfoService bankInfoService;

    @Test
    public void test12() {
        bankInfoService.getBankInfoFromXyg();
    }

    @Autowired
    PowerPayOrderGenerateService powerPayOrderGenerateService;

    @Test
    public void test13() {
        powerPayOrderGenerateService.generatePowerPayOrder("first");
    }

    @Test
    public void testJxQYBA() {
        powerPayOrderGenerateService.powerPayOrderGenerateKaiFa();
    }

    @Test
    public void testTF() {
        powerPayOrderGenerateService.generateTFPowerPayOrder();
    }

    @Test
    public void testJIANGXInew() {
        /*powerPayOrderGenerateService.generateJiangXiNew();*/
    }

    @Test
    public void testJX() {
        powerPayOrderGenerateService.generateJiangXiPayOrder();
    }

    @Test
    public void testJXQY() {
        powerPayOrderGenerateService.generateJiangXiPayOrderQYBA();
    }

    @Autowired
    PowerPayOrderManager powerPayOrderManager;

    @Autowired
    private TPowerUserBankInfoService tPowerUserBankInfoService;

    @Test
    public void test14() {
        List<TPowerUserBankInfoEntity> powerUserBankInfoEntities = tPowerUserBankInfoService.list();
        List<PowerPayOrderVO> ordersForAmortization = powerPayOrderManager.getOrdersForAmortization(Arrays.asList("**************"), powerUserBankInfoEntities);
        System.out.println(ordersForAmortization);
    }

    @Autowired
    PowerPayOrderDetailDao powerPayOrderDetailDao;

    @Autowired
    TPayOrderDeductionDetailService tPayOrderDeductionDetailService;

    @Autowired
    TPowerPayOrderService tPowerPayOrderService;

    @Test
    @DSTransactional
    public void test15() {


        List<PowerPayOrderVO> successList = powerPayOrderDetailDao.getOrders();

        //账单状态改为已结算
        List<String> successId = successList.stream().map(PowerPayOrderVO::getId).collect(Collectors.toList());
        tPowerPayOrderService.update(Wrappers.<TPowerPayOrderEntity>lambdaUpdate().in(TPowerPayOrderEntity::getId, successId).set(TPowerPayOrderEntity::getAccountState, 2));

        List<TPayOrderDeductionDetailEntity> detailList = new ArrayList<>();
        //有抵扣记录的添加抵扣明细
        for (PowerPayOrderVO vo : successList) {
            BigDecimal deductionFee = vo.getDeductionFee();
            if (deductionFee != null && deductionFee.compareTo(BigDecimal.ZERO) > 0) {
                //记录结算明细
                TPayOrderDeductionDetailEntity detail = new TPayOrderDeductionDetailEntity();
                detail.setProjectCompanyName(vo.getCompanyName());
                detail.setProjectCompanyCode(vo.getCompanyCode());
                detail.setDeductionMonth(vo.getShareMonth().replaceAll("-", "/"));
                detail.setFilingType(Integer.valueOf(vo.getPowerGridMergeType()));
                detail.setDeductionFee(deductionFee);
                detail.setUntaxDeductionFee(NumberUtil.div(detail.getDeductionFee(), NumberUtil.add(new BigDecimal("1"), new BigDecimal("0.06"))));
                detail.setTax(NumberUtil.sub(detail.getDeductionFee(), detail.getUntaxDeductionFee()));
                detail.setAccountSeq(PaymentService.calcAccountSeq(DateUtil.today()));
                detailList.add(detail);
            }
        }
        if (CollectionUtils.isNotEmpty(detailList)) {
            tPayOrderDeductionDetailService.saveBatch(detailList);
        }
    }

    @Test
    public void test16() {
        bankInfoService.initRuleFromContract();
    }


    @Test
    public void test17() throws Exception {
        bankInfoService.getReceiptFileFromICBC();
    }

    @Test
    public void test18() throws Exception {
        paymentService.fixData();
    }

    @Test
    public void test19() {
        List<PowerPayOrderVO> vos = powerPayOrderDetailDao.getPowerPayOrderByRequestId("529692");
        for (int i = 0; i < vos.size(); i++) {
            PowerPayOrderVO vo = vos.get(i);
            //支付状态都为待支付，真实状态，后续回查
            TUserPaymentLogEntity tUserPaymentLogEntity = icbcService.buildUserPaymentLog(vo, "529692");
            tUserPaymentLogEntity.setSerialNumber("");
            //待支付
            tUserPaymentLogEntity.setPaymentStatus(0);
            tUserPaymentLogEntity.setUniBusiId(vo.getUniBusiId());
            tUserPaymentLogEntity.setFSeqNo(vo.getFSeqNo());
            //人行大小额 否
            tUserPaymentLogEntity.setFinishRhdxe(0);
            tUserPaymentLogEntity.insert();
        }
    }

    @Autowired
    PayOrderGenerateService payOrderGenerateService;

    @Test
    public void test20() {
        payOrderGenerateService.generatePayOrder(Arrays.asList("**************"));
    }

    @Test
    public void test21() {
        ByteArrayInputStream byteArrayInputStream = new ByteArrayInputStream(new byte[100]);
        Document document = PdfHandler.getDocumentHandler().load(byteArrayInputStream);
        DocumentExtractor documentExtractor = PdfHandler.getDocumentExtractor(document);
    }


    @Test
    public void test22() {
        bankInfoService.getBankInfoJiangxiFromXyg();
    }

    @Autowired
    TUserPaymentLogService tUserPaymentLogService;

    @Test
    public void recoverPayment() throws Exception {
        List<PowerPayOrderVO> vos = powerPayOrderDetailDao.getPowerPayOrderByRequestId("551647");
        Map<String, PowerPayOrderVO> map = new HashMap<>();
        for (PowerPayOrderVO vo : vos) {
            String shareMonth = vo.getShareMonth().replace("-", "");
            String psCode = vo.getPsCode().substring(6);
            map.put(psCode + "-" + shareMonth + "租金", vo);
        }
        List<TUserPaymentLogEntity> userPaymentLogEntities = new ArrayList<>();
        FileInputStream fi = new FileInputStream("C:\\Users\\<USER>\\Desktop\\支付补救********.xlsx");
        XSSFWorkbook workbook = new XSSFWorkbook(fi);
        XSSFSheet sheet = workbook.getSheetAt(0);
        for (int i = 0; i <= sheet.getLastRowNum(); i++) {
            XSSFRow row = sheet.getRow(i);
            XSSFCell cell = row.getCell(0);
            String stringCellValue = cell.getStringCellValue();
            if (stringCellValue.contains("VALUES ( 1,")) {
                //sql
                String sql = stringCellValue.substring(stringCellValue.indexOf("Execute SQL：") + 12);
                String dto = sql.substring(sql.indexOf("'{\"dto\"") + 1, sql.indexOf(", '{\"res\"") - 1);
                int start = sql.indexOf("\"serial_no\":\"") + 13;
                int end = start + 17;
                //serialNo
                String serialNo = sql.substring(start, end);
                JSONObject jsonObject = JSONUtil.parseObj(dto);
                JSONObject dtoJson = jsonObject.getJSONObject("dto");
                String fSeqNo = dtoJson.getStr("f_seq_no");
                JSONObject rd = dtoJson.getJSONArray("rd").getJSONObject(0);
                String amount = rd.getStr("amount");
                String purpose = rd.getStr("purpose");
                String uniBusiId = rd.getStr("uni_busi_id");
                System.out.println(amount);
                System.out.println(purpose);
                System.out.println(uniBusiId);

                PowerPayOrderVO vo = map.get(purpose);
                System.out.println(vo);
                //支付状态都为待支付，真实状态，后续回查
                TUserPaymentLogEntity tUserPaymentLogEntity = icbcService.buildUserPaymentLog(vo, "551647");
                tUserPaymentLogEntity.setSerialNumber(serialNo);
                tUserPaymentLogEntity.setMoney(new BigDecimal(amount).divide(new BigDecimal("100")));
                //待支付
                tUserPaymentLogEntity.setPaymentStatus(0);
                tUserPaymentLogEntity.setUniBusiId(uniBusiId);
                tUserPaymentLogEntity.setFSeqNo(fSeqNo);
                //人行大小额 否
                tUserPaymentLogEntity.setFinishRhdxe(0);
                tUserPaymentLogEntity.setPsId(null);
                userPaymentLogEntities.add(tUserPaymentLogEntity);
            }
        }

        tUserPaymentLogService.saveBatch(userPaymentLogEntities);
    }

    @Test
    public void test23() {
        powerPayOrderDetailService.initJiangXiPayOrder();
    }

    @Autowired
    THttpPaymentLogService tHttpPaymentLogService;

    @Test
    public void test24() throws Exception {
        List<TUserPaymentLogEntity> userPaymentLogEntities = new ArrayList<>();
        FileInputStream fi = new FileInputStream("C:\\Users\\<USER>\\Desktop\\补救支付数据20240828.xlsx");
        XSSFWorkbook workbook = new XSSFWorkbook(fi);
        XSSFSheet sheet = workbook.getSheetAt(0);
        /*for (int i = 0; i <= sheet.getLastRowNum(); i++) {
            XSSFRow row = sheet.getRow(i);
            XSSFCell cell = row.getCell(0);
            String stringCellValue = cell.getStringCellValue();
            if (stringCellValue.contains("Execute SQL")) {
                String sql = stringCellValue.substring(stringCellValue.indexOf("Execute SQL：") + 12);
                String date = sql.substring(sql.length() - 32);
                date = date.replace("' )", "").replace("\n", "");
                String replace = date.replace(date.substring(date.length() - 9), "").replace("T", " ");
                sql = sql.replace(date, replace);
                SqlRunner.db().insert(sql);
            }
        }*/

    }


    @Test
    public void test25() throws Exception {
        //这批支付的支付记录
        List<TUserPaymentLogEntity> paymentLogs = tUserPaymentLogService.list(Wrappers.<TUserPaymentLogEntity>lambdaQuery().eq(TUserPaymentLogEntity::getPaymentStatus, 0));

        Map<String, TUserPaymentLogEntity> map = new HashMap<>();
        for (TUserPaymentLogEntity tUserPaymentLogEntity : paymentLogs) {
            map.put(tUserPaymentLogEntity.getSerialNumber(), tUserPaymentLogEntity);
        }

        Map<String, TUserPaymentLogEntity> purposeMap = new HashMap<>();
        for (TUserPaymentLogEntity tUserPaymentLogEntity : paymentLogs) {
            purposeMap.put(tUserPaymentLogEntity.getPayAbstract(), tUserPaymentLogEntity);
        }


        //回查http请求
        List<THttpPaymentLogEntity> list = tHttpPaymentLogService.list(Wrappers.<THttpPaymentLogEntity>lambdaQuery().eq(THttpPaymentLogEntity::getType, 2));

        //支付http请求
        List<THttpPaymentLogEntity> listPayment = tHttpPaymentLogService.list(Wrappers.<THttpPaymentLogEntity>lambdaQuery().eq(THttpPaymentLogEntity::getType, 1));
        //二次支付的map
        Map<String, TUserPaymentLogEntity> secondMap = new HashMap<>();
        for (THttpPaymentLogEntity tHttpPaymentLogEntity : listPayment) {
            String request = tHttpPaymentLogEntity.getRequest();
            JSONObject requestObj = JSONUtil.parseObj(request);
            String fSeqNo = requestObj.getJSONObject("dto").getStr("f_seq_no");

            String purpose = requestObj.getJSONObject("dto").getJSONArray("rd").getJSONObject(0).getStr("purpose");

            String uni_busi_id = requestObj.getJSONObject("dto").getJSONArray("rd").getJSONObject(0).getStr("uni_busi_id");


            String response = tHttpPaymentLogEntity.getResponse();
            JSONObject respJson = JSONUtil.parseObj(response);
            String serialNo = respJson.getJSONObject("res").getStr("serial_no");

            TUserPaymentLogEntity entity = new TUserPaymentLogEntity();
            entity.setFSeqNo(fSeqNo);
            entity.setUniBusiId(uni_busi_id);
            entity.setSerialNumber(serialNo);

            secondMap.put(purpose, entity);

        }
        List<TUserPaymentLogEntity> successLog = new ArrayList<>();
        List<TPowerPayOrderEntity> successOrder = new ArrayList<>();

        List<TUserPaymentLogEntity> fulLog = new ArrayList<>();
        Integer count = 0;
        //所有回查记录
        for (THttpPaymentLogEntity httpPaymentLogEntity : list) {
            String request = httpPaymentLogEntity.getRequest();
            JSONObject requestObj = JSONUtil.parseObj(request);
            String serialNo = requestObj.getJSONObject("dto").getStr("qry_serial_no");
            System.out.println(serialNo);
            String response = httpPaymentLogEntity.getResponse();
            JSONObject respJson = JSONUtil.parseObj(response);
            JSONObject rdJson = respJson.getJSONObject("res").getJSONArray("rd").getJSONObject(0);
            String purpose = rdJson.getStr("purpose");
            String result = rdJson.getStr("result");
            String instrRetMsg = rdJson.getStr("instr_ret_msg");
            System.out.println(purpose);
            System.out.println(result);
            System.out.println(instrRetMsg);


            TUserPaymentLogEntity tUserPaymentLogEntity = map.get(serialNo);
            if ("7".equals(result)) {
                tUserPaymentLogEntity.setPaymentStatus(1);
                successLog.add(tUserPaymentLogEntity);

                Long orderId = tUserPaymentLogEntity.getOrderId();
                TPowerPayOrderEntity powerPayOrderEntity = tPowerPayOrderService.getById(orderId);
                powerPayOrderEntity.setPayTime(tUserPaymentLogEntity.getPayTime());
                successOrder.add(powerPayOrderEntity);
            } else {
                if (instrRetMsg.contains("跨行处理信息不全")) {
                    //todo,不做操作，后续处理
                    count++;
                } else {
                    //直接记录失败
                    tUserPaymentLogEntity.setPaymentStatus(2);
                    tUserPaymentLogEntity.setFailureReason(instrRetMsg);
                    fulLog.add(tUserPaymentLogEntity);
                }
            }
        }
        System.out.println(count);
        tUserPaymentLogService.updateBatchById(successLog);
        tPowerPayOrderService.updateBatchById(successOrder);
        tUserPaymentLogService.updateBatchById(fulLog);

        List<TUserPaymentLogEntity> rhdxe = new ArrayList<>();
        //所有支付记录
        for (String key : secondMap.keySet()) {
            TUserPaymentLogEntity vo = secondMap.get(key);
            TUserPaymentLogEntity tUserPaymentLogEntity = purposeMap.get(key);
            tUserPaymentLogEntity.setSerialNumber(vo.getSerialNumber());
            tUserPaymentLogEntity.setPaymentStatus(0);
            tUserPaymentLogEntity.setUniBusiId(vo.getUniBusiId());
            tUserPaymentLogEntity.setFSeqNo(vo.getFSeqNo());
            tUserPaymentLogEntity.setFinishRhdxe(1);
            rhdxe.add(tUserPaymentLogEntity);
        }
        tUserPaymentLogService.updateBatchById(rhdxe);


    }

    @Test
    public void test26() {
        paymentService.jiangXiRepay("111");
    }

    @Test
    public void test27() {
        icbcDetailService.getICBCDetail();
    }

    @Test
    public void test28() {
        workerBillService.syncWorkBill("2025-01-01");
    }

    @Test
    public void test29() {
        workerBillService.syncStationNum("2024-01-01");
    }

    @Autowired
    BankInfoChangeService bankInfoChangeService;

    @Test
    public void test30() {
        bankInfoChangeService.getECRecordAndVerify();
    }

    @Test
    public void test31() {
        powerPayOrderGenerateService.powerPayOrderGenerateByStationNos("**************");
    }

    @Test
    public void test32() {
        bankInfoChangeService.initAllBankInfoFromXyg();
    }

    @Test
    public void testAhead() {
        powerPayOrderGenerateService.generateQybaAhead();
    }

    @Test
    public void testAheadJiangxi() {
        powerPayOrderGenerateService.generateQybaAheadJiangXi();
    }

    @Test
    public void testDaYouCun() {
        //powerPayOrderGenerateService.generateDaYouCun();
    }

    @Test
    public void testCheck() {
        icbcService.paymentResultCheckAndRepay();
    }

    @Test
    public void testRePush() {
        paymentService.rePushPaymentByEcNo("692499,691994,692936");
    }

    @Test
    public void updateId() {
        powerPayOrderGenerateService.updatePsId();
    }

    @Test
    public void testSmsNew() {
        paymentService.sendRentSmsNew("***********", "孙家星", "**************", "2024", "09", "12", "1000.00", "4005");
    }

    @Test
    public void testChaoWang() {
        paymentService.chaoWangRepay();
    }

    @Autowired
    ICBCAccountDetailService icbcAccountDetailService;

    @Test
    public void testNewReturn() {
        icbcAccountDetailService.queryPaymentReturn("********", "********");
    }

    @Test
    public void testPaymentReturn() {
        icbcAccountDetailService.paymentReturn();
    }

    @Test
    public void testReturn() {
        List<TUserPaymentLogEntity> paymentLogs = tUserPaymentLogService.list(Wrappers.<TUserPaymentLogEntity>lambdaQuery().eq(TUserPaymentLogEntity::getEcNo, "808025").eq(TUserPaymentLogEntity::getPaymentStatus, 1));
        Map<String, QueryAccountDetailRdDTO> queryAccountDetailRdDTOMap = this.queryPaymentReturn();
        List<TUserPaymentLogEntity> updatePaymentLogs = new ArrayList<>();
        if (CollectionUtils.isNotEmpty(paymentLogs)) {
            for (TUserPaymentLogEntity tUserPaymentLogEntity : paymentLogs) {
                String serialNumber = tUserPaymentLogEntity.getSerialNumber();
                QueryAccountDetailRdDTO queryAccountDetailRdDTO = queryAccountDetailRdDTOMap.get(serialNumber);
                if (queryAccountDetailRdDTO != null) {
                    //如果退汇，更新成为支付失败
                    tUserPaymentLogEntity.setPaymentStatus(2);
                    tUserPaymentLogEntity.setFailureReason(queryAccountDetailRdDTO.getReturnReason());
                    tUserPaymentLogEntity.setPaymentReturn(1);
                    updatePaymentLogs.add(tUserPaymentLogEntity);
                }
            }
        }
        System.out.println(updatePaymentLogs.size());
    }

    public Map<String, QueryAccountDetailRdDTO> queryPaymentReturn() {
        Map<String, QueryAccountDetailRdDTO> returnMap = new HashMap<>();
        THttpPaymentLogEntity httpPaymentLog = tHttpPaymentLogService.getById(959441);
        //明细原始数据
        List<QueryAccountDetailRdDTO> dtos = new ArrayList<>();
        String resp = httpPaymentLog.getResponse();
        com.alibaba.fastjson.JSONObject res = JSON.parseObject(resp).getJSONObject("res");
        RespDTO respDTO = res.toJavaObject(RespDTO.class);
        List<com.alibaba.fastjson.JSONObject> rd = respDTO.getRd();
        if (CollectionUtils.isNotEmpty(rd)) {
            List<QueryAccountDetailRdDTO> collect = rd.stream().map(obj -> obj.toJavaObject(QueryAccountDetailRdDTO.class)).collect(Collectors.toList());
            dtos.addAll(collect);
        }
        //获取借的数据，【支付交易序号】为key
        List<QueryAccountDetailRdDTO> jie = dtos.stream().filter(t -> t != null
                && "1".equals(t.getDrcrf())
                && t.getDebit_amount() != null
                && t.getDebit_amount().compareTo(BigDecimal.ZERO) > 0).collect(Collectors.toList());
        Map<String, QueryAccountDetailRdDTO> jieMap = new HashMap<>();
        if (CollectionUtils.isNotEmpty(jie)) {
            for (QueryAccountDetailRdDTO dto : jie) {
                String receiptInfo = dto.getReceipt_info();
                if (StringUtils.isNotBlank(receiptInfo)) {
                    //支付交易序号
                    String zfjyxh = parseReceiptInfo(receiptInfo, "支付交易序号");
                    if (StringUtils.isNotBlank(zfjyxh)) {
                        jieMap.put(zfjyxh, dto);
                    }
                }
            }
        }

        //获取所有贷的数据，根据event_serial_no匹配，借的数据
        List<QueryAccountDetailRdDTO> dai = dtos.stream().filter(t -> t != null
                && "2".equals(t.getDrcrf())
                && t.getCredit_amount() != null
                && t.getCredit_amount().compareTo(BigDecimal.ZERO) > 0).collect(Collectors.toList());
        for (QueryAccountDetailRdDTO daiRd : dai) {
            //解析贷中的【原支付交易序号】
            String receiptInfo = daiRd.getReceipt_info();
            if (StringUtils.isNotBlank(receiptInfo)) {
                String yzfjyxh = parseReceiptInfo(receiptInfo, "原支付交易序号");
                String thyy = parseReceiptInfo(receiptInfo, "退汇原因");
                if (StringUtils.isNotBlank(yzfjyxh)) {
                    QueryAccountDetailRdDTO queryAccountDetailRdDTO = jieMap.get(yzfjyxh);
                    if (queryAccountDetailRdDTO != null) {
                        String jieReceiptInfo = queryAccountDetailRdDTO.getReceipt_info();
                        String zlbh = parseReceiptInfo(jieReceiptInfo, "指令编号");
                        if (StringUtils.isNotBlank(zlbh) && zlbh.indexOf("-") > 0) {
                            zlbh = zlbh.substring(0, zlbh.indexOf("-"));
                            daiRd.setReturnReason(thyy);
                            returnMap.put(zlbh, daiRd);
                        }
                    }
                }

            }

        }
        return returnMap;
    }


    public static String parseReceiptInfo(String message, String label) {
        if (StringUtils.isBlank(message) || StringUtils.isBlank(label)) {
            return null;
        }
        String prefix = "<" + label + ">";
        String suffix = "</" + label + ">";


        int startIndex = message.indexOf(prefix);
        if (startIndex == -1) {
            return null;
        }
        startIndex += prefix.length();

        int endIndex = message.indexOf(suffix, startIndex);
        if (endIndex == -1) {
            return null;
        }
        return message.substring(startIndex, endIndex);
    }


    @Test
    public void testDuplicate() {
        List<THttpPaymentLogEntity> paymentLogs = tHttpPaymentLogService.lambdaQuery().eq(THttpPaymentLogEntity::getType, 1)
                .ge(THttpPaymentLogEntity::getCreateTime, "2025-04-15 00:00:00")
                .le(THttpPaymentLogEntity::getCreateTime, "2025-04-16 23:59:59")
                .list();

        Map<String, List<SubmitRdReq>> map = new HashMap<>();

        for (THttpPaymentLogEntity paymentLog : paymentLogs) {
            String request = paymentLog.getRequest();
            IcbcMessageDTO<SubmitReq> dto = JSONUtil.toBean(request, IcbcMessageDTO.class);
            SubmitReq submitReq = JSONUtil.toBean(JSONUtil.toJsonStr(dto.getDto()), SubmitReq.class);
            List<SubmitRdReq> rd = submitReq.getRd();
            SubmitRdReq submitRdReq = rd.get(0);
            String payType = submitRdReq.getPay_type();
            if (payType.equals("1")) {
                String purpose = submitRdReq.getPurpose();
                List<SubmitRdReq> submitRdReqs = map.get(purpose);
                if (submitRdReqs == null) {
                    submitRdReqs = new ArrayList<>();
                }
                submitRdReqs.add(submitRdReq);
                map.put(purpose, submitRdReqs);
            }
        }

        System.out.println(map);
        int count = 0;
        List<String> purposes = new ArrayList<>();
        for (String key : map.keySet()) {
            List<SubmitRdReq> submitRdReqs = map.get(key);
            if (submitRdReqs.size() > 1) {
                purposes.add(key);
            }
        }
        purposes.stream().forEach(System.out::println);
    }

    @Test
    public void generateFutureBillKF() {
        List<Integer> list = Arrays.asList(407285, 407286, 407287, 407288, 407289, 407290, 407291, 407292, 407293, 407294, 407295, 407296, 407297, 407298, 407299, 407300, 407301, 407302, 407303, 407304, 407305, 407306, 407307, 407308, 407309, 407310, 407311, 407312, 407313, 407314, 407315, 407316, 407317, 407318, 407319, 407320, 407321, 407322, 407323, 407324, 407325, 407326, 407327, 407328, 407329, 407330, 407331, 407332, 407333, 407334, 407335, 407336, 407338, 407339, 407340, 407341, 407342, 407343, 407344, 407345, 407346, 407347, 407348, 407349, 407350, 407351, 407352, 407353, 407354, 407355, 407356, 407357, 407358, 407359, 407399, 407400, 407401, 407402, 407403, 407404, 407405, 407406, 407407, 407408, 407409, 407410, 407411, 407412, 407413, 407414, 407415, 407416, 407417, 407418, 407419, 407420, 407421, 407422, 407423, 407424, 407425, 407426, 407427, 407428, 407429, 407430, 407431, 407432, 407433, 407434, 407435, 407436, 407437, 407438, 407439, 407440, 407441, 407442, 407443, 407444, 407445, 407446, 407447, 407448, 407449, 407450, 407451, 407452, 407453, 407454, 407455, 407456, 407457, 407458, 407459, 407460, 407461, 407462, 407463, 407464, 407610, 407611, 407612, 407613, 407614, 407615, 407616, 407617, 407618, 407676, 407677, 407678, 407679, 407680, 407681, 407682, 407683, 407684, 407685, 407686, 407687, 407688, 407689, 407690, 407691, 407692, 407693, 407694, 407695, 408234, 408235, 408236, 408237, 407480, 407481, 407482, 407483, 407484, 407485, 407486, 407487, 407488, 407489, 407490, 407491, 407492, 407493, 407494, 407495, 407496, 407497, 407498, 407499, 407500, 407501, 407502, 407503, 407504, 407505, 407506, 407507, 407508, 407509, 407510, 407511, 407512, 407513, 407514, 407515, 407516, 407517, 407518, 407519, 407520, 407521, 407522, 407523, 407524, 407525, 407526, 407527, 407528, 407529, 407530, 407531, 407532, 407533, 407534, 407535, 407536, 407537, 407538, 407539, 407540, 407541, 407542, 407543, 407544, 407545, 407546, 407547, 407548, 407549, 407550, 407551, 407552, 407553, 407554, 407555, 407556, 407557, 407558, 407559, 407560, 407561, 407562, 407563, 407564, 407565, 407566, 407567, 407568, 407569, 407570, 407571, 407572, 407573, 407574, 407575, 407576, 407577, 407578, 407579, 407580, 407581, 407582, 407583, 407584, 407585, 407586, 407587, 407588, 407589, 407590, 407591, 407592, 407593);
        List<TUserPaymentLogEntity> logs = tUserPaymentLogService.lambdaQuery().in(TUserPaymentLogEntity::getId, list).list();
        Map<String, List<TUserPaymentLogEntity>> logMap = logs.stream().collect(Collectors.groupingBy(t -> t.getPsCode()));
        List<TPowerPayOrderEntity> insertList = new ArrayList<>();
        for (String psCode : logMap.keySet()) {
            List<TUserPaymentLogEntity> tUserPaymentLogEntities = logMap.get(psCode);
            int size = tUserPaymentLogEntities.size();
            TUserPaymentLogEntity first = tUserPaymentLogEntities.get(0);
            TUserPaymentLogEntity last = tUserPaymentLogEntities.get(size - 1);
            String s = last.getPayAbstract();
            String month = s.substring(s.indexOf("-") + 1, s.indexOf("租金"));
            TPowerPayOrderEntity powerPayOrder = tPowerPayOrderService.getById(last.getOrderId());
            BigDecimal firstMoney = first.getMoney();
            BigDecimal lastMoney = last.getMoney();


            for (int i = 1; i <= size; i++) {
                TPowerPayOrderEntity p = BeanUtil.copyProperties(powerPayOrder, TPowerPayOrderEntity.class);
                p.setId(null);
                p.setCreateTime(new Date());
                p.setPayTime(null);
                p.setEcNo(null);
                p.setAccountState(2);
                LocalDate date = LocalDate.parse(month + "01", DateTimeFormatter.ofPattern("yyyyMMdd"));
                // 增加指定的月份
                LocalDate newDate = date.plusMonths(i);
                String payMonth = newDate.format(DateTimeFormatter.ofPattern("yyyy-MM"));
                BigDecimal money = lastMoney;
                if (i == 1) {
                    money = lastMoney.subtract(firstMoney);
                    if (p.getDevType() == 1) {
                        p.setAccountState(0);
                    } else {
                        p.setAccountState(3);
                    }
                }
                p.setShareMonth(payMonth);
                p.setShareFee(money);
                insertList.add(p);
            }

        }

        tPowerPayOrderService.saveBatch(insertList);
    }

    @Test
    public void wzj() {
        List<TPowerPayOrderEntity> list = tPowerPayOrderService.lambdaQuery().eq(TPowerPayOrderEntity::getPsCode, "**************").list();
        for (TPowerPayOrderEntity p : list) {
            TPowerPayOrderEntity powerPayOrder = BeanUtil.copyProperties(p, TPowerPayOrderEntity.class);
            powerPayOrder.setId(null);
            if (powerPayOrder.getShareMonth().equals("2024-05")) {
                powerPayOrder.setShareFee(new BigDecimal("151.21"));
            } else {
                powerPayOrder.setShareFee(new BigDecimal("312.5"));
            }
            powerPayOrder.setCreateTime(new Date());
            powerPayOrder.setShareStandard("40");
            powerPayOrder.setPayTime(null);
            powerPayOrder.setEcNo(null);
            powerPayOrder.setPaymentReceipt(null);
            powerPayOrder.setXygId(null);
            powerPayOrder.insert();
        }
    }

    @Test
    public void zmm() {
        powerPayOrderGenerateService.generateThreeCompanies();
    }
}
