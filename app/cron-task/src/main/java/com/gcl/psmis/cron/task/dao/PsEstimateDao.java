package com.gcl.psmis.cron.task.dao;

import com.gcl.psmis.framework.common.dto.psestimate.PsEstimateDTO;
import com.gcl.psmis.framework.common.vo.psestimate.PsEtdCompareVo;
import org.apache.ibatis.annotations.Param;

import java.util.List;
import java.util.Map;

/**
 * 电费预估
 */
public interface PsEstimateDao {
    /**
     * 电费预估明细
     */
    List<PsEstimateDTO> detailList(@Param("date") String date);
    List<PsEstimateDTO> etdCompareList(@Param("date") String date,@Param("psId") long psId,@Param("limit") long limit);

    /**
     * 电费预估汇总
     */
    List<PsEstimateDTO> sum(@Param("accountSeq") String accountSeq);

    List<PsEstimateDTO> icbcList(@Param("startDate") String startDate,@Param("endDate") String endDate);


    List<PsEstimateDTO> getPsestimateSumListByCompany(@Param("accountSeq") String accountSeq);

    List<PsEstimateDTO> getPsestimateSumListByPerson(@Param("accountSeq") String accountSeq);

    List<PsEstimateDTO> pushStatementAllListByCompany(@Param("statementMonth") String statementMonth);

    List<PsEstimateDTO> pushSettlementErrRegCountByPerson(@Param("accountSeq") String accountSeq);

    List<PsEstimateDTO> pushAccountDetailByPerson(@Param("startDate") String startDate,@Param("endDate") String endDate);
}
