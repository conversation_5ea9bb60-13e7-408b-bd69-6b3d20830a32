package com.gcl.psmis.cron.task.dao.yuexiu;

import com.baomidou.dynamic.datasource.annotation.DS;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.gcl.psmis.framework.common.dto.yuexiu.ReissueYueXiuPsDTO;
import com.gcl.psmis.framework.common.dto.yuexiu.YueXiuInverterDTO;
import com.gcl.psmis.framework.common.dto.yuexiu.YueXiuPsDTO;
import com.gcl.psmis.framework.mbg.entity.TDeviceReissueLogEntity;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
* @className: YueXiuDao
* @author: xinan.yuan
* @create: 2024/7/29 14:33
* @description: 越秀数据推送 Dao
*/
@Repository
public interface YueXiuDao {

    IPage<YueXiuInverterDTO> listYueXiuInverter(@Param("page") IPage<YueXiuInverterDTO> page, @Param("sn") String sn);

    IPage<YueXiuPsDTO>  listYueXiuPs(@Param("page") IPage<YueXiuPsDTO> psDTOIPage, @Param("psCode") String psCode);

    @DS("xyg")
    List<YueXiuPsDTO> listYueXiuPsWithOutsideno(@Param("psCodes") List<String> psCodes);

    Page<YueXiuPsDTO> listNewYueXiuPs(@Param("page")Page<YueXiuPsDTO> psDTOIPage, @Param("psCode")String psCode,@Param("startTime")String startTime);

    List<ReissueYueXiuPsDTO> reissuePsByInverter(@Param("receiveTime")String receiveTime,@Param("psCode") String psCode);

    List<TDeviceReissueLogEntity> listReissueLog(@Param("receiveTime")String receiveTime);
}
