package com.gcl.psmis.cron.task.dao;

import com.baomidou.dynamic.datasource.annotation.DS;
import com.gcl.psmis.framework.common.vo.*;
import com.gcl.psmis.framework.mbg.entity.TCompanyNodeStateTotalEntity;
import com.gcl.psmis.framework.mbg.entity.TPsMpptEntity;
import com.gcl.psmis.framework.mbg.entity.TPsChangeRollbackEntity;
import com.gcl.psmis.framework.mq.event.CreateSaveFinishEvent;
import com.gcl.psmis.framework.mq.event.bz.InverterVO;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * @ClassName: EcInterfaceDao
 * @data: 2023/11/29 13:51
 * @author: <EMAIL>
 * @description:
 */

@Repository
public interface EcOperationContractDao {

    /**
     * 从EC端获取 t_power_station、t_user、t_power_station_extend 的数据
     */
    @DS("ec")
    List<EcOperationContractVO> queryEcOperationContract();

}
