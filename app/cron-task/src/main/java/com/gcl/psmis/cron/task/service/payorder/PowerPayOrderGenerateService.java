package com.gcl.psmis.cron.task.service.payorder;

import cn.hutool.core.date.LocalDateTimeUtil;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.gcl.psmis.cron.task.dao.payorder.PowerPayOrderDetailDao;
import com.gcl.psmis.framework.common.vo.payorder.PowerPayOrderVO;
import com.gcl.psmis.framework.mbg.entity.TExcludeRentStationEntity;
import com.gcl.psmis.framework.mbg.entity.TOrgRuleEntity;
import com.gcl.psmis.framework.mbg.entity.TPowerPayOrderEntity;
import com.gcl.psmis.framework.mbg.service.TExcludeRentStationService;
import com.gcl.psmis.framework.mbg.service.TOrgRuleService;
import com.gcl.psmis.framework.mbg.service.TPowerPayOrderService;
import com.gcl.psmis.framework.mbg.service.TPowerUserBankInfoService;
import com.gcl.psmis.manager.dao.rent.PowerPayOrdDao;
import com.gcl.psmis.manager.service.PayOrderGenerateService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.stream.Collectors;

/**
 * @Author: sunjiaxing
 * @CreateTime: 2024-07-04  15:07
 * @Description: 账单生成
 */
@Service
@Slf4j
public class PowerPayOrderGenerateService {

    @Autowired
    PowerPayOrderDetailDao powerPayOrderDetailDao;

    @Autowired
    TPowerPayOrderService tPowerPayOrderService;

    @Autowired
    TPowerUserBankInfoService tPowerUserBankInfoService;

    @Autowired
    PayOrderGenerateService payOrderGenerateService;

    @Autowired
    PowerPayOrdDao powerPayOrdDao;

    @Autowired
    TOrgRuleService tOrgRuleService;

    @Autowired
    TExcludeRentStationService tExcludeRentStationService;


    //生成账单，每天运行一次
    public void generatePowerPayOrder(String first) {
        //今天
        LocalDate now = LocalDate.now();
        int monthValueNow = now.getMonthValue();
        int dayOfMonthNow = now.getDayOfMonth();
        //获取昨天并网的所有户用电站
        List<PowerPayOrderVO> vos = new ArrayList<>();
        log.info("first:{}", first);
        if (StringUtils.isNotBlank(first)) {
            //第一次运行获取所有电站
            vos = powerPayOrdDao.getAllMergeStations();
        } else {
            //之后运行只显示昨日并网的电站
            vos = powerPayOrdDao.getYesterdayMergeStations();
        }
        log.info("vos.size()={}", vos.size());
        List<TPowerPayOrderEntity> insertList = new ArrayList<>();

        if (CollectionUtils.isNotEmpty(vos)) {
            List<String> stationNos = vos.stream().map(PowerPayOrderVO::getPsCode).collect(Collectors.toList());
            List<TPowerPayOrderEntity> powerPayOrderEntities = payOrderGenerateService.generatePayOrder(stationNos);
            insertList.addAll(powerPayOrderEntities);
        }
        if (CollectionUtils.isNotEmpty(insertList)) {
            log.info("insertList.size()={}", insertList.size());
            tPowerPayOrderService.saveBatch(insertList);
        }


        //对于季度付，每季度的第一天，1.1/4.1/7.1/10.1
        //对于年度付，每年的第一天，1.1
        //获取所有有账单的电站,再生成
        //铁发电站不生成
        List<String> tiefaCompanyName = powerPayOrdDao.getTieFaCompanyNames();
        List<TPowerPayOrderEntity> list = tPowerPayOrderService.list(Wrappers.<TPowerPayOrderEntity>lambdaQuery().select(TPowerPayOrderEntity::getPsCode).eq(TPowerPayOrderEntity::getDevType, 0).notIn(TPowerPayOrderEntity::getCompanyName, tiefaCompanyName));
        List<String> psCodes = list.stream().filter(p -> StringUtils.isNotBlank(p.getPsCode())).map(TPowerPayOrderEntity::getPsCode).distinct().collect(Collectors.toList());
        List<TPowerPayOrderEntity> yearAndQuarterOrders = new ArrayList<>();

        LocalDate threeMonthAgo = now.minusMonths(3L);
        String format = threeMonthAgo.format(DateTimeFormatter.ofPattern("yyyy-MM-dd"));
        List<String> grbaPsCodes = powerPayOrdDao.getGRBAStationNosByTime(format);
        Set<String> codes = new HashSet<>();
        codes.addAll(psCodes);
        codes.addAll(grbaPsCodes);
        List<String> psCodeList = new ArrayList<>();
        psCodeList.addAll(codes);

        if (monthValueNow == 1 && dayOfMonthNow == 1) {
            yearAndQuarterOrders = payOrderGenerateService.generatePayOrder(psCodeList);
        } else if (monthValueNow == 4 && dayOfMonthNow == 1) {
            yearAndQuarterOrders = payOrderGenerateService.generatePayOrder(psCodeList);
        } else if (monthValueNow == 7 && dayOfMonthNow == 1) {
            yearAndQuarterOrders = payOrderGenerateService.generatePayOrder(psCodeList);
        } else if (monthValueNow == 10 && dayOfMonthNow == 1) {
            yearAndQuarterOrders = payOrderGenerateService.generatePayOrder(psCodeList);
        }
        if (CollectionUtils.isNotEmpty(yearAndQuarterOrders)) {
            tPowerPayOrderService.saveBatch(yearAndQuarterOrders);
        }
    }

    public void powerPayOrderGenerateKaiFa() {
        //获取昨天并网的开发公司电站
        List<PowerPayOrderVO> vos = powerPayOrdDao.getYesterdayMergeStationsKaiFa();
        List<TPowerPayOrderEntity> insertList = new ArrayList<>();

        if (CollectionUtils.isNotEmpty(vos)) {
            //排除电站
            List<TExcludeRentStationEntity> excludeList = tExcludeRentStationService.list();
            List<String> excludePsCodes = excludeList.stream().map(TExcludeRentStationEntity::getPsCode).collect(Collectors.toList());
            List<String> stationNos = vos.stream().map(PowerPayOrderVO::getPsCode)
                    .filter(t -> !excludePsCodes.contains(t))
                    .collect(Collectors.toList());
            List<TPowerPayOrderEntity> powerPayOrderEntities = payOrderGenerateService.generatePayOrder(stationNos);
            insertList.addAll(powerPayOrderEntities);
        }
        if (CollectionUtils.isNotEmpty(insertList)) {
            log.info("insertList.size()={}", insertList.size());
            tPowerPayOrderService.saveBatch(insertList);
        }

        LocalDate now = LocalDate.now();
        int monthValueNow = now.getMonthValue();
        int dayOfMonthNow = now.getDayOfMonth();
        if (monthValueNow == 1 && dayOfMonthNow == 1) {
            List<TPowerPayOrderEntity> yearOrders = new ArrayList<>();
            List<TPowerPayOrderEntity> list = tPowerPayOrderService.list(Wrappers.<TPowerPayOrderEntity>lambdaQuery().select(TPowerPayOrderEntity::getPsCode)
                    .eq(TPowerPayOrderEntity::getDevType, 1).eq(TPowerPayOrderEntity::getPowerGridMergeType, 2));
            List<String> psCodeList = list.stream().map(TPowerPayOrderEntity::getPsCode).collect(Collectors.toList());
            yearOrders = payOrderGenerateService.generatePayOrder(psCodeList);
            if (CollectionUtils.isNotEmpty(yearOrders)) {
                tPowerPayOrderService.saveBatch(yearOrders);
            }
        }
    }


    public void generateTFPowerPayOrder() {
        //获取铁发账单
        List<PowerPayOrderVO> vos = new ArrayList<>();
        vos = powerPayOrdDao.getTFStations();
        List<TPowerPayOrderEntity> insertList = new ArrayList<>();
        if (CollectionUtils.isNotEmpty(vos)) {
            List<String> stationNos = vos.stream().map(PowerPayOrderVO::getPsCode).collect(Collectors.toList());
            List<TPowerPayOrderEntity> powerPayOrderEntities = payOrderGenerateService.generatePayOrder(stationNos);
            insertList.addAll(powerPayOrderEntities);
        }
        if (CollectionUtils.isNotEmpty(insertList)) {
            log.info("insertList.size()={}", insertList.size());
            tPowerPayOrderService.saveBatch(insertList);
        }
    }

    /*public void generateJiangXiNew() {
        //获取江西公司
        List<PowerPayOrderVO> vos = new ArrayList<>();
        vos = powerPayOrdDao.getJiangXiNewStations();
        List<TPowerPayOrderEntity> insertList = new ArrayList<>();
        if (CollectionUtils.isNotEmpty(vos)) {
            List<String> stationNos = vos.stream().map(PowerPayOrderVO::getPsCode).collect(Collectors.toList());
            List<TPowerPayOrderEntity> powerPayOrderEntities = payOrderGenerateService.generatePayOrder(stationNos);
            insertList.addAll(powerPayOrderEntities);
        }
        if (CollectionUtils.isNotEmpty(insertList)) {
            log.info("insertList.size()={}", insertList.size());
            tPowerPayOrderService.saveBatch(insertList);
        }
    }*/

    public void generateJiangXiPayOrder() {
        //获取江西公司账单
        List<PowerPayOrderVO> vos = new ArrayList<>();
        vos = powerPayOrdDao.getJiangxiStations();
        List<TPowerPayOrderEntity> insertList = new ArrayList<>();
        if (CollectionUtils.isNotEmpty(vos)) {
            List<String> stationNos = vos.stream().map(PowerPayOrderVO::getPsCode).collect(Collectors.toList());
            List<TPowerPayOrderEntity> powerPayOrderEntities = payOrderGenerateService.generatePayOrder(stationNos);
            insertList.addAll(powerPayOrderEntities);
        }
        if (CollectionUtils.isNotEmpty(insertList)) {
            log.info("insertList.size()={}", insertList.size());
            tPowerPayOrderService.saveBatch(insertList);
        }
    }

    public void generateHuBeiPayOrder() {
        //获取江西公司账单
        List<PowerPayOrderVO> vos = new ArrayList<>();
        vos = powerPayOrdDao.getHuBeiStations();
        List<TPowerPayOrderEntity> insertList = new ArrayList<>();
        if (CollectionUtils.isNotEmpty(vos)) {
            List<String> stationNos = vos.stream().map(PowerPayOrderVO::getPsCode).collect(Collectors.toList());
            List<TPowerPayOrderEntity> powerPayOrderEntities = payOrderGenerateService.generatePayOrder(stationNos);
            insertList.addAll(powerPayOrderEntities);
        }
        if (CollectionUtils.isNotEmpty(insertList)) {
            log.info("insertList.size()={}", insertList.size());
            tPowerPayOrderService.saveBatch(insertList);
        }
    }


    public void generateJiangXiPayOrderQYBA() {
        //获取江西公司账单
        List<PowerPayOrderVO> vos = new ArrayList<>();
        vos = powerPayOrdDao.getJiangxiStationsQYBA();
        List<TPowerPayOrderEntity> insertList = new ArrayList<>();
        if (CollectionUtils.isNotEmpty(vos)) {
            List<String> stationNos = vos.stream().map(PowerPayOrderVO::getPsCode).collect(Collectors.toList());
            List<TPowerPayOrderEntity> powerPayOrderEntities = payOrderGenerateService.generatePayOrderJiangXiPayOrderQYBA(stationNos, null);
            insertList.addAll(powerPayOrderEntities);
        }
        if (CollectionUtils.isNotEmpty(insertList)) {
            log.info("insertList.size()={}", insertList.size());
            tPowerPayOrderService.saveBatch(insertList);
        }

        /*List<PowerPayOrderVO> vos2 = new ArrayList<>();
        vos2 = powerPayOrdDao.getJiangxiStationsQYBA();
        List<TPowerPayOrderEntity> insertList2 = new ArrayList<>();
        if (CollectionUtils.isNotEmpty(vos2)) {
            List<String> stationNos = vos2.stream().map(PowerPayOrderVO::getPsCode).collect(Collectors.toList());
            List<TPowerPayOrderEntity> powerPayOrderEntities = payOrderGenerateService.generatePayOrderJiangXiPayOrderQYBA(stationNos, "2025-01-01");
            insertList2.addAll(powerPayOrderEntities);
        }
        if (CollectionUtils.isNotEmpty(insertList2)) {
            log.info("insertList2.size()={}", insertList2.size());
            tPowerPayOrderService.saveBatch(insertList2);
        }*/
    }


    public void powerPayOrderGenerateByStationNos(String stationNo) {
        List<TPowerPayOrderEntity> insertList = new ArrayList<>();
        List<String> stationNos = Arrays.asList(stationNo.split(","));
        List<TPowerPayOrderEntity> powerPayOrderEntities = payOrderGenerateService.generatePayOrder(stationNos);
        insertList.addAll(powerPayOrderEntities);
        if (CollectionUtils.isNotEmpty(insertList)) {
            log.info("insertList.size()={}", insertList.size());
            tPowerPayOrderService.saveBatch(insertList);
        }
    }

    public void reGenerateOrder(String date) {
        List<PowerPayOrderVO> vos = powerPayOrdDao.getByAfterDate(date);
        log.info("vos.size()={}", vos.size());
        List<TPowerPayOrderEntity> insertList = new ArrayList<>();
        if (CollectionUtils.isNotEmpty(vos)) {
            List<String> stationNos = vos.stream().map(PowerPayOrderVO::getPsCode).collect(Collectors.toList());
            List<TPowerPayOrderEntity> powerPayOrderEntities = payOrderGenerateService.generatePayOrder(stationNos);
            insertList.addAll(powerPayOrderEntities);
        }
        if (CollectionUtils.isNotEmpty(insertList)) {
            log.info("insertList.size()={}", insertList.size());
            tPowerPayOrderService.saveBatch(insertList);
        }
    }

    public void reGenerateGRBA() {
        List<String> psCodes = powerPayOrdDao.getGRBAStationNos();
        List<TPowerPayOrderEntity> entities = payOrderGenerateService.generatePayOrder(psCodes);
        if (entities != null) {
            tPowerPayOrderService.saveBatch(entities);
        }
    }

    public void generateQybaAhead() {
        List<String> tiefaCompanyCode = powerPayOrdDao.getTieFaCompanyCodes();
        List<TPowerPayOrderEntity> list = tPowerPayOrderService.list(Wrappers.<TPowerPayOrderEntity>lambdaQuery()
                .select(TPowerPayOrderEntity::getPsCode).eq(TPowerPayOrderEntity::getDevType, 0)
                .eq(TPowerPayOrderEntity::getPowerGridMergeType, 2)
                .notIn(TPowerPayOrderEntity::getCompanyCode, tiefaCompanyCode));
        List<String> psCodes = list.stream().filter(p -> StringUtils.isNotBlank(p.getPsCode())).map(TPowerPayOrderEntity::getPsCode).distinct().collect(Collectors.toList());
        List<TPowerPayOrderEntity> yearAndQuarterOrders = new ArrayList<>();
        Set<String> codes = new HashSet<>();
        codes.addAll(psCodes);
        List<String> psCodeList = new ArrayList<>();
        psCodeList.addAll(codes);
        LocalDate now = LocalDateTimeUtil.parseDate("2025-01-01", "yyyy-MM-dd");
        int monthValueNow = now.getMonthValue();
        int dayOfMonthNow = now.getDayOfMonth();
        if (monthValueNow == 1 && dayOfMonthNow == 1) {
            yearAndQuarterOrders = payOrderGenerateService.generatePayOrderQyba(psCodeList, now.format(DateTimeFormatter.ofPattern("yyyy-MM-dd")));
        }
        System.out.println(yearAndQuarterOrders.size());
        tPowerPayOrderService.saveBatch(yearAndQuarterOrders);
    }

    public void generateQybaAheadJiangXi() {
        List<String> tiefaCompanyCode = powerPayOrdDao.getTieFaCompanyCodes();
        List<TPowerPayOrderEntity> list = tPowerPayOrderService.list(Wrappers.<TPowerPayOrderEntity>lambdaQuery()
                .select(TPowerPayOrderEntity::getPsCode).eq(TPowerPayOrderEntity::getDevType, 1)
                .eq(TPowerPayOrderEntity::getPowerGridMergeType, 2)
                .notIn(TPowerPayOrderEntity::getCompanyCode, tiefaCompanyCode));
        List<String> psCodes = list.stream().filter(p -> StringUtils.isNotBlank(p.getPsCode())).map(TPowerPayOrderEntity::getPsCode).distinct().collect(Collectors.toList());
        List<TPowerPayOrderEntity> yearAndQuarterOrders = new ArrayList<>();
        Set<String> codes = new HashSet<>();
        codes.addAll(psCodes);
        List<String> psCodeList = new ArrayList<>();
        psCodeList.addAll(codes);
        LocalDate now = LocalDateTimeUtil.parseDate("2025-01-01", "yyyy-MM-dd");
        int monthValueNow = now.getMonthValue();
        int dayOfMonthNow = now.getDayOfMonth();
        if (monthValueNow == 1 && dayOfMonthNow == 1) {
            yearAndQuarterOrders = payOrderGenerateService.generatePayOrderQyba(psCodeList, now.format(DateTimeFormatter.ofPattern("yyyy-MM-dd")));
        }
        System.out.println(yearAndQuarterOrders.size());
        tPowerPayOrderService.saveBatch(yearAndQuarterOrders);
    }

//    public void generateDaYouCun() {
//        String[] stationNos = new String[]{"10202409055292", "10202409055255", "10202409055253", "10202409055231", "10202409054967",
//                "10202409054740", "10202408054238", "10202408051890", "10202407051589", "10202407049855", "10202407049506", "10202407049307",
//                "10202407049222", "10202407047775", "10202406046682", "10202406046378", "10202406045827", "10202406045819", "10202405041345",
//                "10202405041329", "10202405041234", "10202405041119", "10202405040968", "10202406046332", "10202405036473", "10202405036076",
//                "10202405036111", "10202405035281", "10202405035088", "10202404025150", "10202404025150", "10202404025150", "10202404024945",
//                "10202404024945", "10202404024945", "10202404024945", "10202405041321", "10202405035752", "10202404023216", "10202405036341",
//                "10202404026266", "10202405034615", "10202405036381", "10202404026475", "10202405033958", "10202404026089"};
//        List<String> psCodeList = Arrays.asList(stationNos);
//
//        List<TPowerPayOrderEntity> powerPayOrderEntities = payOrderGenerateService.generatePayOrderDaYouCun(psCodeList);
//        System.out.println(powerPayOrderEntities.size());
//        tPowerPayOrderService.saveBatch(powerPayOrderEntities);
//    }


    public void reGenerate() {
        LocalDate now = LocalDateTimeUtil.parseDate("2025-01-01", "yyyy-MM-dd");
        int monthValueNow = now.getMonthValue();
        int dayOfMonthNow = now.getDayOfMonth();

        //对于季度付，每季度的第一天，1.1/4.1/7.1/10.1
        //对于年度付，每年的第一天，1.1
        //获取所有有账单的电站,再生成
        //铁发电站不生成
        List<String> tiefaCompanyName = powerPayOrdDao.getTieFaCompanyNames();
        List<TPowerPayOrderEntity> list = tPowerPayOrderService.list(Wrappers.<TPowerPayOrderEntity>lambdaQuery().select(TPowerPayOrderEntity::getPsCode).eq(TPowerPayOrderEntity::getDevType, 0).notIn(TPowerPayOrderEntity::getCompanyName, tiefaCompanyName));
        List<String> psCodes = list.stream().filter(p -> StringUtils.isNotBlank(p.getPsCode())).map(TPowerPayOrderEntity::getPsCode).distinct().collect(Collectors.toList());
        List<TPowerPayOrderEntity> yearAndQuarterOrders = new ArrayList<>();

        LocalDate threeMonthAgo = now.minusMonths(3L);
        String format = threeMonthAgo.format(DateTimeFormatter.ofPattern("yyyy-MM-dd"));
        List<String> grbaPsCodes = powerPayOrdDao.getGRBAStationNosByTime(format);
        Set<String> codes = new HashSet<>();
        codes.addAll(psCodes);
        codes.addAll(grbaPsCodes);
        List<String> psCodeList = new ArrayList<>();
        psCodeList.addAll(codes);

        if (monthValueNow == 1 && dayOfMonthNow == 1) {
            yearAndQuarterOrders = payOrderGenerateService.generatePayOrder(psCodeList);
        }
        if (CollectionUtils.isNotEmpty(yearAndQuarterOrders)) {
            tPowerPayOrderService.saveBatch(yearAndQuarterOrders);
        }
    }


    public void powerPayOrderGenerateByStationNosNoMerge(String stationNo) {
        List<TPowerPayOrderEntity> insertList = new ArrayList<>();
        List<String> stationNos = Arrays.asList(stationNo.split(","));
        List<TPowerPayOrderEntity> powerPayOrderEntities = payOrderGenerateService.generatePayOrderNoMerge(stationNos, null);
        insertList.addAll(powerPayOrderEntities);
        if (CollectionUtils.isNotEmpty(insertList)) {
            log.info("insertList.size()={}", insertList.size());
            tPowerPayOrderService.saveBatch(insertList);
        }
    }

    public void powerPayOrderGenerateByStationNosNoMergeForDaYouCun(String stationNo) throws Exception {
        List<TPowerPayOrderEntity> insertList = new ArrayList<>();
        List<String> stationNos = Arrays.asList(stationNo.split(","));
        List<TPowerPayOrderEntity> powerPayOrderEntities = payOrderGenerateService.generatePayOrderNoMergeForDaYouCun(stationNos, 6);
        insertList.addAll(powerPayOrderEntities);
        if (CollectionUtils.isNotEmpty(insertList)) {
            log.info("insertList.size()={}", insertList.size());
            tPowerPayOrderService.saveBatch(insertList);
        }
    }

    public void generateHuNanPayOrderQYBA() {
        //获取江西公司账单
        List<PowerPayOrderVO> vos = new ArrayList<>();
        vos = powerPayOrdDao.getHuNanStationsQYBA();
        List<TPowerPayOrderEntity> insertList = new ArrayList<>();
        if (CollectionUtils.isNotEmpty(vos)) {
            List<String> stationNos = vos.stream().map(PowerPayOrderVO::getPsCode).collect(Collectors.toList());
            List<TPowerPayOrderEntity> powerPayOrderEntities = payOrderGenerateService.generatePayOrderQyba(stationNos, null);
            insertList.addAll(powerPayOrderEntities);
        }
        if (CollectionUtils.isNotEmpty(insertList)) {
            log.info("insertList.size()={}", insertList.size());
            tPowerPayOrderService.saveBatch(insertList);
        }
    }

    public void updatePsId() {
        powerPayOrdDao.updatePsId();
    }

    public void powerPayOrderGenerateSerialYear() {
        List<TOrgRuleEntity> orgRuleEntities = tOrgRuleService.lambdaQuery().eq(TOrgRuleEntity::getRule, 4).list();
        List<String> orgCodes = orgRuleEntities.stream().map(TOrgRuleEntity::getOrgCode).collect(Collectors.toList());
        //获取符合连续年的电站
        List<PowerPayOrderVO> vos = powerPayOrdDao.getSerialYearCompany(orgCodes);


    }

    public void generateThreeCompanies() {
        List<PowerPayOrderVO> vos = new ArrayList<>();
        vos = powerPayOrdDao.getThreeCompanies();
        List<TPowerPayOrderEntity> insertList = new ArrayList<>();
        if (CollectionUtils.isNotEmpty(vos)) {
            List<String> stationNos = vos.stream().map(PowerPayOrderVO::getPsCode).collect(Collectors.toList());
            List<TPowerPayOrderEntity> powerPayOrderEntities = payOrderGenerateService.generatePayOrder(stationNos);
            insertList.addAll(powerPayOrderEntities);
        }
        if (CollectionUtils.isNotEmpty(insertList)) {
            log.info("insertList.size()={}", insertList.size());
            tPowerPayOrderService.saveBatch(insertList);
        }
    }
}
