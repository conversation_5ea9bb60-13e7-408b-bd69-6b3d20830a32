package com.gcl.psmis.cron.task.dao.nkbi;

import com.baomidou.dynamic.datasource.annotation.DS;
import com.gcl.psmis.framework.common.dto.nkbi.FillFlowAuditPersonDTO;
import com.gcl.psmis.framework.common.dto.nkbi.FillFlowDTO;
import com.gcl.psmis.framework.common.dto.nkbi.FlowCompanyDTO;
import com.gcl.psmis.framework.common.dto.nkbi.SourceLogDTO;
import com.gcl.psmis.framework.mbg.entity.TFillingFlowDetailEntity;
import com.gcl.psmis.framework.mbg.entity.TFillingFlowEntity;
import io.lettuce.core.dynamic.annotation.Param;

import java.util.List;
import java.util.Set;


public interface NkbiDataDao {
    @DS("hr")
    List<SourceLogDTO> getSourceLogByDateStamp(@Param("dateStamp") String dateStamp);

    @DS("hr")
    List<FlowCompanyDTO> getFlowCompany(@Param("flowId") Integer flowId);

    @DS("hr")
    List<FillFlowDTO> getFillFlowByCompanyCode(@Param("fillCompanyCode") String fillCompanyCode,
                                               @Param("sumCompanyCode") String sumCompanyCode,
                                               @Param("yearMonthly") String yearMonthly,
                                               @Param("flowType") Integer flowType,
                                               @Param("sumOrgType") String sumOrgType,
                                               @Param("specFlag") Integer specFlag);

    @DS("hr")
    List<TFillingFlowEntity> getSingleFillingFlows(@Param("fillCompanyCode") String fillCompanyCode,
                                                   @Param("indexType") Integer indexType,
                                                   @Param("yearMonthly") String yearMonthly);

    @DS("hr")
    void updateSumFlowId(@Param("sumFlowId") Long sumFlowId,
                         @Param("indexNos") Set<String> indexNos,
                         @Param("parentCode") String parentCode,
                         @Param("yearMonthly") String yearMonthly,
                         @Param("sumOrgType") String sumOrgType,
                         @Param("indexType") Integer indexType);

    @DS("hr")
    int getCountByYearMonthlyAndParentCodeAndSumOrgType(@Param("yearMonthly") String yearMonthly,
                                                        @Param("parentCode") String parentCode,
                                                        @Param("sumOrgType") String sumOrgType);

    @DS("hr")
    void updateSingleFlowSumStatus(@Param("fillCompanyCode") String fillCompanyCode,
                                   @Param("indexType") Integer indexType,
                                   @Param("yearMonthly") String yearMonthly);

    @DS("hr")
    List<FillFlowAuditPersonDTO> getAuditPerson(@Param("flowId") Integer flowId,
                                                @Param("flowCompanyId") Integer flowCompanyId);

    @DS("hr")
    List<TFillingFlowEntity> getFlows(@Param("yearMonthly") String yearMonthly,
                                      @Param("indexType") Integer indexType,
                                      @Param("flowType") Integer flowType,
                                      @Param("flowStatus") Integer flowStatus);

    @DS("hr")
    List<FillFlowAuditPersonDTO> getAuditPersons(@Param("sumCompanyCode") String sumCompanyCode,
                                                 @Param("fillCompanyCode") String fillCompanyCode,
                                                 @Param("sumOrgType") String sumOrgType,
                                                 @Param("flowId") Integer flowId);

    @DS("hr")
    List<TFillingFlowDetailEntity> selectPlanList(@Param("yearMonthly") String yearMonthly,
                                                  @Param("flowType") Integer flowType);

    List<TFillingFlowDetailEntity> getFlowDetailEntities(@Param("yearMonthly") String yearMonthly,
                                                         @Param("flowType") Integer flowType,
                                                         @Param("indexType") Integer indexType);
}
