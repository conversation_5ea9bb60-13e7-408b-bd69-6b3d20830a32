package com.gcl.psmis.cron.task.dao;

import cn.hutool.core.date.DateTime;
import com.gcl.psmis.framework.common.dto.xj.InspectionPsDTO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <AUTHOR>
 * @ClassName InspectionDao
 * @description: TODO
 * @date 2024年08月08日
 * @version: 1.0
 */
@Mapper
public interface InspectionDao {
    List<InspectionPsDTO> getInspectionPs(@Param("time") DateTime dateTime);
}
