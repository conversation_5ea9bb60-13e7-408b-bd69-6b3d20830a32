package com.gcl.psmis.cron.task.dao;

import com.gcl.psmis.framework.common.dto.remote.SnAppid;
import com.gcl.psmis.framework.mbg.entity.TInverterEntity;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * CronTask中使用的sql
 *
 * <AUTHOR>
 */
public interface CronTaskDao {


    List<SnAppid> queryInverterList(@Param("type") int type, @Param("snList") List<String> snList);
}
