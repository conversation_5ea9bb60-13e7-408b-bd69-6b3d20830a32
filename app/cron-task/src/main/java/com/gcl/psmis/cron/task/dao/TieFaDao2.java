package com.gcl.psmis.cron.task.dao;

import com.baomidou.dynamic.datasource.annotation.DS;
import com.gcl.psmis.framework.common.dto.tiefa.*;
import com.gcl.psmis.framework.mbg.entity.TTfConfigImgEntity;
import org.apache.ibatis.annotations.Param;

import java.util.List;
import java.util.Map;

/**
 * @className: TieFaDao
 * @author: xinan.yuan
 * @create: 2023/11/21 10:38
 * @description:
 */
public interface TieFaDao2 {


    @DS("xyg")
    List<Long> listOrderId(@Param("startTime") String startTime, @Param("endTime") String endTime);

    /**
     * @param startTime
     * @param endTime
     * @return {@link List< Long>}
     * @desc 录单orderIds
     * @date 2023/11/21 15:31
     */
    @DS("xyg")
    List<Long> listRecordOrderId(@Param("startTime") String startTime, @Param("endTime") String endTime);

    /**
     * @param startTime
     * @param endTime
     * @return {@link List< Long>}
     * @desc 录单orderIds
     * @date 2023/11/21 15:31
     */
    @DS("xyg")
    List<Long> listRecordOrderId3(@Param("startTime") String startTime, @Param("endTime") String endTime);

    /**
     * @param orderIds
     * @return {@link List< NewEnterDTO>}
     * @desc 录单阶段订单信息
     * @date 2023/11/21 22:05
     */
    @DS("xyg")
    List<NewEnterDTO> listRecordOrderInfos(@Param("orderIds") List<Long> orderIds);


    /**
     * @param startTime
     * @param endTime
     * @return {@link List< Long>}
     * @desc 开发收资orderIds
     * @date 2023/11/21 22:03
     */
    @DS("xyg")
    List<Long> listNewConkfOrderId(@Param("startTime") String startTime, @Param("endTime") String endTime);

    /**
     * @param startTime
     * @param endTime
     * @return {@link List< Long>}
     * @desc 开发收资orderIds
     * @date 2023/11/21 22:03
     */
    @DS("xyg")
    List<Long> listNewConkfOrderId3(@Param("startTime") String startTime, @Param("endTime") String endTime);

    /**
     * @param orderIds
     * @return {@link List< NewConkfDTO>}
     * @desc 开发收资阶段订单信息
     * @date 2023/11/22 16:19
     */
    @DS("xyg")
    List<NewConkfDTO> listNewConkfOrderInfos(@Param("orderIds") List<Long> orderIds, @Param("itemSql") String itemSql, @Param("imgSql") String imgSql);

    /**
     * @param startTime
     * @param endTime
     * @return {@link List< Long>}
     * @desc 施工完成ordIds
     * @date 2023/11/23 10:20
     */
    @DS("xyg")
    List<Long> listNewStructOrderIds(@Param("startTime") String startTime, @Param("endTime") String endTime);

    /**
     * @param startTime
     * @param endTime
     * @return {@link List< Long>}
     * @desc 施工完成ordIds
     * @date 2023/11/23 10:20
     */
    @DS("xyg")
    List<Long> listNewStructOrderIds3(@Param("startTime") String startTime, @Param("endTime") String endTime);

    @DS("xyg")
    List<NewStructDTO> listNewStructOrderInfos(@Param("orderIds") List<Long> orderIds, @Param("imgSql") String imgSql);

    /**
     * @param orderId
     * @return {@link List< NewStructBomDTO>}
     * @desc 施工bom信息
     * @date 2023/11/23 11:04
     */
    @DS("xyg")
    List<NewStructBomDTO> listNewStructBomInfos(@Param("orderId") Long orderId);

    /**
     * @param orderId
     * @return {@link List< NewStructInverterBomDTO>}
     * @desc 施工逆变器bom信息
     * @date 2023/11/23 11:04
     */
    @DS("xyg")
    List<NewStructInverterBomDTO> listNewStructInverterBoms(@Param("orderId") Long orderId);

    /**
     * @param startTime
     * @param endTime
     * @return {@link List< Long>}
     * @desc 并网完成ordIds
     * @date 2023/11/23 15:35
     */
    @DS("xyg")
    List<Long> listNewOngridOrderIds(@Param("startTime") String startTime, @Param("endTime") String endTime);

    /**
     * @param startTime
     * @param endTime
     * @return {@link List< Long>}
     * @desc 并网完成ordIds
     * @date 2023/11/23 15:35
     */
    @DS("xyg")
    List<Long> listNewOngridOrderIds3(@Param("startTime") String startTime, @Param("endTime") String endTime);

    /**
     * @param orderIds
     * @return {@link List< NewOngridDTO>}
     * @desc 并网完成阶段订单信息
     * @date 2023/11/23 15:35
     */
    @DS("xyg")
    List<NewOngridDTO> listNewOngridOrderInfos(@Param("orderIds") List<Long> orderIds, @Param("imgSql") String imgSql);

    /**
     * 代理商地区行政编码
     */
    @DS("xyg")
    String listAgentAreaCode(long orgId);

    /**
     * @param orderid
     * @return {@link List< NewConkfDesBomDTO>}
     * @desc
     * @date 2023/11/27 15:59
     */
    @DS("xyg")
    List<NewConkfDesBomDTO> queryDesignInfo(@Param("orderId") Long orderid);

    @DS("xyg")
    List<NewConkfDesBomDTO> queryDesignInfo2(@Param("orderId") Long orderid);

    /**
     * 更新电站建转运时间
     */
    @DS("xyg")
    List<Long> listProOrderIds(@Param("startTime") String startTime, @Param("endTime") String endTime);

    /**
     * 更新电站建转运时间
     */
    @DS("xyg")
    List<Long> listProOrderIds3(@Param("startTime") String startTime, @Param("endTime") String endTime);

    /**
     * 更新电站建转运时间
     */
    @DS("xyg")
    List<ProOrderDTO> listProOrderInfos(@Param("orderIds") List<Long> orderIds);

    /**
     * 同步租金信息
     */
    @DS("xyg")
    List<ProRentDTO> listProRentInfos(@Param("orderIds") List<Long> orderIds);

    /**
     * 同步租金信息
     */
    @DS("xyg")
    List<RentDTO> listRent(@Param("orderNo") String orderNo);

    @DS("xyg")
    List<Long> getPower(@Param("orderId") Long orderId);

    @DS("xyg")
    List<Map<String, String>> getHouseTopType(@Param("orderId") Long orderId);

    //查询合同状态
    @DS("xyg")
    String getContractStatus(@Param("orderId") Long orderId);

    //查询合同path
    @DS("xyg")
    String getContractPath(@Param("orderId") Long orderId);

    @DS("xyg")
    List<String> getContractPath2(@Param("orderId") Long orderId);

    @DS("xyg")
    List<String> getDesignName(@Param("orderId") Long orderId);

    @DS("xyg")
    List<Long> listOrderIdByNo(@Param("orderNoStr") String orderNoStr);

    String existOrder(@Param("orderNoStr") String orderNo, @Param("type") String type);

    String existErrStep(@Param("orderNoStr") String orderNo);

    List<String> listExistOrderNo(@Param("orderNoStr") String orderNoStr);

    @DS("xyg")
    List<Map<String, Object>> listMaterialBom(@Param("orderId") Long orderId);

    @DS("xyg")
    List<Long> listContractList2();

    @DS("xyg")
    List<Long> queryArchivedList(@Param("startTime") String startTime, @Param("endTime") String endTime, @Param("stationList") List<String> stationList);

    @DS("xyg")
    List<Long> queryCompletedList(@Param("startTime") String startTime, @Param("endTime") String endTime, @Param("stationList") List<String> stationList);

    @DS("xyg")
    List<Long> queryGridConnectedList(@Param("startTime") String startTime, @Param("endTime") String endTime, @Param("stationList") List<String> stationList);

    @DS("xyg")
    List<TiefaStationDTO> queryGridConnectedList3(@Param("startTime") String startTime, @Param("endTime") String endTime, @Param("stationList") List<String> stationList);

    List<String> listHistoryOrderIdsS2(@Param("type") String type);

    List<String> listTempImport();

    List<PowerDataDTO> queryGridConnectedSnInfoList(@Param("stationList") List<String> stationList, @Param("orgList") List<String> orgList);

    @DS("xyg")
    List<String> queryArchivedStopList(@Param("stationList") List<String> stationList, @Param("notInList") List<String> notInList);

    List<TiefaStepDTO> queryStep(@Param("stationList") List<String> stationList);

    List<TiefaMultiDTO> queryImageConfig();

    @DS("xyg")
    List<StationMultiDTO> queryOrderMulti(@Param("stationList") List<String> stationList, @Param("lists") List<TiefaMultiDTO> lists);

    @DS("xyg")
    List<InstallDTO> queryInstallSchema(@Param("stationList") List<String> stationList);

    @DS("xyg")
    List<String> queryProjectOrgCodeList();
}
