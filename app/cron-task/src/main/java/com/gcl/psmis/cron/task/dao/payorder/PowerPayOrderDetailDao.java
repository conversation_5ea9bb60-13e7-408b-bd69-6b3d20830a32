package com.gcl.psmis.cron.task.dao.payorder;

import com.baomidou.dynamic.datasource.annotation.DS;
import com.gcl.psmis.framework.common.vo.account.SharingInfoVo;
import com.gcl.psmis.framework.common.vo.payorder.*;
import com.gcl.psmis.framework.common.vo.rent.PicPathVO;
import com.gcl.psmis.framework.mbg.entity.TEcCommitRecordEntity;
import com.gcl.psmis.framework.mbg.entity.TPowerPayOrderEntity;
import com.gcl.psmis.framework.mbg.entity.TUserPaymentLogEntity;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.Date;
import java.util.List;


/**
 * 处理账单信息
 *
 * <AUTHOR>
 */
@Mapper
public interface PowerPayOrderDetailDao {
    /**
     * 鑫阳光获取账单明细
     *
     * @return List
     */
    @DS("xyg")
    List<XYGPowerOrderDetailVO> getDetailData();

    /**
     * 根据电站编码,支付年月更新账单数据
     *
     * @param data TPowerPayOrderEntity
     */
    void updateBatch(@Param("list") List<TPowerPayOrderEntity> data);

    @DS("w-xyg")
    void updateAccountState(@Param("accountState") Integer accountState, @Param("xygIds") List<String> xygIds);

    /**
     * 批量修改支付状态
     *
     * @param paySuccessData data
     */
    void updatePaymentStatus(@Param("data") List<TUserPaymentLogEntity> paySuccessData);

    @DS("xyg")
    String getCardPicsByCusid(@Param("cusid") Long cusid);

    @DS("rent-ec")
    List<Long> getFiledRequestIds();

    void updateBankInfoAccountStateByOrderIds(@Param("ids") List<String> orderIds, @Param("accountStatus") Integer accountStatus);

    void updateOrderBankInfo();

    List<PowerPayOrderVO> getPowerPayOrderByRequestId(@Param("requestId") String requestId);

    List<PowerPayOrderVO> getUnpaidOrder();

    @DS("xyg")
    List<XYGPowerOrderDetailVO> getQYBA();


    @DS("xyg")
    List<XYGPowerOrderDetailVO> getGRBA();

    @DS("aly-xyg")
    List<PicPathVO> getCardPics();

    List<TEcCommitRecordEntity> getEcCommitRecordsByDate(@Param("lastDate") String lastDate);

    Date getMinDateByEcNo(@Param("ecNo") String ecNo);


    PowerPayOrderVO getPowerPayOrderByRequestIdAndOrderId(@Param("requestId") String requestId, @Param("orderId") Long orderId);

    @DS("xyg")
    List<OrderCustomerVO> getAllBankInfoFromXyg();

    @DS("xyg")
    List<ContractChangeVO> getHistoryContracts();

    @DS("xyg")
    List<OrgRuleVO> getOrgRule();


    @DS("xyg")
    List<SharingInfoVo> getSharingInfosByStationNos(@Param("stationNos") List<String> stationNos);

    List<PowerPayOrderVO> getOrders();

    List<PowerPayOrderVO> getNoPaymentByRequestId(@Param("requestId") String requestId);

    @DS("xyg")
    List<OrderCustomerVO> getJiangXiBankInfoFromXyg();

    @DS("xyg")
    List<PowerPayOrderVO> getJiangXiOrder();

    List<TUserPaymentLogEntity> getChaoWang();
}
