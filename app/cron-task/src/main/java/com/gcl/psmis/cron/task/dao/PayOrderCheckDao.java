package com.gcl.psmis.cron.task.dao;

import com.gcl.psmis.framework.common.vo.payorder.PayOrderCheckVO;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 账单检查
 */
public interface PayOrderCheckDao {
    /**
     * 检查是否生成多余的账单
     */
    List<PayOrderCheckVO> listOverPayOrder(@Param("date") String date);
    /**
     * 账单比对检查
     */
    List<PayOrderCheckVO> listAccountAndPayOrder(@Param("date") String date, @Param("offset") long offset, @Param("rows") long rows);

}
