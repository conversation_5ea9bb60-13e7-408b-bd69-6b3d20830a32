package com.gcl.psmis.cron.task.dao;

import com.baomidou.dynamic.datasource.annotation.DS;
import com.gcl.psmis.framework.common.vo.PsBasicCheckOnlyZwVO;
import com.gcl.psmis.framework.common.vo.PsBasicCheckVO;
import io.lettuce.core.dynamic.annotation.Param;
import org.apache.ibatis.annotations.MapKey;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Map;

@Repository
public interface PsBasicCheckDao {
    /**
     * 从鑫阳光原始数据中获取已完工的电站编号
     */
    @DS("xyg")
    List<String> psBasicCheckGetXygCompletedPs();
    /**
     * 从智维系统数据中获取已完工的电站编号
     */
    List<String> psBasicCheckGetZwCompletedPs();
    /**
     * 从鑫阳光原始数据中获取已完工公共部分的信息
     */
    @DS("xyg")
    @MapKey("staNum")
    Map<String, PsBasicCheckVO> psBasicCheckXygCompletedCommon(@Param("powerStations") List<String> powerStations);
    /**
     * 从智维系统数据中获取已完工公共部分的信息
     */
    @MapKey("staNum")
    Map<String, PsBasicCheckVO> psBasicCheckZwCompletedCommon(@Param("powerStations") List<String> powerStations);

    /**
     * 从鑫阳光原始数据中获取只存在于智维系统的电站的状态
     */
    @DS("xyg")
    @MapKey("staNum")
    Map<String, PsBasicCheckOnlyZwVO> psBasicCheckOnlyZwCompleted(@Param("powerStations") List<String> powerStations);

    /**
     * 从鑫阳光数据中获取已并网的电站编号
     */
    @DS("xyg")
    List<String> psBasicCheckGetXygGridPs();
    /**
     * 从智维系统数据中获取已并网的电站编号
     */
    List<String> psBasicCheckGetZwGridPs();
    /**
     * 从鑫阳光原始数据中获取已并网公共部分的信息
     */
    @DS("xyg")
    @MapKey("staNum")
    Map<String, PsBasicCheckVO> psBasicCheckXygGridCommon(@Param("powerStations") List<String> powerStations);
    /**
     * 从智维系统数据中获取已并网公共部分的信息
     */
    @MapKey("staNum")
    Map<String, PsBasicCheckVO> psBasicCheckZwGridCommon(@Param("powerStations") List<String> powerStations);

    @DS("xyg")
    @MapKey("staNum")
    Map<String, PsBasicCheckOnlyZwVO> psBasicCheckOnlyZwGrid(@Param("powerStations") List<String> powerStations);
}
