package com.gcl.psmis.cron.task.dao;

import com.gcl.psmis.framework.common.dto.psestimate.PsEstimateDTO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * project: PsStatementDao
 * Powered 2024-05-23 15:33:37
 *
 * <AUTHOR>
 * @version 1.0
 * @since 1.8
 */
@Mapper
public interface PsStatementDao {


    List<PsEstimateDTO> pushOffsetSumListByCompany(@Param("accountPeriod") String accountPeriod);

    List<PsEstimateDTO> getPsestimateSumListByPerson(@Param("accountSeq12")String accountSeq12,@Param("accountSeq13") String accountSeq13);

    List<PsEstimateDTO> pushOffsetSumListByPerson(@Param("accountPeriod") String accountPeriod);
}
