package com.gcl.psmis.cron.task.dao.black;

import com.baomidou.dynamic.datasource.annotation.DS;
import com.gcl.psmis.framework.common.dto.black.ZhiYunNCDTO;
import com.gcl.psmis.framework.common.dto.openapi.AgentCodeAndPhoneData;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
* @className: BlackNCDao
* @author: xinan.yuan
* @create: 2024/8/1 10:05
* @description:
*/
@Repository
public interface BlackNCDao {
    @DS("zhiyun")
    List<ZhiYunNCDTO> listZhiYunNC(@Param("startTime") String startTime);
    @DS("xyg")
    List<AgentCodeAndPhoneData> getAgentList(@Param("fromno") String fromno);
    @DS("xyg")
    int countOrderByFromNo(@Param("fromno")String fromno);
    @DS("xyg")
    int countAssembly(@Param("fromno")String fromno);
}
