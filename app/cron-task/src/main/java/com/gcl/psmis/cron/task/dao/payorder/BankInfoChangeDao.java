package com.gcl.psmis.cron.task.dao.payorder;

import com.baomidou.dynamic.datasource.annotation.DS;
import com.gcl.psmis.framework.common.dto.payorder.ECBankCardChangeVo;
import com.gcl.psmis.framework.common.vo.payorder.BankCardVo;
import com.gcl.psmis.framework.common.vo.payorder.OrderCustomerVO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;
import java.util.Set;

/**
 * @Author: sunjiaxing
 * @CreateTime: 2024-11-25  10:21
 * @Description: 银行卡变更
 */
@Mapper
public interface BankInfoChangeDao {

    @DS("ec")
    List<ECBankCardChangeVo> getECChangeRecords();

    @DS("xyg")
    List<OrderCustomerVO> getAllOrderCustomer(@Param("ordercustomerids") Set<Long> ordercustomerids);

    @DS("xyg")
    List<BankCardVo> getCardPicsByCusids(@Param("cusids") List<Long> cusids);
}
