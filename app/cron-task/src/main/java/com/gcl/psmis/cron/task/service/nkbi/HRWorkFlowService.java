package com.gcl.psmis.cron.task.service.nkbi;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.date.DatePattern;
import cn.hutool.core.date.DateTime;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONArray;
import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import com.baomidou.dynamic.datasource.annotation.DSTransactional;
import com.gcl.psmis.framework.common.ResponseResult;
import com.gcl.psmis.framework.common.constant.CommonResultCode;
import com.gcl.psmis.framework.common.dto.nkbi.FillFlowAuditPersonDTO;
import com.gcl.psmis.framework.common.dto.nkbi.FillFlowDTO;
import com.gcl.psmis.framework.common.dto.nkbi.FlowCompanyDTO;
import com.gcl.psmis.framework.common.exception.BussinessException;
import com.gcl.psmis.framework.gencode.enums.GenRuleCode;
import com.gcl.psmis.framework.gencode.util.GenCodeUtil;
import com.gcl.psmis.framework.mbg.entity.TFillingFlowDetailEntity;
import com.gcl.psmis.framework.mbg.entity.TFillingFlowEntity;
import com.gcl.psmis.framework.mbg.entity.TFlowVsBusinessEntity;
import com.gcl.psmis.framework.mbg.service.TFlowVsBusinessService;
import com.gcl.psmis.workflow.api.WorkFlowRpcService;
import com.gcl.psmis.workflow.constant.ProcessKeyEnum;
import com.gcl.psmis.workflow.constant.task.FlowNodeEnum;
import com.gcl.psmis.workflow.constant.task.SingleWorkFlowEnum;
import com.gcl.psmis.workflow.constant.task.SumWorkFlowEnum;
import com.gcl.psmis.workflow.domain.dto.FlowBizDTO;
import com.gcl.psmis.workflow.domain.dto.IdentityLinkDTO;
import com.gcl.psmis.workflow.domain.req.ProcessCompleteReq;
import com.gcl.psmis.workflow.domain.req.StartProcessInstanceReq;
import com.xxl.job.core.context.XxlJobHelper;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.stream.Collectors;

/**
 * HR工作流服务 - 优化版本
 * 负责人资单体填报流程的发起、管理和完成
 *
 * <AUTHOR>
 * @date 2022/05/09
 * @version 2.0
 */
@Service
@Slf4j
@RequiredArgsConstructor
public class HRWorkFlowService {

    // ==================== 常量定义 ====================

    /** 流程类型：单体流程 */
    private static final int FLOW_TYPE_SINGLE = 1;

    /** 流程类型：汇总流程 */
    private static final int FLOW_TYPE_SUM = 2;

    /** 指标类型：计划 */
    private static final int INDEX_TYPE_PLAN = 1;

    /** 指标类型：实际 */
    private static final int INDEX_TYPE_ACTUAL = 2;

    /** 流程状态：未提交 */
    private static final int FLOW_STATUS_UNSUBMITTED = 1;

    /** 流程状态：已提交 */
    private static final int FLOW_STATUS_SUBMITTED = 2;

    /** 流程状态：归档 */
    private static final int FLOW_STATUS_ARCHIVED = 3;

    /** 审核节点：第一节点 */
    private static final int AUDIT_NODE_FIRST = 1;

    /** 审核选择：通过 */
    private static final int AUDIT_CHOICE_APPROVE = 1;

    /** 系统操作员 */
    private static final String SYSTEM_OPERATOR = "system";

    /** 系统操作员名称 */
    private static final String SYSTEM_OPERATOR_NAME = "系统";

    /** 线程休眠时间（毫秒） */
    private static final int THREAD_SLEEP_MILLIS = 100;

    /** 默认查询月份偏移量 */
    private static final int DEFAULT_QUERY_MONTH_OFFSET = -3;

    // ==================== 依赖注入 ====================

    private final WorkFlowRpcService workFlowRpcService;

    @Autowired
    private FillingFlowService fillingFlowService;

    @Autowired
    private GenCodeUtil genCodeUtil;

    @Autowired
    private TFlowVsBusinessService tFlowVsBusinessService;

    // ==================== 公共API方法 ====================

    /**
     * 初始化单体计划填报工作流
     *
     * @param yearMonthly 年月字符串，为空时默认为下个月
     */
    public void initSinglePlanFillWorkflow(String yearMonthly) {
        final String methodName = "initSinglePlanFillWorkflow";
        log.info("[{}] 开始初始化单体计划填报工作流, yearMonthly: {}", methodName, yearMonthly);

        String targetYearMonthly = getTargetYearMonthly(yearMonthly, true);
        validateAndInitProcess("单体上报计划", FLOW_TYPE_SINGLE, ProcessKeyEnum.SINGLE_FILLING_FLOW, INDEX_TYPE_PLAN, targetYearMonthly);

        log.info("[{}] 单体计划填报工作流初始化完成", methodName);
    }

    /**
     * 初始化单体实际填报工作流
     *
     * @param yearMonthly 年月字符串，为空时默认为上个月
     */
    public void initSingleActualFillWorkflow(String yearMonthly) {
        final String methodName = "initSingleActualFillWorkflow";
        log.info("[{}] 开始初始化单体实际填报工作流, yearMonthly: {}", methodName, yearMonthly);

        String targetYearMonthly = getTargetYearMonthly(yearMonthly, false);
        validateAndInitProcess("单体上报实际", FLOW_TYPE_SINGLE, ProcessKeyEnum.SINGLE_FILLING_FLOW, INDEX_TYPE_ACTUAL, targetYearMonthly);

        log.info("[{}] 单体实际填报工作流初始化完成", methodName);
    }

    /**
     * 初始化汇总计划填报工作流
     *
     * @param yearMonthly 年月字符串，为空时默认为下个月
     */
    public void initSumPlanFillWorkflow(String yearMonthly) {
        final String methodName = "initSumPlanFillWorkflow";
        log.info("[{}] 开始初始化汇总计划填报工作流, yearMonthly: {}", methodName, yearMonthly);

        String targetYearMonthly = getTargetYearMonthly(yearMonthly, true);
        validateAndInitProcess("汇总上报计划", FLOW_TYPE_SUM, ProcessKeyEnum.SUM_FILLING_FLOW, INDEX_TYPE_PLAN, targetYearMonthly);

        log.info("[{}] 汇总计划填报工作流初始化完成", methodName);
    }

    /**
     * 初始化汇总实际填报工作流
     *
     * @param yearMonthly 年月字符串，为空时默认为上个月
     */
    public void initSumActualFillWorkflow(String yearMonthly) {
        final String methodName = "initSumActualFillWorkflow";
        log.info("[{}] 开始初始化汇总实际填报工作流, yearMonthly: {}", methodName, yearMonthly);

        String targetYearMonthly = getTargetYearMonthly(yearMonthly, false);
        validateAndInitProcess("汇总上报实际", FLOW_TYPE_SUM, ProcessKeyEnum.SUM_FILLING_FLOW, INDEX_TYPE_ACTUAL, targetYearMonthly);

        log.info("[{}] 汇总实际填报工作流初始化完成", methodName);
    }

    // ==================== 私有工具方法 ====================

    /**
     * 获取目标年月字符串
     *
     * @param yearMonthly 输入的年月字符串
     * @param isNextMonth true表示下个月，false表示上个月
     * @return 目标年月字符串
     */
    private String getTargetYearMonthly(String yearMonthly, boolean isNextMonth) {
        if (StrUtil.isNotBlank(yearMonthly)) {
            return yearMonthly;
        }

        return isNextMonth
            ? DateUtil.format(DateUtil.nextMonth(), DatePattern.NORM_MONTH_PATTERN)
            : DateUtil.format(DateUtil.lastMonth(), DatePattern.NORM_MONTH_PATTERN);
    }

    /**
     * 验证并初始化流程 - 优化版本
     *
     * @param flowName       流程名称
     * @param flowType       流程类型
     * @param processKeyEnum 流程关键字枚举
     * @param indexType      指标类型
     * @param yearMonthly    年月标识
     */
    private void validateAndInitProcess(String flowName, int flowType, ProcessKeyEnum processKeyEnum, int indexType, String yearMonthly) {
        final String methodName = "validateAndInitProcess";
        log.info("[{}] 开始验证并初始化流程: {}, 类型: {}, 年月: {}", methodName, flowName, flowType, yearMonthly);

        try {
            // 1. 验证数据同步状态
            validateDataSync(yearMonthly);

            // 2. 获取流程公司数据
            FlowProcessContext context = buildFlowProcessContext(flowType, yearMonthly);

            // 3. 验证流程公司数据
            validateFlowCompanyData(context.getFlowCompanyDTOS());

            // 4. 记录流程初始化开始
            logInitProcessStart(flowName);

            // 5. 启动流程
            startFlows(context, flowType, processKeyEnum, indexType, yearMonthly, flowName);

            // 6. 记录流程完成
            logProcessCompletion(flowName);

            log.info("[{}] 流程验证和初始化完成: {}", methodName, flowName);

        } catch (Exception e) {
            log.error("[{}] 流程验证和初始化失败: {}", methodName, flowName, e);
            throw e;
        }
    }

    /**
     * 验证数据同步状态
     */
    private void validateDataSync(String yearMonthly) {
        if (!fillingFlowService.verifySync(yearMonthly)) {
            logErrorAndThrowException("中转表数据未同步完成，请稍后重试");
        }
    }

    /**
     * 构建流程处理上下文
     */
    private FlowProcessContext buildFlowProcessContext(int flowType, String yearMonthly) {
        FlowProcessContext context = new FlowProcessContext();

        if (flowType == FLOW_TYPE_SINGLE) {
            // 单体流程
            context.setFlowCompanyDTOS(fillingFlowService.getFlowCompanyDTOS(FLOW_TYPE_SINGLE));
            context.setSumFlows(fillingFlowService.getSumFlows(yearMonthly));

        } else if (flowType == FLOW_TYPE_SUM) {
            // 汇总流程
            List<FlowCompanyDTO> allCompanies = fillingFlowService.getFlowCompanyDTOS(null);

            // 分离单体和汇总流程数据
            List<FlowCompanyDTO> singleFlows = allCompanies.stream()
                .filter(item -> item.getFlowId() == FLOW_TYPE_SINGLE)
                .collect(Collectors.toList());

            context.setFlowCompanyDTOS(allCompanies.stream()
                .filter(item -> item.getFlowId() == FLOW_TYPE_SUM)
                .collect(Collectors.toList()));

            if (CollUtil.isNotEmpty(singleFlows)) {
                context.setSingleFlowMap(singleFlows.stream()
                    .collect(Collectors.groupingBy(FlowCompanyDTO::getSumCompanyCode)));
            }
        }

        return context;
    }

    /**
     * 验证流程公司数据
     */
    private void validateFlowCompanyData(List<FlowCompanyDTO> flowCompanyDTOS) {
        if (CollUtil.isEmpty(flowCompanyDTOS)) {
            logErrorAndThrowException("未查询到流程公司数据，请检查流程配置");
        }
    }

    /**
     * 发起流程
     *
     * @param companyDTO                  公司数据传输对象，包含填报公司和汇总公司的代码
     * @param processKeyEnum              流程关键字枚举，用于标识不同的流程
     * @param indexType                   指标类型
     * @param yearMonthly                 年月字符串，用于指定流程的时间范围
     * @param singleFlowMap
     * @param flowName
     * @param tFillingFlowEntities
     * @param sumFlows
     * @param lastMonthFlowDetailEntities
     */
    @DSTransactional
    public void startFlow(FlowCompanyDTO companyDTO, int flowType, ProcessKeyEnum processKeyEnum, int indexType, String yearMonthly,
                          Map<String, List<FlowCompanyDTO>> singleFlowMap, String flowName, List<TFillingFlowEntity> tFillingFlowEntities,
                          List<TFillingFlowEntity> sumFlows, List<TFillingFlowDetailEntity> lastMonthFlowDetailEntities) {
        try {
            // 校验当月流程是否已经发起
            if (isDuplicateFlow(tFillingFlowEntities, companyDTO)) {
                XxlJobHelper.log(String.format("当月流程已经发起，%s创建" + flowName + "失败!", companyDTO.getFillCompanyCode()));
                return;
            }
            // 如果是汇总流程， 校验单体填报是否完成 没有完成直接跳过
//            if (flowType == 2 && !fillingFlowService.checkSingleFlowStatus(companyDTO, singleFlowMap, indexType, yearMonthly)) {
//                XxlJobHelper.log(String.format("体填报未完成，%s创建" + flowName + "失败!", companyDTO.getFillCompanyCode()));
//                return;
//            }
            // 1、获取填报公司code
            String sumCompanyCode = companyDTO.getSumCompanyCode();
            if (flowType == 2) {
                sumCompanyCode = companyDTO.getFillCompanyCode();
            }
            // 3 、根据流程公司数据  获取所有填报公司详细数据
            List<FillFlowDTO> fillFlowDTOS = fillingFlowService.getFillFlowByCompanyCode(companyDTO.getFillCompanyCode(),
                    sumCompanyCode, yearMonthly, flowType, companyDTO.getSumOrgType(), companyDTO.getSpecFlag());

            // 如果没有数据，则跳过该流程
            if (CollUtil.isEmpty(fillFlowDTOS)) {
                XxlJobHelper.log("未查询到填报公司" + companyDTO.getFillCompanyCode() + "详细数据，请检查流程配置");
                return;
                //                if (!checkDataIntegrity(companyDTO, flowType, yearMonthly)) {
//                    return;
//                }
            }

            // 获取审核人员
            String persons = extractAuditPersons(companyDTO);

            // 构建流程实体
            TFillingFlowEntity flowEntity = buildFlowEntity(companyDTO, flowType, processKeyEnum, indexType, yearMonthly);

            // 发起工作流
            startWorkFlow(processKeyEnum, flowEntity, persons, flowName);

            // 保存流程业务数据
            fillingFlowService.saveFlowData(flowEntity, fillFlowDTOS, companyDTO.getFillCompanyCode(), sumFlows, lastMonthFlowDetailEntities);

            // 记录成功日志
            logFlowSuccess(flowEntity.getFlowNo(), flowName);

        } catch (InterruptedException e) {
            log.error("流程发起被中断: {}", flowName, e);
            Thread.currentThread().interrupt();
            throw new RuntimeException("流程发起被中断", e);
        } catch (Exception e) {
            log.error("流程发起失败: {}, 公司: {}", flowName, companyDTO.getFillCompanyCode(), e);
            throw new RuntimeException("流程发起失败: " + e.getMessage(), e);
        }
    }

    // ==================== 私有辅助方法 ====================

    /**
     * 检查是否为重复流程
     */
    private boolean isDuplicateFlow(List<TFillingFlowEntity> existingFlows, FlowCompanyDTO companyDTO) {
        if (CollUtil.isEmpty(existingFlows)) {
            return false;
        }

        return existingFlows.stream().anyMatch(flow ->
            Objects.equals(flow.getParentCode(), companyDTO.getSumCompanyCode()) &&
            Objects.equals(flow.getCompanyCode(), companyDTO.getFillCompanyCode()) &&
            Objects.equals(flow.getSumOrgType(), companyDTO.getSumOrgType())
        );
    }

    /**
     * 提取审核人员信息 - 优化版本
     *
     * @param companyDTO 公司数据传输对象
     * @return 审核人员字符串，多个人员用逗号分隔
     */
    private String extractAuditPersons(FlowCompanyDTO companyDTO) {
        List<FillFlowAuditPersonDTO> auditPersons = companyDTO.getAuditPerson();
        if (CollUtil.isEmpty(auditPersons)) {
            log.warn("公司 {} 未配置审核人员", companyDTO.getFillCompanyCode());
            return "";
        }

        List<String> auditCodes = auditPersons.stream()
            .filter(audit -> Objects.equals(audit.getNodeCode(), AUDIT_NODE_FIRST))
            .map(FillFlowAuditPersonDTO::getAuditCode)
            .filter(StrUtil::isNotBlank)
            .collect(Collectors.toList());

        return CollUtil.isNotEmpty(auditCodes) ? StrUtil.join(",", auditCodes) : "";
    }

    /**
     * 构建流程实体
     */
    private TFillingFlowEntity buildFlowEntity(FlowCompanyDTO companyDTO, int flowType, ProcessKeyEnum processKeyEnum,
                                               int indexType, String yearMonthly) throws InterruptedException {
        // 生成流程编号
        String flowNo = generateFlowNo(processKeyEnum.name());

        // 休眠避免数据同步问题
        Thread.sleep(THREAD_SLEEP_MILLIS);

        // 设置汇总状态
        Integer sumStatus = (flowType == FLOW_TYPE_SINGLE) ? FLOW_STATUS_UNSUBMITTED : null;

        return fillingFlowService.getTFillingFlowEntity(companyDTO, flowType, processKeyEnum, indexType, yearMonthly, sumStatus, flowNo);
    }

    /**
     * 生成流程编号 - 优化版本
     */
    private String generateFlowNo(String processKeyName) {
        String ruleCode = genCodeUtil.getSysCodeByCodeRule(GenRuleCode.FILLING_FLOW);
        return StrUtil.concat(true, processKeyName, "_", ruleCode);
    }



    /**
     * 记录流程成功日志
     */
    private void logFlowSuccess(String flowNo, String flowName) {
        String message = String.format("%s创建%s成功!", flowNo, flowName);
        XxlJobHelper.log(message);
        log.info(message);
    }

    /**
     * 校验数据完整性
     *
     * @param companyDTO
     * @param flowType
     * @param yearMonthly
     * @return
     */
    private boolean checkDataIntegrity(FlowCompanyDTO companyDTO, int flowType, String yearMonthly) {
        if (flowType == 1) {
            XxlJobHelper.log("未查询到填报公司" + companyDTO.getFillCompanyCode() + "详细数据，请检查流程配置");
            return false;
        } else {
            // 校验单体填报是否有数据，如果没有，跳过
            int count = fillingFlowService.getCountByYearMonthlyAndParentCodeAndSumOrgType(yearMonthly,
                    companyDTO.getFillCompanyCode(), companyDTO.getSumOrgType());
            if (count == 0) {
                XxlJobHelper.log("未查询到填报公司" + companyDTO.getFillCompanyCode() + "详细数据，请检查流程配置");
                return false;
            }
        }
        return true;
    }



    /**
     * 记录错误日志并抛出异常
     */
    private void logErrorAndThrowException(String message) {
        XxlJobHelper.log(message);
        throw new BussinessException(message);
    }

    /**
     * 记录初始化流程开始日志
     */
    private void logInitProcessStart(String flowName) {
        XxlJobHelper.log("中转表数据同步完成，开始发起" + flowName + "流程");
    }

    /**
     * 记录流程完成日志
     */
    private void logProcessCompletion(String flowName) {
        XxlJobHelper.log(flowName + "流程发起完成");
    }

    /**
     * 启动多个流程 - 优化版本
     * 支持批量处理和更好的错误处理
     */
    private void startFlows(FlowProcessContext context, int flowType, ProcessKeyEnum processKeyEnum, int indexType,
                           String yearMonthly, String flowName) {
        final String methodName = "startFlows";
        List<FlowCompanyDTO> flowCompanyDTOS = context.getFlowCompanyDTOS();

        log.info("[{}] 开始批量启动流程: {}, 公司数量: {}", methodName, flowName, flowCompanyDTOS.size());

        // 获取已发起的流程数据
        List<TFillingFlowEntity> existingFlows = fillingFlowService.getTFillingFlowEntities(yearMonthly, flowType, indexType, null);

        // 获取上个月的流程数据
        String lastYearMonthly = calculateLastYearMonthly(yearMonthly);
        List<TFillingFlowDetailEntity> lastMonthFlowDetails = fillingFlowService.getFlowDetailEntities(lastYearMonthly, flowType, indexType);

        // 统计信息
        int successCount = 0;
        int skipCount = 0;
        int errorCount = 0;

        // 遍历每个流程公司数据，并发起流程
        for (FlowCompanyDTO companyDTO : flowCompanyDTOS) {
            try {
                startFlow(companyDTO, flowType, processKeyEnum, indexType, yearMonthly,
                         context.getSingleFlowMap(), flowName, existingFlows, context.getSumFlows(), lastMonthFlowDetails);
                successCount++;

            } catch (Exception e) {
                errorCount++;
                log.error("[{}] 公司 {} 流程启动失败", methodName, companyDTO.getFillCompanyCode(), e);
                XxlJobHelper.log(String.format("公司 %s 流程启动失败: %s", companyDTO.getFillCompanyCode(), e.getMessage()));
            }
        }

        // 记录统计信息
        log.info("[{}] 流程启动完成: {}, 成功: {}, 跳过: {}, 失败: {}",
            methodName, flowName, successCount, skipCount, errorCount);
        XxlJobHelper.log(String.format("%s流程启动完成，成功: %d, 失败: %d", flowName, successCount, errorCount));
    }

    /**
     * 计算上个月的年月字符串
     */
    private String calculateLastYearMonthly(String yearMonthly) {
        return DateUtil.format(
            DateUtil.offsetMonth(DateUtil.parse(yearMonthly + "-01"), -1),
            DatePattern.NORM_MONTH_PATTERN
        );
    }

    /**
     * 发起工作流 - 优化版本
     *
     * @param processKeyEnum 流程关键字枚举
     * @param flowEntity     流程实体
     * @param persons        审核人员
     * @param flowName       流程名称
     */
    public void startWorkFlow(ProcessKeyEnum processKeyEnum, TFillingFlowEntity flowEntity, String persons, String flowName) {
        final String methodName = "startWorkFlow";
        log.debug("[{}] 开始发起工作流: {}, 流程号: {}", methodName, flowName, flowEntity.getFlowNo());

        try {
            // 构建启动请求
            StartProcessInstanceReq request = buildStartProcessRequest(processKeyEnum, flowEntity, persons);

            // 调用工作流服务
            ResponseResult result = workFlowRpcService.start(request);

            // 验证结果
            validateWorkflowResult(result, flowName);

            log.debug("[{}] 工作流启动成功: {}", methodName, flowEntity.getFlowNo());

        } catch (Exception e) {
            log.error("[{}] 工作流启动失败: {}, 流程号: {}", methodName, flowName, flowEntity.getFlowNo(), e);
            throw new BussinessException("开启" + flowName + "流程失败: " + e.getMessage());
        }
    }

    /**
     * 构建启动流程请求
     */
    private StartProcessInstanceReq buildStartProcessRequest(ProcessKeyEnum processKeyEnum, TFillingFlowEntity flowEntity, String persons) {
        StartProcessInstanceReq request = new StartProcessInstanceReq();
        request.setAuthenticatedAccount(SYSTEM_OPERATOR);
        request.setProcessKey(processKeyEnum);
        request.setBusinessKey(flowEntity.getFlowNo());

        // 设置流程变量
        Map<String, Object> variables = new HashMap<>();
        variables.put("person", persons);
        variables.put("operatorAccount", SYSTEM_OPERATOR);
        variables.put("operatorName", SYSTEM_OPERATOR_NAME);
        request.setVariables(variables);

        return request;
    }

    /**
     * 验证工作流结果
     */
    private void validateWorkflowResult(ResponseResult result, String flowName) {
        if (result.getCode() != CommonResultCode.SUCCESS.code()) {
            String errorMsg = String.format("开启%s流程失败，错误码: %s, 错误信息: %s",
                flowName, result.getCode(), result.getMessage());
            throw new BussinessException(errorMsg);
        }
    }

    /**
     * 完成填报工作流 - 优化版本
     *
     * @param yearMonthly 年月字符串，为空时默认查询前3个月数据
     */
    public void completeFillWorkflow(String yearMonthly) {
        final String methodName = "completeFillWorkflow";

        // 参数处理
        String targetYearMonthly = getTargetYearMonthlyForComplete(yearMonthly);
        log.info("[{}] 开始完成填报工作流, yearMonthly: {}", methodName, targetYearMonthly);

        try {
            // 获取待完成的流程
            List<TFillingFlowEntity> pendingFlows = getPendingFlows(targetYearMonthly);

            if (CollUtil.isEmpty(pendingFlows)) {
                log.info("[{}] 没有待完成的流程", methodName);
                XxlJobHelper.log("没有待完成的流程");
                return;
            }

            // 批量处理流程
            processFlowsCompletion(pendingFlows);

            log.info("[{}] 填报工作流完成处理结束", methodName);

        } catch (Exception e) {
            log.error("[{}] 完成填报工作流失败", methodName, e);
            XxlJobHelper.log("完成填报工作流失败: " + e.getMessage());
            throw e;
        }
    }

    /**
     * 获取目标年月（用于完成流程）
     */
    private String getTargetYearMonthlyForComplete(String yearMonthly) {
        if (StrUtil.isNotBlank(yearMonthly)) {
            return yearMonthly;
        }

        // 默认查询当前月份前3个月数据
        return DateUtil.format(
            DateUtil.offsetMonth(new DateTime(), DEFAULT_QUERY_MONTH_OFFSET),
            DatePattern.NORM_MONTH_PATTERN
        );
    }

    /**
     * 获取待完成的流程
     */
    private List<TFillingFlowEntity> getPendingFlows(String yearMonthly) {
        List<TFillingFlowEntity> allFlows = new ArrayList<>();

        // 获取单体流程（状态为2：已提交）
        List<TFillingFlowEntity> singleFlows = fillingFlowService.getFlows(yearMonthly, FLOW_STATUS_SUBMITTED);
        if (CollUtil.isNotEmpty(singleFlows)) {
            allFlows.addAll(singleFlows);
        }

        // 获取汇总流程（状态为3：归档）
        List<TFillingFlowEntity> sumFlows = fillingFlowService.getFlows(yearMonthly, FLOW_STATUS_ARCHIVED);
        if (CollUtil.isNotEmpty(sumFlows)) {
            allFlows.addAll(sumFlows);
        }

        return allFlows;
    }

    /**
     * 批量处理流程完成
     */
    private void processFlowsCompletion(List<TFillingFlowEntity> flows) {
        final String methodName = "processFlowsCompletion";
        int successCount = 0;
        int errorCount = 0;

        log.info("[{}] 开始处理 {} 个待完成流程", methodName, flows.size());

        for (TFillingFlowEntity flow : flows) {
            try {
                auditWorkflow(flow);
                successCount++;

            } catch (Exception e) {
                errorCount++;
                log.error("[{}] 流程 {} 完成失败", methodName, flow.getFlowNo(), e);
                XxlJobHelper.log(String.format("流程 %s 完成失败: %s", flow.getFlowNo(), e.getMessage()));
            }
        }

        // 记录统计信息
        log.info("[{}] 流程完成处理结束，成功: {}, 失败: {}", methodName, successCount, errorCount);
        XxlJobHelper.log(String.format("流程完成处理结束，成功: %d, 失败: %d", successCount, errorCount));
    }


    /**
     * 审核工作流 - 优化版本
     *
     * @param flowEntity 填报流程实体
     */
    public void auditWorkflow(TFillingFlowEntity flowEntity) {
        final String methodName = "auditWorkflow";

        // 参数验证
        validateFlowEntity(flowEntity);

        log.debug("[{}] 开始审核工作流: {}", methodName, flowEntity.getFlowNo());

        try {
            // 获取流程节点枚举
            FlowNodeEnum flowNodeEnum = determineFlowNodeEnum(flowEntity);

            // 获取流程业务信息
            TFlowVsBusinessEntity businessEntity = getFlowBusinessEntity(flowEntity.getFlowNo());

            // 构建完成请求
            ProcessCompleteReq completeReq = buildProcessCompleteRequest(flowEntity, businessEntity, flowNodeEnum);

            // 执行流程完成
            executeProcessComplete(completeReq, flowNodeEnum);

            // 更新流程状态
            fillingFlowService.updateFlowStatus(flowEntity);

            log.debug("[{}] 工作流审核完成: {}", methodName, flowEntity.getFlowNo());

        } catch (Exception e) {
            log.error("[{}] 工作流审核失败: {}", methodName, flowEntity.getFlowNo(), e);
            throw e;
        }
    }

    /**
     * 验证流程实体
     */
    private void validateFlowEntity(TFillingFlowEntity flowEntity) {
        if (flowEntity == null) {
            throw new BussinessException("无表单信息！");
        }

        if (StrUtil.isBlank(flowEntity.getFlowNo())) {
            throw new BussinessException("流程编号不能为空！");
        }

        if (StrUtil.isBlank(flowEntity.getLastAuditCode())) {
            throw new BussinessException("审核人信息不能为空！");
        }
    }

    /**
     * 确定流程节点枚举
     */
    private FlowNodeEnum determineFlowNodeEnum(TFillingFlowEntity flowEntity) {
        if (Objects.equals(flowEntity.getFlowType(), FLOW_TYPE_SINGLE)) {
            return SingleWorkFlowEnum.ACTIVITY_SINGLE_FILLING_02;
        } else if (Objects.equals(flowEntity.getFlowType(), FLOW_TYPE_SUM)) {
            return SumWorkFlowEnum.ACTIVITY_SUM_FILLING_03;
        } else {
            throw new BussinessException("不支持的流程类型: " + flowEntity.getFlowType());
        }
    }

    /**
     * 获取流程业务实体
     */
    private TFlowVsBusinessEntity getFlowBusinessEntity(String flowNo) {
        TFlowVsBusinessEntity businessEntity = tFlowVsBusinessService.lambdaQuery()
            .eq(TFlowVsBusinessEntity::getBusinessKey, flowNo)
            .one();

        if (businessEntity == null) {
            throw new BussinessException("无当前工单流程信息！");
        }

        return businessEntity;
    }

    /**
     * 构建流程完成请求
     */
    private ProcessCompleteReq buildProcessCompleteRequest(TFillingFlowEntity flowEntity,
                                                          TFlowVsBusinessEntity businessEntity,
                                                          FlowNodeEnum flowNodeEnum) {
        ProcessCompleteReq completeReq = new ProcessCompleteReq();
        completeReq.setProcessInstanceId(businessEntity.getProcessInstanceId());
        completeReq.setFlowNodeEnum(flowNodeEnum);
        completeReq.setOperatorAccount(flowEntity.getLastAuditCode());
        completeReq.setOperatorName(flowEntity.getLastAuditCode());

        // 设置流程变量
        Map<String, Object> variables = new HashMap<>();
        variables.put("person", flowEntity.getLastAuditCode());
        variables.put("choose", AUDIT_CHOICE_APPROVE);
        variables.put("operatorAccount", flowEntity.getLastAuditCode());
        variables.put("operatorName", flowEntity.getLastAuditCode());
        completeReq.setVariables(variables);

        return completeReq;
    }

    /**
     * 执行流程完成
     */
    private void executeProcessComplete(ProcessCompleteReq completeReq, FlowNodeEnum flowNodeEnum) {
        ResponseResult result = workFlowRpcService.processComplete(completeReq);

        if (result.getCode() != CommonResultCode.SUCCESS.code()) {
            String errorMsg = String.format("%s提交流程失败，错误码: %s, 错误信息: %s",
                flowNodeEnum.getDesc(), result.getCode(), result.getMessage());
            throw new BussinessException(errorMsg);
        }
    }

    /**
     * 校验流程状态
     *
     * @param one
     * @param empNo
     * @param flowNodeEnum
     */
    private void checkFlowStatus(TFlowVsBusinessEntity one, String empNo, FlowNodeEnum flowNodeEnum) {
        FlowBizDTO flowBizDTO = new FlowBizDTO();
        flowBizDTO.setProcInsId(one.getProcessInstanceId());
        flowBizDTO.setUserNo(empNo);
        ResponseResult responseResult = workFlowRpcService.queryTaskByUser(flowBizDTO);
        if (responseResult.getCode() != CommonResultCode.SUCCESS.code()) {
            throw new BussinessException("当前人员无此流程节点代办任务,请确认！");
        }
        JSONObject jsonObj = JSONUtil.parseObj(responseResult.getData());
        String code = jsonObj.getStr("taskDefinitionKey");
        if (!code.equals(flowNodeEnum.getCode())) {
            throw new BussinessException("当前人员的待办节点不对,请确认！");
        }
    }

    /**
     * 同步审核人
     *
     * @param flowNo
     * @param yearMonthly
     */
    public void syncAuditPerson(String flowNo, String yearMonthly) {
        if (StrUtil.isEmpty(flowNo) && StrUtil.isEmpty(yearMonthly)) {
            log.error("同步审核人参数为空");
            return;
        }
        List<TFillingFlowEntity> tFillingFlowEntities = fillingFlowService.getTFillingFlowEntities(yearMonthly, null, null, flowNo);
        if (CollUtil.isEmpty(tFillingFlowEntities)) {
            log.error("无此年月度流程信息");
            return;
        }
        tFillingFlowEntities.forEach(tFillingFlowEntity -> {
            syncAudit(tFillingFlowEntity);
        });
    }

    private void syncAudit(TFillingFlowEntity tFillingFlowEntity) {
        FlowNodeEnum flowNodeEnum = getFlowNodeEnum(tFillingFlowEntity);
        List<String> persons = getPersonsByNodeCode(flowNodeEnum, tFillingFlowEntity);
        if (CollUtil.isEmpty(persons)) {
            log.error("无此节点审核人信息");
            return;
        }
        TFlowVsBusinessEntity one = tFlowVsBusinessService.lambdaQuery()
                .eq(TFlowVsBusinessEntity::getBusinessKey, tFillingFlowEntity.getFlowNo())
                .one();
        if (one == null) {
            log.error("无此流程信息");
            return;
        }
        ResponseResult candidate = workFlowRpcService.getIdentityLinks(one.getProcessInstanceId(), "candidate");
        if (candidate.getCode() != CommonResultCode.SUCCESS.code()) {
            log.error("无此流程信息");
            return;
        }
        JSONArray jsonArray = JSONUtil.parseArray(candidate.getData());
        List<String> groupIds = new ArrayList<>();
        for (int i = 0; i < jsonArray.size(); i++) {
            JSONObject jsonObject = jsonArray.getJSONObject(i);
            String groupId = jsonObject.getStr("groupId");
            if (StrUtil.isNotEmpty(groupId)) {
                groupIds.add(groupId);
            }
        }
        List<String> addPersons = persons.stream().filter(item -> !groupIds.contains(item)).collect(Collectors.toList());
        List<String> delPersons = groupIds.stream().filter(item -> !persons.contains(item)).collect(Collectors.toList());
        if (!CollUtil.isEmpty(addPersons)) {
            addIdentityLinks(addPersons, one.getProcessInstanceId());
        }
        if (!CollUtil.isEmpty(delPersons)) {
            delIdentityLinks(delPersons, one.getProcessInstanceId());
        }
    }

    private void delIdentityLinks(List<String> delPersons, String processInstanceId) {
        delPersons.forEach(item -> {
            IdentityLinkDTO identityLink = new IdentityLinkDTO();
            identityLink.setGroupId(item);
            identityLink.setType("candidate");
            workFlowRpcService.deleteIdentityLink(processInstanceId, identityLink);
        });
    }

    private void addIdentityLinks(List<String> addPersons, String processInstanceId) {
        addPersons.forEach(item -> {
            IdentityLinkDTO identityLink = new IdentityLinkDTO();
            identityLink.setGroupId(item);
            identityLink.setType("candidate");
            workFlowRpcService.addIdentityLinks(processInstanceId, identityLink);
        });

    }


    /**
     * 获取流程节点枚举
     *
     * @param tFillingFlowEntity
     * @return
     */
    public FlowNodeEnum getFlowNodeEnum(TFillingFlowEntity tFillingFlowEntity) {
        Integer flowStatus = tFillingFlowEntity.getFlowStatus();
        Integer flowType = tFillingFlowEntity.getFlowType();
        if (flowType.equals(1)) {
            return getSingleFlowNodeEnum(flowStatus);
        } else if (flowType.equals(2)) {
            return getSumFlowNodeEnum(flowStatus);
        } else {
            throw new BussinessException("流程类型异常！");
        }
    }

    /**
     * 获取汇总流程节点枚举
     *
     * @param flowStatus
     * @return
     */
    private FlowNodeEnum getSumFlowNodeEnum(Integer flowStatus) {
        // 1-未提交，2-已提交，3-归档，4-已提交撤回,5-流程结束
        if (flowStatus.equals(1)) {
            return SumWorkFlowEnum.ACTIVITY_SUM_FILLING_01;
        } else if (flowStatus.equals(2)) {
            return SumWorkFlowEnum.ACTIVITY_SUM_FILLING_02;
        } else if (flowStatus.equals(3)) {
            return SumWorkFlowEnum.ACTIVITY_SUM_FILLING_03;
        } else if (flowStatus.equals(4)) {
            return SumWorkFlowEnum.ACTIVITY_SUM_FILLING_01;
        }
        return null;
    }

    /**
     * 获取单体流程节点枚举
     *
     * @param flowStatus
     * @return
     */
    private FlowNodeEnum getSingleFlowNodeEnum(Integer flowStatus) {
        // 1-未提交，2-已提交，3-归档，4-已提交撤回,5-流程结束
        if (flowStatus.equals(1)) {
            return SingleWorkFlowEnum.ACTIVITY_SINGLE_FILLING_01;
        } else if (flowStatus.equals(2)) {
            return SingleWorkFlowEnum.ACTIVITY_SINGLE_FILLING_02;
        } else if (flowStatus.equals(4)) {
            return SingleWorkFlowEnum.ACTIVITY_SINGLE_FILLING_01;
        }
        return null;
    }

    /**
     * 根据节点获取审核人
     *
     * @param flowNodeEnum
     * @param tFillingFlowEntity
     * @return
     */
    private List<String> getPersonsByNodeCode(FlowNodeEnum flowNodeEnum, TFillingFlowEntity tFillingFlowEntity) {
        List<String> personList = new ArrayList<>();
        if (tFillingFlowEntity.getFlowType().equals(1)) {
            personList = getSingleAuditPerson(flowNodeEnum, tFillingFlowEntity);
        } else {
            personList = getSumAuditPerson(flowNodeEnum, tFillingFlowEntity);
        }
        return personList;
    }

    /**
     * 汇总流程获取审核人
     *
     * @param flowNodeEnum
     * @param tFillingFlowEntity
     * @return
     */
    public List<String> getSumAuditPerson(FlowNodeEnum flowNodeEnum, TFillingFlowEntity tFillingFlowEntity) {
        List<String> personList = new ArrayList<>();

        List<FillFlowAuditPersonDTO> auditPerson = fillingFlowService.getAuditPersons(tFillingFlowEntity.getParentCode(),
                tFillingFlowEntity.getCompanyCode(), tFillingFlowEntity.getSumOrgType(), 2);


        if (flowNodeEnum.equals(SumWorkFlowEnum.ACTIVITY_SUM_FILLING_01) || flowNodeEnum.equals(SumWorkFlowEnum.ACTIVITY_SUM_FILLING_02)) {
            personList = auditPerson.stream().filter(audit -> audit.getNodeCode().equals(1)).map(FillFlowAuditPersonDTO::getAuditCode).collect(Collectors.toList());
        } else if (flowNodeEnum.equals(SumWorkFlowEnum.ACTIVITY_SUM_FILLING_03) || flowNodeEnum.equals(SumWorkFlowEnum.ACTIVITY_SUM_FILLING_04)) {
            personList = auditPerson.stream().filter(audit -> audit.getNodeCode().equals(2)).map(FillFlowAuditPersonDTO::getAuditCode).collect(Collectors.toList());
        }
        return personList;
    }

    /**
     * 单独流程获取审核人
     *
     * @param flowNodeEnum
     * @param tFillingFlowEntity
     * @return
     */
    public List<String> getSingleAuditPerson(FlowNodeEnum flowNodeEnum, TFillingFlowEntity tFillingFlowEntity) {
        List<FillFlowAuditPersonDTO> auditPerson = fillingFlowService.getAuditPersons(tFillingFlowEntity.getParentCode(),
                tFillingFlowEntity.getCompanyCode(), tFillingFlowEntity.getSumOrgType(), 1);
        List<String> personList = auditPerson.stream().filter(audit -> audit.getNodeCode().equals(1)).map(FillFlowAuditPersonDTO::getAuditCode).collect(Collectors.toList());
        return personList;
    }

    // ==================== 内部类 ====================

    /**
     * 流程处理上下文类
     * 封装流程处理过程中需要的各种数据
     */
    private static class FlowProcessContext {
        /** 流程公司数据列表 */
        private List<FlowCompanyDTO> flowCompanyDTOS;

        /** 单体流程映射 */
        private Map<String, List<FlowCompanyDTO>> singleFlowMap = new HashMap<>();

        /** 汇总流程列表 */
        private List<TFillingFlowEntity> sumFlows = new ArrayList<>();

        public List<FlowCompanyDTO> getFlowCompanyDTOS() {
            return flowCompanyDTOS;
        }

        public void setFlowCompanyDTOS(List<FlowCompanyDTO> flowCompanyDTOS) {
            this.flowCompanyDTOS = flowCompanyDTOS;
        }

        public Map<String, List<FlowCompanyDTO>> getSingleFlowMap() {
            return singleFlowMap;
        }

        public void setSingleFlowMap(Map<String, List<FlowCompanyDTO>> singleFlowMap) {
            this.singleFlowMap = singleFlowMap;
        }

        public List<TFillingFlowEntity> getSumFlows() {
            return sumFlows;
        }

        public void setSumFlows(List<TFillingFlowEntity> sumFlows) {
            this.sumFlows = sumFlows;
        }
    }
}

