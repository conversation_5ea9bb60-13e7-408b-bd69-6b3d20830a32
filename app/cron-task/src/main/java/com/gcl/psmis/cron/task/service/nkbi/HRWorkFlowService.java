package com.gcl.psmis.cron.task.service.nkbi;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.date.DatePattern;
import cn.hutool.core.date.DateTime;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONArray;
import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import com.baomidou.dynamic.datasource.annotation.DSTransactional;
import com.gcl.psmis.framework.common.ResponseResult;
import com.gcl.psmis.framework.common.constant.CommonResultCode;
import com.gcl.psmis.framework.common.dto.nkbi.FillFlowAuditPersonDTO;
import com.gcl.psmis.framework.common.dto.nkbi.FillFlowDTO;
import com.gcl.psmis.framework.common.dto.nkbi.FlowCompanyDTO;
import com.gcl.psmis.framework.common.exception.BussinessException;
import com.gcl.psmis.framework.gencode.enums.GenRuleCode;
import com.gcl.psmis.framework.gencode.util.GenCodeUtil;
import com.gcl.psmis.framework.mbg.entity.TFillingFlowDetailEntity;
import com.gcl.psmis.framework.mbg.entity.TFillingFlowEntity;
import com.gcl.psmis.framework.mbg.entity.TFlowVsBusinessEntity;
import com.gcl.psmis.framework.mbg.service.TFlowVsBusinessService;
import com.gcl.psmis.workflow.api.WorkFlowRpcService;
import com.gcl.psmis.workflow.constant.ProcessKeyEnum;
import com.gcl.psmis.workflow.constant.task.FlowNodeEnum;
import com.gcl.psmis.workflow.constant.task.SingleWorkFlowEnum;
import com.gcl.psmis.workflow.constant.task.SumWorkFlowEnum;
import com.gcl.psmis.workflow.domain.dto.FlowBizDTO;
import com.gcl.psmis.workflow.domain.dto.IdentityLinkDTO;
import com.gcl.psmis.workflow.domain.req.ProcessCompleteReq;
import com.gcl.psmis.workflow.domain.req.StartProcessInstanceReq;
import com.xxl.job.core.context.XxlJobHelper;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.stream.Collectors;


/**
 * <AUTHOR>
 * @date 2022/05/09
 * @description 人资单体填报流程发起
 */
@Service
@Slf4j
@RequiredArgsConstructor
public class HRWorkFlowService {
    private final WorkFlowRpcService workFlowRpcService;
    @Autowired
    private FillingFlowService fillingFlowService;
    @Autowired
    private GenCodeUtil genCodeUtil;

    @Autowired
    private TFlowVsBusinessService tFlowVsBusinessService;

    /**
     * 初始化计划填报工作流
     */
    public void initSinglePlanFillWorkflow(String yearMonthly) {
        if (StrUtil.isBlank(yearMonthly)) {
            yearMonthly = DateUtil.format(DateUtil.nextMonth(), DatePattern.NORM_MONTH_PATTERN);
        }
        validateAndInitProcess("单体上报计划", 1, ProcessKeyEnum.SINGLE_FILLING_FLOW, 1, yearMonthly);
    }

    /**
     * 初始化实际填报工作流
     */
    public void initSingleActualFillWorkflow(String yearMonthly) {
        if (StrUtil.isBlank(yearMonthly)) {
            yearMonthly = DateUtil.format(DateUtil.lastMonth(), DatePattern.NORM_MONTH_PATTERN);
        }
        validateAndInitProcess("单体上报实际", 1, ProcessKeyEnum.SINGLE_FILLING_FLOW, 2, yearMonthly);
    }

    /**
     * 初始化汇总填报工作流
     */
    public void initSumPlanFillWorkflow(String yearMonthly) {
        if (StrUtil.isBlank(yearMonthly)) {
            yearMonthly = DateUtil.format(DateUtil.nextMonth(), DatePattern.NORM_MONTH_PATTERN);
        }
        validateAndInitProcess("汇总上报计划", 2, ProcessKeyEnum.SUM_FILLING_FLOW, 1, yearMonthly);
    }

    /**
     * 初始化汇总实际填报工作流
     */
    public void initSumActualFillWorkflow(String yearMonthly) {
        if (StrUtil.isBlank(yearMonthly)) {
            yearMonthly = DateUtil.format(DateUtil.lastMonth(), DatePattern.NORM_MONTH_PATTERN);
        }
        // 验证并初始化流程
        validateAndInitProcess("汇总上报实际", 2, ProcessKeyEnum.SUM_FILLING_FLOW, 2, yearMonthly);
    }

    /**
     * 验证并初始化流程
     * 此方法用于在启动流程之前验证必要的条件，并进行初始化设置
     *
     * @param flowName       流程名称，用于日志记录和识别特定流程
     * @param flowType       流程类型，决定使用哪种方式来处理流程数据
     * @param processKeyEnum 流程关键字枚举，用于识别和配置特定的流程
     * @param indexType      索引类型，与流程类型一起用于确定如何处理数据
     * @param yearMonthly    年月标识，用于验证数据同步状态和流程相关性
     */
    private void validateAndInitProcess(String flowName, int flowType, ProcessKeyEnum processKeyEnum, int indexType, String yearMonthly) {
        // 检查中转表数据是否同步完成
        if (!fillingFlowService.verifySync(yearMonthly)) {
            logErrorAndThrowException("中转表数据未同步完成，请稍后重试");
        }

        List<FlowCompanyDTO> flowCompanyDTOS = null;
        Map<String, List<FlowCompanyDTO>> singleFlowMap = new HashMap<>();
        // 获取单公司填报数据
        List<TFillingFlowEntity> sumFlows = new ArrayList<>();

        // 根据流程类型获取待发起的流程公司数据
        if (flowType == 1) {
            flowCompanyDTOS = fillingFlowService.getFlowCompanyDTOS(1);
            // 获取当月主营公司的数据
            sumFlows = fillingFlowService.getSumFlows(yearMonthly);

        }
        if (flowType == 2) {
            List<FlowCompanyDTO> all = fillingFlowService.getFlowCompanyDTOS(null);
            // 分离并处理特定流程数据
            List<FlowCompanyDTO> singleFlows = all.stream().filter(item -> item.getFlowId() == 1).collect(Collectors.toList());
            flowCompanyDTOS = all.stream().filter(item -> item.getFlowId() == 2).collect(Collectors.toList());
            if (CollUtil.isNotEmpty(singleFlows)) {
                singleFlowMap = singleFlows.stream().collect(Collectors.groupingBy(FlowCompanyDTO::getSumCompanyCode));
            }
        }

        if (CollUtil.isEmpty(flowCompanyDTOS)) {
            logErrorAndThrowException("未查询到流程公司数据，请检查流程配置");
        }

        // 记录流程初始化开始
        logInitProcessStart(flowName);
        // 启动流程
        startFlows(flowCompanyDTOS, flowType, processKeyEnum, indexType, yearMonthly, singleFlowMap, flowName, sumFlows);
        // 记录流程完成
        logProcessCompletion(flowName);
    }

    /**
     * 发起流程
     *
     * @param companyDTO           公司数据传输对象，包含填报公司和汇总公司的代码
     * @param processKeyEnum       流程关键字枚举，用于标识不同的流程
     * @param indexType            指标类型
     * @param yearMonthly          年月字符串，用于指定流程的时间范围
     * @param singleFlowMap
     * @param flowName
     * @param tFillingFlowEntities
     * @param sumFlows
     */
    @DSTransactional
    public void startFlow(FlowCompanyDTO companyDTO, int flowType, ProcessKeyEnum processKeyEnum, int indexType, String yearMonthly,
                          Map<String, List<FlowCompanyDTO>> singleFlowMap, String flowName, List<TFillingFlowEntity> tFillingFlowEntities,
                          List<TFillingFlowEntity> sumFlows) {
        try {
            // 校验当月流程是否已经发起
            if (checkDuplicateFlow(tFillingFlowEntities, companyDTO, flowName)) {
                XxlJobHelper.log(String.format("当月流程已经发起，%s创建" + flowName + "失败!", companyDTO.getFillCompanyCode()));
                return;
            }
            // 如果是汇总流程， 校验单体填报是否完成 没有完成直接跳过
//            if (flowType == 2 && !fillingFlowService.checkSingleFlowStatus(companyDTO, singleFlowMap, indexType, yearMonthly)) {
//                XxlJobHelper.log(String.format("体填报未完成，%s创建" + flowName + "失败!", companyDTO.getFillCompanyCode()));
//                return;
//            }
            // 1、获取填报公司code
            String sumCompanyCode = companyDTO.getSumCompanyCode();
            if (flowType == 2) {
                sumCompanyCode = companyDTO.getFillCompanyCode();
            }
            // 3 、根据流程公司数据  获取所有填报公司详细数据
            List<FillFlowDTO> fillFlowDTOS = fillingFlowService.getFillFlowByCompanyCode(companyDTO.getFillCompanyCode(),
                    sumCompanyCode, yearMonthly, flowType, companyDTO.getSumOrgType(), companyDTO.getSpecFlag());

            // 如果没有数据，则跳过该流程
            if (CollUtil.isEmpty(fillFlowDTOS)) {
                XxlJobHelper.log("未查询到填报公司" + companyDTO.getFillCompanyCode() + "详细数据，请检查流程配置");
                return;
                //                if (!checkDataIntegrity(companyDTO, flowType, yearMonthly)) {
//                    return;
//                }
            }

            String persons = getPersons(companyDTO);
            // 汇总状态
            Integer sumStatus = flowType == 1 ? 1 : null;
            // 流程实体主数据
            String flowNo = getFlowNo(processKeyEnum.name());
            // 休眠200毫秒，避免数据同步问题
            Thread.sleep(100);
            TFillingFlowEntity build = fillingFlowService.getTFillingFlowEntity(companyDTO, flowType, processKeyEnum, indexType, yearMonthly, sumStatus, flowNo);
            // 发起流程
            startWorkFlow(processKeyEnum, build, persons, flowName);
            //保存流程业务数据
            fillingFlowService.saveFlowData(build, fillFlowDTOS, companyDTO.getFillCompanyCode(), sumFlows);
            // log
            XxlJobHelper.log(String.format("%s创建" + flowName + "成功!", build.getFlowNo()));
        } catch (InterruptedException e) {
            throw new RuntimeException(e);
        }
    }

    private Boolean checkDuplicateFlow(List<TFillingFlowEntity> tFillingFlowEntities, FlowCompanyDTO companyDTO, String flowName) {
        if (CollUtil.isEmpty(tFillingFlowEntities)) {
            return false;
        }

        boolean match = tFillingFlowEntities.stream().anyMatch(item -> {
            if (item.getParentCode().equals(companyDTO.getSumCompanyCode())
                    && item.getCompanyCode().equals(companyDTO.getFillCompanyCode())
                    && Objects.deepEquals(companyDTO.getSumOrgType(), item.getSumOrgType())) {
                return true;
            }
            return false;
        });

        return match;
    }

    /**
     * 获取
     *
     * @param companyDTO
     * @return
     */
    private String getPersons(FlowCompanyDTO companyDTO) {
        String persons = "";
        List<FillFlowAuditPersonDTO> auditPerson = companyDTO.getAuditPerson();
        if (CollUtil.isEmpty(auditPerson)) {
            return persons;
        }
        List<String> auditCodes = auditPerson.stream().filter(audit -> audit.getNodeCode().equals(1))
                .map(FillFlowAuditPersonDTO::getAuditCode).collect(Collectors.toList());
        if (CollUtil.isNotEmpty(auditCodes)) {
            persons = StrUtil.join(",", auditCodes);
        }
        return persons;
    }

    /**
     * 校验数据完整性
     *
     * @param companyDTO
     * @param flowType
     * @param yearMonthly
     * @return
     */
    private boolean checkDataIntegrity(FlowCompanyDTO companyDTO, int flowType, String yearMonthly) {
        if (flowType == 1) {
            XxlJobHelper.log("未查询到填报公司" + companyDTO.getFillCompanyCode() + "详细数据，请检查流程配置");
            return false;
        } else {
            // 校验单体填报是否有数据，如果没有，跳过
            int count = fillingFlowService.getCountByYearMonthlyAndParentCodeAndSumOrgType(yearMonthly,
                    companyDTO.getFillCompanyCode(), companyDTO.getSumOrgType());
            if (count == 0) {
                XxlJobHelper.log("未查询到填报公司" + companyDTO.getFillCompanyCode() + "详细数据，请检查流程配置");
                return false;
            }
        }
        return true;
    }

    /**
     * 获取流程编号
     *
     * @param name
     * @return
     */
    private String getFlowNo(String name) {
        String ruleCode = genCodeUtil.getSysCodeByCodeRule(GenRuleCode.FILLING_FLOW);
        return StrUtil.concat(true, name, "_", ruleCode);
    }

    /**
     * 记录错误日志并抛出异常
     */
    private void logErrorAndThrowException(String message) {
        XxlJobHelper.log(message);
        throw new BussinessException(message);
    }

    /**
     * 记录初始化流程开始日志
     */
    private void logInitProcessStart(String flowName) {
        XxlJobHelper.log("中转表数据同步完成，开始发起" + flowName + "流程");
    }

    /**
     * 记录流程完成日志
     */
    private void logProcessCompletion(String flowName) {
        XxlJobHelper.log(flowName + "流程发起完成");
    }

    /**
     * 启动多个流程
     */
    private void startFlows(List<FlowCompanyDTO> flowCompanyDTOS, int flowType, ProcessKeyEnum processKeyEnum, int indexType
            , String yearMonthly, Map<String, List<FlowCompanyDTO>> singleFlowMap, String flowName, List<TFillingFlowEntity> sumFlows) {
        // 获取所有当前月份的已经发起的流程数据
        List<TFillingFlowEntity> tFillingFlowEntities = fillingFlowService.getTFillingFlowEntities(yearMonthly, flowType, indexType, null);
        String lastYearMonthly = DateUtil.format(DateUtil.offsetMonth(DateUtil.parse(yearMonthly + "-01"), -1), DatePattern.NORM_MONTH_PATTERN);
        List<TFillingFlowDetailEntity> lastMonthFlowDetailEntities = fillingFlowService.getFlowDetailEntities(lastYearMonthly, flowType, indexType);

        // 遍历每个流程公司数据，并发起流程
        for (FlowCompanyDTO companyDTO : flowCompanyDTOS) {
            startFlow(companyDTO, flowType, processKeyEnum, indexType, yearMonthly, singleFlowMap, flowName, tFillingFlowEntities, sumFlows);
        }
    }

    /**
     * 发起流程
     *
     * @param processKeyEnum
     * @param build
     * @param persons
     * @param flowName
     */
    public void startWorkFlow(ProcessKeyEnum processKeyEnum, TFillingFlowEntity build, String persons, String flowName) {
        StartProcessInstanceReq req = new StartProcessInstanceReq();
        // 发起流程
        req.setAuthenticatedAccount("system");
        req.setProcessKey(processKeyEnum);
        req.setBusinessKey(build.getFlowNo());
        Map<String, Object> var = new HashMap<>();
        var.put("person", persons);
        var.put("operatorAccount", "System");
        var.put("operatorName", "系统");
        req.setVariables(var);
        ResponseResult defectResult = workFlowRpcService.start(req);
        if (defectResult.getCode() != CommonResultCode.SUCCESS.code()) {
            throw new BussinessException("开启" + flowName + "流程失败!");
        }
    }

    /**
     * 完成流程
     *
     * @param yearMonthly
     */
    public void completeFillWorkflow(String yearMonthly) {
        if (StrUtil.isBlank(yearMonthly)) {
            // 默认查询当前月份前3个月数据
            yearMonthly = DateUtil.format(DateUtil.offsetMonth(new DateTime(), -3), DatePattern.NORM_MONTH_PATTERN);
        }
        List<TFillingFlowEntity> singleFlows = fillingFlowService.getFlows(yearMonthly, 2);
        List<TFillingFlowEntity> sumFlows = fillingFlowService.getFlows(yearMonthly, 3);
        List<TFillingFlowEntity> tFillingFlowEntities = new ArrayList<>();
        if (CollUtil.isNotEmpty(singleFlows)) {
            tFillingFlowEntities.addAll(singleFlows);
        }
        if (CollUtil.isNotEmpty(sumFlows)) {
            tFillingFlowEntities.addAll(sumFlows);
        }
        for (TFillingFlowEntity tFillingFlowEntity : tFillingFlowEntities) {
            try {
                auditWorkflow(tFillingFlowEntity);
            } catch (Exception e) {
                XxlJobHelper.log(e.getMessage());
            }
        }
    }


    public void auditWorkflow(TFillingFlowEntity tFillingFlowEntity) {
        if (tFillingFlowEntity == null) {
            throw new BussinessException("无表单信息！");
        }
        FlowNodeEnum flowNodeEnum = SingleWorkFlowEnum.ACTIVITY_SINGLE_FILLING_02;
        // 单体流程
        if (tFillingFlowEntity.getFlowType().equals(2)) {
            flowNodeEnum = SumWorkFlowEnum.ACTIVITY_SUM_FILLING_03;
        }
        // 获取下个节点code
        // 待提交->提交 (撤回)——>特殊节点--> 流程结束。
        TFlowVsBusinessEntity one = tFlowVsBusinessService.lambdaQuery().eq(TFlowVsBusinessEntity::getBusinessKey, tFillingFlowEntity.getFlowNo()).one();
        if (one == null) {
            throw new BussinessException("无当前工单流程信息！");
        }
        String lastAuditCode = tFillingFlowEntity.getLastAuditCode();
        // 校验流程状态 批量暂时不校验
//        checkFlowStatus(one, lastAuditCode, flowNodeEnum);
        ProcessCompleteReq completeReq = new ProcessCompleteReq();
        completeReq.setProcessInstanceId(one.getProcessInstanceId());
        HashMap<String, Object> map = new HashMap<>();

        // 获取当前节点code审核人
        String person = lastAuditCode;
        completeReq.setFlowNodeEnum(flowNodeEnum);
        map.put("person", person);
        map.put("choose", 1);
        map.put("operatorAccount", lastAuditCode);
        map.put("operatorName", lastAuditCode);
        completeReq.setVariables(map);
        completeReq.setOperatorAccount(lastAuditCode);
        completeReq.setOperatorName(lastAuditCode);
        ResponseResult res = workFlowRpcService.processComplete(completeReq);
        if (res.getCode() != CommonResultCode.SUCCESS.code()) {
            throw new BussinessException(flowNodeEnum.getDesc() +
                    flowNodeEnum.getDesc() + "提交流程失败!");
        }
        fillingFlowService.updateFlowStatus(tFillingFlowEntity);
    }

    /**
     * 校验流程状态
     *
     * @param one
     * @param empNo
     * @param flowNodeEnum
     */
    private void checkFlowStatus(TFlowVsBusinessEntity one, String empNo, FlowNodeEnum flowNodeEnum) {
        FlowBizDTO flowBizDTO = new FlowBizDTO();
        flowBizDTO.setProcInsId(one.getProcessInstanceId());
        flowBizDTO.setUserNo(empNo);
        ResponseResult responseResult = workFlowRpcService.queryTaskByUser(flowBizDTO);
        if (responseResult.getCode() != CommonResultCode.SUCCESS.code()) {
            throw new BussinessException("当前人员无此流程节点代办任务,请确认！");
        }
        JSONObject jsonObj = JSONUtil.parseObj(responseResult.getData());
        String code = jsonObj.getStr("taskDefinitionKey");
        if (!code.equals(flowNodeEnum.getCode())) {
            throw new BussinessException("当前人员的待办节点不对,请确认！");
        }
    }

    /**
     * 同步审核人
     *
     * @param flowNo
     * @param yearMonthly
     */
    public void syncAuditPerson(String flowNo, String yearMonthly) {
        if (StrUtil.isEmpty(flowNo) && StrUtil.isEmpty(yearMonthly)) {
            log.error("同步审核人参数为空");
            return;
        }
        List<TFillingFlowEntity> tFillingFlowEntities = fillingFlowService.getTFillingFlowEntities(yearMonthly, null, null, flowNo);
        if (CollUtil.isEmpty(tFillingFlowEntities)) {
            log.error("无此年月度流程信息");
            return;
        }
        tFillingFlowEntities.forEach(tFillingFlowEntity -> {
            syncAudit(tFillingFlowEntity);
        });
    }

    private void syncAudit(TFillingFlowEntity tFillingFlowEntity) {
        FlowNodeEnum flowNodeEnum = getFlowNodeEnum(tFillingFlowEntity);
        List<String> persons = getPersonsByNodeCode(flowNodeEnum, tFillingFlowEntity);
        if (CollUtil.isEmpty(persons)) {
            log.error("无此节点审核人信息");
            return;
        }
        TFlowVsBusinessEntity one = tFlowVsBusinessService.lambdaQuery()
                .eq(TFlowVsBusinessEntity::getBusinessKey, tFillingFlowEntity.getFlowNo())
                .one();
        if (one == null) {
            log.error("无此流程信息");
            return;
        }
        ResponseResult candidate = workFlowRpcService.getIdentityLinks(one.getProcessInstanceId(), "candidate");
        if (candidate.getCode() != CommonResultCode.SUCCESS.code()) {
            log.error("无此流程信息");
            return;
        }
        JSONArray jsonArray = JSONUtil.parseArray(candidate.getData());
        List<String> groupIds = new ArrayList<>();
        for (int i = 0; i < jsonArray.size(); i++) {
            JSONObject jsonObject = jsonArray.getJSONObject(i);
            String groupId = jsonObject.getStr("groupId");
            if (StrUtil.isNotEmpty(groupId)) {
                groupIds.add(groupId);
            }
        }
        List<String> addPersons = persons.stream().filter(item -> !groupIds.contains(item)).collect(Collectors.toList());
        List<String> delPersons = groupIds.stream().filter(item -> !persons.contains(item)).collect(Collectors.toList());
        if (!CollUtil.isEmpty(addPersons)) {
            addIdentityLinks(addPersons, one.getProcessInstanceId());
        }
        if (!CollUtil.isEmpty(delPersons)) {
            delIdentityLinks(delPersons, one.getProcessInstanceId());
        }
    }

    private void delIdentityLinks(List<String> delPersons, String processInstanceId) {
        delPersons.forEach(item -> {
            IdentityLinkDTO identityLink = new IdentityLinkDTO();
            identityLink.setGroupId(item);
            identityLink.setType("candidate");
            workFlowRpcService.deleteIdentityLink(processInstanceId, identityLink);
        });
    }

    private void addIdentityLinks(List<String> addPersons, String processInstanceId) {
        addPersons.forEach(item -> {
            IdentityLinkDTO identityLink = new IdentityLinkDTO();
            identityLink.setGroupId(item);
            identityLink.setType("candidate");
            workFlowRpcService.addIdentityLinks(processInstanceId, identityLink);
        });

    }


    /**
     * 获取流程节点枚举
     *
     * @param tFillingFlowEntity
     * @return
     */
    public FlowNodeEnum getFlowNodeEnum(TFillingFlowEntity tFillingFlowEntity) {
        Integer flowStatus = tFillingFlowEntity.getFlowStatus();
        Integer flowType = tFillingFlowEntity.getFlowType();
        if (flowType.equals(1)) {
            return getSingleFlowNodeEnum(flowStatus);
        } else if (flowType.equals(2)) {
            return getSumFlowNodeEnum(flowStatus);
        } else {
            throw new BussinessException("流程类型异常！");
        }
    }

    /**
     * 获取汇总流程节点枚举
     *
     * @param flowStatus
     * @return
     */
    private FlowNodeEnum getSumFlowNodeEnum(Integer flowStatus) {
        // 1-未提交，2-已提交，3-归档，4-已提交撤回,5-流程结束
        if (flowStatus.equals(1)) {
            return SumWorkFlowEnum.ACTIVITY_SUM_FILLING_01;
        } else if (flowStatus.equals(2)) {
            return SumWorkFlowEnum.ACTIVITY_SUM_FILLING_02;
        } else if (flowStatus.equals(3)) {
            return SumWorkFlowEnum.ACTIVITY_SUM_FILLING_03;
        } else if (flowStatus.equals(4)) {
            return SumWorkFlowEnum.ACTIVITY_SUM_FILLING_01;
        }
        return null;
    }

    /**
     * 获取单体流程节点枚举
     *
     * @param flowStatus
     * @return
     */
    private FlowNodeEnum getSingleFlowNodeEnum(Integer flowStatus) {
        // 1-未提交，2-已提交，3-归档，4-已提交撤回,5-流程结束
        if (flowStatus.equals(1)) {
            return SingleWorkFlowEnum.ACTIVITY_SINGLE_FILLING_01;
        } else if (flowStatus.equals(2)) {
            return SingleWorkFlowEnum.ACTIVITY_SINGLE_FILLING_02;
        } else if (flowStatus.equals(4)) {
            return SingleWorkFlowEnum.ACTIVITY_SINGLE_FILLING_01;
        }
        return null;
    }

    /**
     * 根据节点获取审核人
     *
     * @param flowNodeEnum
     * @param tFillingFlowEntity
     * @return
     */
    private List<String> getPersonsByNodeCode(FlowNodeEnum flowNodeEnum, TFillingFlowEntity tFillingFlowEntity) {
        List<String> personList = new ArrayList<>();
        if (tFillingFlowEntity.getFlowType().equals(1)) {
            personList = getSingleAuditPerson(flowNodeEnum, tFillingFlowEntity);
        } else {
            personList = getSumAuditPerson(flowNodeEnum, tFillingFlowEntity);
        }
        return personList;
    }

    /**
     * 汇总流程获取审核人
     *
     * @param flowNodeEnum
     * @param tFillingFlowEntity
     * @return
     */
    public List<String> getSumAuditPerson(FlowNodeEnum flowNodeEnum, TFillingFlowEntity tFillingFlowEntity) {
        List<String> personList = new ArrayList<>();

        List<FillFlowAuditPersonDTO> auditPerson = fillingFlowService.getAuditPersons(tFillingFlowEntity.getParentCode(),
                tFillingFlowEntity.getCompanyCode(), tFillingFlowEntity.getSumOrgType(), 2);


        if (flowNodeEnum.equals(SumWorkFlowEnum.ACTIVITY_SUM_FILLING_01) || flowNodeEnum.equals(SumWorkFlowEnum.ACTIVITY_SUM_FILLING_02)) {
            personList = auditPerson.stream().filter(audit -> audit.getNodeCode().equals(1)).map(FillFlowAuditPersonDTO::getAuditCode).collect(Collectors.toList());
        } else if (flowNodeEnum.equals(SumWorkFlowEnum.ACTIVITY_SUM_FILLING_03) || flowNodeEnum.equals(SumWorkFlowEnum.ACTIVITY_SUM_FILLING_04)) {
            personList = auditPerson.stream().filter(audit -> audit.getNodeCode().equals(2)).map(FillFlowAuditPersonDTO::getAuditCode).collect(Collectors.toList());
        }
        return personList;
    }

    /**
     * 单独流程获取审核人
     *
     * @param flowNodeEnum
     * @param tFillingFlowEntity
     * @return
     */
    public List<String> getSingleAuditPerson(FlowNodeEnum flowNodeEnum, TFillingFlowEntity tFillingFlowEntity) {
        List<FillFlowAuditPersonDTO> auditPerson = fillingFlowService.getAuditPersons(tFillingFlowEntity.getParentCode(),
                tFillingFlowEntity.getCompanyCode(), tFillingFlowEntity.getSumOrgType(), 1);
        List<String> personList = auditPerson.stream().filter(audit -> audit.getNodeCode().equals(1)).map(FillFlowAuditPersonDTO::getAuditCode).collect(Collectors.toList());
        return personList;
    }

}

