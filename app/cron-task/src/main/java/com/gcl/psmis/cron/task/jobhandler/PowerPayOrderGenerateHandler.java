package com.gcl.psmis.cron.task.jobhandler;

import cn.hutool.core.exceptions.ExceptionUtil;
import com.gcl.psmis.cron.task.service.payorder.PowerPayOrderGenerateService;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.context.XxlJobHelper;
import com.xxl.job.core.handler.annotation.XxlJob;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * @Author: sunjiaxing
 * @CreateTime: 2024-07-04  15:05
 * @Description: 账单生成
 */
@Slf4j
@Service
public class PowerPayOrderGenerateHandler {

    @Autowired
    PowerPayOrderGenerateService powerPayOrderGenerateService;

    @XxlJob("powerPayOrderGenerate")
    public ReturnT<String> powerPayOrderGenerate() throws Exception {
        XxlJobHelper.log("powerPayOrderGenerate start...");
        try {
            String first = XxlJobHelper.getJobParam();
            powerPayOrderGenerateService.generatePowerPayOrder(first);
        } catch (Exception e) {
            e.printStackTrace();
            XxlJobHelper.handleFail("powerPayOrderGenerate fail..." + ExceptionUtil.stacktraceToString(e));
            return ReturnT.FAIL;
        }

        XxlJobHelper.log("powerPayOrderGenerate end...");
        return ReturnT.SUCCESS;
    }

    @XxlJob("powerPayOrderGenerateKaiFa")
    public ReturnT<String> powerPayOrderGenerateKaiFa() throws Exception {
        XxlJobHelper.log("powerPayOrderGenerate start...");
        try {
            powerPayOrderGenerateService.powerPayOrderGenerateKaiFa();
        } catch (Exception e) {
            e.printStackTrace();
            XxlJobHelper.handleFail("powerPayOrderGenerateKaiFa fail..." + ExceptionUtil.stacktraceToString(e));
            return ReturnT.FAIL;
        }

        XxlJobHelper.log("powerPayOrderGenerateKaiFa end...");
        return ReturnT.SUCCESS;
    }


    @XxlJob("powerPayOrderGenerateSerialYear")
    public ReturnT<String> powerPayOrderGenerateSerialYear() throws Exception {
        XxlJobHelper.log("powerPayOrderGenerateSerialYear start...");
        try {
            powerPayOrderGenerateService.powerPayOrderGenerateSerialYear();
        } catch (Exception e) {
            e.printStackTrace();
            XxlJobHelper.handleFail("powerPayOrderGenerateSerialYear fail..." + ExceptionUtil.stacktraceToString(e));
            return ReturnT.FAIL;
        }

        XxlJobHelper.log("powerPayOrderGenerateSerialYear end...");
        return ReturnT.SUCCESS;
    }


    @XxlJob("powerPayOrderGenerateByStationNos")
    public ReturnT<String> powerPayOrderGenerateByStationNos() throws Exception {
        XxlJobHelper.log("powerPayOrderGenerateByStationNos start...");
        try {
            String stationNos = XxlJobHelper.getJobParam();
            powerPayOrderGenerateService.powerPayOrderGenerateByStationNos(stationNos);
        } catch (Exception e) {
            e.printStackTrace();
            XxlJobHelper.handleFail("powerPayOrderGenerateByStationNos fail..." + ExceptionUtil.stacktraceToString(e));
            return ReturnT.FAIL;
        }

        XxlJobHelper.log("powerPayOrderGenerateByStationNos end...");
        return ReturnT.SUCCESS;
    }

    @XxlJob("powerPayOrderGenerateByStationNosNoMerge")
    public ReturnT<String> powerPayOrderGenerateByStationNosNoMerge() throws Exception {
        XxlJobHelper.log("powerPayOrderGenerateByStationNosNoMerge start...");
        try {
            String stationNos = XxlJobHelper.getJobParam();
            powerPayOrderGenerateService.powerPayOrderGenerateByStationNosNoMerge(stationNos);
        } catch (Exception e) {
            e.printStackTrace();
            XxlJobHelper.handleFail("powerPayOrderGenerateByStationNosNoMerge fail..." + ExceptionUtil.stacktraceToString(e));
            return ReturnT.FAIL;
        }

        XxlJobHelper.log("powerPayOrderGenerateByStationNos end...");
        return ReturnT.SUCCESS;
    }

    @XxlJob("powerPayOrderGenerateByStationNosNoMergeForDaYouCun")
    public ReturnT<String> powerPayOrderGenerateByStationNosNoMergeForDaYouCun() throws Exception {
        XxlJobHelper.log("powerPayOrderGenerateByStationNosNoMergeForDaYouCun start...");
        try {
            String stationNos = XxlJobHelper.getJobParam();
            powerPayOrderGenerateService.powerPayOrderGenerateByStationNosNoMergeForDaYouCun(stationNos);
        } catch (Exception e) {
            e.printStackTrace();
            XxlJobHelper.handleFail("powerPayOrderGenerateByStationNosNoMergeForDaYouCun fail..." + ExceptionUtil.stacktraceToString(e));
            return ReturnT.FAIL;
        }

        XxlJobHelper.log("powerPayOrderGenerateByStationNosNoMergeForDaYouCun end...");
        return ReturnT.SUCCESS;
    }


    @XxlJob("reGenerateOrder")
    public ReturnT<String> reGenerateOrder() throws Exception {
        XxlJobHelper.log("reGenerateOrder start...");
        try {
            String date = XxlJobHelper.getJobParam();
            powerPayOrderGenerateService.reGenerateOrder(date);
        } catch (Exception e) {
            e.printStackTrace();
            XxlJobHelper.handleFail("reGenerateOrder fail..." + ExceptionUtil.stacktraceToString(e));
            return ReturnT.FAIL;
        }

        XxlJobHelper.log("reGenerateOrder end...");
        return ReturnT.SUCCESS;
    }

    @XxlJob("reGenerateGRBA")
    public ReturnT<String> reGenerateGRBA() throws Exception {
        XxlJobHelper.log("reGenerateGRBA start...");
        try {
            powerPayOrderGenerateService.reGenerateGRBA();
        } catch (Exception e) {
            e.printStackTrace();
            XxlJobHelper.handleFail("reGenerateGRBA fail..." + ExceptionUtil.stacktraceToString(e));
            return ReturnT.FAIL;
        }
        XxlJobHelper.log("reGenerateGRBA end...");
        return ReturnT.SUCCESS;
    }

    @XxlJob("generateQybaAhead")
    public ReturnT<String> generateQybaAhead() throws Exception {
        XxlJobHelper.log("generateQybaAhead start...");
        try {
            powerPayOrderGenerateService.generateQybaAhead();
        } catch (Exception e) {
            e.printStackTrace();
            XxlJobHelper.handleFail("generateQybaAhead fail..." + ExceptionUtil.stacktraceToString(e));
            return ReturnT.FAIL;
        }
        XxlJobHelper.log("generateQybaAhead end...");
        return ReturnT.SUCCESS;
    }

    @XxlJob("reGenerate")
    public ReturnT<String> reGenerate() throws Exception {
        XxlJobHelper.log("reGenerate start...");
        try {
            powerPayOrderGenerateService.reGenerate();
        } catch (Exception e) {
            e.printStackTrace();
            XxlJobHelper.handleFail("reGenerate fail..." + ExceptionUtil.stacktraceToString(e));
            return ReturnT.FAIL;
        }
        XxlJobHelper.log("reGenerate end...");
        return ReturnT.SUCCESS;
    }

    @XxlJob("updatePsId")
    public ReturnT<String> updatePsId() throws Exception {
        XxlJobHelper.log("reGenerate start...");
        try {
            powerPayOrderGenerateService.updatePsId();
        } catch (Exception e) {
            e.printStackTrace();
            XxlJobHelper.handleFail("reGenerate fail..." + ExceptionUtil.stacktraceToString(e));
            return ReturnT.FAIL;
        }
        XxlJobHelper.log("reGenerate end...");
        return ReturnT.SUCCESS;
    }

    @XxlJob("generateHuBeiPayOrder")
    public ReturnT<String> generateHuBeiPayOrder() throws Exception {
        XxlJobHelper.log("generateHuBeiPayOrder start...");
        try {
            powerPayOrderGenerateService.generateHuBeiPayOrder();
        } catch (Exception e) {
            e.printStackTrace();
            XxlJobHelper.handleFail("generateHuBeiPayOrder fail..." + ExceptionUtil.stacktraceToString(e));
            return ReturnT.FAIL;
        }
        XxlJobHelper.log("generateHuBeiPayOrder end...");
        return ReturnT.SUCCESS;
    }

    @XxlJob("generateThreeCompanies")
    public ReturnT<String> generateThreeCompanies() throws Exception {
        XxlJobHelper.log("generateThreeCompanies start...");
        try {
            powerPayOrderGenerateService.generateThreeCompanies();
        } catch (Exception e) {
            e.printStackTrace();
            XxlJobHelper.handleFail("generateThreeCompanies fail..." + ExceptionUtil.stacktraceToString(e));
            return ReturnT.FAIL;
        }
        XxlJobHelper.log("generateThreeCompanies end...");
        return ReturnT.SUCCESS;
    }


}
