package com.gcl.psmis.cron.task.dao;

import com.baomidou.dynamic.datasource.annotation.DS;
import com.gcl.psmis.framework.common.dto.IcbcPojo.IcbcDetailDTO;
import com.gcl.psmis.framework.common.dto.elebill.PsProjectDTO;
import com.gcl.psmis.framework.common.dto.operation.OperationProvinceDTO;
import com.gcl.psmis.framework.common.dto.operation.OperationPsDTO;
import com.gcl.psmis.framework.common.dto.operation.OperationProvinceDTO;
import com.gcl.psmis.framework.common.dto.operation.SpareDevOpsWhDTO;
import com.gcl.psmis.framework.common.dto.operation.XygCompanyDTO;
import com.gcl.psmis.framework.common.vo.elebill.PsSettlementInfoVO;
import com.gcl.psmis.framework.mbg.entity.TSpareDevOpsWhEntity;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * <AUTHOR>
 * @ClassName BalanceAbnormalDao
 * @description:
 * @date 2024年05月21日
 * @version: 1.0
 */
@Repository
public interface BalanceAbnormalDao {
    @DS("xyg")
    List<IcbcDetailDTO> getIcbcInfo(@Param("flag") String flag, @Param("dateTime") String dateTime);

    List<PsProjectDTO> getPsInfo(@Param("psIds") List<Long> psIds);

    PsSettlementInfoVO getSettlementInfo(@Param("psCode") String psCode);

    @DS("xyg")
    XygCompanyDTO sysXygCompany(@Param("zwCompanyCodes") String zwCompanyCodes);

    @DS("xyg")
    List<SpareDevOpsWhDTO> getWHByCompanyCode(@Param("companyCode") String companyCode);

    List<XygCompanyDTO> sysCompany();

    @DS("xyg")
    List<XygCompanyDTO> sysXygCompanyInfo(@Param("params") List<String> params);

    @DS("xyg")
    List<XygCompanyDTO> updateSysXygCompanyInfo(@Param("params") List<String> params);

    OperationPsDTO getOperationPsInfo(@Param("operationCode") String operationCode);

    List<OperationProvinceDTO> getOperationProvince();

    List<OperationProvinceDTO> getOperationPsDevProvince();

    List<OperationProvinceDTO> getPsTypeOperations();


}
