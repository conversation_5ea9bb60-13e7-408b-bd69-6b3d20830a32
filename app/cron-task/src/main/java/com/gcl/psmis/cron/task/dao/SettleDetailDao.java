package com.gcl.psmis.cron.task.dao;

import com.baomidou.dynamic.datasource.annotation.DS;
import com.gcl.psmis.framework.common.vo.DesignVo;
import com.gcl.psmis.framework.common.vo.OrdProvinceVo;
import com.gcl.psmis.framework.mbg.entity.OrdOrderEntity;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <AUTHOR> @Description
 * @Date 2024/1/16
 * @Param
 **/
@Mapper
@DS("xyg")
public interface SettleDetailDao {

    //查询电站设计方案
    List<DesignVo> getDicList(@Param("ordList") List<Long> ordList);

    //查询已户用和并网的电站（剔除资方的项目公司）
    List<OrdOrderEntity> list();

    List<OrdProvinceVo> getProvinceInfo(@Param("ordIdList") List<Long> ordIdList);

    List<DesignVo> getBomDetail(Long orderId);

    List<OrdOrderEntity> listGrid(@Param("startTime") String startTime, @Param("endTime") String endTime);
}
