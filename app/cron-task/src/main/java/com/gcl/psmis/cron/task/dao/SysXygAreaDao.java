package com.gcl.psmis.cron.task.dao;

import com.baomidou.dynamic.datasource.annotation.DS;
import com.gcl.psmis.framework.common.dto.XygAreaDTO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <AUTHOR>
 * @ClassName SysXygAreaDao
 * @description: TODO
 * @date 2024年06月19日
 * @version: 1.0
 */
@Mapper
public interface SysXygAreaDao {

    @DS("xyg")
    XygAreaDTO getAreaInfoByAreaId(@Param("areaId") Integer areaId);

    @DS("xyg")
    XygAreaDTO getAreaInfoByTownId(@Param("townId") Integer townId);

    @DS("xyg")
    List<XygAreaDTO> getAreaInfosByTownId();

}
