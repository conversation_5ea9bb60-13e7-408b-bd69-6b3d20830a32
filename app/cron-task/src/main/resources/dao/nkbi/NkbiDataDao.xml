<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.gcl.psmis.cron.task.dao.nkbi.NkbiDataDao">
    <update id="updateSumFlowId">
        UPDATE t_filling_flow_detail ffd
        join t_filling_flow ff on ffd.single_flow_id = ff.id
        SET ffd.sum_flow_id = #{sumFlowId},
        ffd.year_target_sign=0,
        ffd.monthly_plan_sign=0,
        ffd.monthly_analyze_sign=0,
        ffd.month_actual_sign=0
        WHERE ffd.parent_code = #{parentCode}
        and ffd.year_monthly = #{yearMonthly}
        and ff.index_type = #{indexType}
        <if test="sumOrgType != null and sumOrgType != ''">
            AND ffd.sum_org_type = #{sumOrgType}
        </if>
        <if test="sumOrgType == null or sumOrgType  == ''">
            AND ffd.sum_org_type is null
        </if>
        <if test="indexNos != null ">
            AND ffd.index_no IN
            <foreach collection="indexNos" item="indexNo" open="(" close=")" separator=",">
                #{indexNo}
            </foreach>
        </if>
    </update>
    <update id="updateSingleFlowSumStatus">
        update t_filling_flow
        set sum_status = 2
        WHERE parent_code = #{fillCompanyCode}
          AND index_type = #{indexType}
          AND year_monthly = #{yearMonthly}
    </update>

    <select id="getSourceLogByDateStamp" resultType="com.gcl.psmis.framework.common.dto.nkbi.SourceLogDTO">
        SELECT tsl.date_stamp,
               tsl.system_source,
               tsl.status
        FROM t_source_log tsl
        where tsl.date_stamp = #{dateStamp}
    </select>
    <select id="getFlowCompany" resultType="com.gcl.psmis.framework.common.dto.nkbi.FlowCompanyDTO">
        SELECT tcf.id,
            tcf.flow_id,
            tcf.flow_name,
            tcf.fill_company_code,
            tcf.sum_company_code,
            tcf.level_type,
            tcf.child_flag,
            tcf.sum_org_type,
            tcf.spec_flag
        FROM t_flow_company tcf
        <where>
            <if test="flowId != null">
                AND tcf.flow_id = #{flowId}
            </if>
        </where>

    </select>
    <select id="getFillFlowByCompanyCode" resultType="com.gcl.psmis.framework.common.dto.nkbi.FillFlowDTO">
        SELECT
            ticc.index_no,
            IFNULL(ticc.index_name_alias, ticc.index_name) indexName,
            ticc.unit indexUnit,
            IFNULL(ticc.index_parent_alias, ticc.index_parent) indexParent,
            tic.level_type,
            tic.fill_level,
            tic.org_no companyCode,
            tic.parent_org_no parentOrgNo,
            ttd.year_target,
            ttd.monthly_analyze,
            ttd.monthly_plan,
            ttd.monthly_actual,
            ticc.year_target_sign,
            ticc.monthly_plan_sign,
            ticc.monthly_analyze_sign,
            ticc.month_actual_sign,
            ticc.target_system_source,
            ticc.actual_system_source,
            ticc.analyze_system_source,
            ticc.plan_system_source,
            ticc.sum_org_type,
            ticc.index_describe,
            ticc.sort,
            ticc.id configId
        FROM t_index_company tic
        <if test="specFlag != null and specFlag==0">
            JOIN t_index_company_config ticc ON tic.org_no = ticc.company_code
        </if>
        <if test="specFlag != null and specFlag==1">
            JOIN t_index_company_config ticc ON tic.parent_org_no = ticc.company_code
        </if>
        AND tic.index_no = ticc.index_no
        AND CAST(CONCAT(#{yearMonthly}, '-01') AS DATETIME) >= ticc.start_time
        AND ( ticc.end_time IS NULL
        OR ticc.end_time >= CAST(CONCAT(#{yearMonthly}, '-01') AS DATETIME))
            LEFT JOIN t_transfer_data ttd ON ttd.index_no = tic.index_no
        AND tic.org_no = ttd.company_code
        AND tic.level_type = ttd.level_type
        AND tic.fill_level = ttd.fill_level
        AND ttd.year_monthly = #{yearMonthly}
            LEFT JOIN t_org_filter tof ON tof.source_org_no = tic.org_no
        AND tof.filter_flag = 1
        WHERE
        tic.org_no = #{fillCompanyCode}
        AND tic.parent_org_no = #{sumCompanyCode}
        <if test="flowType != null and flowType == 1">
            AND tic.level_type = 'DT'
            AND tic.fill_level != 'BB'
        </if>
        <if test="flowType != null and flowType == 2">
            AND((tic.level_type = 'DT' and tic.fill_level='BB')
            or (tic.level_type = 'ZT' and tic.fill_level='TOTAL'))
        </if>
        <if test="sumOrgType != null and sumOrgType != ''">
            AND ticc.`sum_org_type` = #{sumOrgType}
        </if>
    </select>
    <select id="getSingleFillingFlows" resultType="com.gcl.psmis.framework.mbg.entity.TFillingFlowEntity">
        SELECT id,
               flow_no,
               flow_name,
               flow_type,
               index_type,
               flow_status,
               year_monthly,
               company_code,
               company_name,
               parent_code,
               parent_name,
               fill_user_code,
               fill_user_name,
               sum_status,
               submit_time,
               create_time,
               update_time,
               create_by_no,
               create_by_name,
               sum_org_type,
               last_audit_code,
               archive_status
        FROM t_filling_flow
        WHERE flow_status = 2
          AND parent_code = #{fillCompanyCode}
          AND index_type = #{indexType}
          AND year_monthly = #{yearMonthly}
    </select>
    <select id="getCountByYearMonthlyAndParentCodeAndSumOrgType" resultType="java.lang.Integer">
        SELECT count(1)
        FROM t_filling_flow_detail
        WHERE parent_code = #{parentCode}
          and year_monthly = #{yearMonthly}
          and sum_org_type = #{sumOrgType}
    </select>
    <select id="getAuditPerson" resultType="com.gcl.psmis.framework.common.dto.nkbi.FillFlowAuditPersonDTO">
        select tfa.flow_id,
        tfa.flow_company_id,
        tfa.node_code,
        tfa.audit_code
        from t_filling_flow_audit_user tfa
        <where>
            <if test="flowId != null">
                AND tfa.flow_id = #{flowId}
            </if>
            <if test="flowCompanyId !=  null">
                AND tfa.flow_company_id = #{flowCompanyId}
            </if>
        </where>
    </select>
    <select id="getFlows" resultType="com.gcl.psmis.framework.mbg.entity.TFillingFlowEntity">
        SELECT id,
               flow_no,
               flow_name,
               flow_type,
               index_type,
               flow_status,
               year_monthly,
               company_code,
               company_name,
               parent_code,
               parent_name,
               fill_user_code,
               fill_user_name,
               sum_status,
               submit_time,
               create_time,
               update_time,
               create_by_no,
               create_by_name,
               sum_org_type,
               last_audit_code,
               archive_status
        FROM t_filling_flow
        WHERE index_type = #{indexType}
        AND flow_type = #{flowType}
        AND flow_status = #{flowStatus}
        AND year_monthly = #{yearMonthly}
    </select>
    
    <select id="getAuditPersons" resultType="com.gcl.psmis.framework.common.dto.nkbi.FillFlowAuditPersonDTO">
        SELECT
        ffa.flow_id,
        ffa.flow_company_id,
        ffa.node_code,
        ffa.audit_code
        FROM
        t_filling_flow_audit_user ffa
        JOIN t_flow_company fc ON fc.id = ffa.flow_company_id
        <where>
            <if test="fillCompanyCode != null and fillCompanyCode != ''">
                AND fc.fill_company_code = #{fillCompanyCode}
            </if>
            <if test="sumCompanyCode != null and sumCompanyCode != ''">
                AND fc.sum_company_code = #{sumCompanyCode}
            </if>
            <if test="sumOrgType != null and sumOrgType != ''">
                AND fc.sum_org_type = #{sumOrgType}
            </if>
            <if test="flowId != null">
                AND ffa.flow_id = #{flowId}
            </if>
        </where>
    </select>
    <select id="selectPlanList" resultType="com.gcl.psmis.framework.mbg.entity.TFillingFlowDetailEntity">
        SELECT
            ffd.id,
            ffd.single_flow_id,
            ffd.sum_flow_id,
            ffd.index_no,
            ffd.index_name,
            ffd.index_unit,
            ffd.index_parent,
            ffd.level_type,
            ffd.fill_level,
            ffd.company_code,
            ffd.parent_code,
            ffd.year_monthly,
            ffd.target_system_source,
            ffd.actual_system_source,
            ffd.analyze_system_source,
            ffd.plan_system_source,
            ffd.year_target,
            ffd.monthly_analyze,
            ffd.monthly_plan,
            ffd.monthly_actual,
            ffd.plan_filed_flag,
            ffd.actual_filed_flag,
            ffd.actual_value,
            ffd.year_target_sign,
            ffd.month_actual_sign,
            ffd.monthly_plan_sign,
            ffd.monthly_analyze_sign,
            ffd.create_time,
            ffd.update_time,
            ffd.year_target_update_time,
            ffd.monthly_analyze_update_time,
            ffd.monthly_plan_update_time,
            ffd.monthly_actual_update_time,
            ffd.sum_org_type,
            ffd.index_describe,
            ffd.remark,
            ffd.sort,
            ffd.config_id,
            ffd.monthly_plan_t1,
            ffd.monthly_plan_t2
        FROM t_filling_flow_detail ffd
        <if test="flowType == 1">
            join t_filling_flow ff
            ON ff.id = ffd.single_flow_id
        </if>
        <if test="flowType == 2">
            join t_filling_flow ff
            ON ff.id = ffd.sum_flow_id
        </if>
        WHERE ffd.year_monthly = #{yearMonthly}
        AND ff.index_type = 1
        AND (ff.flow_status = 2 OR ff.flow_status = 3 OR ff.flow_status = 5)
    </select>
    <select id="getFlowDetailEntities" resultType="com.gcl.psmis.framework.mbg.entity.TFillingFlowDetailEntity">
        SELECT
            ffd.id,
            ffd.single_flow_id,
            ffd.sum_flow_id,
            ffd.index_no,
            ffd.index_name,
            ffd.index_unit,
            ffd.index_parent,
            ffd.level_type,
            ffd.fill_level,
            ffd.company_code,
            ffd.parent_code,
            ffd.year_monthly,
            ffd.target_system_source,
            ffd.actual_system_source,
            ffd.analyze_system_source,
            ffd.plan_system_source,
            ffd.year_target,
            ffd.monthly_analyze,
            ffd.monthly_plan,
            ffd.monthly_actual,
            ffd.plan_filed_flag,
            ffd.actual_filed_flag,
            ffd.actual_value,
            ffd.year_target_sign,
            ffd.month_actual_sign,
            ffd.monthly_plan_sign,
            ffd.monthly_analyze_sign,
            ffd.create_time,
            ffd.update_time,
            ffd.year_target_update_time,
            ffd.monthly_analyze_update_time,
            ffd.monthly_plan_update_time,
            ffd.monthly_actual_update_time,
            ffd.sum_org_type,
            ffd.index_describe,
            ffd.remark,
            ffd.sort,
            ffd.config_id,
            ffd.monthly_plan_t1,
            ffd.monthly_plan_t2
        FROM t_filling_flow_detail ffd
        <if test="flowType == 1">
            join t_filling_flow ff
            ON ff.id = ffd.single_flow_id
        </if>
        <if test="flowType == 2">
            join t_filling_flow ff
            ON ff.id = ffd.sum_flow_id
        </if>
        WHERE ff.year_monthly = #{yearMonthly}
        and ff.index_type = #{indexType}
        and ff.flow_type = #{flowType}
    </select>
</mapper>