package com.gcl.psmis.workflow.constant.task;

import lombok.Getter;

/**
 * @className: SingleWorkFlowEnum
 * @author: sq
 * @create: 2025/04/14
 * @description: 单体工作流节点枚举
 * @version: 1.0
 */
@Getter
public class SingleWorkFlowEnum extends FlowNodeEnum {

    public static final SingleWorkFlowEnum START_EVENT_SINGLE_FILLING_01 = new SingleWorkFlowEnum("StartEvent_SINGLE_FILLING_01", "流程发起");
    public static final SingleWorkFlowEnum ACTIVITY_SINGLE_FILLING_01 = new SingleWorkFlowEnum("Activity_SINGLE_FILLING_01", "待提交");
    public static final SingleWorkFlowEnum ACTIVITY_SINGLE_FILLING_02 = new SingleWorkFlowEnum("Activity_SINGLE_FILLING_02", "隐藏节点");
    public static final SingleWorkFlowEnum ACTIVITY_SINGLE_FILLING_03 = new SingleWorkFlowEnum("ACTIVITY_SINGLE_FILLING_03", "归档");
    public static final SingleWorkFlowEnum EVENT_SINGLE_FILLING_02 = new SingleWorkFlowEnum("Event_SINGLE_FILLING_02", "流程结束");
    public static final SingleWorkFlowEnum SINGLE_FLOW_USER = new SingleWorkFlowEnum("single_flow_user", "隐藏节点特殊审批人员");



    SingleWorkFlowEnum(String code, String desc) {
        super(code, desc);
    }


}
