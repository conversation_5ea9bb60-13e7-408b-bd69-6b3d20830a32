package com.gcl.psmis.workflow.constant.task;

import lombok.Getter;

/**
* @className: DefectOrderFlowEnum
* @author: ganXu
* @create: 2024/1/28 07:58
* @description: 缺陷工单流程枚举
*/
@Getter
public class RegisterDefectFlowEnum extends FlowNodeEnum{

    public static final RegisterDefectFlowEnum REGIST_ALARM_START=new RegisterDefectFlowEnum("regist_alarm_start","手动登记缺陷");
    public static final RegisterDefectFlowEnum REGIST_ALARM_AUDIT=new RegisterDefectFlowEnum("regist_alarm_audit","缺陷审核");
    public static final RegisterDefectFlowEnum REGIST_ALARM_PASS=new RegisterDefectFlowEnum("regist_alarm_pass","缺陷审核通过");
    public static final RegisterDefectFlowEnum REGIST_ALARM_REJECT=new RegisterDefectFlowEnum("regist_alarm_reject","缺陷审核驳回");

    RegisterDefectFlowEnum(String code, String desc) {
        super(code,desc);
    }



}
