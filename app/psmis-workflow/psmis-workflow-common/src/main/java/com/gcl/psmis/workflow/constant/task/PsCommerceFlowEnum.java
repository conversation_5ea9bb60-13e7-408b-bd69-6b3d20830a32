package com.gcl.psmis.workflow.constant.task;

import lombok.Getter;

/**
 * @className: PsCommerceFlowEnum
 * @author: xinan.yuan
 * @create: 2024/1/28 07:58
 * @description: 工商业验收
 */
@Getter
public class PsCommerceFlowEnum extends FlowNodeEnum {

    public static final PsCommerceFlowEnum StartEvent_PS_CHECK_REC_01 = new PsCommerceFlowEnum("StartEvent_PS_CHECK_REC_01", "创建验收单");
    public static final PsCommerceFlowEnum Activity_PS_CHECK_REC_01 = new PsCommerceFlowEnum("Activity_PS_CHECK_REC_01", "开始验收(待验收)");
    public static final PsCommerceFlowEnum Activity_PS_CHECK_REC_02 = new PsCommerceFlowEnum("Activity_PS_CHECK_REC_02", "验收中");
    public static final PsCommerceFlowEnum Activity_PS_CHECK_REC_03 = new PsCommerceFlowEnum("Activity_PS_CHECK_REC_03", "待整改");
    public static final PsCommerceFlowEnum Activity_PS_CHECK_REC_04 = new PsCommerceFlowEnum("Activity_PS_CHECK_REC_04", "待审核");
    public static final PsCommerceFlowEnum Event_PS_CHECK_REC_01 = new PsCommerceFlowEnum("Event_PS_CHECK_REC_01", "验收完成");


    PsCommerceFlowEnum(String code, String desc) {
        super(code, desc);
    }


}
