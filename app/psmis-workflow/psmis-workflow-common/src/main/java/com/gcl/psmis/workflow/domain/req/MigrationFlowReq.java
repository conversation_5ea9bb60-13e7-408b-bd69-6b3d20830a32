package com.gcl.psmis.workflow.domain.req;

import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 * @ClassName MigrationRequest
 * @description: TODO
 * @date 2024年03月11日
 * @version: 1.0
 */
@Data
public class MigrationFlowReq {
    /**
     * 需要迁移的流程定义id
     */
    private String sourceProcessDefinitionId;
    /**
     * 迁移目标流程定义id
     */
    private String targetProcessDefinitionId;
    /**
     * 需要迁移的流程实例id集合
     */
    private List<String> processInstanceIds;

}
