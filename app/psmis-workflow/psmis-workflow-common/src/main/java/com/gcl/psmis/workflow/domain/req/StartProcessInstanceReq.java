package com.gcl.psmis.workflow.domain.req;

import com.gcl.psmis.workflow.constant.ProcessKeyEnum;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.util.Map;

/**
* @className: StartProcessInstanceReq
* @author: xinan.yuan
* @create: 2024/1/22 15:06
* @description:
*/
@NoArgsConstructor
@AllArgsConstructor
@Data
@Builder
public class StartProcessInstanceReq {

    /**
     * 发起人
     */
    @ApiModelProperty(value = "发起人不传为system")
    private String authenticatedAccount;


    /**
     * 流程定义key
     */
    @ApiModelProperty(value = "流程定义key", required = true)
    @NotNull(message = "流程定义key不能为空")
    private ProcessKeyEnum processKey;

    /**
     * 业务key
     */
    @ApiModelProperty(value = "业务key")
    @NotBlank(message = "业务key不能为空")
    private String businessKey;

    /**
     * 扩展信息
     */
    @ApiModelProperty(value = "扩展信息")
    private Map<String,Object> variables;

 }
