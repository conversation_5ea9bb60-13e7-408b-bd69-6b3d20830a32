package com.gcl.psmis.workflow.domain.dto;

import com.baomidou.mybatisplus.annotation.TableField;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Map;

/**
 * <AUTHOR>
 * @ClassName FlowBizDTO
 * @description: TODO
 * @date 2024年01月25日
 * @version: 1.0
 */
@Data
public class FlowBizDTO {

    @ApiModelProperty(value = "流程实例id")
    private String procInsId;

    @ApiModelProperty(value = "扩展信息")
    private Map<String,Object> variables;

    @ApiModelProperty(value = "跳转节点的code")
    private String moveNodeCode;

    @ApiModelProperty(value = "删除流程理由")
    private String reason;

    @ApiModelProperty(value = "用户工号")
    private String userNo;

    @ApiModelProperty(value = "节点code")
    private String nodeCode;

    @ApiModelProperty("业务key")
    @TableField("business_key")
    private String businessKey;
}
