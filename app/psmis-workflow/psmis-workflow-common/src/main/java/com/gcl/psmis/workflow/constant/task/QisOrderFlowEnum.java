package com.gcl.psmis.workflow.constant.task;

import lombok.Getter;

/**
* @className: AlarmAuditFlowEnum
* @author: ganXu
* @create: 2024/1/28 07:58
* @description: 告警审核流程枚举
*/
@Getter
public class QisOrderFlowEnum extends FlowNodeEnum{

    public static final QisOrderFlowEnum QIS_START=new QisOrderFlowEnum("qis_start","巡检流程发起");
    public static final QisOrderFlowEnum QIS_ORDER=new QisOrderFlowEnum("qis_order","巡检派单");
    public static final QisOrderFlowEnum QIS_EXECUTE=new QisOrderFlowEnum("qis_execute","执行工单");
    public static final QisOrderFlowEnum QIS_FINISH=new QisOrderFlowEnum("qis_finish","完成工单");
    public static final QisOrderFlowEnum QIS_CHECK=new QisOrderFlowEnum("qis_check","验收工单");
    public static final QisOrderFlowEnum QIS_CHECK_REJECT=new QisOrderFlowEnum("qis_check_reject","验收不合格");
    public static final QisOrderFlowEnum QIS_CHECK_PASS=new QisOrderFlowEnum("qis_check_pass","验收合格");
;

    QisOrderFlowEnum(String code, String desc) {
        super(code,desc);
    }



}
