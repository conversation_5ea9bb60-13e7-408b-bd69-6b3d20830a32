package com.gcl.psmis.workflow.constant.task;

import lombok.Getter;

/**
* @className: PsCheckRecFlowEnum
* @author: ganXu
* @create: 2024/1/28 07:58
* @description: 电站清洗单流程枚举
*/
@Getter
public class PsCleanFlowEnum extends FlowNodeEnum{

    public static final PsCleanFlowEnum StartEvent_CELAN=new PsCleanFlowEnum("StartEvent_CELAN","验收发起");
    public static final PsCleanFlowEnum CLEAN_01=new PsCleanFlowEnum("CLEAN_01","待清洗");
    public static final PsCleanFlowEnum CLEAN_02=new PsCleanFlowEnum("CLEAN_02","清洗中");
    public static final PsCleanFlowEnum CLEAN_03=new PsCleanFlowEnum("CLEAN_03","待审核");
    public static final PsCleanFlowEnum Event_CLEAN=new PsCleanFlowEnum("Event_CLEAN","已完成");


    PsCleanFlowEnum(String code, String desc) {
        super(code,desc);
    }



}
