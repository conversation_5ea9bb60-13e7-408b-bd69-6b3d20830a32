package com.gcl.psmis.workflow.constant.task;

import lombok.Getter;

/**
* @className: DefectOrderFlowEnum
* @author: ganXu
* @create: 2024/1/28 07:58
* @description: 缺陷工单流程枚举
*/
@Getter
public class DefectDelayFlowEnum extends FlowNodeEnum{

    public static final DefectDelayFlowEnum DEFECT_DELAY_APPLY=new DefectDelayFlowEnum("defect_delay_apply","延期申请");
    public static final DefectDelayFlowEnum DEFECT_DELAY_AUDIT=new DefectDelayFlowEnum("defect_delay_audit","延期审核");
    public static final DefectDelayFlowEnum DEFECT_DELAY_PASS=new DefectDelayFlowEnum("defect_delay_pass","延期审核通过");
    public static final DefectDelayFlowEnum DEFECT_DELAY_REJECT=new DefectDelayFlowEnum("defect_delay_reject","延期审核驳回");

    DefectDelayFlowEnum(String code, String desc) {
        super(code,desc);
    }



}
