package com.gcl.psmis.workflow.constant.task;

import lombok.Getter;

/**
 * @className: SumWorkFlowEnum
 * @author: sq
 * @create: 2025/04/14
 * @description: 汇总工作流节点枚举
 * @version: 1.0
 */
@Getter
public class SumWorkFlowEnum extends FlowNodeEnum {

    public static final SumWorkFlowEnum START_EVENT_SUM_FILLING_01 = new SumWorkFlowEnum("StartEvent_SUM_FILLING_01", "流程发起");
    public static final SumWorkFlowEnum ACTIVITY_SUM_FILLING_01 = new SumWorkFlowEnum("Activity_SUM_FILLING_01", "待提交");
    public static final SumWorkFlowEnum ACTIVITY_SUM_FILLING_02 = new SumWorkFlowEnum("Activity_SUM_FILLING_02", "已提交(隐藏)");
    public static final SumWorkFlowEnum ACTIVITY_SUM_FILLING_03 = new SumWorkFlowEnum("Activity_SUM_FILLING_03", "归档");
    public static final SumWorkFlowEnum ACTIVITY_SUM_FILLING_04 = new SumWorkFlowEnum("Activity_SUM_FILLING_04", "已归档(隐藏)");
    public static final SumWorkFlowEnum EVENT_SINGLE_FILLING_02 = new SumWorkFlowEnum("Event_SINGLE_FILLING_02", "流程结束");;
    public static final SingleWorkFlowEnum SUM_FLOW_USER = new SingleWorkFlowEnum("sum_flow_user", "隐藏节点特殊审批人员");

    SumWorkFlowEnum(String code, String desc) {
        super(code, desc);
    }


}
