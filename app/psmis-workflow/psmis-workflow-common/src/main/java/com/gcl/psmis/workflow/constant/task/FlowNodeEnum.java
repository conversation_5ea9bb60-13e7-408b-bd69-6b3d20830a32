package com.gcl.psmis.workflow.constant.task;

import com.fasterxml.jackson.annotation.JsonSubTypes;
import com.fasterxml.jackson.annotation.JsonTypeInfo;
import io.swagger.annotations.ApiModelProperty;
import io.swagger.annotations.ApiParam;
import lombok.Getter;

/**
* @className: FlowNodeEnum
* @author: xinan.yuan
* @create: 2024/1/28 08:09
* @description: 流程节点枚举
*/
@Getter
public class FlowNodeEnum {
    private final String code;
    private final String desc;

    protected FlowNodeEnum(String code, String desc) {
        this.code = code;
        this.desc=desc;
    }



}
