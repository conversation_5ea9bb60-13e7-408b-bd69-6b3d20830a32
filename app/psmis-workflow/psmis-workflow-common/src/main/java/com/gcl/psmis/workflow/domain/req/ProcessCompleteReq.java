package com.gcl.psmis.workflow.domain.req;

import com.gcl.psmis.workflow.constant.task.FlowNodeEnum;
import io.swagger.annotations.ApiModelProperty;
import io.swagger.annotations.ApiParam;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.util.Map;

/**
* @className: ProcessComplateReq
* @author: xinan.yuan
* @create: 2024/1/24 16:15
* @description: 
*/
@NoArgsConstructor
@AllArgsConstructor
@Data
@Builder
public class ProcessCompleteReq {

    /**
     * 流程实例id
     */
    @ApiModelProperty(value = "流程实例id", required = true)
    @NotBlank(message = "流程实例id不能为空")
    private String processInstanceId;


    /**
     * 当前流程流转节点
     */
    @ApiModelProperty(value = "当前流程流转节点",required = true)
    @NotNull(message = "当前流程流转节点不能为空")
    private FlowNodeEnum flowNodeEnum;


    /**
     * 扩展信息
     */
    @ApiModelProperty(value = "扩展信息")
    private Map<String,Object> variables;


    @ApiModelProperty(value = "操作人工号")
    private String operatorAccount;

    @ApiModelProperty(value = "操作人名字")
    private String operatorName;
 }
