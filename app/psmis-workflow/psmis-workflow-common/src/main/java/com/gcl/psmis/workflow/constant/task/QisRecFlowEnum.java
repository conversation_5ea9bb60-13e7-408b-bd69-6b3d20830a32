package com.gcl.psmis.workflow.constant.task;

import lombok.Getter;

/**
* @className: QisRecFlowEnum
* @author: xinan.yuan
* @create: 2024/1/28 07:58
* @description: 巡检整改单流程枚举
*/
@Getter
public class QisRecFlowEnum extends FlowNodeEnum{

    public static final QisRecFlowEnum StartEvent_QIS_01=new QisRecFlowEnum("StartEvent_QIS_01","开始巡检整改");
    public static final QisRecFlowEnum Activity_QIS_01=new QisRecFlowEnum("Activity_QIS_01","所有省技术待办");
    public static final QisRecFlowEnum Activity_QIS_02=new QisRecFlowEnum("Activity_QIS_02","省技术锁单");
    public static final QisRecFlowEnum Activity_QIS_03=new QisRecFlowEnum("Activity_QIS_03","省技术提交整改方案保存信息");

    public static final QisRecFlowEnum Activity_QIS_04=new QisRecFlowEnum("Activity_QIS_04","代理商提交整改结果");


    public static final QisRecFlowEnum Activity_QIS_05=new QisRecFlowEnum("Activity_QIS_05","省技术审核页面");
    public static final QisRecFlowEnum Activity_QIS_06=new QisRecFlowEnum("Activity_QIS_06","省技术审核锁单");
    public static final QisRecFlowEnum Activity_QIS_07=new QisRecFlowEnum("Activity_QIS_07","省技术审核保存");

    public static final QisRecFlowEnum Activity_QIS_08=new QisRecFlowEnum("Activity_QIS_08","质量审核");

    public static final QisRecFlowEnum Activity_SUB_QIS_01=new QisRecFlowEnum("Activity_SUB_QIS_01","整改方案子流程");
    public static final QisRecFlowEnum Activity_SUB_QIS_02=new QisRecFlowEnum("Activity_SUB_QIS_02","技术审核子流程");

    //记录流水的节点Code
    public static final QisRecFlowEnum Rectification_plan=new QisRecFlowEnum("Rectification_plan","整改方案");
    public static final QisRecFlowEnum Technology_audit=new QisRecFlowEnum("Technology_audit","技术审核");




    public static final QisRecFlowEnum Event_QIS_01=new QisRecFlowEnum( "Event_QIS_01","巡检整改完成");

    QisRecFlowEnum(String code,String desc) {
        super(code,desc);
    }



}
