package com.gcl.psmis.msg.utils;

import cn.hutool.core.date.DateUtil;
import cn.hutool.core.text.CharSequenceUtil;
import com.gcl.psmis.msg.domain.xxl.XxlJobGroup;
import com.gcl.psmis.msg.domain.xxl.XxlJobInfo;
import com.gcl.psmis.framework.mbg.entity.TMsgTemplateEntity;

import com.gcl.psmis.msg.constant.CommonConstant;
import com.gcl.psmis.msg.constant.XxlJobConstant;
import com.gcl.psmis.msg.enums.xxljob.*;
import com.gcl.psmis.msg.service.CronTaskService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import java.util.Date;
import java.util.Objects;

/**
* @className: XxlJobUtils
* @author: xinan.yuan
* @create: 2023/12/10 09:13
* @description: xxlJob工具类
*/
@Component
public class XxlJobUtils {

    @Value("${xxl.job.executor.appname}")
    private String appName;

    @Value("${xxl.job.executor.jobHandlerName}")
    private String jobHandlerName;

    @Autowired
    private CronTaskService cronTaskService;

    /**
     * 构建xxlJobInfo信息
     *
     * @param tMsgTemplateEntity
     * @return
     */
    public XxlJobInfo buildXxlJobInfo(TMsgTemplateEntity tMsgTemplateEntity) {

        String scheduleConf = tMsgTemplateEntity.getExpectPushTime();
        // 如果没有指定cron表达式，说明立即执行(给到xxl-job延迟5秒的cron表达式)
        if (tMsgTemplateEntity.getExpectPushTime().equals(String.valueOf(CommonConstant.FALSE))) {
            scheduleConf = DateUtil.format(DateUtil.offsetSecond(new Date(), XxlJobConstant.DELAY_TIME), CommonConstant.CRON_FORMAT);
        }

        XxlJobInfo xxlJobInfo = XxlJobInfo.builder()
                .jobGroup(queryJobGroupId()).jobDesc(tMsgTemplateEntity.getName())
                .author(tMsgTemplateEntity.getCreateByNo())
                .scheduleConf(scheduleConf)
                .scheduleType(ScheduleTypeEnum.CRON.name())
                .misfireStrategy(MisfireStrategyEnum.DO_NOTHING.name())
                .executorRouteStrategy(ExecutorRouteStrategyEnum.CONSISTENT_HASH.name())
                .executorHandler(XxlJobConstant.JOB_HANDLER_NAME)
                .executorParam(String.valueOf(tMsgTemplateEntity.getId()))
                .executorBlockStrategy(ExecutorBlockStrategyEnum.SERIAL_EXECUTION.name())
                .executorTimeout(XxlJobConstant.TIME_OUT)
                .executorFailRetryCount(XxlJobConstant.RETRY_COUNT)
                .glueType(GlueTypeEnum.BEAN.name())
                .triggerStatus(CommonConstant.FALSE)
                .glueRemark(CharSequenceUtil.EMPTY)
                .glueSource(CharSequenceUtil.EMPTY)
                .alarmEmail(CharSequenceUtil.EMPTY)
                .childJobId(CharSequenceUtil.EMPTY).build();

        if (Objects.nonNull(tMsgTemplateEntity.getCronTaskId())) {
            xxlJobInfo.setId(tMsgTemplateEntity.getCronTaskId());
        }
        return xxlJobInfo;
    }

    /**
     * 根据就配置文件的内容获取jobGroupId，没有则创建
     *
     * @return
     */
    private Integer queryJobGroupId() {
        Integer groupId = cronTaskService.getGroupId(appName, jobHandlerName);
        if (Objects.isNull(groupId)) {
            XxlJobGroup xxlJobGroup = XxlJobGroup.builder().appname(appName).title(jobHandlerName).addressType(CommonConstant.FALSE).build();
            if (cronTaskService.createGroup(xxlJobGroup)) {
                return cronTaskService.getGroupId(appName, jobHandlerName);
            }
        }
        return groupId;
    }

}
