package com.gcl.psmis.msg.mq.springeventbus;

import com.alibaba.fastjson.JSON;
import com.gcl.psmis.msg.constant.MessageQueuePipeline;
import com.gcl.psmis.msg.domain.RecallTaskInfo;
import com.gcl.psmis.msg.domain.TaskInfo;
import com.gcl.psmis.msg.receiver.SpringEventBusReceiver;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.context.ApplicationListener;
import org.springframework.stereotype.Service;

/**
* @className: SpringEventBusReceiverListener
* @author: xinan.yuan
* @create: 2023/12/15 09:38
* @description:
*/
@Service
@ConditionalOnProperty(name = "mc.mq.pipeline", havingValue = MessageQueuePipeline.SPRING_EVENT_BUS)
public class SpringEventBusReceiverListener implements ApplicationListener<McSpringEventBusEvent> {

    @Autowired
    private SpringEventBusReceiver springEventBusReceiver;

    @Value("${mc.business.topic.name}")
    private String sendTopic;
    @Value("${mc.business.recall.topic.name}")
    private String recallTopic;

    @Override
    public void onApplicationEvent(McSpringEventBusEvent event) {
        String topic = event.getMcSpringEventSource().getTopic();
        String jsonValue = event.getMcSpringEventSource().getJsonValue();
        if (topic.equals(sendTopic)) {
            springEventBusReceiver.consume(JSON.parseArray(jsonValue, TaskInfo.class));
        } else if (topic.equals(recallTopic)) {
            springEventBusReceiver.recall(JSON.parseObject(jsonValue, RecallTaskInfo.class));
        }
    }
}
