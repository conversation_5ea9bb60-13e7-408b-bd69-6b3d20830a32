package com.gcl.psmis.vr.domain.dto;


/**
 * 手动录入电量
 *
 * <AUTHOR>
 */
public class WppRecordReq extends BaseReq {

	/**
	 * 字段描述: [字段功能描述]
	 */
	private static final long	serialVersionUID	= -1538327167894032409L;

	/**
	 * 测点编号 SN
	 */
	private String				mpNo;
	// 电表读数
	private Long				wpp;
	// 日期格式 20160101
	private String				date;

	public String getMpNo() {

		return mpNo;
	}

	public void setMpNo(String mpNo) {

		this.mpNo = mpNo;
	}

	public Long getWpp() {

		return wpp;
	}

	public void setWpp(Long wpp) {

		this.wpp = wpp;
	}

	public String getDate() {

		return date;
	}

	public void setDate(String date) {

		this.date = date;
	}

}
