package com.gcl.psmis.vr.service.external;

import cn.hutool.core.collection.ListUtil;
import cn.hutool.core.date.DateTime;
import cn.hutool.core.date.DateUtil;
import cn.hutool.json.JSONUtil;
import com.gcl.psmis.framework.common.util.TimeHelper;
import com.gcl.psmis.framework.mbg.entity.TVrDispatchPlanEntity;
import com.gcl.psmis.framework.mbg.service.TVrDispatchPlanService;
import com.gcl.psmis.framework.taos.domain.entity.PsVrIot15StDeviceEntity;
import com.gcl.psmis.framework.taos.domain.entity.PsVrIotStDeviceEntity;
import com.gcl.psmis.framework.taos.wrapper.TdWrappers;
import com.gcl.psmis.vr.domain.request.external.ConfirmPlanReq;
import com.gcl.psmis.vr.domain.request.external.RealIotData;
import com.gcl.psmis.vr.domain.request.external.RealIotDataReq;
import com.gcl.psmis.vr.domain.request.external.StrategyDispatchReq;
import com.gcl.psmis.vr.domain.response.external.*;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * @className: ExternalService
 * @author: xinan.yuan
 * @create: 2025/4/7 11:18
 * @description: 对外接口服务
 */
@Service
@Slf4j
@RequiredArgsConstructor
public class ExternalService {

    private final TVrDispatchPlanService tvrDispatchPlanService;

    private final RedisTemplate<String, String> redisTemplate;

    //已装表的户号
    public static final List<String> POWER_ACCOUNT = ListUtil.of("****************", "****************", "****************");

    public StrategyDispatchResp getStrategyPlan(StrategyDispatchReq req) {

        log.info("getStrategyPlan 入参：{}", JSONUtil.toJsonStr(req));
        List<TVrDispatchPlanEntity> planEntities = tvrDispatchPlanService.lambdaQuery()
                .ge(TVrDispatchPlanEntity::getCreateTime, DateUtil.parse(DateUtil.format(DateTime.now(), "yyyy-MM-dd")))
                .ge(TVrDispatchPlanEntity::getStartTime, DateUtil.parse(DateUtil.format(DateTime.now(), "yyyy-MM-dd")))
                .eq(TVrDispatchPlanEntity::getPowerAccount, req.getPowerAccount())
                .eq(TVrDispatchPlanEntity::getConfirmFlag, 0)
                .eq(TVrDispatchPlanEntity::getDelFlag, 0).list();
        if (CollectionUtils.isEmpty(planEntities)) {
            return null;
        }
        StrategyDispatchResp resp = new StrategyDispatchResp();
        resp.setPowerAccount(req.getPowerAccount());
        List<StrategyDispatchPlanResp> planResps = planEntities.stream().map(plan -> {
            StrategyDispatchPlanResp planResp = new StrategyDispatchPlanResp();
            BeanUtils.copyProperties(plan, planResp);
            return planResp;
        }).collect(Collectors.toList());
        resp.setPlans(planResps);
        return resp;
    }

    public void confirmPlanReceived(ConfirmPlanReq req) {
        tvrDispatchPlanService.lambdaUpdate().set(TVrDispatchPlanEntity::getConfirmFlag, 1)
                .in(TVrDispatchPlanEntity::getPlanCode, req.getPlanCodes())
                .eq(TVrDispatchPlanEntity::getPowerAccount, req.getPowerAccount())
                .update();
    }

    public void uploadRealtimeData(RealIotDataReq req) {
        String now = DateUtil.now();
        List<PsVrIotStDeviceEntity> psVrIotStDeviceEntities = new ArrayList<>();

        for (RealIotData data : req.getDataList()) {
            String sn = redisTemplate.opsForValue().get(data.getSn());
            if (StringUtils.isBlank(sn)) {
                log.warn("设备{}未注册", data.getSn());
                continue;
            }
            PsVrIotStDeviceEntity psVrIotStDeviceEntity = new PsVrIotStDeviceEntity();
            this.copyData(data, psVrIotStDeviceEntity);
            psVrIotStDeviceEntity.setSn(sn);
            psVrIotStDeviceEntity.setTs(DateUtil.date(req.getTs()).toString());
            psVrIotStDeviceEntity.setDeviceSn(sn);
            psVrIotStDeviceEntity.setRt(0);
            psVrIotStDeviceEntity.setResourceType(0);
            psVrIotStDeviceEntity.setUrsn(sn);
            psVrIotStDeviceEntity.setUserResourceSn(sn);
            psVrIotStDeviceEntity.setReceiveTime(now);
            psVrIotStDeviceEntities.add(psVrIotStDeviceEntity);
        }
        TdWrappers.lambdaUpdate(PsVrIotStDeviceEntity.class).saveBatch(psVrIotStDeviceEntities);

        save15mData(psVrIotStDeviceEntities);

        //--------------------已装表的户号不合并为户号数据插入------------------------------
        if (POWER_ACCOUNT.contains(req.getPowerAccount())) {
            return;
        }
        //--------------------合并为户号数据插入------------------------------
        PsVrIotStDeviceEntity powerAccountLog = new PsVrIotStDeviceEntity();
        powerAccountLog.setSn("GCL" + req.getPowerAccount());
        powerAccountLog.setTs(DateUtil.date(req.getTs()).toString());
        powerAccountLog.setDeviceSn("GCL" + req.getPowerAccount());
        powerAccountLog.setRt(1);
        powerAccountLog.setResourceType(1);
        powerAccountLog.setUrsn("GCL" + req.getPowerAccount());
        powerAccountLog.setUserResourceSn("GCL" + req.getPowerAccount());
        powerAccountLog.setReceiveTime(now);
        this.setSumDeviceValues(psVrIotStDeviceEntities, powerAccountLog);
        TdWrappers.lambdaUpdate(PsVrIotStDeviceEntity.class).save(powerAccountLog);

        PsVrIotStDeviceEntity powerDeviceAccountLog = new PsVrIotStDeviceEntity();
        BeanUtils.copyProperties(powerAccountLog, powerDeviceAccountLog);
        powerDeviceAccountLog.setRt(0);
        powerDeviceAccountLog.setResourceType(0);
        powerDeviceAccountLog.setSn("GCLD" + req.getPowerAccount());
        powerDeviceAccountLog.setDeviceSn("GCLD" + req.getPowerAccount());
        TdWrappers.lambdaUpdate(PsVrIotStDeviceEntity.class).save(powerDeviceAccountLog);


        save15mData(ListUtil.of(powerAccountLog));
        save15mData(ListUtil.of(powerDeviceAccountLog));

    }

    private void save15mData(List<PsVrIotStDeviceEntity> psVrIotStDeviceEntities) {
        List<PsVrIot15StDeviceEntity> collect = psVrIotStDeviceEntities.stream().map(psVrIotStDeviceEntity -> {
            Map<String, String> timeSlot = TimeHelper.getTimeSlot(psVrIotStDeviceEntity.getTs());
            String startTime = timeSlot.get("startTime");
            String endTime = timeSlot.get("endTime");

            List<PsVrIotStDeviceEntity> psVrIotStDeviceEntityList = TdWrappers.lambdaQuery(PsVrIotStDeviceEntity.class)
                    .eq(PsVrIotStDeviceEntity::getSn, psVrIotStDeviceEntity.getSn())
                    .ge(PsVrIotStDeviceEntity::getTs, startTime)
                    .le(PsVrIotStDeviceEntity::getTs, endTime)
                    .list();
            PsVrIot15StDeviceEntity psVrIot15StDeviceEntity = new PsVrIot15StDeviceEntity();
            BeanUtils.copyProperties(psVrIotStDeviceEntity, psVrIot15StDeviceEntity);
            psVrIot15StDeviceEntity.setTs(endTime);
            psVrIot15StDeviceEntity.setEtd(calculateAverage(psVrIotStDeviceEntityList, PsVrIotStDeviceEntity::getEtd).toString());
            psVrIot15StDeviceEntity.setEto(calculateAverage(psVrIotStDeviceEntityList, PsVrIotStDeviceEntity::getEto).toString());
            psVrIot15StDeviceEntity.setLoad(calculateAverage(psVrIotStDeviceEntityList, PsVrIotStDeviceEntity::getLoad).toString());
            psVrIot15StDeviceEntity.setP(calculateAverage(psVrIotStDeviceEntityList, PsVrIotStDeviceEntity::getP).toString());
            psVrIot15StDeviceEntity.setC(calculateAverage(psVrIotStDeviceEntityList, PsVrIotStDeviceEntity::getC).toString());
            psVrIot15StDeviceEntity.setQ(calculateAverage(psVrIotStDeviceEntityList, PsVrIotStDeviceEntity::getQ).toString());
            psVrIot15StDeviceEntity.setIo(calculateAverage(psVrIotStDeviceEntityList, PsVrIotStDeviceEntity::getIo).toString());
            psVrIot15StDeviceEntity.setUo(calculateAverage(psVrIotStDeviceEntityList, PsVrIotStDeviceEntity::getUo).toString());

            return psVrIot15StDeviceEntity;
        }).collect(Collectors.toList());
        //批量插入15分钟数据
        TdWrappers.lambdaUpdate(PsVrIot15StDeviceEntity.class).saveBatch(collect);
    }


    public void setSumDeviceValues(List<PsVrIotStDeviceEntity> devices, PsVrIotStDeviceEntity powerAccountLog) {

        BigDecimal totalLoad = devices.stream()
                .map(PsVrIotStDeviceEntity::getLoad)
                .map(this::parseBigDecimal)
                .reduce(BigDecimal.ZERO, BigDecimal::add);

        BigDecimal totalIo = devices.stream()
                .map(PsVrIotStDeviceEntity::getIo)
                .map(this::parseBigDecimal)
                .reduce(BigDecimal.ZERO, BigDecimal::add);

        BigDecimal totalUo = devices.stream()
                .map(PsVrIotStDeviceEntity::getUo)
                .map(this::parseBigDecimal)
                .reduce(BigDecimal.ZERO, BigDecimal::add);

        powerAccountLog.setLoad(totalLoad.toPlainString());
        powerAccountLog.setIo(totalIo.toPlainString());
        powerAccountLog.setUo(totalUo.toPlainString());
    }

    private BigDecimal parseBigDecimal(String value) {
        try {
            return value != null && !value.trim().isEmpty() ? new BigDecimal(value.trim()) : BigDecimal.ZERO;
        } catch (NumberFormatException e) {
            return BigDecimal.ZERO;
        }
    }

    /**
     * @param data
     * @param psVrIotStDeviceEntity
     * @return
     * @desc 数据拷贝
     * @date 2025/4/18 09:03
     */
    private void copyData(RealIotData data, PsVrIotStDeviceEntity psVrIotStDeviceEntity) {
        data.setP(data.getLoad());
        if (data.getLoad() != null) psVrIotStDeviceEntity.setLoad(data.getLoad() + "");
        if (data.getP() != null) psVrIotStDeviceEntity.setP(data.getP() + "");
        if (data.getQ() != null) psVrIotStDeviceEntity.setQ(data.getQ() + "");
        if (data.getPa() != null) psVrIotStDeviceEntity.setPa(data.getPa() + "");
        if (data.getPb() != null) psVrIotStDeviceEntity.setPb(data.getPb() + "");
        if (data.getPc() != null) psVrIotStDeviceEntity.setPc(data.getPc() + "");
        if (data.getQa() != null) psVrIotStDeviceEntity.setQa(data.getQa() + "");
        if (data.getQb() != null) psVrIotStDeviceEntity.setQb(data.getQb() + "");
        if (data.getQc() != null) psVrIotStDeviceEntity.setQc(data.getQc() + "");
        if (data.getC() != null) psVrIotStDeviceEntity.setC(data.getC() + "");

        if (data.getUa() != null) psVrIotStDeviceEntity.setUa(data.getUa() + "");
        if (data.getUb() != null) psVrIotStDeviceEntity.setUb(data.getUb() + "");
        if (data.getUc() != null) psVrIotStDeviceEntity.setUc(data.getUc() + "");

        if (data.getIa() != null) psVrIotStDeviceEntity.setIa(data.getIa() + "");
        if (data.getIb() != null) psVrIotStDeviceEntity.setIb(data.getIb() + "");
        if (data.getIc() != null) psVrIotStDeviceEntity.setIc(data.getIc() + "");

        if (data.getRb() != null) psVrIotStDeviceEntity.setRb(data.getRb() + "");
        if (data.getRf() != null) psVrIotStDeviceEntity.setRf(data.getRf() + "");
        if (data.getRv() != null) psVrIotStDeviceEntity.setRv(data.getRv() + "");
        if (data.getRc() != null) psVrIotStDeviceEntity.setRc(data.getRc() + "");
        if (data.getSoc() != null) psVrIotStDeviceEntity.setSoc(data.getSoc() + "");
        if (data.getRm() != null) psVrIotStDeviceEntity.setRm(data.getRm() + "");
        if (data.getIo() != null) psVrIotStDeviceEntity.setSoc(data.getIo() + "");
        if (data.getUo() != null) psVrIotStDeviceEntity.setRm(data.getUo() + "");
    }

    /**
     * @param psVrIotStDeviceEntityList 数据列表
     * @param fieldExtractor            字段提取器
     * @return
     * @desc 计算平均值
     * @date 2024/3/1 13:37
     */
    public static BigDecimal calculateAverage(List<PsVrIotStDeviceEntity> psVrIotStDeviceEntityList, Function<PsVrIotStDeviceEntity, String> fieldExtractor) {
        // 如果列表为空，则返回0.0
        if (psVrIotStDeviceEntityList == null || psVrIotStDeviceEntityList.isEmpty()) {
            return BigDecimal.ZERO;
        }

        // 使用Stream计算字段的总和
        BigDecimal total = psVrIotStDeviceEntityList.stream()
                .map(fieldExtractor) // 获取字段值
                .map(value -> {
                    // 将字段值转换为BigDecimal，如果为空则返回0
                    return value != null ? new BigDecimal(value) : BigDecimal.ZERO;
                })
                .reduce(BigDecimal.ZERO, BigDecimal::add); // 将所有字段值求和

        // 计算平均值
        BigDecimal average = total.divide(BigDecimal.valueOf(psVrIotStDeviceEntityList.size()), 4, BigDecimal.ROUND_DOWN);

        return average;
    }

    public StrategyDispatchForSpecificResp getStrategyPlanForSpecific(StrategyDispatchReq req) {
        log.info("getStrategyPlanForSpecific 入参：{}", JSONUtil.toJsonStr(req));
        TVrDispatchPlanEntity plan = tvrDispatchPlanService.lambdaQuery()
                .ge(TVrDispatchPlanEntity::getCreateTime, DateUtil.parse(DateUtil.format(DateTime.now(), "yyyy-MM-dd")))
                .ge(TVrDispatchPlanEntity::getStartTime, DateUtil.parse(DateUtil.format(DateTime.now(), "yyyy-MM-dd")))
                .eq(TVrDispatchPlanEntity::getPowerAccount, req.getPowerAccount())
                .eq(TVrDispatchPlanEntity::getConfirmFlag, 0)
                .eq(TVrDispatchPlanEntity::getDelFlag, 0).one();
        if (plan == null) {
            return null;
        }
        StrategyDispatchForSpecificResp resp = new StrategyDispatchForSpecificResp();
        resp.setPowerAccount(req.getPowerAccount());
        StrategyDispatchPlanForSpecificResp planResp = new StrategyDispatchPlanForSpecificResp();
        planResp.setType("0");
        BeanUtils.copyProperties(plan, planResp);
        resp.setPlan(planResp);
        BaseLineResp baseLineResp = new BaseLineResp();
        //todo 基线
        resp.setBaseline(baseLineResp);
        return resp;
    }
}