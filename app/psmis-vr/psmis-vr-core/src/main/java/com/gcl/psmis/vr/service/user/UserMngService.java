package com.gcl.psmis.vr.service.user;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.date.DateUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.gcl.framework.data.holder.CurrentUserHolder;
import com.gcl.psmis.framework.common.exception.BussinessException;
import com.gcl.psmis.framework.common.req.vr.user.UserCreateReq;
import com.gcl.psmis.framework.common.req.vr.user.UserQueryReq;
import com.gcl.psmis.framework.common.vo.vr.api.ResponseDTO;
import com.gcl.psmis.framework.common.vo.vr.user.UserDetailVO;
import com.gcl.psmis.framework.common.vo.vr.user.UserListVO;
import com.gcl.psmis.framework.common.vo.vr.user.UserLoadTrendVO;
import com.gcl.psmis.framework.mbg.entity.BaseRoleEntity;
import com.gcl.psmis.framework.mbg.entity.TRegionEntity;
import com.gcl.psmis.framework.mbg.entity.TVrElectricalPicEntity;
import com.gcl.psmis.framework.mbg.entity.TVrUserEntity;
import com.gcl.psmis.framework.mbg.mapper.TRegionMapper;
import com.gcl.psmis.framework.mbg.mapper.TVrElectricalPicMapper;
import com.gcl.psmis.framework.mbg.mapper.TVrUserMapper;
import com.gcl.psmis.framework.mbg.service.BaseRoleService;
import com.gcl.psmis.framework.mbg.service.TVrElectricalPicService;
import com.gcl.psmis.framework.mbg.service.TVrUserService;
import com.gcl.psmis.framework.taos.domain.entity.PsVrIot15StDeviceEntity;
import com.gcl.psmis.framework.taos.wrapper.TdWrappers;
import com.gcl.psmis.vr.dao.user.UserMngDao;
import com.gcl.psmis.vr.service.resource.ResourceService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;

/**
 * @className: UserMngService
 * @author: wuchenyu
 * @description:
 * @date: 2024/2/26 15:50
 * @version: 1.0
 */
@Slf4j
@Service
public class UserMngService {

    @Autowired
    private UserMngDao userMngDao;

    @Autowired
    private TVrUserMapper tVrUserMapper;

    @Autowired
    private TVrElectricalPicMapper tVrElectricalPicMapper;

    @Autowired
    private TVrElectricalPicService tVrElectricalPicService;

    @Autowired
    private ResourceService resourceService;

    @Autowired
    private TVrUserService tVrUserService;

    @Autowired
    private TRegionMapper tRegionMapper;

    @Autowired
    private BaseRoleService baseRoleService;


    public IPage<UserListVO> getUserList(Page<UserListVO> page, UserQueryReq params) {

        // 获取用户信息
        log.info("当前登陆人姓名:{}", CurrentUserHolder.getUserDTOThreadLocal().getUserName());
        boolean vppsz = !baseRoleService.lambdaQuery()
                .in(BaseRoleEntity::getFId, CurrentUserHolder.getUserDTOThreadLocal().getRoleIds())
                .eq(BaseRoleEntity::getFEncode, "vppsz").list().isEmpty();
        if (vppsz) {
            params.setSzFlag(1);
        }
        return userMngDao.getUserList(page, params);
    }


    public void addUser(UserCreateReq params) {

        if (StringUtils.isNotBlank(params.getRemark())) {
            if (params.getRemark().length() > 30) {
                throw new BussinessException("备注长度不能超过30位");
            }
        }
        if (params.getAncillaryServiceCustomers() == 0 && params.getDemandResponseUsers() == 0) {
            throw new BussinessException("不能既不是电力辅助服务用户也不是需求侧响应用户");
        }
        TVrUserEntity tVrUserEntity = tVrUserMapper.selectOne(new LambdaQueryWrapper<TVrUserEntity>().eq(TVrUserEntity::getAbbAccount, params.getAbbAccount()).eq(TVrUserEntity::getDelFlag, 0));
        if (tVrUserEntity != null) {
            throw new BussinessException("简称首字母已存在");
        }
        TVrUserEntity user = new TVrUserEntity();
        String userCode = this.generatorCode(params);
        user.setCode(userCode);
        BeanUtils.copyProperties(params, user);
        List<String> picList = params.getPicList();
        int count = tVrUserMapper.insert(user);
        if (count <= 0) {
            throw new BussinessException("新增用户失败");
        }

        if (StringUtils.isNotBlank(params.getThirdId())) {
            // 新增用户成功 // http://124.71.128.41:85/apitest/rest/syncRecource?COM_NO=我们平台的用户id&mpNo=我们平台的sn编号&code=你们平台生成的code
            ResponseDTO responseDTO = resourceService.getRequire(params.getThirdId(), null, userCode);
            // 判断响应结果
            if (!responseDTO.getResultCode().equals("1000")) {
                throw new BussinessException("接口调用错误");
            }
        }


        List<TVrElectricalPicEntity> tVrElectricalPicEntityList = new ArrayList<>();
        for (String pic : picList) {
            TVrElectricalPicEntity tVrElectricalPicEntity = new TVrElectricalPicEntity();
            tVrElectricalPicEntity.setSourceType(0);
            tVrElectricalPicEntity.setSourceId(user.getId());
            tVrElectricalPicEntity.setUrl(pic);
            tVrElectricalPicEntityList.add(tVrElectricalPicEntity);
        }
        tVrElectricalPicService.saveBatch(tVrElectricalPicEntityList);
    }

    public void updateUser(UserCreateReq params) {

        if (StringUtils.isNotBlank(params.getRemark())) {
            if (params.getRemark().length() > 30) {
                throw new BussinessException("备注长度不能超过30位");
            }
        }

        if (params.getAncillaryServiceCustomers() == 0 && params.getDemandResponseUsers() == 0) {
            throw new BussinessException("不能既不是电力辅助服务用户也不是需求侧响应用户");
        }
        TVrUserEntity user = tVrUserMapper.selectById(params.getId());
        if (user == null) {
            throw new BussinessException("参数错误 用户不存在");
        }
        String oldAbbAccount = user.getAbbAccount();
        BeanUtils.copyProperties(params, user);
        user.setRemark(params.getRemark() == null ? "" : params.getRemark());
        user.setThirdId(params.getThirdId() == null ? "" : params.getThirdId());
        user.setAbbAccount(oldAbbAccount);
        tVrUserMapper.updateById(user);

        // 深圳的暂时不加校验
        TVrUserEntity tVrUserEntity = tVrUserService.lambdaQuery()
                .eq(TVrUserEntity::getId, params.getId())
                .eq(TVrUserEntity::getDelFlag, 0)
                .ne(TVrUserEntity::getProvinceId, 44)
                .ne(TVrUserEntity::getCityId, 4585)
                .one();

        // 更新用户成功 // http://124.71.128.41:85/apitest/rest/syncRecource?COM_NO=我们平台的用户id&mpNo=我们平台的sn编号&code=你们平台生成的code
        if (tVrUserEntity != null && StringUtils.isNotBlank(tVrUserEntity.getThirdId())) {
            ResponseDTO responseDTO = resourceService.getRequire(tVrUserEntity.getThirdId(), null, tVrUserEntity.getCode());
            // 判断响应结果
            if (!responseDTO.getResultCode().equals("1000")) {
                throw new BussinessException("接口调用错误");
            }
        }

        userMngDao.deletePic(user.getId());
        List<String> picList = params.getPicList();
        List<TVrElectricalPicEntity> tVrElectricalPicEntityList = new ArrayList<>();
        for (String pic : picList) {
            TVrElectricalPicEntity tVrElectricalPicEntity = new TVrElectricalPicEntity();
            tVrElectricalPicEntity.setSourceType(0);
            tVrElectricalPicEntity.setSourceId(user.getId());
            tVrElectricalPicEntity.setUrl(pic);
            tVrElectricalPicEntityList.add(tVrElectricalPicEntity);
        }
        tVrElectricalPicService.saveBatch(tVrElectricalPicEntityList);

    }

    public UserDetailVO getUserDetail(Long userId) {
        return userMngDao.getUserDetail(userId);
    }


    public String generatorCode(UserCreateReq params) {
        String userCategory = params.getUserCategory();
        Long regionId = params.getRegionId();
        TRegionEntity region = tRegionMapper.selectById(regionId);
        if (region == null) {
            throw new BussinessException("区id错误");
        }
        String adCode = region.getAdCode();
        String abbAccount = params.getAbbAccount();
        int requireType = getUserRequireType(params);
        String maxPeakValue = handleValue(params.getMaxPeakValue());
        String maxPeakReduction = handleValue(params.getMaxPeakReduction());
        String regulationDuration = Character.toString(params.getRegulationDuration().charAt(params.getRegulationDuration().length() - 1));

        StringBuilder stringBuilder = new StringBuilder();

        String code = stringBuilder.append("GCL").append(adCode).append(abbAccount).append(requireType).append("T").append(maxPeakValue).append("X").append(maxPeakReduction).append(userCategory).append(regulationDuration).toString();

        return code;
    }

    private int getUserRequireType(UserCreateReq params) {
        int ancillaryServiceCustomers = params.getAncillaryServiceCustomers();
        int demandResponseUsers = params.getDemandResponseUsers();
        if (ancillaryServiceCustomers == 1 && demandResponseUsers == 1) {
            return 3;
        } else if (ancillaryServiceCustomers == 1 && demandResponseUsers == 0) {
            return 1;
        } else if (ancillaryServiceCustomers == 0 && demandResponseUsers == 1) {
            return 2;
        } else {
            throw new BussinessException("参数错误");
        }
    }

    private String handleValue(String val) {
        Double d = Double.valueOf(val);
        String result = String.valueOf(d / 1000);
        int i = result.indexOf(".");
        String s = Character.toString(result.charAt(i - 1)) + Character.toString(result.charAt(i + 1));
        return s;
    }


    public List<UserLoadTrendVO> getLoadTrend(String code, String date) {

        String today = DateUtil.today();

        String startTime = "00:00:00";
        String endTime = "23:59:59";

        if (today.equals(date)) {
            String now = DateUtil.now();
            endTime = now.substring(11, 19);
        }

        List<PsVrIot15StDeviceEntity> list = TdWrappers.lambdaQuery(PsVrIot15StDeviceEntity.class)
                .eq(PsVrIot15StDeviceEntity::getUserResourceSn, code)
                .ge(PsVrIot15StDeviceEntity::getTs, date + " " + startTime)
                .le(PsVrIot15StDeviceEntity::getTs, date + " " + endTime)
                .eq(PsVrIot15StDeviceEntity::getResourceType, 1)
                .list();
        if (CollUtil.isEmpty(list)) {
            return null;
        }
        List<UserLoadTrendVO> loadTrendList = new ArrayList<>();
        String now = DateUtil.now();
        for (PsVrIot15StDeviceEntity psVrIot15StDeviceEntity : list) {

            String ts = psVrIot15StDeviceEntity.getTs();
            UserLoadTrendVO userLoadTrendVO = new UserLoadTrendVO();
            userLoadTrendVO.setLoad(psVrIot15StDeviceEntity.getLoad() == null ? null : new BigDecimal(psVrIot15StDeviceEntity.getLoad()).setScale(2, BigDecimal.ROUND_HALF_UP).toString());
            userLoadTrendVO.setIo(psVrIot15StDeviceEntity.getUo() == null ? null : new BigDecimal(psVrIot15StDeviceEntity.getIo()).setScale(2, BigDecimal.ROUND_HALF_UP).toString());
            userLoadTrendVO.setUo(psVrIot15StDeviceEntity.getUo() == null ? null : new BigDecimal(psVrIot15StDeviceEntity.getUo()).setScale(2, BigDecimal.ROUND_HALF_UP).toString());

            userLoadTrendVO.setQ(psVrIot15StDeviceEntity.getQ() == null ? null : new BigDecimal(psVrIot15StDeviceEntity.getQ()).setScale(2, BigDecimal.ROUND_HALF_UP).toString());
            userLoadTrendVO.setPa(psVrIot15StDeviceEntity.getPa() == null ? null : new BigDecimal(psVrIot15StDeviceEntity.getPa()).setScale(2, BigDecimal.ROUND_HALF_UP).toString());
            userLoadTrendVO.setPb(psVrIot15StDeviceEntity.getPb() == null ? null : new BigDecimal(psVrIot15StDeviceEntity.getPb()).setScale(2, BigDecimal.ROUND_HALF_UP).toString());
            userLoadTrendVO.setPc(psVrIot15StDeviceEntity.getPc() == null ? null : new BigDecimal(psVrIot15StDeviceEntity.getPc()).setScale(2, BigDecimal.ROUND_HALF_UP).toString());
            userLoadTrendVO.setQa(psVrIot15StDeviceEntity.getQa() == null ? null : new BigDecimal(psVrIot15StDeviceEntity.getQa()).setScale(2, BigDecimal.ROUND_HALF_UP).toString());
            userLoadTrendVO.setQb(psVrIot15StDeviceEntity.getQb() == null ? null : new BigDecimal(psVrIot15StDeviceEntity.getQb()).setScale(2, BigDecimal.ROUND_HALF_UP).toString());
            userLoadTrendVO.setQc(psVrIot15StDeviceEntity.getQc() == null ? null : new BigDecimal(psVrIot15StDeviceEntity.getQc()).setScale(2, BigDecimal.ROUND_HALF_UP).toString());
            userLoadTrendVO.setUa(psVrIot15StDeviceEntity.getUa() == null ? null : new BigDecimal(psVrIot15StDeviceEntity.getUa()).setScale(2, BigDecimal.ROUND_HALF_UP).toString());
            userLoadTrendVO.setUb(psVrIot15StDeviceEntity.getUb() == null ? null : new BigDecimal(psVrIot15StDeviceEntity.getUb()).setScale(2, BigDecimal.ROUND_HALF_UP).toString());
            userLoadTrendVO.setUc(psVrIot15StDeviceEntity.getUc() == null ? null : new BigDecimal(psVrIot15StDeviceEntity.getUc()).setScale(2, BigDecimal.ROUND_HALF_UP).toString());
            userLoadTrendVO.setIa(psVrIot15StDeviceEntity.getIa() == null ? null : new BigDecimal(psVrIot15StDeviceEntity.getIa()).setScale(2, BigDecimal.ROUND_HALF_UP).toString());
            userLoadTrendVO.setIb(psVrIot15StDeviceEntity.getIb() == null ? null : new BigDecimal(psVrIot15StDeviceEntity.getIb()).setScale(2, BigDecimal.ROUND_HALF_UP).toString());
            userLoadTrendVO.setIc(psVrIot15StDeviceEntity.getIc() == null ? null : new BigDecimal(psVrIot15StDeviceEntity.getIc()).setScale(2, BigDecimal.ROUND_HALF_UP).toString());

            userLoadTrendVO.setDateTime(ts);
            userLoadTrendVO.setTime(ts.substring(11, 16));//时分  18:18
            userLoadTrendVO.setNow(now);
            loadTrendList.add(userLoadTrendVO);
        }

        return loadTrendList;
    }

}
