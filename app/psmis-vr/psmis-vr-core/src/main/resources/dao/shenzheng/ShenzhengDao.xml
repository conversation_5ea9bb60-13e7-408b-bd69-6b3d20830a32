<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.gcl.psmis.vr.dao.shenzheng.ShenzhengVppDao">

    <sql id="select">
        SELECT i.id,
               i.reply_result,
               CASE
                   WHEN i.reply_result = 1 THEN
                       '参加'
                   WHEN i.reply_result = 0 THEN
                       '不参加'
                   ELSE '未填报'
                   END        AS reply_result_name,
               exchange_type,
               invitation_time,
               i.create_time,
               reply_time,
               invitation_id,
               '广东省深圳市' as address,
               c.name            aggregatorCompanyName,
               c.credit_code,
               start_time,
               end_time
        from t_vr_aggregator_invitation i
                 left join t_vr_aggregator_company c on i.credit_code = c.credit_code
        where i.del_flag = 0
    </sql>
    <select id="getInvitationList" resultType="com.gcl.psmis.vr.domain.vo.GetInvitationListVO">
        <include refid="select"/>
        <if test="req.startTime!= null and req.startTime!=''">
            AND i.invitation_time &gt;=#{req.startTime}
        </if>
        <if test="req.endTime!= null and req.endTime!=''">
            AND i.invitation_time &lt;= #{req.endTime}
        </if>
        <if test="req.invitationId!= null and req.invitationId!=''">
            AND i.invitation_id like concat ('%',#{req.invitationId},'%')
        </if>
        <if test="req.exchangeType!= null and req.exchangeType!=''">
            AND i.exchange_type = #{req.exchangeType}
        </if>
        <if test="req.creditCode!= null and req.creditCode!=''">
            AND i.credit_code = #{req.creditCode}
        </if>
        <if test="req.replyResult!= null">
            AND i.reply_result = #{req.replyResult}
        </if>
    </select>
    <select id="getById" resultType="com.gcl.psmis.vr.domain.vo.GetInvitationListVO">
        <include refid="select"/>
        AND i.invitation_id = #{invitationId}
    </select>
    <select id="getInvitationPlan" resultType="com.gcl.psmis.vr.domain.vo.InvitationPlanVo">
        SELECT distinct r.user_name AS companyName
        FROM t_vr_agg_invitation_plan p
                 LEFT JOIN t_vr_resource r ON p.resource_code = r.code
        WHERE line_type = 2
          AND p.del_flag = 0
          and r.parent_resource_id is null
          and p.invitation_id = #{invitationId}
    </select>
    <select id="getInvitationPlanDetails" resultType="com.gcl.psmis.vr.domain.vo.InvitationPlanDetailVo">
        SELECT p.*,
               r.resource_name AS resourceName
        FROM t_vr_agg_invitation_plan p
                 LEFT JOIN t_vr_resource r ON p.resource_code = r.code
        WHERE line_type = 2
          AND p.del_flag = 0
          and r.parent_resource_id is null
          and r.user_name = #{companyName}
          and p.invitation_id = #{invitationId}
    </select>
    <select id="getUserByCreditCode" resultType="java.lang.String">
        SELECT u.`code`
        FROM t_vr_aggregator_company c
                 LEFT JOIN t_vr_aggregator_company c2 ON c.id = c2.parent_id
                 LEFT JOIN t_vr_user u ON u.company_credit_code = c2.credit_code
        WHERE c2.`level` = 2
          AND c.credit_code = #{creditCode}

    </select>

    <select id="getPsVrIotStDeviceList" resultType="com.gcl.psmis.framework.taos.domain.entity.PsVrIotStDeviceEntity">

        SELECT
        sum( `etd` ) AS `etd`,
        sum( `eto` ) AS `eto`,
        sum( `load` ) AS `load`,
        sum( `p` ) AS `p`,
        sum( `q` ) AS `q`,
        sum( `pa` ) AS `pa`,
        sum( `pb` ) AS `pb`,
        sum( `pc` ) AS `pc`,
        sum( `qa` ) AS `qa`,
        sum( `qb` ) AS `qb`,
        sum( `qc` ) AS `qc`,
        sum( `c` ) AS `c`,
        sum( `ua` ) AS `ua`,
        sum( `ub` ) AS `ub`,
        sum( `uc` ) AS `uc`,
        sum( `ia` ) AS `ia`,
        sum( `ib` ) AS `ib`,
        sum( `ic` ) AS `ic`,
        sum( `rb` ) AS `rb`,
        sum( `rf` ) AS `rf`,
        sum( `rv` ) AS `rv`,
        sum( `rc` ) AS `rc`,
        sum( `io` ) AS `io`,
        sum( `uo` ) AS uo,
        ts
        FROM
        (
        SELECT
        last ( etd ) AS etd,
        last ( eto ) AS eto,
        last ( LOAD ) AS LOAD,
        last ( `p` ) AS `p`,
        last ( `q` ) AS `q`,
        last ( `pa` ) AS `pa`,
        last ( `pb` ) AS `pb`,
        last ( `pc` ) AS `pc`,
        last ( `qa` ) AS `qa`,
        last ( `qb` ) AS `qb`,
        last ( `qc` ) AS `qc`,
        last ( `c` ) AS `c`,
        last ( `ua` ) AS `ua`,
        last ( `ub` ) AS `ub`,
        last ( `uc` ) AS `uc`,
        last ( `ia` ) AS `ia`,
        last ( `ib` ) AS `ib`,
        last ( `ic` ) AS `ic`,
        last ( `rb` ) AS `rb`,
        last ( `rf` ) AS `rf`,
        last ( `rv` ) AS `rv`,
        last ( `rc` ) AS `rc`,
        last ( `io` ) AS `io`,
        last ( `uo` ) AS uo,
        _wstart AS ts
        FROM
        ps_vr_iot15.st_device
        <where>
            AND resource_type = 1
            <if test="req.startTime!= null and req.startTime!=''">
                AND ts &gt;=#{req.startTime}
            </if>
            <if test="req.endTime!= null and req.endTime!=''">
                AND ts &lt;= #{req.endTime}
            </if>
            <if test="req.resourceSns !=null and req.resourceSns.size()>0">
                AND user_resource_sn in
                <foreach collection="req.resourceSns" index="index" item="item" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </if>
        </where>
        PARTITION BY tbname INTERVAL ( 15m ) fill ( NULL )
        ) c PARTITION BY ts
        ORDER BY
        ts
    </select>

</mapper>