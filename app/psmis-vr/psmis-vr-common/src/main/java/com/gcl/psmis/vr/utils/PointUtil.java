package com.gcl.psmis.vr.utils;

import com.gcl.psmis.framework.common.exception.BussinessException;
import com.gcl.psmis.vr.enums.PointEnum;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;

import java.lang.reflect.Field;
import java.math.BigDecimal;

/**
 * @description: 96点数工具类
 * @author: liujinjing
 * @date: 2025/5/30 15:58
 */
@Slf4j
public class PointUtil {

    /**
     * 设置对应point的val
     */
    public static <T> void setPointValue(String time, String val, T entity) {
        String code = PointEnum.getCodeByName(time);
        if (StringUtils.isEmpty(code)) {
            throw new BussinessException("未获取到96点数对应标识！");
        }

        //
        Class<?> entityClass = entity.getClass();
        Field point = null;
        try {
            point = entityClass.getDeclaredField("point" + code);
            point.setAccessible(true);

            point.set(entity, new BigDecimal(val));
        } catch (Exception e) {
            log.error(e.getMessage(), e);
        }

    }
}
