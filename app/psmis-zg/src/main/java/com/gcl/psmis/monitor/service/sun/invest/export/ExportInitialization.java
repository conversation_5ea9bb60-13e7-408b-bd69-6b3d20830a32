package com.gcl.psmis.monitor.service.sun.invest.export;

import com.gcl.psmis.monitor.constants.enums.ExportEnum;
import lombok.Data;
import org.springframework.beans.BeansException;
import org.springframework.beans.factory.config.BeanPostProcessor;
import org.springframework.stereotype.Component;

import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;

@Component
@Data
/**
 * @description: 导出
 * @author: liujinjing
 * @date: 2025/6/10 16:23
 */
public class ExportInitialization implements BeanPostProcessor {

    // 存储所有AbstractCostAnalysisExportService子类实例
    public static final Map<ExportEnum, AbstractCostAnalysisExportService> exportServices = new ConcurrentHashMap<>();

    @Override
    public Object postProcessAfterInitialization(Object bean, String beanName) throws BeansException {
        // 检查是否是AbstractCostAnalysisExportService的子类
        if (bean instanceof AbstractCostAnalysisExportService) {
            AbstractCostAnalysisExportService exportService = (AbstractCostAnalysisExportService) bean;

            // 添加到集合中
            exportServices.put(exportService.getExportEnum(), exportService);
        }
        return bean;
    }
}
