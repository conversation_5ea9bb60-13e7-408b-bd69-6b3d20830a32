package com.gcl.psmis.monitor.entity.request.invest.income;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.util.List;

/**
 * 筛选安装形式入参
 **/
@Data
@ApiModel(value = "筛选安装形式入参", description = "筛选安装形式入参")
public class SelectInstallFormRequest {

    @ApiModelProperty("安装场景ID")
    @NotEmpty(message = "安装场景必选")
    private List<Integer> sceneId;

    @ApiModelProperty("组件功率")
    @NotEmpty(message = "组件功率必选")
    private List<Integer> zuPower;

    @ApiModelProperty("省份ID")
    @NotNull(message = "省份必选")
    private Integer provinceId;

    @ApiModelProperty("城市ID")
    private Integer cityId;

    @ApiModelProperty("资方报价版本ID")
    private Integer quotaVersionId;

}
