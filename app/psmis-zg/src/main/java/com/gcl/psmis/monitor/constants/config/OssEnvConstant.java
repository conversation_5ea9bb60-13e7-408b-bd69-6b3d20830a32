package com.gcl.psmis.monitor.constants.config;

import lombok.Data;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Configuration;

/**
 * @ClassName OssEnvConstant
 * @Description 默认配置常量类
 * <AUTHOR>
 * @Date 2023/8/3 18:10
 **/
@Data
@Configuration
public class OssEnvConstant {
    /**
     * todo
     * 测试环境固定值
     * 提交前去掉默认值
     */
    /*@Value("${oss-url.tianyi1:https://gclhytest.obs.cn-jssz1.ctyun.cn/}")
    private String tianyi;

    @Value("${oss-url.bucket1:gclhytest}")
    private String bucket;*/

    @Value("${oss.tianyi}")
    private String tianyi;

    @Value("${oss.bucket}")
    private String bucket;

}
