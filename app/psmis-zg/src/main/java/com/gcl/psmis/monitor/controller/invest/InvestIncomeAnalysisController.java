package com.gcl.psmis.monitor.controller.invest;

import cn.hutool.core.collection.CollUtil;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.gcl.psmis.framework.common.ResponseResult;
import com.gcl.psmis.framework.common.annotation.PostGclApiByToken;
import com.gcl.psmis.framework.common.vo.invest.InstallFormVO;
import com.gcl.psmis.monitor.constants.ApiConstant;
import com.gcl.psmis.monitor.constants.enums.ExportEnum;
import com.gcl.psmis.monitor.entity.request.invest.income.DeviceCostRequest;
import com.gcl.psmis.monitor.entity.request.invest.income.RevenueCalcRequest;
import com.gcl.psmis.monitor.entity.request.invest.income.RevenueContrastRequest;
import com.gcl.psmis.monitor.entity.request.invest.income.SelectInstallFormRequest;
import com.gcl.psmis.monitor.entity.vo.invest.income.DeviceCostVO;
import com.gcl.psmis.monitor.entity.vo.invest.income.RevenueCalcVO;
import com.gcl.psmis.monitor.entity.vo.invest.income.RevenueContrastVO;
import com.gcl.psmis.monitor.service.sun.invest.InvestIncomeAnalysisService;
import com.gcl.psmis.monitor.service.sun.invest.export.ExportInitialization;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.validation.Valid;
import java.util.List;

/**
 * @ClassName PowerStationCostAnalysisController
 * @Description 投资收益分析控制层
 * @Date 2024/6/5
 **/
@Slf4j
@RestController
@Api(tags = "投资收益分析API(新)")
@RequestMapping(ApiConstant.ZG_INVEST_INCOME_ANALYSIS)
public class InvestIncomeAnalysisController {

    @Autowired
    private InvestIncomeAnalysisService investIncomeAnalysisService;

    @ApiOperation("根据安装场景、组件功率获取安装形式")
    @PostGclApiByToken("/getInstallForm")
    public List<InstallFormVO> getInstallForm(@RequestBody @Valid SelectInstallFormRequest request) {
        return investIncomeAnalysisService.getInstallForm(request);
    }

    @ApiOperation("投资收益对比查询")
    @PostGclApiByToken("/revenueContrastList")
    public List<RevenueContrastVO> revenueContrastList(@RequestBody @Valid RevenueContrastRequest request) {
        return investIncomeAnalysisService.revenueContrastList(request);
    }

    @ApiOperation("投资收益对比导出")
    @PostGclApiByToken("/exportRevenueContrast")
    public ResponseResult exportRevenueContrast(@RequestBody @Valid RevenueContrastRequest request) {
        return investIncomeAnalysisService.exportRevenueContrast(request);
    }

    @ApiOperation("根据安装场景、组件功率获取安装形式，排除资方没有的")
    @PostGclApiByToken("/getInstallFormForCapital")
    public List<InstallFormVO> getInstallFormForCapital(@RequestBody @Valid SelectInstallFormRequest request) {
        return investIncomeAnalysisService.getInstallFormForCapital(request);
    }

    @ApiOperation("投资收益测算查询")
    @PostGclApiByToken("/revenueCalcPage")
    public IPage<RevenueCalcVO> revenueCalcPage(@RequestBody @Valid RevenueCalcRequest request) {
        return investIncomeAnalysisService.revenueCalcPage(new Page<>(request.getPageNum(), request.getPageSize()), request);
    }

    @ApiOperation("投资收益测算导出")
    @PostGclApiByToken("/exportRevenueCalc")
    public ResponseResult exportRevenueCalc(@RequestBody @Valid RevenueCalcRequest request) {
        return investIncomeAnalysisService.exportRevenueCalc(request);
    }

    @ApiOperation("设备成本查询")
    @PostGclApiByToken("/deviceCostPage")
    public IPage<DeviceCostVO> deviceCostPage(@RequestBody @Valid DeviceCostRequest request) {
        return investIncomeAnalysisService.deviceCostPage(new Page<>(request.getPageNum(), request.getPageSize()), request);
    }

    @ApiOperation("设备成本导出")
    @PostGclApiByToken("/exportDeviceCost")
    public ResponseResult exportDeviceCost(@RequestBody @Valid DeviceCostRequest request) {
        return ExportInitialization.exportServices.get(ExportEnum.DEVICE_COST).export(request);
    }

}
