package com.gcl.psmis.monitor.service.sun.invest;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.date.DateTime;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.gcl.psmis.framework.common.exception.BussinessException;
import com.gcl.psmis.framework.mbg.entity.TModelPsCostEntity;
import com.gcl.psmis.framework.mbg.entity.TUserEntity;
import com.gcl.psmis.framework.mbg.service.TUserService;
import com.gcl.psmis.monitor.dao.sun.invmod.InvestmentModelDao;
import com.gcl.psmis.monitor.entity.dto.invest.costAnalysis.CostAnalysisDTO;
import com.gcl.psmis.monitor.entity.vo.invest.costAnalysis.ActualCostVO;
import com.gcl.psmis.monitor.entity.vo.invest.costAnalysis.CompareCostVO;
import com.gcl.psmis.monitor.entity.vo.invest.costAnalysis.TheoryCostVO;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import java.util.Date;
import java.util.List;
import java.util.stream.Collectors;

/**
 * @description: 电站成本分析Service
 * @author: liujinjing
 * @date: 2025/6/6 9:22
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class PowerStationCostAnalysisService {
    private final TUserService tUserService;

    private final InvestmentModelDao investmentModelDao;

    public IPage<TheoryCostVO> theoryCostPage(IPage<TModelPsCostEntity> page, CostAnalysisDTO dto) {
        //查询电站成本表获取理论信息
        IPage<TModelPsCostEntity> psCostEntityIPage = getPsCostEntityList(page, dto);

        List<TModelPsCostEntity> psCostEntityList = psCostEntityIPage.getRecords();
        List<TheoryCostVO> theoryCostVOList = BeanUtil.copyToList(psCostEntityList, TheoryCostVO.class);

        if (CollectionUtils.isEmpty(theoryCostVOList)) {
            return new Page<>();
        }

        theoryCostVOList.forEach(theoryCostVO -> {
            theoryCostVO.setMixFlagName(theoryCostVO.getMixFlag() == 1 ? "是" : "否");
            theoryCostVO.setProvinceCityRegion(theoryCostVO.getProvinceName() + theoryCostVO.getCityName()
                    + theoryCostVO.getRegionName());
        });

        IPage<TheoryCostVO> theoryCostVOIPage = new Page<>();
        theoryCostVOIPage.setSize(dto.getPageSize());
        theoryCostVOIPage.setCurrent(dto.getPageNum());
        theoryCostVOIPage.setRecords(theoryCostVOList);
        theoryCostVOIPage.setTotal(psCostEntityIPage.getTotal());

        return theoryCostVOIPage;
    }

    public IPage<ActualCostVO> actualCostPage(IPage<TModelPsCostEntity> page, CostAnalysisDTO dto) {
        dto.setPsStatus("已并网");
        //查询电站成本表获取实际信息
        IPage<TModelPsCostEntity> psCostEntityIPage = getPsCostEntityList(page, dto);
        List<TModelPsCostEntity> psCostEntityList = psCostEntityIPage.getRecords();

        List<ActualCostVO> actualCostVOList = BeanUtil.copyToList(psCostEntityList, ActualCostVO.class);
        if (CollectionUtils.isEmpty(actualCostVOList)) {
            return new Page<>();
        }

        actualCostVOList.forEach(actualCostVO -> {
            actualCostVO.setMixFlagName(actualCostVO.getMixFlag() == 1 ? "是" : "否");
            actualCostVO.setProvinceCityRegion(actualCostVO.getProvinceName() + actualCostVO.getCityName()
                    + actualCostVO.getRegionName());
        });

        IPage<ActualCostVO> actualCostVOIPage = new Page<>();
        actualCostVOIPage.setSize(dto.getPageSize());
        actualCostVOIPage.setCurrent(dto.getPageNum());
        actualCostVOIPage.setRecords(actualCostVOList);
        actualCostVOIPage.setTotal(psCostEntityIPage.getTotal());

        return actualCostVOIPage;
    }

    public IPage<CompareCostVO> compareCostPage(IPage<TModelPsCostEntity> page, CostAnalysisDTO dto) {
        dto.setPsStatus("已并网");
        //查询电站成本表获取实际和理论信息
        IPage<TModelPsCostEntity> psCostEntityIPage = getPsCostEntityList(page, dto);
        List<TModelPsCostEntity> psCostEntityList = psCostEntityIPage.getRecords();

        List<CompareCostVO> compareCostVOList = BeanUtil.copyToList(psCostEntityList, CompareCostVO.class);
        if (CollectionUtils.isEmpty(compareCostVOList)) {
            return new Page<>();
        }

        compareCostVOList.forEach(compareCostVO -> {
            compareCostVO.setMixFlagName(compareCostVO.getMixFlag() == 1 ? "是" : "否");
            compareCostVO.setProvinceCityRegion(compareCostVO.getProvinceName() + compareCostVO.getCityName()
                    + compareCostVO.getRegionName());
        });

        IPage<CompareCostVO> compareCostVOIPage = new Page<>();
        compareCostVOIPage.setSize(dto.getPageSize());
        compareCostVOIPage.setCurrent(dto.getPageNum());
        compareCostVOIPage.setRecords(compareCostVOList);
        compareCostVOIPage.setTotal(psCostEntityIPage.getTotal());

        return compareCostVOIPage;
    }

    /**
     * @description: 查询电站成本表获取信息
     * @param:CostAnalysisDTO
     * @return: IPage<TModelPsCostEntity>
     * <AUTHOR>
     * @date: 2025/6/6 15:29
     */
    private IPage<TModelPsCostEntity> getPsCostEntityList(IPage<TModelPsCostEntity> page, CostAnalysisDTO dto) {

        //成本年月不能超过当月
        DateTime costYearMonthDateTime = DateUtil.parse(dto.getCostYearMonth(), "yyyy/MM");
        DateTime nowDateTime = DateUtil.beginOfMonth(new Date());
        if (costYearMonthDateTime.isAfterOrEquals(nowDateTime)) {
            throw new BussinessException("成本年月不能超过当月!");
        }
        //成本年月转换
        if (dto.getCostYearMonth().contains("/")) {
            dto.setCostYearMonth(dto.getCostYearMonth().replace("/", "-"));
        }

        //户号通过t_user获取userId
        if (StringUtils.isNotBlank(dto.getCusName())) {
            List<TUserEntity> userList = tUserService.lambdaQuery()
                    .like(TUserEntity::getName, dto.getCusName())
                    .list();
            if (CollUtil.isEmpty(userList)) {
                return page;
            }
            List<Long> userIdList = userList.stream().map(TUserEntity::getId).collect(Collectors.toList());
            dto.setUserIdList(userIdList);
        }

        return investmentModelDao.psCostPage(page, dto);
    }
}
