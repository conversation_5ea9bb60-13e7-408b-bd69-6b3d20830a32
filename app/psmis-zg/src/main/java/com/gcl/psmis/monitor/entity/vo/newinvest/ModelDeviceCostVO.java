package com.gcl.psmis.monitor.entity.vo.newinvest;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;

/**
 * 投资模型-设备成本
 */
@Builder
@AllArgsConstructor
@NoArgsConstructor
@Data
@ApiModel(value = "ModelDeviceCostVO")
public class ModelDeviceCostVO {

    @ApiModelProperty("主键id")
    private Long id;

    @ApiModelProperty("设备类型(组件功率)")
    private String deviceType;

    @ApiModelProperty("安装形式")
    private String installForm;

    @ApiModelProperty("建档日期范围")
    private String createdTimeStr;

    @ApiModelProperty("组件成本(元/w)")
    private BigDecimal assemblyCost;

    @ApiModelProperty("支架成本(元/w)")
    private BigDecimal bracketCost;

    @ApiModelProperty("并网箱成本(元/w)")
    private BigDecimal gridCost;

    @ApiModelProperty("逆变器成本(元/w)")
    private BigDecimal inverterCost;

    @ApiModelProperty("电气辅材成本(元/w)")
    private BigDecimal fcCost;

}
