package com.gcl.psmis.monitor.entity.vo.invest.costAnalysis;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;

/**
 * @description:  电站成本对比分析出参
 * @author: liujinjing
 * @date: 2025/6/5 10:41
 */
@Data
@ApiModel(value = "电站成本对比分析出参", description = "电站成本对比分析出参")
@AllArgsConstructor
public class CompareCostVO {

    @ApiModelProperty("电站编号")
    private String psCode;
    @ApiModelProperty("产品名称")
    private String productName;
    @ApiModelProperty("是否混装(0否1是)")
    private Integer mixFlag;
    @ApiModelProperty("是否混装")
    private String mixFlagName;
    @ApiModelProperty("产权方")
    private String propertyOwner;
    @ApiModelProperty("电费归集方式")
    private String electricityBillMode;
    @ApiModelProperty("省市区")
    private String provinceCityRegion;
    @ApiModelProperty("省")
    private String provinceName;
    @ApiModelProperty("省市")
    private String cityName;
    @ApiModelProperty("区")
    private String regionName;
    @ApiModelProperty("备案类型")
    private String recordType;
    @ApiModelProperty("电站状态")
    private String psStatus;
    @ApiModelProperty("组件类型")
    private String assemblyType;
    @ApiModelProperty("安装形式")
    private String installForm;
    @ApiModelProperty("建档日期")
    private Date createdTime;
    @ApiModelProperty("齐套发货日期")
    private Date qiTaoTime;
    @ApiModelProperty("有效发电首日")
    private Date firstPowerTime;
    @ApiModelProperty("并网通过日期")
    private Date connectedTime;
    @ApiModelProperty("首年小时数")
    private BigDecimal firstYearHour;

    /**
     * 租金（元/块）
     */
    @ApiModelProperty("第1年租金")
    private BigDecimal onePhaseYearRent;
    @ApiModelProperty("第2-5年租金")
    private BigDecimal twoPhaseYearRent;
    @ApiModelProperty("第6-10年租金")
    private BigDecimal threePhaseYearRent;
    @ApiModelProperty("第11-15年租金")
    private BigDecimal fourPhaseYearRent;
    @ApiModelProperty("第16-20年租金")
    private BigDecimal fivePhaseYearRent;
    @ApiModelProperty("第21-25年租金")
    private BigDecimal sixPhaseYearRent;

    //理论成本
    /**
     * 代理商成本
     */
    @ApiModelProperty("开发服务费")
    private BigDecimal theoryDevelopCost;
    @ApiModelProperty("设备材料费")
    private BigDecimal theoryDeviceCost;
    @ApiModelProperty("光伏安装施工费")
    private BigDecimal theoryInstallCost;

    /**
     * 代理商奖励
     */
    @ApiModelProperty("容量奖励")
    private BigDecimal theoryCapacityReward;
    @ApiModelProperty("时效奖励")
    private BigDecimal theoryActualEffectReward;

    /**
     * 设备成本
     */
    @ApiModelProperty("组件")
    private BigDecimal theoryAssembly;
    @ApiModelProperty("支架")
    private BigDecimal theoryBracket;
    @ApiModelProperty("并网箱")
    private BigDecimal theoryBox;
    @ApiModelProperty("逆变器")
    private BigDecimal theoryInverter;
    @ApiModelProperty("电气辅材")
    private BigDecimal theoryFc;

    /**
     * 管理成本
     */
    @ApiModelProperty("供应链")
    private BigDecimal theorySupplyChainCost;
    @ApiModelProperty("管理费")
    private BigDecimal theoryManageCost;
    @ApiModelProperty("财务费")
    private BigDecimal theoryFinanceCost;


    @ApiModelProperty("理论造价合计")
    private BigDecimal theoryTotalCost;

    //实际成本
    /**
     * 代理商成本
     */
    @ApiModelProperty("开发服务费")
    private BigDecimal actualDevelopCost;
    @ApiModelProperty("设备材料费")
    private BigDecimal actualDeviceCost;
    @ApiModelProperty("光伏安装施工费")
    private BigDecimal actualInstallCost;

    /**
     * 代理商奖励
     */
    @ApiModelProperty("容量奖励")
    private BigDecimal actualCapacityReward;
    @ApiModelProperty("时效奖励")
    private BigDecimal actualActualEffectReward;

    /**
     * 设备成本
     */
    @ApiModelProperty("组件")
    private BigDecimal actualAssembly;
    @ApiModelProperty("支架")
    private BigDecimal actualBracket;
    @ApiModelProperty("并网箱")
    private BigDecimal actualBox;
    @ApiModelProperty("逆变器")
    private BigDecimal actualInverter;
    @ApiModelProperty("电气辅材")
    private BigDecimal actualFc;

    /**
     * 管理成本
     */
    @ApiModelProperty("仓储")
    private BigDecimal actualStorageCost;
    @ApiModelProperty("仓储调拨")
    private BigDecimal actualAllotCost;
    @ApiModelProperty("装卸")
    private BigDecimal actualMoveCost;
    @ApiModelProperty("物流")
    private BigDecimal actualLogisticsCost;
    @ApiModelProperty("管理费")
    private BigDecimal actualManageCost;
    @ApiModelProperty("财务费")
    private BigDecimal actualFinanceCost;

    @ApiModelProperty("实际造价合计")
    private BigDecimal actualTotalCost;
}
