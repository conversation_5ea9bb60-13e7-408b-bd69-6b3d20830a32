package com.gcl.psmis.monitor.entity.dto.invest.costAnalysis;

import com.gcl.psmis.framework.common.req.base.BasePageReq;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotNull;
import java.util.List;

/**
 * @ClassName CostAnalysisDTO
 * @Description 电站成本分析入参
 * <AUTHOR>
 * @Date 2025/06/05 10:23
 **/
@Data
@ApiModel(value = "电站成本分析入参类", description = "电站成本分析入参类")
public class CostAnalysisDTO extends BasePageReq {
    @ApiModelProperty(value = "成本年月", required = true)
    @NotNull(message = "成本年月必选")
    private String costYearMonth;
    @ApiModelProperty("电站编号")
    private String stationNo;
    @ApiModelProperty("户主姓名")
    private String cusName;
    @ApiModelProperty("项目公司")
    private String projectCompany;
    @ApiModelProperty("省市id集合")
    private List<Integer> proCitys;
    @ApiModelProperty("产品名称")
    private String productName;

    private List<Long> userIdList;
    private String psStatus;
}
