package com.gcl.psmis.monitor.service.newinvest;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.thread.ThreadUtil;
import com.baomidou.dynamic.datasource.annotation.DSTransactional;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.gcl.psmis.framework.common.exception.BussinessException;
import com.gcl.psmis.framework.common.req.base.BasePageReq;
import com.gcl.psmis.framework.mbg.entity.TModelAgentRewardEntity;
import com.gcl.psmis.framework.mbg.entity.TModelDeviceCostEntity;
import com.gcl.psmis.framework.mbg.entity.TModelManageCostEntity;
import com.gcl.psmis.framework.mbg.service.TModelAgentRewardService;
import com.gcl.psmis.framework.mbg.service.TModelDeviceCostService;
import com.gcl.psmis.framework.mbg.service.TModelManageCostService;
import com.gcl.psmis.monitor.entity.dto.TimeRange;
import com.gcl.psmis.monitor.entity.dto.newinvest.InvestModelDetailDto;
import com.gcl.psmis.monitor.entity.dto.newinvest.ModelAgentRewardDto;
import com.gcl.psmis.monitor.entity.dto.newinvest.ModelDeviceCostDto;
import com.gcl.psmis.monitor.entity.dto.newinvest.ModelManageCostDto;
import com.gcl.psmis.monitor.entity.vo.newinvest.InvestModelDetailVO;
import com.gcl.psmis.monitor.entity.vo.newinvest.ModelAgentRewardVO;
import com.gcl.psmis.monitor.entity.vo.newinvest.ModelDeviceCostVO;
import com.gcl.psmis.monitor.entity.vo.newinvest.ModelManageCostVO;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.Comparator;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

@Slf4j
@Service
@RequiredArgsConstructor
public class InvestModelBillService {

    private final TModelAgentRewardService tModelAgentRewardService;
    private final TModelDeviceCostService tModelDeviceCostService;
    private final TModelManageCostService tModelManageCostService;

    /**
     * 获取投资模型详情
     *
     * @return
     */
    public InvestModelDetailVO getDetail(BasePageReq req) {
        InvestModelDetailVO result = new InvestModelDetailVO();
        //管理成本
        List<TModelManageCostEntity> modelManageCosts = tModelManageCostService.list();
        if (CollUtil.isNotEmpty(modelManageCosts)) {
            result.setModelManageCosts(BeanUtil.copyToList(modelManageCosts, ModelManageCostVO.class));
        }
        //代理商奖励
        List<TModelAgentRewardEntity> agentRewards = tModelAgentRewardService.list();
        if (CollUtil.isNotEmpty(agentRewards)) {
            result.setAgentRewards(BeanUtil.copyToList(agentRewards, ModelAgentRewardVO.class));
        }
        //设备成本
        Page<TModelDeviceCostEntity> page = new Page<>(req.getPageNum(), req.getPageSize());
        Page<TModelDeviceCostEntity> pages = tModelDeviceCostService.page(page);
        if (CollUtil.isNotEmpty(pages.getRecords())) {
            //转换分页
            IPage<ModelDeviceCostVO> resultPage = new Page<>();
            resultPage.setRecords(BeanUtil.copyToList(pages.getRecords(), ModelDeviceCostVO.class));
            resultPage.setTotal(pages.getTotal());
            resultPage.setPages(pages.getPages());
            resultPage.setCurrent(pages.getCurrent());
            result.setModelDevices(resultPage);
        }
        return result;
    }

    /**
     * 导入实际成本
     *
     * @param dtos 导入参数
     */
    public List<ModelDeviceCostDto> importDevices(List<ModelDeviceCostDto> dtos) {
        List<ModelDeviceCostDto> result = new ArrayList<>();
        //拆分设备类型
        dtos.forEach(item -> {
            String[] split = item.getDeviceType().split("、");
            if (split.length > 1) {
                for (String type : split) {
                    ModelDeviceCostDto modelDeviceCostDto = BeanUtil.copyProperties(item, ModelDeviceCostDto.class);
                    modelDeviceCostDto.setDeviceType(type);
                    result.add(modelDeviceCostDto);
                }
            } else if (split.length == 1) {
                result.add(item);
            }
        });
        //判断导入数据:”组件类型+安装形式“的建档日期不可重叠
        //根据组件类型+安装形式分组
        Map<String, List<ModelDeviceCostDto>> groupMap = result.stream().collect(Collectors.groupingBy(item -> item.getDeviceType() + item.getInstallForm()));
        LocalDateTime nowTime = LocalDateTime.now();
        for (Map.Entry<String, List<ModelDeviceCostDto>> map : groupMap.entrySet()) {
            List<ModelDeviceCostDto> groupList = map.getValue();
            //校验相同组件类型+安装形式数据:”建档日期“是否重叠
            List<TimeRange> checkList = new ArrayList<>();
            for (ModelDeviceCostDto modelDeviceCostDto : groupList) {
                String[] split = modelDeviceCostDto.getCreatedTimeStr().split("-");
                if (split.length != 2) {
                    throw new BussinessException("导入数据:”建档日期“格式错误: " + modelDeviceCostDto.getCreatedTimeStr());
                }
                LocalDateTime startTime = LocalDateTime.parse(split[0] + " 00:00:00", DateTimeFormatter.ofPattern("yyyy.MM.dd HH:mm:ss"));
                if (!"至今".equals(split[1])) {
                    nowTime = LocalDateTime.parse(split[1] + " 00:00:00", DateTimeFormatter.ofPattern("yyyy.MM.dd HH:mm:ss"));
                }
                checkList.add(new TimeRange(startTime, nowTime));
            }
            //校验时间是否重叠
            if (this.checkTimeRange(checkList)) {
                throw new BussinessException("导入数据:”建档日期“存在重叠时间: " + map.getKey());
            }
        }
        return dtos;
    }

    private Boolean checkTimeRange(List<TimeRange> ranges) {
        boolean result = false;
        // 按开始时间排序
        ranges.sort(Comparator.comparing(TimeRange::getStart));

        // 遍历检查相邻区间是否重叠
        for (int i = 0; i < ranges.size() - 1; i++) {
            TimeRange current = ranges.get(i);
            TimeRange next = ranges.get(i + 1);

            // 如果当前区间的结束时间晚于下一个区间的开始时间，则重叠
            if (current.getEnd().isAfter(next.getStart())) {
                result = true;
                break;
            }
        }
        return result;
    }

    /**
     * 提交投资模型
     *
     * @param dto
     */
    @DSTransactional(rollbackFor = Exception.class)
    public void submit(InvestModelDetailDto dto) {
        //管理成本
        //先删后增
        List<ModelManageCostDto> manageCosts = dto.getModelManageCosts();
        tModelManageCostService.remove(Wrappers.<TModelManageCostEntity>lambdaQuery().isNotNull(TModelManageCostEntity::getCreateTime));
        tModelManageCostService.saveBatch(BeanUtil.copyToList(manageCosts, TModelManageCostEntity.class));

        //代理商奖励
        //先删后增
        List<ModelAgentRewardDto> agentRewards = dto.getAgentRewards();
        tModelAgentRewardService.remove(Wrappers.<TModelAgentRewardEntity>lambdaQuery().isNotNull(TModelAgentRewardEntity::getCreateTime));
        tModelAgentRewardService.saveBatch(BeanUtil.copyToList(agentRewards, TModelAgentRewardEntity.class));
        //设备成本
        //先删后增
        List<ModelDeviceCostDto> deviceCosts = dto.getDeviceCosts();
        tModelDeviceCostService.remove(Wrappers.<TModelDeviceCostEntity>lambdaQuery().isNotNull(TModelDeviceCostEntity::getCreateTime));
        tModelDeviceCostService.saveBatch(BeanUtil.copyToList(deviceCosts, TModelDeviceCostEntity.class));

        //TODO 异步调用更新电站成本的定时任务
        ThreadUtil.execAsync(() -> {

        });
    }
}
