package com.gcl.psmis.monitor.entity.request.invest.income;

import com.gcl.psmis.manager.domain.invest.model.StagingFeesReq;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotNull;

@Data
@ApiModel(value = "RevenueContrastRequest", description = "投资收益对比查询请求类")
public class RevenueContrastRequest {

    @ApiModelProperty("安装形式ID")
    @NotNull(message = "安装形式必选")
    private Integer installFormId;

    @ApiModelProperty("安装形式名称")
    private String installFormName;

    @ApiModelProperty("组件功率")
    @NotNull(message = "组件功率必选")
    private Integer zuPower;

    @ApiModelProperty("省份ID")
    @NotNull(message = "省份必选")
    private Integer provinceId;

    @ApiModelProperty("省份名称")
    private String provinceName;

    @ApiModelProperty("城市ID")
    private Integer cityId;

    @ApiModelProperty("城市名称")
    private String cityName;

    @ApiModelProperty("并网月份")
    private String connectedMonth;

}
