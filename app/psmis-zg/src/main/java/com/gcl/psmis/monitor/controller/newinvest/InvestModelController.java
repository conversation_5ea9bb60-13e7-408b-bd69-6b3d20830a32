package com.gcl.psmis.monitor.controller.newinvest;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.exceptions.ExceptionUtil;
import cn.hutool.core.util.StrUtil;
import com.gcl.psmis.framework.common.annotation.GetGclApiByToken;
import com.gcl.psmis.framework.common.annotation.PostGclApiByToken;
import com.gcl.psmis.framework.common.exception.BussinessException;
import com.gcl.psmis.framework.common.req.base.BasePageReq;
import com.gcl.psmis.framework.export.util.poi.MultiSheetExcelTool;
import com.gcl.psmis.monitor.entity.dto.newinvest.InvestModelDetailDto;
import com.gcl.psmis.monitor.entity.dto.newinvest.ModelDeviceCostDto;
import com.gcl.psmis.monitor.entity.vo.newinvest.InvestModelDetailVO;
import com.gcl.psmis.monitor.service.newinvest.InvestModelBillService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.multipart.MultipartFile;

import java.util.List;

@Slf4j
@RestController
@Api(tags = "投资模型相关模块")
@RequestMapping("/zg/invest/model")
@RequiredArgsConstructor
public class InvestModelController {

    private final InvestModelBillService investModelBillService;

    @ApiOperation(value = "投资模型详情")
    @GetGclApiByToken(value = "/getDetail")
    public InvestModelDetailVO getDetail(BasePageReq req) {
        return investModelBillService.getDetail(req);
    }

    @ApiOperation("投资模型提交")
    @PostGclApiByToken("/submit")
    public void submit(@RequestBody @Validated InvestModelDetailDto dto) {
        investModelBillService.submit(dto);
    }

    @ApiOperation("导入设备成本")
    @PostGclApiByToken(value = "importDevices", headers = "content-type=multipart/form-data")
    public List<ModelDeviceCostDto> importDevices(@ApiParam(name = "file", required = true) MultipartFile file) {

        try (XSSFWorkbook workbook = new XSSFWorkbook(file.getInputStream())) {
            //实际数据
            List<ModelDeviceCostDto> dtos = MultiSheetExcelTool.ImportBuilder().importExcel(workbook, ModelDeviceCostDto.class, 0);
            if (CollUtil.isEmpty(dtos)) {
                throw new BussinessException("导入数据为空!");
            }
            //校验数据
            for (ModelDeviceCostDto dto : dtos) {
                //建档时间
                if (StrUtil.isBlank(dto.getCreatedTimeStr())) {
                    throw new BussinessException("请填写设备建档时间!");
                }
                //设备类型
                if (StrUtil.isBlank(dto.getDeviceType())) {
                    throw new BussinessException("请填写设备类型!");
                }
                //安装形式
                if (StrUtil.isBlank(dto.getInstallForm())) {
                    throw new BussinessException("请填写安装形式!");
                }
            }
            return investModelBillService.importDevices(dtos);
        } catch (Exception e) {
            log.error("导入设备成本失败{}", ExceptionUtil.stacktraceToString(e));
            throw new BussinessException("导入设备成本失败" + ExceptionUtil.stacktraceToOneLineString(e));
        }
    }
}
