package com.gcl.psmis.monitor.entity.dto.newinvest;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;
import java.util.Date;

/**
 * 投资模型-管理成本
 */
@Builder
@AllArgsConstructor
@NoArgsConstructor
@Data
@ApiModel(value = "ModelManageCostDto")
public class ModelManageCostDto {

    @ApiModelProperty("主键id")
    private Long id;

    @ApiModelProperty("建档日期开始日期")
    @JsonFormat(pattern = "yyyy/MM/dd", timezone = "GMT+8")
    private Date createdBeginTime;

    @ApiModelProperty("建档日期开始日期")
    @JsonFormat(pattern = "yyyy/MM/dd", timezone = "GMT+8")
    private Date createdEndTime;

    @ApiModelProperty("供应链成本(元/w)")
    private BigDecimal supplyChainCost;

    @ApiModelProperty("管理成本(元/w)")
    private BigDecimal manageCost;

    @ApiModelProperty("财务成本(元/w)")
    private BigDecimal financeCost;

    @ApiModelProperty("创建时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date createTime;

}
