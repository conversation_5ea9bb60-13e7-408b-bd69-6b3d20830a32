package com.gcl.psmis.monitor.util;

import cn.hutool.core.date.DateUtil;
import com.gcl.psmis.monitor.entity.dto.ZgKpiReportDTO;
import com.gcl.psmis.monitor.process.dto.KpiReportDTO;
import com.gcl.psmis.monitor.process.request.KpiRequest;
import org.apache.commons.lang3.StringUtils;

import java.text.SimpleDateFormat;
import java.util.Calendar;
import java.util.HashMap;
import java.util.Map;

/**
 * @ClassName ReportTimeUtil
 * @Description 总览时间提取
 * <AUTHOR>
 * @Date 2023/10/18 15:23
 **/
public class ReportTimeUtil {

    public static Map<String, Object> doTimeReport(String sTime, String eTime, Integer t) {
        Map<String, Object> maps = new HashMap<>();
        //获取时间段,0-今天，1-本周，2-本月，3-本年，4-近30天
        //当前时间段的开始和结束时间
        String nowStartTime = null, nowEndTime = null;
        if (StringUtils.isNotBlank(sTime) && StringUtils.isNotBlank(eTime)) {
            nowStartTime = sTime;
            nowEndTime = eTime;
        } else {
            if (t != null) {
                if (t == 0) {
                    //今天和昨天
                    nowStartTime = nowEndTime = DateCustomUtil.getStartOrEndDayOfDay(0);
                } else if (t == 1) {//昨天
                    nowStartTime = nowEndTime = DateCustomUtil.getStartOrEndDayOfDay(-1);
                } else if (t == 2) {
                    //本周开始和结束时间
                    String[] nowWeek = DateCustomUtil.getStartOrEndDayOfWeek(0);
                    nowStartTime = nowWeek[0];
                    nowEndTime = nowWeek[1];
                } else if (t == 3) {//上周
                    String[] nowWeek = DateCustomUtil.getStartOrEndDayOfWeek(-1);
                    nowStartTime = nowWeek[0];
                    nowEndTime = nowWeek[1];
                } else if (t == 4) {
                    //本月开始和结束时间
                    String[] nowMonth = DateCustomUtil.getStartOrEndDayOfMonth(0);
                    nowStartTime = nowMonth[0];
                    nowEndTime = nowMonth[1];
                } else if (t == 5) {//上个月
                    String[] nowMonth = DateCustomUtil.getStartOrEndDayOfMonth(-1);
                    nowStartTime = nowMonth[0];
                    nowEndTime = nowMonth[1];
                } else if (t == 6) {
                    //本年开始和结束时间
                    String[] nowYear = DateCustomUtil.getStartOrEndDayOfYear(0);
                    nowStartTime = nowYear[0];
                    nowEndTime = nowYear[1];
                } else if (t == 7) {
                    //上年开始和结束时间
                    String[] lastYear = DateCustomUtil.getStartOrEndDayOfYear(-1);
                    nowStartTime = lastYear[0];
                    nowEndTime = lastYear[1];

                }else if(t==8){
                    //近30/60天的开始和结束时间 30天
                    String[] day = DateCustomUtil.getStartOrEndDayOfThirtyDay();
                    nowStartTime = day[1];
                    nowEndTime = day[0];
                }
            }
        }
        maps.put("nowStartTime", nowStartTime);
        maps.put("nowEndTime", nowEndTime);

        return maps;
    }

    public static void main(String[] args) {
        System.out.println(DateUtil.formatDate(DateUtil.offsetDay(DateUtil.parse("2024-03-05"), -30)));
        System.out.println(DateUtil.formatDate(DateUtil.offsetDay(DateUtil.parse("2024-03-01"), -30)));
    }

    /**
     * @param dto
     * @return Map<Object>
     * @desc 处理不同时间段，为了计算同期环比
     * <AUTHOR>
     * @date 2023/9/19 18:44
     */
    public static Map<String, Object> doTime(KpiRequest dto) {
        Map<String, Object> maps = new HashMap<>();
        //获取时间段,0-今天，1-本周，2-本月，3-本年，4-近30天
        //当前时间段的开始和结束时间
        String nowStartTime = null, nowEndTime = null;
        //过去时间段的开始和结束时间
        String passStartTime = null, passEndTime = null;
        if (StringUtils.isNotBlank(dto.getSTime()) && StringUtils.isNotBlank(dto.getETime())) {
            //自定义时间段
            //String customTime = DateCustomUtil.getCustomTime(dto.getSTime(), dto.getETime());
            nowStartTime = dto.getSTime();
            nowEndTime = dto.getETime();
            passStartTime = DateUtil.formatDate(DateUtil.offsetDay(DateUtil.parse(dto.getSTime()), -30));
            passEndTime = DateUtil.formatDate(DateUtil.offsetDay(DateUtil.parse(dto.getETime()), -30));
        } else {
            if (dto.getT() == null) {
                if (dto.getKpi() == 0 || dto.getKpi() == 1) {
                    dto.setT(4);
                }
            } else {
                if (dto.getT() == 0) {
                    //今天和昨天
                    nowStartTime = nowEndTime = DateCustomUtil.getStartOrEndDayOfDay(0);
                    passStartTime = passEndTime = DateCustomUtil.getStartOrEndDayOfDay(-1);
                } else if (dto.getT() == 1) {
                    //昨天
                    nowStartTime = nowEndTime = DateCustomUtil.getStartOrEndDayOfDay(-1);
                    //前天
                    SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd");
                    Calendar front = Calendar.getInstance();
                    front.add(Calendar.DATE, -2);
                    passStartTime = passEndTime = sdf.format(front.getTime());
                } else if (dto.getT() == 2) {
                    //本周开始和结束时间
                    String[] nowWeek = DateCustomUtil.getStartOrEndDayOfWeek(0);
                    nowStartTime = nowWeek[0];
                    nowEndTime = nowWeek[1];
                    //上周开始和结束时间
                    String[] passWeek = DateCustomUtil.getStartOrEndDayOfWeek(-1);
                    passStartTime = passWeek[0];
                    passEndTime = passWeek[1];
                } else if (dto.getT() == 3) {
                    //本周开始和结束时间
                    String[] nowWeek = DateCustomUtil.getStartOrEndDayOfWeek(-1);
                    nowStartTime = nowWeek[0];
                    nowEndTime = nowWeek[1];
                    //上周开始和结束时间
                    String[] passWeek = DateCustomUtil.getStartOrEndDayOfWeek(-2);
                    passStartTime = passWeek[0];
                    passEndTime = passWeek[1];
                } else if (dto.getT() == 4) {
                    //本月开始和结束时间
                    String[] nowMonth = DateCustomUtil.getStartOrEndDayOfMonth(0);
                    nowStartTime = nowMonth[0];
                    nowEndTime = nowMonth[1];
                    //上月开始和结束时间
                    String[] passMonth = DateCustomUtil.getStartOrEndDayOfMonth(-1);
                    passStartTime = passMonth[0];
                    passEndTime = passMonth[1];
                } else if (dto.getT() == 5) {
                    //上月开始和结束时间
                    String[] nowMonth = DateCustomUtil.getStartOrEndDayOfMonth(-1);
                    nowStartTime = nowMonth[0];
                    nowEndTime = nowMonth[1];
                    //上上月开始和结束时间
                    String[] passMonth = DateCustomUtil.getStartOrEndDayOfMonth(-2);
                    passStartTime = passMonth[0];
                    passEndTime = passMonth[1];
                } else if (dto.getT() == 6) {
                    //本年开始和结束时间
                    String[] nowYear = DateCustomUtil.getStartOrEndDayOfYear(0);
                    nowStartTime = nowYear[0];
                    nowEndTime = nowYear[1];
                    //上年开始和结束时间
                    String[] passYear = DateCustomUtil.getStartOrEndDayOfYear(-1);
                    passStartTime = passYear[0];
                    passEndTime = passYear[1];
                } else if (dto.getT() == 7) {
                    //近30/60天的开始和结束时间
                    String[] day = DateCustomUtil.getStartOrEndDayOfThirtyDay();
                    nowStartTime = day[1];
                    nowEndTime = day[0];
                    passStartTime = day[2];
                    passEndTime = day[1];
                }
            }

        }

        maps.put("nowStartTime", nowStartTime);
        maps.put("nowEndTime", nowEndTime);
        maps.put("passStartTime", passStartTime);
        maps.put("passEndTime", passEndTime);
        return maps;
    }
}
