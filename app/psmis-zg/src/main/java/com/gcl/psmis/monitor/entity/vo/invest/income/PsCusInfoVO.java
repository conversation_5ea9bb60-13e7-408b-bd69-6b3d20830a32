package com.gcl.psmis.monitor.entity.vo.invest.income;

import com.gcl.psmis.framework.common.annotation.ExportConfig;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@Data
@ApiModel(value = "PsCusInfoVO", description = "电站户主信息VO")
public class PsCusInfoVO {

    @ApiModelProperty("电站编号")
    @ExportConfig(width = 150, value = "电站编号")
    private String psCode;

    @ApiModelProperty("户主姓名")
    @ExportConfig(width = 150, value = "户主姓名")
    private String cusName;

}
