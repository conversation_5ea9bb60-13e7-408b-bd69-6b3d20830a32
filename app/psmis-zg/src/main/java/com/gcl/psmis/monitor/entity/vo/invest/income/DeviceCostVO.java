package com.gcl.psmis.monitor.entity.vo.invest.income;

import com.gcl.psmis.framework.common.annotation.ExportConfig;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;

@Data
@ApiModel(value = "DeviceCostVO", description = "设备成本VO")
public class DeviceCostVO {

    @ApiModelProperty("电站编号")
    @ExportConfig(width = 150, value = "电站编号")
    private String psCode;

    @ApiModelProperty("户主姓名")
    @ExportConfig(width = 150, value = "户主姓名")
    private String cusName;

    @ApiModelProperty("组件（不含税）")
    @ExportConfig(width = 150, value = "组件")
    private BigDecimal assemblyWithoutTax;

    @ApiModelProperty("支架（不含税）")
    @ExportConfig(width = 150, value = "支架")
    private BigDecimal bracketWithoutTax;

    @ApiModelProperty("并网箱（不含税）")
    @ExportConfig(width = 150, value = "并网箱")
    private BigDecimal boxWithoutTax;

    @ApiModelProperty("逆变器（不含税）")
    @ExportConfig(width = 150, value = "逆变器")
    private BigDecimal inverterWithoutTax;

    @ApiModelProperty("电气辅材（不含税）")
    @ExportConfig(width = 150, value = "电气辅材")
    private BigDecimal fcWithoutTax;

    @ApiModelProperty("组件（含税）")
    @ExportConfig(width = 150, value = "组件")
    private BigDecimal assemblyWithTax;

    @ApiModelProperty("支架（含税）")
    @ExportConfig(width = 150, value = "支架")
    private BigDecimal bracketWithTax;

    @ApiModelProperty("并网箱（含税）")
    @ExportConfig(width = 150, value = "并网箱")
    private BigDecimal boxWithTax;

    @ApiModelProperty("逆变器（含税）")
    @ExportConfig(width = 150, value = "逆变器")
    private BigDecimal inverterWithTax;

    @ApiModelProperty("电气辅材（含税）")
    @ExportConfig(width = 150, value = "电气辅材")
    private BigDecimal fcWithTax;

}
