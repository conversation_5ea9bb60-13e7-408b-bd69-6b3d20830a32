package com.gcl.psmis.monitor.constants.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * @description: 导出标识枚举
 * @author: liujinjing
 * @date: 2025/6/10 11:45
 */
@Getter
@AllArgsConstructor
public enum ExportEnum {
    POWER_STATION_THEORY_COST(0, "电站理论成本"),
    POWER_STATION_ACTUAL_COST(1, "电站实际成本"),
    POWER_STATION_COMPARE_COST(2, "电站对比成本"),
    DEVICE_COST(3, "设备成本"),
    ;

    private final Integer code;
    private final String desc;


}
