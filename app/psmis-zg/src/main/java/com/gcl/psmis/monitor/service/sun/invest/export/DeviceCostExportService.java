package com.gcl.psmis.monitor.service.sun.invest.export;

import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.gcl.psmis.monitor.constants.enums.ExportEnum;
import com.gcl.psmis.monitor.entity.request.invest.income.DeviceCostRequest;
import com.gcl.psmis.monitor.entity.vo.invest.income.DeviceCostVO;
import com.gcl.psmis.monitor.service.sun.invest.InvestIncomeAnalysisService;
import com.google.common.collect.Lists;
import org.apache.commons.collections.CollectionUtils;
import org.apache.poi.ss.usermodel.Cell;
import org.apache.poi.ss.usermodel.Row;
import org.apache.poi.ss.usermodel.Sheet;
import org.apache.poi.ss.util.CellRangeAddress;
import org.apache.poi.xssf.usermodel.XSSFCellStyle;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import javax.annotation.PostConstruct;
import java.util.List;
import java.util.Objects;

/**
 * 设备成本导出服务类
 */
@Component
public class DeviceCostExportService extends AbstractCostAnalysisExportService<DeviceCostVO, DeviceCostRequest> {

    @PostConstruct
    public void init() {
        this.exportEnum = ExportEnum.DEVICE_COST;
        this.rowHeaderNum = 2;
    }

    @Autowired
    private InvestIncomeAnalysisService investIncomeAnalysisService;

    @Override
    public List<String[]> getRowHeaderList() {
        List<String[]> rowHeaderList = Lists.newArrayList();
        rowHeaderList.add(new String[]{"电站编号", "户主姓名", "不含税设备成本(元/W)", "", "", "", "", "含税设备成本(元/W)", "", "", "", ""});
        rowHeaderList.add(new String[]{"", "", "组件", "支架", "并网箱", "逆变器", "电气辅材", "组件", "支架", "并网箱", "逆变器", "电气辅材"});
        return rowHeaderList;
    }

    @Override
    public void mergeCell(Sheet sheet) {
        // 合并单元格
        // 合并参数分别为： 起始行，结束行，起始列，结束列
        sheet.addMergedRegion(new CellRangeAddress(0, 1, 0, 0)); // 将第一列 的 第一行到第二行合并
        sheet.addMergedRegion(new CellRangeAddress(0, 1, 1, 1)); // 将第二列 的 第一行到第二行合并
        sheet.addMergedRegion(new CellRangeAddress(0, 0, 2, 6)); // 将第一行 的 第三列到第七列合并
        sheet.addMergedRegion(new CellRangeAddress(0, 0, 7, 11)); // 将第一行 的 第八列到第十二列合并
    }

    @Override
    public List<DeviceCostVO> getExportData(DeviceCostRequest request) {
        IPage<DeviceCostVO> stationCostVOIPage = investIncomeAnalysisService.deviceCostPage(new Page<>(-1, -1),  request);
        return stationCostVOIPage.getRecords();
    }

    @Override
    public void fillSheetByRecords(Sheet sheet, List<DeviceCostVO> records, Integer rowNum, Integer columnNum, XSSFCellStyle contentStyle) {
        if (CollectionUtils.isEmpty(records)) {
            return;
        }

        for (DeviceCostVO record : records) {
            Row tempRow = sheet.createRow(rowNum++);
            tempRow.setHeight((short) 500);
            for (int i = 0; i < columnNum; i++) {
                // 列宽自适应，i为自适应的列，true就是自适应，false就是不自适应，默认不自适应
                sheet.autoSizeColumn(i, true);
                Cell tempCell = tempRow.createCell(i);
                tempCell.setCellStyle(contentStyle);
                String tempValue = "";
                switch (i) {
                    case 0:
                        tempValue = record.getPsCode();
                        break;
                    case 1:
                        tempValue = record.getCusName();
                        break;
                    case 2:
                        tempValue = Objects.nonNull(record.getAssemblyWithoutTax()) ? String.valueOf(record.getAssemblyWithoutTax()) : "";
                        break;
                    case 3:
                        tempValue = Objects.nonNull(record.getBracketWithoutTax()) ? String.valueOf(record.getBracketWithoutTax()) : "";
                        break;
                    case 4:
                        tempValue = Objects.nonNull(record.getBoxWithoutTax()) ? String.valueOf(record.getBoxWithoutTax()) : "";
                        break;
                    case 5:
                        tempValue = Objects.nonNull(record.getInverterWithoutTax()) ? String.valueOf(record.getInverterWithoutTax()) : "";
                        break;
                    case 6:
                        tempValue = Objects.nonNull(record.getFcWithoutTax()) ? String.valueOf(record.getFcWithoutTax()) : "";
                        break;
                    case 7:
                        tempValue = Objects.nonNull(record.getAssemblyWithTax()) ? String.valueOf(record.getAssemblyWithTax()) : "";
                        break;
                    case 8:
                        tempValue = Objects.nonNull(record.getBracketWithTax()) ? String.valueOf(record.getBracketWithTax()) : "";
                        break;
                    case 9:
                        tempValue = Objects.nonNull(record.getBoxWithTax()) ? String.valueOf(record.getBoxWithTax()) : "";
                        break;
                    case 10:
                        tempValue = Objects.nonNull(record.getInverterWithTax()) ? String.valueOf(record.getInverterWithTax()) : "";
                        break;
                    case 11:
                        tempValue = Objects.nonNull(record.getFcWithTax()) ? String.valueOf(record.getFcWithTax()) : "";
                        break;
                }
                tempCell.setCellValue(tempValue);
            }
        }
    }

    @Override
    public void tableTitleStyleColumnWidth(Sheet sheet) {
    }
}
