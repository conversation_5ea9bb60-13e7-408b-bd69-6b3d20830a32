package com.gcl.psmis.monitor.entity.request.invest.income;

import com.gcl.psmis.framework.common.req.base.BasePageReq;
import com.gcl.psmis.framework.mbg.entity.TUserEntity;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

@Data
@ApiModel(value = "DeviceCostRequest", description = "设备成本请求类")
public class DeviceCostRequest extends BasePageReq {

    @ApiModelProperty("电站编号")
    private String psCode;

    @ApiModelProperty("户主姓名")
    private String cusName;

    private List<String> psCodeList;

}
