package com.gcl.psmis.monitor.service.sun.invest.export;

import com.alibaba.excel.util.DateUtils;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.gcl.psmis.monitor.constants.enums.ExportEnum;
import com.gcl.psmis.monitor.entity.dto.invest.costAnalysis.CostAnalysisDTO;
import com.gcl.psmis.monitor.entity.vo.invest.costAnalysis.ActualCostVO;
import com.gcl.psmis.monitor.service.sun.invest.PowerStationCostAnalysisService;
import com.gcl.psmis.monitor.util.StringUtil;
import com.google.common.collect.Lists;
import org.apache.commons.collections.CollectionUtils;
import org.apache.poi.ss.usermodel.Cell;
import org.apache.poi.ss.usermodel.Row;
import org.apache.poi.ss.usermodel.Sheet;
import org.apache.poi.ss.util.CellRangeAddress;
import org.apache.poi.xssf.usermodel.XSSFCellStyle;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import javax.annotation.PostConstruct;
import java.util.List;
import java.util.Objects;

/**
 * @description: 实际成本导出服务类
 * @author: liujinjing
 * @date: 2025/6/10 16:26
 */
@Component
public class ActualCosAnalysisExportService extends AbstractCostAnalysisExportService<ActualCostVO, CostAnalysisDTO> {

    @PostConstruct
    public void init() {
        this.exportEnum = ExportEnum.POWER_STATION_ACTUAL_COST;
        this.rowHeaderNum = 3;
    }

    @Autowired
    private PowerStationCostAnalysisService stationCostAnalysisService;

    @Override
    public List<String[]> getRowHeaderList() {
        List<String[]> rowHeaderList = Lists.newArrayList();
        rowHeaderList.add(new String[]{"电站编号", "产品名称", "是否混装", "产权方", "电费归集方式", "省市区", "备案类型", "电站状态", "组件类型", "安装形式", "建档日期", "齐套发货日期", "有效发电首日", "并网通过日期", "首年小时数", "租金（元/块）", "", "", "", "", "", "实际单瓦造价（元/W）", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", ""});
        rowHeaderList.add(new String[]{"", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "代理商成本", "", "", "代理商奖励", "", "设备成本", "", "", "", "", "管理成本", "", "", "", "", "", "实际造价合计"});
        rowHeaderList.add(new String[]{"", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "第1年租金", "第2-5年租金", "第6-10年租金", "第11-15年租金", "第16-20年租金", "第21-25年租金", "开发服务费", "设备材料费", "光伏安装施工费", "容量奖励", "时效奖励", "组件", "支架", "并网箱", "逆变器", "电气辅材", "仓储", "仓储调拨", "装卸", "物流", "管理费", "财务费", ""});
        return rowHeaderList;
    }

    @Override
    public void mergeCell(Sheet sheet) {
        // 合并单元格
        // 合并参数分别为： 起始行，结束行，起始列，结束列
        // 将第一列 的 第一行到第三行合并
        sheet.addMergedRegion(new CellRangeAddress(0, 2, 0, 0));
        sheet.addMergedRegion(new CellRangeAddress(0, 2, 1, 1));
        sheet.addMergedRegion(new CellRangeAddress(0, 2, 2, 2));
        sheet.addMergedRegion(new CellRangeAddress(0, 2, 3, 3));
        sheet.addMergedRegion(new CellRangeAddress(0, 2, 4, 4));
        sheet.addMergedRegion(new CellRangeAddress(0, 2, 5, 5));
        sheet.addMergedRegion(new CellRangeAddress(0, 2, 6, 6));
        sheet.addMergedRegion(new CellRangeAddress(0, 2, 7, 7));
        sheet.addMergedRegion(new CellRangeAddress(0, 2, 8, 8));
        sheet.addMergedRegion(new CellRangeAddress(0, 2, 9, 9));
        sheet.addMergedRegion(new CellRangeAddress(0, 2, 10, 10));
        sheet.addMergedRegion(new CellRangeAddress(0, 2, 11, 11));
        sheet.addMergedRegion(new CellRangeAddress(0, 2, 12, 12));
        sheet.addMergedRegion(new CellRangeAddress(0, 2, 13, 13));
        sheet.addMergedRegion(new CellRangeAddress(0, 2, 14, 14));
        sheet.addMergedRegion(new CellRangeAddress(0, 1, 15, 20));
        sheet.addMergedRegion(new CellRangeAddress(0, 0, 21, 37));
        sheet.addMergedRegion(new CellRangeAddress(1, 1, 21, 23));
        sheet.addMergedRegion(new CellRangeAddress(1, 1, 24, 25));
        sheet.addMergedRegion(new CellRangeAddress(1, 1, 26, 30));
        sheet.addMergedRegion(new CellRangeAddress(1, 1, 31, 36));
        sheet.addMergedRegion(new CellRangeAddress(1, 2, 37, 37));
    }

    @Override
    public List<ActualCostVO> getExportData(CostAnalysisDTO dto) {
        IPage<ActualCostVO> stationCostVOIPage = stationCostAnalysisService.actualCostPage(new Page<>(-1, -1), dto);
        return stationCostVOIPage.getRecords();
    }

    @Override
    public void fillSheetByRecords(Sheet sheet, List<ActualCostVO> records, Integer rowNum, Integer columnNum, XSSFCellStyle contentStyle) {
        if (CollectionUtils.isEmpty(records)) {
            return;
        }

        for (ActualCostVO record : records) {
            Row tempRow = sheet.createRow(rowNum++);
            tempRow.setHeight((short) 500);
            for (int i = 0; i < columnNum; i++) {
                //列宽自适应，j为自适应的列，true就是自适应，false就是不自适应，默认不自适应
                sheet.autoSizeColumn(i, true);
                Cell tempCell = tempRow.createCell(i);
                tempCell.setCellStyle(contentStyle);
                String tempValue = "";
                switch (i) {
                    case 0:
                        tempValue = StringUtil.judgeWithDefaultValue(record.getPsCode(), "--");
                        break;
                    case 1:
                        tempValue = StringUtil.judgeWithDefaultValue(record.getProductName(), "--");
                        break;
                    case 2:
                        tempValue = StringUtil.judgeWithDefaultValue(record.getMixFlagName(), "--");
                        break;
                    case 3:
                        tempValue = StringUtil.judgeWithDefaultValue(record.getPropertyOwner(), "--");
                        break;
                    case 4:
                        tempValue = StringUtil.judgeWithDefaultValue(record.getElectricityBillMode(), "--");
                        break;
                    case 5:
                        tempValue = StringUtil.judgeWithDefaultValue(record.getProvinceCityRegion(), "--");
                        break;
                    case 6:
                        tempValue = StringUtil.judgeWithDefaultValue(record.getRecordType(), "--");
                        break;
                    case 7:
                        tempValue = StringUtil.judgeWithDefaultValue(record.getPsStatus(), "--");
                        break;
                    case 8:
                        tempValue = StringUtil.judgeWithDefaultValue(record.getAssemblyType(), "--");
                        break;
                    case 9:
                        tempValue = StringUtil.judgeWithDefaultValue(record.getInstallForm(), "--");
                        break;
                    case 10:
                        tempValue = Objects.nonNull(record.getCreatedTime()) ? DateUtils.format(record.getCreatedTime(), "yyyy/MM/dd"): "--";
                        break;
                    case 11:
                        tempValue = Objects.nonNull(record.getQiTaoTime()) ? DateUtils.format(record.getQiTaoTime(), "yyyy/MM/dd"): "--";
                        break;
                    case 12:
                        tempValue = Objects.nonNull(record.getFirstPowerTime()) ? DateUtils.format(record.getFirstPowerTime(), "yyyy/MM/dd"): "--";
                        break;
                    case 13:
                        tempValue = Objects.nonNull(record.getConnectedTime()) ? DateUtils.format(record.getConnectedTime(), "yyyy/MM/dd"): "--";
                        break;
                    case 14:
                        tempValue = StringUtil.judgeWithDefaultValue(record.getFirstYearHour(), "--");
                        break;
                    case 15:
                        tempValue = StringUtil.judgeWithDefaultValue(record.getOnePhaseYearRent(), "--");
                        break;
                    case 16:
                        tempValue = StringUtil.judgeWithDefaultValue(record.getTwoPhaseYearRent(), "--");
                        break;
                    case 17:
                        tempValue = StringUtil.judgeWithDefaultValue(record.getThreePhaseYearRent(), "--");
                        break;
                    case 18:
                        tempValue = StringUtil.judgeWithDefaultValue(record.getFourPhaseYearRent(), "--");
                        break;
                    case 19:
                        tempValue = StringUtil.judgeWithDefaultValue(record.getFivePhaseYearRent(), "--");
                        break;
                    case 20:
                        tempValue = StringUtil.judgeWithDefaultValue(record.getSixPhaseYearRent(), "--");
                        break;
                    case 21:
                        tempValue = StringUtil.judgeWithDefaultValue(record.getActualDevelopCost(), "--");
                        break;
                    case 22:
                        tempValue = StringUtil.judgeWithDefaultValue(record.getActualDeviceCost(), "--");
                        break;
                    case 23:
                        tempValue = StringUtil.judgeWithDefaultValue(record.getActualInstallCost(), "--");
                        break;
                    case 24:
                        tempValue = StringUtil.judgeWithDefaultValue(record.getActualCapacityReward(), "--");
                        break;
                    case 25:
                        tempValue = StringUtil.judgeWithDefaultValue(record.getActualActualEffectReward(), "--");
                        break;
                    case 26:
                        tempValue = StringUtil.judgeWithDefaultValue(record.getActualAssembly(), "--");
                        break;
                    case 27:
                        tempValue = StringUtil.judgeWithDefaultValue(record.getActualBracket(), "--");
                        break;
                    case 28:
                        tempValue = StringUtil.judgeWithDefaultValue(record.getActualBox(), "--");
                        break;
                    case 29:
                        tempValue = StringUtil.judgeWithDefaultValue(record.getActualInverter(), "--");
                        break;
                    case 30:
                        tempValue = StringUtil.judgeWithDefaultValue(record.getActualFc(), "--");
                        break;
                    case 31:
                        tempValue = StringUtil.judgeWithDefaultValue(record.getActualStorageCost(), "--");
                        break;
                    case 32:
                        tempValue = StringUtil.judgeWithDefaultValue(record.getActualAllotCost(), "--");
                        break;
                    case 33:
                        tempValue = StringUtil.judgeWithDefaultValue(record.getActualMoveCost(), "--");
                        break;
                    case 34:
                        tempValue = StringUtil.judgeWithDefaultValue(record.getActualLogisticsCost(), "--");
                        break;
                    case 35:
                        tempValue = StringUtil.judgeWithDefaultValue(record.getActualManageCost(), "--");
                        break;
                    case 36:
                        tempValue = StringUtil.judgeWithDefaultValue(record.getActualFinanceCost(), "--");
                        break;
                    case 37:
                        tempValue = StringUtil.judgeWithDefaultValue(record.getActualTotalCost(), "--");
                        break;
                }
                tempCell.setCellValue(tempValue);
            }
        }
    }

    @Override
    public void tableTitleStyleColumnWidth(Sheet sheet) {
    }
}
