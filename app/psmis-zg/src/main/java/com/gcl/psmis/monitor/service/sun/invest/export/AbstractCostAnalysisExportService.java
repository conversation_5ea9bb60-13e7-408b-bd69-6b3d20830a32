package com.gcl.psmis.monitor.service.sun.invest.export;

import com.gcl.psmis.framework.common.ResponseResult;
import com.gcl.psmis.framework.common.constant.CommonResultCode;
import com.gcl.psmis.framework.export.executor.ExportExecutor;
import com.gcl.psmis.framework.export.task.ExportTask;
import com.gcl.psmis.framework.oss.config.util.UploadOssUtil;
import com.gcl.psmis.monitor.constants.enums.ExportEnum;
import com.gcl.psmis.monitor.entity.vo.ZgStandProvinceVO;
import lombok.Getter;
import lombok.extern.slf4j.Slf4j;
import org.apache.poi.ss.usermodel.*;
import org.apache.poi.xssf.usermodel.XSSFCellStyle;
import org.apache.poi.xssf.usermodel.XSSFFont;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;
import org.springframework.beans.factory.annotation.Autowired;

import java.io.ByteArrayOutputStream;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.List;

@Slf4j
@Getter
/**
 * @description: 成本分析导出抽象服务类
 * @author: liujinjing
 * @date: 2025/6/10 16:23
 */
public abstract class AbstractCostAnalysisExportService<T, R> {

    protected ExportEnum exportEnum;

    protected Integer rowHeaderNum;

    @Autowired
    private UploadOssUtil ossUtil;

    @Autowired
    private ExportExecutor exportExecutor;

    /**
     * 导出主流程
     */
    public ResponseResult export(R r) {
        // 1. 创建excel工作簿（workbook）:excel2003使用HSSF,excel2007使用XSSF,excel2010使用SXSSF(大数据量)
        XSSFWorkbook workbook = new XSSFWorkbook();
        // 2. 创建excel工作表（sheet）
        Sheet sheet = workbook.createSheet();
        // 3，设置样式以及字体样式
        XSSFCellStyle headerStyle = createHeadCellStyle(workbook);
        XSSFCellStyle contentStyle = createContentCellStyle(workbook);
        // 合并单元格
        mergeCell(sheet);
        // 记录行号
        int rowNum = 0;
        List<String[]> rowHeaderList = getRowHeaderList();
        for (int i = 0; i < rowHeaderNum; i++) {
            Row row = sheet.createRow(rowNum++); // 创建行(索引从0开始)
            row.setHeight((short) 600); // 设置行高
            // 定义表头内容
            String[] rowHeaderArr = rowHeaderList.get(i);
            for (int j = 0; j < rowHeaderArr.length; j++) {
                Cell cell = row.createCell(j); // 创建表头
                cell.setCellValue(rowHeaderArr[j]); // 设置表头字段
                cell.setCellStyle(headerStyle); // 设置表头样式
            }
        }

        // 设置表头单元格的宽度
        tableTitleStyleColumnWidth(sheet);

        // 获取导出数据
        List<T> records = getExportData(r);
        // 数据填充
        fillSheetByRecords(sheet, records, rowNum, rowHeaderList.get(0).length, contentStyle);
        // 导出excel
        try {
            ByteArrayOutputStream bao = new ByteArrayOutputStream();
            workbook.write(bao);
            String path = ossUtil.upload(bao, exportEnum.getDesc() + LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyyMMdd")) + ".xlsx");
            ExportTask<ZgStandProvinceVO> exportTask = new ExportTask<>(path, exportEnum.getDesc() + LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyyMMdd")));
            exportExecutor.addTask(exportTask);
        } catch (Exception e) {
            log.error("导出{}失败,{}", exportEnum.getDesc(), e.getMessage());
            return new ResponseResult(CommonResultCode.FAIL, e.getMessage());
        }
        return new ResponseResult(CommonResultCode.SUCCESS);
    }

    public abstract List<String[]> getRowHeaderList();

    /**
     * 合并单元格
     */
    public abstract void mergeCell(Sheet sheet);

    /**
     * 获取导出数据
     */
    public abstract List<T> getExportData(R r);

    /**
     * 填充数据
     */
    public abstract void fillSheetByRecords(Sheet sheet, List<T> records, Integer rowNum, Integer columnNum, XSSFCellStyle contentStyle);
    /**
     * 设置表头单元格的宽度
     */
    public abstract void tableTitleStyleColumnWidth(Sheet sheet);

    /**
     * 创建表头样式
     *
     * @param wb
     * @return
     */
    private static XSSFCellStyle createHeadCellStyle(XSSFWorkbook wb) {
        XSSFCellStyle cellStyle = wb.createCellStyle();
        cellStyle.setWrapText(true);// 设置自动换行
        cellStyle.setFillForegroundColor(IndexedColors.GREY_25_PERCENT.getIndex());//背景颜色
        cellStyle.setAlignment(HorizontalAlignment.CENTER); //水平居中
        cellStyle.setVerticalAlignment(VerticalAlignment.CENTER); //垂直对齐
        cellStyle.setFillPattern(FillPatternType.SOLID_FOREGROUND);
//        cellStyle.setBottomBorderColor(IndexedColors.BLACK.index);
        cellStyle.setBorderBottom(BorderStyle.THIN); //下边框
        cellStyle.setBorderLeft(BorderStyle.THIN); //左边框
        cellStyle.setBorderRight(BorderStyle.THIN); //右边框
        cellStyle.setBorderTop(BorderStyle.THIN); //上边框
        XSSFFont headerFont = (XSSFFont) wb.createFont(); // 创建字体样式
        headerFont.setBold(true); //字体加粗
        headerFont.setFontName("黑体"); // 设置字体类型
        headerFont.setFontHeightInPoints((short) 12); // 设置字体大小
        cellStyle.setFont(headerFont); // 为标题样式设置字体样式
        return cellStyle;
    }

    /**
     * 创建内容样式
     *
     * @param wb
     * @return
     */
    private static XSSFCellStyle createContentCellStyle(XSSFWorkbook wb) {
        XSSFCellStyle cellStyle = wb.createCellStyle();
        cellStyle.setVerticalAlignment(VerticalAlignment.CENTER);// 垂直居中
        cellStyle.setAlignment(HorizontalAlignment.CENTER);// 水平居中
        cellStyle.setWrapText(false);// 设置不自动换行
        // 生成12号字体
        XSSFFont font = wb.createFont();
        font.setColor((short) 8);
        font.setFontHeightInPoints((short) 12);
        cellStyle.setFont(font);
        return cellStyle;
    }
}
