package com.gcl.psmis.monitor.entity.request.invest.income;

import com.gcl.psmis.framework.common.req.base.BasePageReq;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.util.List;

@Data
@ApiModel(value = "RevenueCalcRequest", description = "投资收益测算请求类")
public class RevenueCalcRequest extends BasePageReq {

    @ApiModelProperty("省Id")
    private Integer provinceId;

    @ApiModelProperty("市Id")
    private Integer cityId;

    @ApiModelProperty("安装形式")
    @NotEmpty(message = "安装形式必选")
    private List<Integer> installFormId;

    @ApiModelProperty("并网月份")
    private String connectedMonth;

    @ApiModelProperty("组件功率")
    @NotEmpty(message = "组件功率必选")
    private List<Integer> zuPowers;

    @ApiModelProperty("资方报价版本ID")
    @NotNull(message = "资方报价版本必选")
    private Integer quotaVersionId;

    @ApiModelProperty("导出列集合")
    private List<String> collumList;

}
