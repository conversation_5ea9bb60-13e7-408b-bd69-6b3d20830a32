package com.gcl.psmis.monitor.service.sun.invest;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.date.DateTime;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.map.MapUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.gcl.psmis.framework.common.ResponseResult;
import com.gcl.psmis.framework.common.constant.CommonResultCode;
import com.gcl.psmis.framework.common.exception.BussinessException;
import com.gcl.psmis.framework.common.vo.invest.InstallFormVO;
import com.gcl.psmis.framework.export.executor.ExportExecutor;
import com.gcl.psmis.framework.export.task.ExportAsyncDataTask;
import com.gcl.psmis.framework.mbg.entity.TInvestInstallFormEntity;
import com.gcl.psmis.framework.mbg.entity.TInvestIrrAllEntity;
import com.gcl.psmis.framework.mbg.entity.TInvestQuotaEntity;
import com.gcl.psmis.framework.mbg.entity.TModelPsCostEntity;
import com.gcl.psmis.framework.mbg.service.*;
import com.gcl.psmis.monitor.dao.sun.invmod.InvestmentModelDao;
import com.gcl.psmis.monitor.entity.request.invest.income.DeviceCostRequest;
import com.gcl.psmis.monitor.entity.request.invest.income.RevenueCalcRequest;
import com.gcl.psmis.monitor.entity.request.invest.income.RevenueContrastRequest;
import com.gcl.psmis.monitor.entity.request.invest.income.SelectInstallFormRequest;
import com.gcl.psmis.monitor.entity.vo.invest.income.DeviceCostVO;
import com.gcl.psmis.monitor.entity.vo.invest.income.PsCusInfoVO;
import com.gcl.psmis.monitor.entity.vo.invest.income.RevenueCalcVO;
import com.gcl.psmis.monitor.entity.vo.invest.income.RevenueContrastVO;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.Arrays;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.function.Function;
import java.util.stream.Collectors;

@Slf4j
@Service
public class InvestIncomeAnalysisService {

    @Autowired
    private InvestmentModelDao investmentModelDao;
    @Autowired
    private TInvestInstallFormService tInvestInstallFormService;
    @Autowired
    private TModelPsCostService tModelPsCostService;
    @Autowired
    private TInvestQuotaService tInvestQuotaService;
    @Autowired
    private TInvestIrrAllService tInvestIrrAllService;
    @Autowired
    private TUserService tUserService;

    @Autowired
    private ExportExecutor exportExecutor;

    private final BigDecimal taxRate = new BigDecimal("0.13");

    public List<InstallFormVO> getInstallForm(SelectInstallFormRequest request) {
        return investmentModelDao.getInstallFormNew(request);
    }

    public List<RevenueContrastVO> revenueContrastList(RevenueContrastRequest request) {
        List<RevenueContrastVO> resultList = Lists.newArrayList();
        if (Objects.nonNull(request.getInstallFormId())) {
            TInvestInstallFormEntity installForm = tInvestInstallFormService.getById(request.getInstallFormId());
            request.setInstallFormName(installForm.getInstallFormName());
        }
        LambdaQueryWrapper<TModelPsCostEntity> modelPsCostQueryWrapper = new LambdaQueryWrapper<>();
        modelPsCostQueryWrapper.eq(TModelPsCostEntity::getProvinceId, request.getProvinceId());
        if (request.getCityId() != null && request.getCityId() != 0) {
            modelPsCostQueryWrapper.eq(TModelPsCostEntity::getCityId, request.getCityId());
        }
        modelPsCostQueryWrapper.eq(TModelPsCostEntity::getInstallForm, request.getInstallFormName());
        modelPsCostQueryWrapper.eq(TModelPsCostEntity::getAssemblyPower, String.valueOf(request.getZuPower()));
        modelPsCostQueryWrapper.isNotNull(TModelPsCostEntity::getConnectedTime);
        if (StrUtil.isNotBlank(request.getConnectedMonth())) {
            DateTime parseTime = DateUtil.parse(request.getConnectedMonth(), "yyyy/MM");
            DateTime beginTime = DateUtil.beginOfMonth(parseTime);
            DateTime endTime = DateUtil.endOfMonth(parseTime);
            modelPsCostQueryWrapper.between(TModelPsCostEntity::getConnectedTime, beginTime, endTime);
        }
        List<TModelPsCostEntity> modelPsCostList = tModelPsCostService.list(modelPsCostQueryWrapper);
        if (CollUtil.isEmpty(modelPsCostList)) {
            return resultList;
        }

        // 装机容量
        BigDecimal capitalSum = modelPsCostList.stream().map(TModelPsCostEntity::getCapital).reduce(BigDecimal::add).orElse(BigDecimal.ZERO);

        // 设备成本-组件
        BigDecimal theoryAssemblySum = BigDecimal.ZERO;
        BigDecimal actualAssemblySum = BigDecimal.ZERO;
        // 设备成本-支架
        BigDecimal theoryBracketSum = BigDecimal.ZERO;
        BigDecimal actualBracketSum = BigDecimal.ZERO;
        // 设备成本-逆变器
        BigDecimal theoryBoxSum = BigDecimal.ZERO;
        BigDecimal actualBoxSum = BigDecimal.ZERO;
        // 设备成本-并网箱
        BigDecimal theoryInverterSum = BigDecimal.ZERO;
        BigDecimal actualInverterSum = BigDecimal.ZERO;
        // 设备成本-小辅材
        BigDecimal theoryFcSum = BigDecimal.ZERO;
        BigDecimal actualFcSum = BigDecimal.ZERO;

        // 代理商成本-开发服务费
        BigDecimal theoryDevelopCostSum = BigDecimal.ZERO;
        BigDecimal actualDevelopCostSum = BigDecimal.ZERO;
        // 代理商成本-设备材料费
        BigDecimal theoryDeviceCostSum = BigDecimal.ZERO;
        BigDecimal actualDeviceCostSum = BigDecimal.ZERO;
        // 代理商成本-光伏安装施工费
        BigDecimal theoryInstallCostSum = BigDecimal.ZERO;
        BigDecimal actualInstallCostSum = BigDecimal.ZERO;

        // 代理商奖励-容量奖励
        BigDecimal theoryCapacityRewardSum = BigDecimal.ZERO;
        BigDecimal actualCapacityRewardSum = BigDecimal.ZERO;
        // 代理商奖励-时效奖励
        BigDecimal theoryActualEffectRewardSum = BigDecimal.ZERO;
        BigDecimal actualActualEffectRewardSum = BigDecimal.ZERO;

        // 管理费用-供应链
        BigDecimal theorySupplyChainCostSum = BigDecimal.ZERO;
        BigDecimal actualSupplyChainCostSum = BigDecimal.ZERO;
        // 管理费用-管理费
        BigDecimal theoryManageCostSum = BigDecimal.ZERO;
        BigDecimal actualManageCostSum = BigDecimal.ZERO;
        // 管理费用-财务费
        BigDecimal theoryFinanceCostSum = BigDecimal.ZERO;
        BigDecimal actualFinanceCostSum = BigDecimal.ZERO;

        for (TModelPsCostEntity modelPsCost : modelPsCostList) {
            // 设备成本-组件
            theoryAssemblySum = theoryAssemblySum.add(Objects.nonNull(modelPsCost.getTheoryAssembly()) ? modelPsCost.getTheoryAssembly().multiply(modelPsCost.getCapital()) : BigDecimal.ZERO);
            actualAssemblySum = actualAssemblySum.add(Objects.nonNull(modelPsCost.getActualAssembly()) ? modelPsCost.getActualAssembly().multiply(modelPsCost.getCapital()) : BigDecimal.ZERO);
            // 设备成本-支架
            theoryBracketSum = theoryBracketSum.add(Objects.nonNull(modelPsCost.getTheoryBracket()) ? modelPsCost.getTheoryBracket().multiply(modelPsCost.getCapital()) : BigDecimal.ZERO);
            actualBracketSum = actualBracketSum.add(Objects.nonNull(modelPsCost.getActualBracket()) ? modelPsCost.getActualBracket().multiply(modelPsCost.getCapital()) : BigDecimal.ZERO);
            // 设备成本-逆变器
            theoryBoxSum = theoryBoxSum.add(Objects.nonNull(modelPsCost.getTheoryBox()) ? modelPsCost.getTheoryBox().multiply(modelPsCost.getCapital()) : BigDecimal.ZERO);
            actualBoxSum = actualBoxSum.add(Objects.nonNull(modelPsCost.getActualBox()) ? modelPsCost.getActualBox().multiply(modelPsCost.getCapital()) : BigDecimal.ZERO);
            // 设备成本-并网箱
            theoryInverterSum = theoryInverterSum.add(Objects.nonNull(modelPsCost.getTheoryInverter()) ? modelPsCost.getTheoryInverter().multiply(modelPsCost.getCapital()) : BigDecimal.ZERO);
            actualInverterSum = actualInverterSum.add(Objects.nonNull(modelPsCost.getActualInverter()) ? modelPsCost.getActualInverter().multiply(modelPsCost.getCapital()) : BigDecimal.ZERO);
            // 设备成本-小辅材
            theoryFcSum = theoryFcSum.add(Objects.nonNull(modelPsCost.getTheoryFc()) ? modelPsCost.getTheoryFc().multiply(modelPsCost.getCapital()) : BigDecimal.ZERO);
            actualFcSum = actualFcSum.add(Objects.nonNull(modelPsCost.getActualFc()) ? modelPsCost.getActualFc().multiply(modelPsCost.getCapital()) : BigDecimal.ZERO);

            // 代理商成本-开发服务费
            theoryDevelopCostSum = theoryDevelopCostSum.add(Objects.nonNull(modelPsCost.getTheoryDevelopCost()) ? modelPsCost.getTheoryDevelopCost().multiply(modelPsCost.getCapital()) : BigDecimal.ZERO);
            actualDevelopCostSum = actualDevelopCostSum.add(Objects.nonNull(modelPsCost.getActualDevelopCost()) ? modelPsCost.getActualDevelopCost().multiply(modelPsCost.getCapital()) : BigDecimal.ZERO);
            // 代理商成本-设备材料费
            theoryDeviceCostSum = theoryDeviceCostSum.add(Objects.nonNull(modelPsCost.getTheoryDeviceCost()) ? modelPsCost.getTheoryDeviceCost().multiply(modelPsCost.getCapital()) : BigDecimal.ZERO);
            actualDeviceCostSum = actualDeviceCostSum.add(Objects.nonNull(modelPsCost.getActualDeviceCost()) ? modelPsCost.getActualDeviceCost().multiply(modelPsCost.getCapital()) : BigDecimal.ZERO);
            // 代理商成本-光伏安装施工费
            theoryInstallCostSum = theoryInstallCostSum.add(Objects.nonNull(modelPsCost.getTheoryInstallCost()) ? modelPsCost.getTheoryInstallCost().multiply(modelPsCost.getCapital()) : BigDecimal.ZERO);
            actualInstallCostSum = actualInstallCostSum.add(Objects.nonNull(modelPsCost.getActualInstallCost()) ? modelPsCost.getActualInstallCost().multiply(modelPsCost.getCapital()) : BigDecimal.ZERO);

            // 代理商奖励-容量奖励
            theoryCapacityRewardSum = theoryCapacityRewardSum.add(Objects.nonNull(modelPsCost.getTheoryCapacityReward()) ? modelPsCost.getTheoryCapacityReward().multiply(modelPsCost.getCapital()) : BigDecimal.ZERO);
            actualCapacityRewardSum = actualCapacityRewardSum.add(Objects.nonNull(modelPsCost.getActualCapacityReward()) ? modelPsCost.getActualCapacityReward().multiply(modelPsCost.getCapital()) : BigDecimal.ZERO);
            // 代理商奖励-时效奖励
            theoryActualEffectRewardSum = theoryActualEffectRewardSum.add(Objects.nonNull(modelPsCost.getTheoryActualEffectReward()) ? modelPsCost.getTheoryActualEffectReward().multiply(modelPsCost.getCapital()) : BigDecimal.ZERO);
            actualActualEffectRewardSum = actualActualEffectRewardSum.add(Objects.nonNull(modelPsCost.getActualActualEffectReward()) ? modelPsCost.getActualActualEffectReward().multiply(modelPsCost.getCapital()) : BigDecimal.ZERO);

            // 管理费用-供应链
            theorySupplyChainCostSum = theorySupplyChainCostSum.add(Objects.nonNull(modelPsCost.getTheorySupplyChainCost()) ? modelPsCost.getTheorySupplyChainCost().multiply(modelPsCost.getCapital()) : BigDecimal.ZERO);
            actualSupplyChainCostSum = actualSupplyChainCostSum.add(Objects.nonNull(modelPsCost.getActualSupplyChainCost()) ? modelPsCost.getActualSupplyChainCost().multiply(modelPsCost.getCapital()) : BigDecimal.ZERO);
            // 管理费用-管理费
            theoryManageCostSum = theoryManageCostSum.add(Objects.nonNull(modelPsCost.getTheoryManageCost()) ? modelPsCost.getTheoryManageCost().multiply(modelPsCost.getCapital()) : BigDecimal.ZERO);
            actualManageCostSum = actualManageCostSum.add(Objects.nonNull(modelPsCost.getActualManageCost()) ? modelPsCost.getActualManageCost().multiply(modelPsCost.getCapital()) : BigDecimal.ZERO);
            // 管理费用-财务费
            theoryFinanceCostSum = theoryFinanceCostSum.add(Objects.nonNull(modelPsCost.getTheoryFinanceCost()) ? modelPsCost.getTheoryFinanceCost().multiply(modelPsCost.getCapital()) : BigDecimal.ZERO);
            actualFinanceCostSum = actualFinanceCostSum.add(Objects.nonNull(modelPsCost.getActualFinanceCost()) ? modelPsCost.getActualFinanceCost().multiply(modelPsCost.getCapital()) : BigDecimal.ZERO);
        }

        // 设备成本-组件
        BigDecimal theoryAssembly = theoryAssemblySum.divide(capitalSum, 3, RoundingMode.HALF_UP);
        BigDecimal actualAssembly = actualAssemblySum.divide(capitalSum, 3, RoundingMode.HALF_UP);
        resultList.add(RevenueContrastVO.builder().costType("设备成本").costItem("组件").theoryCost(theoryAssembly).actualCost(actualAssembly).diffCost(theoryAssembly.subtract(actualAssembly)).build());
        // 设备成本-支架
        BigDecimal theoryBracket = theoryBracketSum.divide(capitalSum, 3, RoundingMode.HALF_UP);
        BigDecimal actualBracket = actualBracketSum.divide(capitalSum, 3, RoundingMode.HALF_UP);
        resultList.add(RevenueContrastVO.builder().costType("设备成本").costItem("支架").theoryCost(theoryBracket).actualCost(actualBracket).diffCost(theoryBracket.subtract(actualBracket)).build());
        // 设备成本-逆变器
        BigDecimal theoryBox = theoryBoxSum.divide(capitalSum, 3, RoundingMode.HALF_UP);
        BigDecimal actualBox = actualBoxSum.divide(capitalSum, 3, RoundingMode.HALF_UP);
        resultList.add(RevenueContrastVO.builder().costType("设备成本").costItem("逆变器").theoryCost(theoryBox).actualCost(actualBox).diffCost(theoryBox.subtract(actualBox)).build());
        // 设备成本-并网箱
        BigDecimal theoryInverter = theoryInverterSum.divide(capitalSum, 3, RoundingMode.HALF_UP);
        BigDecimal actualInverter = actualInverterSum.divide(capitalSum, 3, RoundingMode.HALF_UP);
        resultList.add(RevenueContrastVO.builder().costType("设备成本").costItem("并网箱").theoryCost(theoryInverter).actualCost(actualInverter).diffCost(theoryInverter.subtract(actualInverter)).build());
        // 设备成本-小辅材
        BigDecimal theoryFc = theoryFcSum.divide(capitalSum, 3, RoundingMode.HALF_UP);
        BigDecimal actualFc = actualFcSum.divide(capitalSum, 3, RoundingMode.HALF_UP);
        resultList.add(RevenueContrastVO.builder().costType("设备成本").costItem("小辅材").theoryCost(theoryFc).actualCost(actualFc).diffCost(theoryFc.subtract(actualFc)).build());

        // 代理商成本-开发服务费
        BigDecimal theoryDevelopCost = theoryDevelopCostSum.divide(capitalSum, 3, RoundingMode.HALF_UP);
        BigDecimal actualDevelopCost = actualDevelopCostSum.divide(capitalSum, 3, RoundingMode.HALF_UP);
        resultList.add(RevenueContrastVO.builder().costType("代理商成本").costItem("开发服务费").theoryCost(theoryDevelopCost).actualCost(actualDevelopCost).diffCost(theoryDevelopCost.subtract(actualDevelopCost)).build());
        // 代理商成本-设备材料费
        BigDecimal theoryDeviceCost = theoryDeviceCostSum.divide(capitalSum, 3, RoundingMode.HALF_UP);
        BigDecimal actualDeviceCost = actualDeviceCostSum.divide(capitalSum, 3, RoundingMode.HALF_UP);
        resultList.add(RevenueContrastVO.builder().costType("代理商成本").costItem("设备材料费").theoryCost(theoryDeviceCost).actualCost(actualDeviceCost).diffCost(theoryDeviceCost.subtract(actualDeviceCost)).build());
        // 代理商成本-光伏安装施工费
        BigDecimal theoryInstallCost = theoryInstallCostSum.divide(capitalSum, 3, RoundingMode.HALF_UP);
        BigDecimal actualInstallCost = actualInstallCostSum.divide(capitalSum, 3, RoundingMode.HALF_UP);
        resultList.add(RevenueContrastVO.builder().costType("代理商成本").costItem("光伏安装施工费").theoryCost(theoryInstallCost).actualCost(actualInstallCost).diffCost(theoryInstallCost.subtract(actualInstallCost)).build());

        // 代理商奖励-容量奖励
        BigDecimal theoryCapacityReward = theoryCapacityRewardSum.divide(capitalSum, 3, RoundingMode.HALF_UP);
        BigDecimal actualCapacityReward = actualCapacityRewardSum.divide(capitalSum, 3, RoundingMode.HALF_UP);
        resultList.add(RevenueContrastVO.builder().costType("代理商奖励").costItem("容量奖励").theoryCost(theoryCapacityReward).actualCost(actualCapacityReward).diffCost(theoryCapacityReward.subtract(actualCapacityReward)).build());
        // 代理商奖励-时效奖励
        BigDecimal theoryActualEffectReward = theoryActualEffectRewardSum.divide(capitalSum, 3, RoundingMode.HALF_UP);
        BigDecimal actualActualEffectReward = actualActualEffectRewardSum.divide(capitalSum, 3, RoundingMode.HALF_UP);
        resultList.add(RevenueContrastVO.builder().costType("代理商奖励").costItem("时效奖励").theoryCost(theoryActualEffectReward).actualCost(actualActualEffectReward).diffCost(theoryActualEffectReward.subtract(actualActualEffectReward)).build());

        // 管理费用-供应链
        BigDecimal theorySupplyChainCost = theorySupplyChainCostSum.divide(capitalSum, 3, RoundingMode.HALF_UP);
        BigDecimal actualSupplyChainCost = actualSupplyChainCostSum.divide(capitalSum, 3, RoundingMode.HALF_UP);
        resultList.add(RevenueContrastVO.builder().costType("管理费用").costItem("供应链").theoryCost(theorySupplyChainCost).actualCost(actualSupplyChainCost).diffCost(theorySupplyChainCost.subtract(actualSupplyChainCost)).build());
        // 管理费用-管理费
        BigDecimal theoryManageCost = theoryManageCostSum.divide(capitalSum, 3, RoundingMode.HALF_UP);
        BigDecimal actualManageCost = actualManageCostSum.divide(capitalSum, 3, RoundingMode.HALF_UP);
        resultList.add(RevenueContrastVO.builder().costType("管理费用").costItem("管理费").theoryCost(theoryManageCost).actualCost(actualManageCost).diffCost(theoryManageCost.subtract(actualManageCost)).build());
        // 管理费用-财务费
        BigDecimal theoryFinanceCost = theoryFinanceCostSum.divide(capitalSum, 3, RoundingMode.HALF_UP);
        BigDecimal actualFinanceCost = actualFinanceCostSum.divide(capitalSum, 3, RoundingMode.HALF_UP);
        resultList.add(RevenueContrastVO.builder().costType("管理费用").costItem("财务费").theoryCost(theoryFinanceCost).actualCost(actualFinanceCost).diffCost(theoryFinanceCost.subtract(actualFinanceCost)).build());

        return resultList;
    }

    public ResponseResult exportRevenueContrast(RevenueContrastRequest request) {
        ExportAsyncDataTask exportAsyncDataTask = new ExportAsyncDataTask(() -> {
            List<RevenueContrastVO> resultList = this.revenueContrastList(request);
            // 计算设备成本总计
            BigDecimal theoryEquipmentCostTotal = resultList.subList(0, 5).stream().map(RevenueContrastVO::getTheoryCost).reduce(BigDecimal::add).orElse(BigDecimal.ZERO);
            BigDecimal actualEquipmentCostTotal = resultList.subList(0, 5).stream().map(RevenueContrastVO::getActualCost).reduce(BigDecimal::add).orElse(BigDecimal.ZERO);
            // 计算代理商成本总计
            BigDecimal theoryAgentCostTotal = resultList.subList(5, 8).stream().map(RevenueContrastVO::getTheoryCost).reduce(BigDecimal::add).orElse(BigDecimal.ZERO);
            BigDecimal actualAgentCostTotal = resultList.subList(5, 8).stream().map(RevenueContrastVO::getActualCost).reduce(BigDecimal::add).orElse(BigDecimal.ZERO);
            // 计算代理商奖励总计
            BigDecimal theoryAgentRewardTotal = resultList.subList(8, 10).stream().map(RevenueContrastVO::getTheoryCost).reduce(BigDecimal::add).orElse(BigDecimal.ZERO);
            BigDecimal actualAgentRewardTotal = resultList.subList(8, 10).stream().map(RevenueContrastVO::getActualCost).reduce(BigDecimal::add).orElse(BigDecimal.ZERO);
            // 计算管理费用总计
            BigDecimal theoryManageCostTotal = resultList.subList(10, 13).stream().map(RevenueContrastVO::getTheoryCost).reduce(BigDecimal::add).orElse(BigDecimal.ZERO);
            BigDecimal actualManageCostTotal = resultList.subList(10, 13).stream().map(RevenueContrastVO::getActualCost).reduce(BigDecimal::add).orElse(BigDecimal.ZERO);
            // 插入设备成本总计、代理商成本总计、代理商奖励总计、管理费用总计
            resultList.add(0, RevenueContrastVO.builder().costType("设备成本").costItem("设备成本").theoryCost(theoryEquipmentCostTotal).actualCost(actualEquipmentCostTotal).diffCost(theoryEquipmentCostTotal.subtract(actualEquipmentCostTotal)).build());
            resultList.add(6, RevenueContrastVO.builder().costType("代理商成本").costItem("代理商成本").theoryCost(theoryAgentCostTotal).actualCost(actualAgentCostTotal).diffCost(theoryAgentCostTotal.subtract(actualAgentCostTotal)).build());
            resultList.add(10,RevenueContrastVO.builder().costType("代理商奖励").costItem("代理商奖励").theoryCost(theoryAgentRewardTotal).actualCost(actualAgentRewardTotal).diffCost(theoryAgentRewardTotal.subtract(actualAgentRewardTotal)).build());
            resultList.add(13,RevenueContrastVO.builder().costType("管理费用").costItem("管理费用").theoryCost(theoryManageCostTotal).actualCost(actualManageCostTotal).diffCost(theoryManageCostTotal.subtract(actualManageCostTotal)).build());
            // 总计
            BigDecimal theoryTotal = theoryEquipmentCostTotal.add(theoryAgentCostTotal).add(theoryAgentRewardTotal).add(theoryManageCostTotal);
            BigDecimal actualTotal = actualEquipmentCostTotal.add(actualAgentCostTotal).add(actualAgentRewardTotal).add(actualManageCostTotal);
            resultList.add(RevenueContrastVO.builder().costType("总计").costItem("总计").theoryCost(theoryTotal).actualCost(actualTotal).diffCost(theoryTotal.subtract(actualTotal)).build());
            return resultList;
        }, Arrays.asList("费用科目", "理论成本(元/W)", "实际成本(元/W)", "理论成本-实际成本(元/W)"), "投资收益对比" + DateUtil.format(new DateTime(), "yyyyMMdd"));
        exportExecutor.addTask(exportAsyncDataTask);
        return new ResponseResult(CommonResultCode.SUCCESS);
    }

    public List<InstallFormVO> getInstallFormForCapital(SelectInstallFormRequest request) {
        return investmentModelDao.getInstallFormForCapitalNew(request);
    }

    public IPage<RevenueCalcVO> revenueCalcPage(IPage<RevenueCalcVO> page, RevenueCalcRequest request) {
        IPage<RevenueCalcVO> revenueCalcVOIPage = investmentModelDao.revenueCalcPage(page, request);
        if (CollUtil.isEmpty(revenueCalcVOIPage.getRecords())) {
            return revenueCalcVOIPage;
        }
        List<RevenueCalcVO> revenueCalcVOList = revenueCalcVOIPage.getRecords();
        // 获取备案方式 1个人备案 2企业备案
        Integer regType = Integer.valueOf(tInvestQuotaService.lambdaQuery()
                .eq(TInvestQuotaEntity::getStatus, 0)
                .eq(TInvestQuotaEntity::getId, request.getQuotaVersionId())
                .one().getPowerGridMergeType());
        for (RevenueCalcVO revenueCalcVO : revenueCalcVOList) {
            LambdaQueryWrapper<TModelPsCostEntity> modelPsCostQueryWrapper = new LambdaQueryWrapper<>();
            modelPsCostQueryWrapper.eq(TModelPsCostEntity::getProvinceId, request.getProvinceId());
            if (request.getCityId() != null && request.getCityId() != 0) {
                modelPsCostQueryWrapper.eq(TModelPsCostEntity::getCityId, request.getCityId());
            }
            modelPsCostQueryWrapper.eq(TModelPsCostEntity::getInstallForm, request.getInstallFormId().get(0));
            modelPsCostQueryWrapper.eq(TModelPsCostEntity::getAssemblyPower, String.valueOf(revenueCalcVO.getZuPower()));
            modelPsCostQueryWrapper.isNotNull(TModelPsCostEntity::getConnectedTime);
            if (StrUtil.isNotBlank(request.getConnectedMonth())) {
                DateTime parseTime = DateUtil.parse(request.getConnectedMonth(), "yyyy/MM");
                DateTime beginTime = DateUtil.beginOfMonth(parseTime);
                DateTime endTime = DateUtil.endOfMonth(parseTime);
                modelPsCostQueryWrapper.between(TModelPsCostEntity::getConnectedTime, beginTime, endTime);
            }
            List<TModelPsCostEntity> modelPsCostList = tModelPsCostService.list(modelPsCostQueryWrapper);
            BigDecimal theoryTotalCostSum = modelPsCostList.stream().map(TModelPsCostEntity::getTheoryTotalCost).reduce(BigDecimal::add).orElse(BigDecimal.ZERO);
            BigDecimal actualTotalCostSum = modelPsCostList.stream().map(TModelPsCostEntity::getActualTotalCost).reduce(BigDecimal::add).orElse(BigDecimal.ZERO);
            revenueCalcVO.setTheoryCost(theoryTotalCostSum);
            revenueCalcVO.setActualCost(actualTotalCostSum);
            revenueCalcVO.setPremium();
            // 查询理论IRR
            LambdaQueryWrapper<TInvestIrrAllEntity> theoryIrrQueryWrapper = new LambdaQueryWrapper<>();
            theoryIrrQueryWrapper.eq(TInvestIrrAllEntity::getDelFlag, 0);
            theoryIrrQueryWrapper.eq(TInvestIrrAllEntity::getProvinceId, request.getProvinceId());
            theoryIrrQueryWrapper.eq(TInvestIrrAllEntity::getCityId, request.getCityId());
            theoryIrrQueryWrapper.eq(TInvestIrrAllEntity::getInstallFormId, request.getInstallFormId().get(0));
            theoryIrrQueryWrapper.eq(TInvestIrrAllEntity::getZuPower, revenueCalcVO.getZuPower());
            theoryIrrQueryWrapper.eq(TInvestIrrAllEntity::getProjectType, regType);
//            theoryIrrQueryWrapper.eq(TInvestIrrAllEntity::getInvModId, null);
            theoryIrrQueryWrapper.eq(TInvestIrrAllEntity::getIrrSource, 0);
            theoryIrrQueryWrapper.eq(TInvestIrrAllEntity::getIrrType, 1);
            theoryIrrQueryWrapper.eq(TInvestIrrAllEntity::getDimension, 0);
            List<TInvestIrrAllEntity> theoryIrrList = tInvestIrrAllService.list(theoryIrrQueryWrapper);
            if (CollUtil.isNotEmpty(theoryIrrList)) {
                revenueCalcVO.setTheoryRevenueRate(theoryIrrList.get(0).getIrr());
            }
            // 查询实际IRR
            LambdaQueryWrapper<TInvestIrrAllEntity> actualIrrQueryWrapper = new LambdaQueryWrapper<>();
            actualIrrQueryWrapper.eq(TInvestIrrAllEntity::getDelFlag, 0);
            actualIrrQueryWrapper.eq(TInvestIrrAllEntity::getProvinceId, request.getProvinceId());
            actualIrrQueryWrapper.eq(TInvestIrrAllEntity::getCityId, request.getCityId());
            actualIrrQueryWrapper.eq(TInvestIrrAllEntity::getInstallFormId, request.getInstallFormId().get(0));
            actualIrrQueryWrapper.eq(TInvestIrrAllEntity::getZuPower, revenueCalcVO.getZuPower());
            actualIrrQueryWrapper.eq(TInvestIrrAllEntity::getProjectType, regType);
            if (StrUtil.isNotBlank(request.getConnectedMonth())) {
                actualIrrQueryWrapper.likeRight(TInvestIrrAllEntity::getConnectedTime, request.getConnectedMonth().replace("/", "-"));
            }
            actualIrrQueryWrapper.eq(TInvestIrrAllEntity::getIrrSource, 0);
            actualIrrQueryWrapper.eq(TInvestIrrAllEntity::getIrrType, 0);
            actualIrrQueryWrapper.eq(TInvestIrrAllEntity::getDimension, 0);
            List<TInvestIrrAllEntity> actualIrrList = tInvestIrrAllService.list(actualIrrQueryWrapper);
            if (CollUtil.isNotEmpty(actualIrrList)) {
                revenueCalcVO.setFactRevenueRate(actualIrrList.get(0).getIrr());
            }
        }

        // 将处理过后的数据封装分页
        revenueCalcVOIPage.setRecords(revenueCalcVOList);
        return revenueCalcVOIPage;
    }

    public ResponseResult exportRevenueCalc(RevenueCalcRequest request) {
        ExportAsyncDataTask exportAsyncDataTask = new ExportAsyncDataTask(() -> {
            IPage<RevenueCalcVO> revenueCalcVOIPage = this.revenueCalcPage(new Page<>(-1, -1), request);
            if (CollUtil.isEmpty(revenueCalcVOIPage.getRecords())) {
                throw new BussinessException("未查询到数据, 导出数据为空!");
            }
            return revenueCalcVOIPage.getRecords();
        }, request.getCollumList(), "投资收益测算列表" + DateUtil.format(new DateTime(), "yyyyMMdd"));
        exportExecutor.addTask(exportAsyncDataTask);
        return new ResponseResult(CommonResultCode.SUCCESS);
    }

    public IPage<DeviceCostVO> deviceCostPage(IPage<DeviceCostVO> page, DeviceCostRequest request) {
        IPage<DeviceCostVO> resultIPage = new Page<>(request.getPageNum(), request.getPageSize());
        if (StrUtil.isNotBlank(request.getCusName()) || StrUtil.isNotBlank(request.getPsCode())) {
            List<String> psCodeList = investmentModelDao.getPsCodeListByCondition(request.getCusName(), request.getPsCode());
            if (CollUtil.isEmpty(psCodeList)) {
                return resultIPage;
            }
            request.setPsCodeList(psCodeList);
        }
        resultIPage = investmentModelDao.deviceCostPage(page, request);
        if (CollUtil.isNotEmpty(resultIPage.getRecords())) {
            List<String> psCodes = resultIPage.getRecords().stream().map(DeviceCostVO::getPsCode).distinct().collect(Collectors.toList());
            List<PsCusInfoVO> psCusInfoVOS = investmentModelDao.getCusInfoInPsCodes(psCodes);
            Map<String, PsCusInfoVO> psCusInfoVOMap = Maps.newHashMap();
            if (CollUtil.isNotEmpty(psCusInfoVOS)) {
                psCusInfoVOMap = psCusInfoVOS.stream().collect(Collectors.toMap(PsCusInfoVO::getPsCode, Function.identity()));
            }
            for (DeviceCostVO deviceCostVO : resultIPage.getRecords()) {
                if (MapUtil.isNotEmpty(psCusInfoVOMap)) {
                    PsCusInfoVO psCusInfoVO = psCusInfoVOMap.get(deviceCostVO.getCusName());
                    if (Objects.nonNull(psCusInfoVO)) {
                        deviceCostVO.setCusName(psCusInfoVO.getCusName());
                    }
                }
                deviceCostVO.setAssemblyWithTax(Objects.nonNull(deviceCostVO.getAssemblyWithoutTax()) ? deviceCostVO.getAssemblyWithoutTax().multiply(BigDecimal.ONE.add(taxRate)) : null);
                deviceCostVO.setBracketWithTax(Objects.nonNull(deviceCostVO.getBracketWithoutTax()) ? deviceCostVO.getBracketWithoutTax().multiply(BigDecimal.ONE.add(taxRate)) : null);
                deviceCostVO.setBoxWithTax(Objects.nonNull(deviceCostVO.getBoxWithoutTax()) ? deviceCostVO.getBoxWithoutTax().multiply(BigDecimal.ONE.add(taxRate)) : null);
                deviceCostVO.setInverterWithTax(Objects.nonNull(deviceCostVO.getInverterWithoutTax()) ? deviceCostVO.getInverterWithoutTax().multiply(BigDecimal.ONE.add(taxRate)) : null);
                deviceCostVO.setFcWithTax(Objects.nonNull(deviceCostVO.getFcWithoutTax()) ? deviceCostVO.getFcWithoutTax().multiply(BigDecimal.ONE.add(taxRate)) : null);
            }
        }
        return resultIPage;
    }

}
