package com.gcl.psmis.monitor.controller.riskorder;

import cn.hutool.core.collection.CollUtil;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.gcl.psmis.framework.common.annotation.PostGclApiByToken;
import com.gcl.psmis.monitor.constants.ApiConstant;
import com.gcl.psmis.monitor.dao.sun.ZgConditionDao;
import com.gcl.psmis.monitor.dao.sun.riskorder.RiskOrderDao;
import com.gcl.psmis.monitor.entity.dto.riskorder.RiskOrderBaseDTO;
import com.gcl.psmis.monitor.entity.vo.CompanyVO;
import com.gcl.psmis.monitor.process.request.HomeProvinceRequest;
import com.gcl.psmis.monitor.process.service.ReportService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

/**
 * @ClassName RiskOrderController
 * @Description 风险订单
 * <AUTHOR>
 * @Date 2024/4/12 9:05
 **/
@Slf4j
@RestController
@RequiredArgsConstructor
@Api(tags = "智观:风险订单")
@RequestMapping(ApiConstant.ZG_RISKORDER_PATH)
public class RiskOrderController {

    private final ReportService service;
    private final ZgConditionDao conditionDao;

    @PostGclApiByToken("/page")
    @ApiOperation(value = "风险订单分页查询")
    public IPage<?> riskOrderPage(@RequestBody RiskOrderBaseDTO dto) {
        HomeProvinceRequest homeProvince = new HomeProvinceRequest();
        homeProvince.setType(dto.getCompanyClassify() == 3 ? 0 : 1);
        if (CollUtil.isNotEmpty(dto.getProvince())) {
            homeProvince.getProvinceIdList().addAll(dto.getProvince());
        }else{
            List<CompanyVO> companys=new ArrayList<>();
            if(dto.getCompanyClassify() == 3){
                companys = this.conditionDao.getHyCompanys("户用");
                homeProvince.getProvinceIdList().addAll(companys.stream().map(CompanyVO::getCompanyId).collect(Collectors.toList()));
            }else{
                companys = this.conditionDao.getHyCompanys("开发");
            }
            homeProvince.getProvinceIdList().addAll(companys.stream().map(CompanyVO::getCompanyId).collect(Collectors.toList()));
        }
        dto.getHomeProvince().add(homeProvince);
        return service.riskOrderPage(dto);
    }

    @PostGclApiByToken("/hyPage")
    @ApiOperation(value = "户用管理-经营指标-风险订单分页查询")
    public IPage<?> hyRiskOrderPage(@RequestBody RiskOrderBaseDTO dto) {
        HomeProvinceRequest homeProvince = new HomeProvinceRequest();
        homeProvince.setType(0);
        if (CollUtil.isNotEmpty(dto.getProvince())) {
            homeProvince.getProvinceIdList().addAll(dto.getProvince());
        } else {
            //查询户用
            List<CompanyVO> hyCompanys = this.conditionDao.getHyCompanys("户用");
            homeProvince.getProvinceIdList().addAll(hyCompanys.stream().map(CompanyVO::getCompanyId).collect(Collectors.toList()));
        }
        dto.getHomeProvince().add(homeProvince);
        return service.riskOrderPage(dto);
    }

    @PostGclApiByToken("/export")
    @ApiOperation(value = "风险订单导出")
    public void riskOrderExport(@RequestBody RiskOrderBaseDTO dto) {
        dto.setNoPage(1);
        dto.setCompanyClassify(dto.getCompanyClassify() == null ? 3 : dto.getCompanyClassify());
        HomeProvinceRequest homeProvince = new HomeProvinceRequest();
        homeProvince.setType(dto.getCompanyClassify() == 3 ? 0 : 1);
        if (CollUtil.isNotEmpty(dto.getProvince())) {
            homeProvince.getProvinceIdList().addAll(dto.getProvince());
        }else{
            List<CompanyVO> companys=new ArrayList<>();
            if(dto.getCompanyClassify() == 3){
                companys = this.conditionDao.getHyCompanys("户用");
                homeProvince.getProvinceIdList().addAll(companys.stream().map(CompanyVO::getCompanyId).collect(Collectors.toList()));
            }else{
                companys = this.conditionDao.getHyCompanys("开发");
            }
            homeProvince.getProvinceIdList().addAll(companys.stream().map(CompanyVO::getCompanyId).collect(Collectors.toList()));
        }
        dto.getHomeProvince().add(homeProvince);
        service.riskOrderExport(dto);
    }


}
