package com.gcl.psmis.monitor.entity.vo.invest.income;

import com.gcl.psmis.framework.common.annotation.ExportConfig;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Builder;
import lombok.Data;

import java.math.BigDecimal;

@Data
@Builder
@ApiModel(value = "RevenueContrastVO", description = "投资收益对比VO")
public class RevenueContrastVO {

    @ApiModelProperty("费用类型")
    private String costType;

    @ApiModelProperty("费用科目")
    @ExportConfig(width = 150, value = "费用科目")
    private String costItem;

    @ApiModelProperty("理论成本(元/W)")
    @ExportConfig(width = 150, value = "理论成本(元/W)")
    private BigDecimal theoryCost;

    @ApiModelProperty("实际成本(元/W)")
    @ExportConfig(width = 150, value = "实际成本(元/W)")
    private BigDecimal actualCost;

    @ApiModelProperty("理论成本-实际成本(元/W)")
    @ExportConfig(width = 150, value = "理论成本-实际成本(元/W)")
    private BigDecimal diffCost;

}
