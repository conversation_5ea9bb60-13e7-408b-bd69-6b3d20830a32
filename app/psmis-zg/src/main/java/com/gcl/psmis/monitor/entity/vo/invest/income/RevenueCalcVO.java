package com.gcl.psmis.monitor.entity.vo.invest.income;

import com.gcl.psmis.framework.common.annotation.ExportConfig;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;
import java.math.RoundingMode;

@Data
@ApiModel(value = "RevenueCalcVO", description = "投资收益测算VO")
public class RevenueCalcVO {

    @ApiModelProperty("省ID")
    private Integer provinceId;
    @ApiModelProperty("省")
    @ExportConfig(width = 150, value = "省")
    private String province;

    @ApiModelProperty("市ID")
    private Integer cityId;
    @ApiModelProperty("市")
    @ExportConfig(width = 150, value = "市")
    private String city;

    @ApiModelProperty("组件功率")
    @ExportConfig(width = 150, value = "组件功率")
    private Integer zuPower;

    @ApiModelProperty("投资模型（元/W）")
    @ExportConfig(width = 150, value = "投资模型（元/W）")
    private BigDecimal theoryCost;

    @ApiModelProperty("实际成本（元/W）")
    @ExportConfig(width = 150, value = "实际成本（元/W）")
    private BigDecimal actualCost;

    @ApiModelProperty("销售定价含税（元/W）")
    @ExportConfig(width = 150, value = "销售定价含税（元/W）")
    private String pricingTax;

    @ApiModelProperty("销售定价（元/W）")
    @ExportConfig(width = 150, value = "销售定价（元/W）")
    private String pricing;

    @ApiModelProperty("理论溢价（元/W）")
    @ExportConfig(width = 150, value = "理论溢价（元/W）")
    private BigDecimal theoryPremium;

    @ApiModelProperty("实际溢价（元/W）")
    @ExportConfig(width = 150, value = "实际溢价（元/W）")
    private BigDecimal factPremium;

    @ApiModelProperty("理论收益率（%）")
    @ExportConfig(width = 150, value = "理论收益率（%）")
    private BigDecimal theoryRevenueRate;

    @ApiModelProperty("实际收益率（%）")
    @ExportConfig(width = 150, value = "实际收益率（%）")
    private BigDecimal factRevenueRate;

    public void setPremium() {
        // 判断销售定价是否等于 “--”
        if (!"--".equals(this.pricing)) {
            BigDecimal pricing = new BigDecimal(this.pricing);
            if (this.theoryCost != null) {
                // 计算理论溢价 == 销售定价 - 投资模型
                this.theoryPremium = pricing.subtract(this.theoryCost).setScale(6, RoundingMode.HALF_UP);
            }
            if (this.actualCost != null) {
                // 计算实际溢价 == 销售定价 - 实际成本
                this.factPremium = pricing.subtract(this.actualCost).setScale(6, RoundingMode.HALF_UP);
            }
        }
    }

}
