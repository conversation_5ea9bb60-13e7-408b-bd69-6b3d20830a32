package com.gcl.psmis.monitor.service.sun;

import com.gcl.framework.data.holder.CurrentUserHolder;
import com.gcl.framework.data.dto.UserDTO;
import com.gcl.psmis.monitor.dao.sun.ZgConditionDao;
import com.gcl.psmis.monitor.dao.sun.ZgDataDao;
import com.gcl.psmis.monitor.dao.sun.ZgFuncDataDao;
import com.gcl.psmis.monitor.dao.sun.v2.ZgProvinceDao;
import com.gcl.psmis.monitor.entity.dto.ZgReportDTO;
import com.gcl.psmis.monitor.entity.vo.*;
import com.gcl.psmis.monitor.entity.vo.v2.ProvinceVO;
import com.gcl.psmis.monitor.service.sun.v2.ZgProvinceService;
import com.gcl.psmis.monitor.util.ReportTimeUtil;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 鑫阳光接口
 *
 * <AUTHOR>
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class ZgDataService extends ZgBaseService {

//    private final ZgDataDao dao;
//    private final ZgConditionDao conditionDao;
//    private final ZgFuncDataDao funcDataDao;
//    private final ZgAgentService agentService;
//    private final ZgProvinceDao provinceDao;
//    private final ZgProvinceService provinceService;
//
//    /***
//     * @desc 数据总览
//     * <AUTHOR>
//     * @date 2023/9/15 17:20
//     * @param dto
//     * @return ZgReportVO
//     */
//    public ZgReportVO report(ZgReportDTO dto) {
//        ZgReportVO vo = new ZgReportVO();
//
//        Map<String, Object> timeReport = ReportTimeUtil.doTimeReport(dto.getSTime(), dto.getETime(), dto.getT());
//        dto.setSTime(timeReport.containsKey("nowStartTime") && timeReport.get("nowStartTime") != null ? timeReport.get("nowStartTime").toString() + " 00:00:00" : null);
//        dto.setETime(timeReport.containsKey("nowEndTime") && timeReport.get("nowEndTime") != null ? timeReport.get("nowEndTime").toString() + " 23:59:59" : null);
//
//        //获取当前登录人的userId
//        UserDTO userDTOThreadLocal = CurrentUserHolder.getUserDTOThreadLocal();
//        String userId = this.funcDataDao.getUserIdByLoginName(userDTOThreadLocal.getEmpNo());
//
//        if (StringUtils.isNotBlank(userId)) {
//            dto.setUId(userId);
//            Integer isallagent = conditionDao.getIsallagent(userId);
//            dto.setCanViewAll(isallagent);
//
//            //  户用电站开发
//            if (dto.getTabType() == 0) {
//                doHyStationDev(dto, vo, userId, isallagent);
//            } else {
//                List<Integer> child = doSelectCondition(dto, userId, isallagent);
//                doVariousStates(dto, vo, userId, child);
//
//            }
//        }
//        return vo;
//    }
//
//    /***
//     * @desc 处理选择条件
//     * <AUTHOR>
//     * @date 2023/10/24 11:48
//     * @param dto
//     * @param userId
//     * @param isallagent
//     * @return List<Integer>
//     */
//    private List<Integer> doSelectCondition(ZgReportDTO dto, String userId, Integer isallagent) {
//        //地区电站
//        List<Integer> child = new ArrayList<>();
//        //选择条件：优先查看户用/开发公司
//        if (!CollectionUtils.isEmpty(dto.getProvince())) {
//            dto.setCanViewAll(0);
//            //按照省份查询所有城市
//            child = this.provinceDao.getCityByProvince(dto.getProvince());
//        } else {
//            //查询所有城市
//            List<ProvinceVO> provinceList = this.provinceService.getProvinceList();
//            List<String> collect = provinceList.stream().map(ProvinceVO::getCode).collect(Collectors.toList());
//            child = this.provinceDao.getCityByProvince(collect.stream().map(Integer::parseInt).collect(Collectors.toList()));
//            //小程序调用户用或者开发，会只传viewType，但不传省集合，但是PC端会传省集合，pc端就走上面的if
////            if (dto.getViewType() != null && dto.getViewType() > 0) {
////                if (dto.getViewType() == 1) {
////                    dto.setOrgName("户用");
////                } else {
////                    dto.setOrgName("开发");
////                }
////                List<CooTypeVO> provinceList = this.conditionDao.getProvinceList(userId, dto.getOrgName(), isallagent);
////                List<String> collect = provinceList.stream().map(CooTypeVO::getId).collect(Collectors.toList());
////                List<Integer> list = collect.stream().map(Integer::parseInt).collect(Collectors.toList());
////                child = this.conditionDao.getChild(list);
////            } else {
////                //查询所有省份
//////                List<ProvinceVO> provinceList = provinceService.getProvinceList();
//////                if (!CollectionUtils.isEmpty(provinceList)) {
////                //查询当前登录人下所有的经销商
////                List<CooTypeVO> allAgent = this.conditionDao.getDealerListByIsAllAgent(null, dto.getUId(), null);
////                if (!CollectionUtils.isEmpty(allAgent)) {
////                    List<String> collect = allAgent.stream().map(CooTypeVO::getId).collect(Collectors.toList());
////                    List<Integer> list = collect.stream().map(Integer::parseInt).collect(Collectors.toList());
////                    child.addAll(list);
////                }
////            }
//
//        }
//        return child;
//    }
//
//    private void doVariousStates(ZgReportDTO dto, ZgReportVO vo, String userId, List<Integer> child) {
//        List<Integer> agentIds = new ArrayList<>();
//        //准入经销商数
//        if (!CollectionUtils.isEmpty(child)) {
//            dto.setAreaIds(child);
//        }
//
//        this.getViewType(dto, userId, "report");
//        //清空权限导致的set问题
//        dto.getDealerIds().clear();
//        dto.getChilds().clear();
//
//        //查询所有省公司,准入需要保持不变
//        List<CooTypeVO> provinceList = this.conditionDao.getProvinceList(userId, dto.getOrgName(), 1);
//        List<Integer> levelOnesArray = new ArrayList<>();
//        if (CollectionUtils.isEmpty(provinceList)) {
//            //也有可能没有省公司,直接查询代理商
//            List<CooTypeVO> levelOneList = this.conditionDao.getDealerListByIsAllAgent(null, null, dto.getUId(), null);
//            List<String> collect = levelOneList.stream().map(CooTypeVO::getId).collect(Collectors.toList());
//            List<Integer> list = collect.stream().map(Integer::parseInt).collect(Collectors.toList());
//            levelOnesArray.addAll(list);
//        } else {
//            List<String> collect = provinceList.stream().map(CooTypeVO::getId).collect(Collectors.toList());
//            List<Integer> list = collect.stream().map(Integer::parseInt).collect(Collectors.toList());
//            levelOnesArray = this.conditionDao.getChild(list);
//        }
//        //准入经销商数
//        dto.setLevelOne(levelOnesArray.size());
//        agentIds.addAll(levelOnesArray);
//
//        vo.setAccessDistributors(String.valueOf(dto.getLevelOne()));
//        //先查询一级代理商是否有录单，如果有就+1，如果没有就查二级，二级如果存在录单，就算+1，
//        Integer ordersDistributors = 0;
//        if (!CollectionUtils.isEmpty(agentIds)) {
//            for (Integer ordersAgentId : agentIds) {
//                Integer orderAgent = this.dao.getOrderAgentReport(Arrays.asList(ordersAgentId), dto.getSTime(), dto.getETime());
//                log.info("一级代理商ID：{},=====>录单数:{}", ordersAgentId, orderAgent);
//                if (orderAgent != null && orderAgent > 0) {
//                    //如果不为空，就说明这个一级代理商有录单，就不需要查询二级
//                    ordersDistributors++;
//                } else {
//                    //一级代理商没有录单，需要查询二级
//                    List<AgentVO> secondAgent = conditionDao.getSecondAgent(Arrays.asList(ordersAgentId));
//                    if (!CollectionUtils.isEmpty(secondAgent)) {
//                        List<Integer> collect = secondAgent.stream().map(AgentVO::getId).collect(Collectors.toList());
//                        Integer secondAgentOrders = this.dao.getOrderAgentReport(collect, dto.getSTime(), dto.getETime());
//                        log.info("二级代理商ID：{},=====>录单数:{}", collect, secondAgentOrders);
//                        if (secondAgentOrders != null && secondAgentOrders > 0) {
//                            ordersDistributors++;
//                        }
//                    }
//                }
//            }
//        }
//        //录单经销商数
//        vo.setOrdersDistributors(String.valueOf(ordersDistributors));
//        //清空录单ids
//        dto.getOrdersAgentIds().clear();//否则会影响下面sql的if逻辑处理
//
//        log.info("[已]状态的总览 dto:{}", dto);
//        //转化率,已并网除以商机
//        Integer ordersIntentState = this.dao.getOrdersIntentState(dto);
//
//        //处理已经的所有状态
//        doAlreadyStatus(dto, vo, ordersIntentState);
//    }
//
//
//    private void doHyStationDev(ZgReportDTO dto, ZgReportVO vo, String userId, Integer isallagent) {
//        List<Integer> child = new ArrayList<>();
//        List<Integer> agentIds = new ArrayList<>();
//        //选择条件：省公司进来
//        if (!CollectionUtils.isEmpty(dto.getProvince())) {
//            dto.setCanViewAll(0);
//            child = this.conditionDao.getChild(dto.getProvince());
//        } else {
//            //小程序调用户用或者开发，会只传viewType，但不传省集合，但是PC端会传省集合，pc端就走上面的if
//            if (dto.getViewType() != null && dto.getViewType() > 0) {
//                if (dto.getViewType() == 1) {
//                    dto.setOrgName("户用");
//                } else {
//                    dto.setOrgName("开发");
//                }
//            }
//            //查询所有省公司
//            List<CooTypeVO> provinceList = this.conditionDao.getProvinceList(userId, dto.getOrgName(), isallagent);
//            if (CollectionUtils.isEmpty(provinceList)) {
//                //也有可能没有省公司,直接查询代理商
//                List<CooTypeVO> levelOneList = this.conditionDao.getDealerListByIsAllAgent(null, null, dto.getUId(), null);
//                List<String> collect = levelOneList.stream().map(CooTypeVO::getId).collect(Collectors.toList());
//                List<Integer> list = collect.stream().map(Integer::parseInt).collect(Collectors.toList());
//                child.addAll(list);
//            } else {
//                //如果没有省公司，就直接查询经销商
//                List<String> collect = provinceList.stream().map(CooTypeVO::getId).collect(Collectors.toList());
//                List<Integer> list = collect.stream().map(Integer::parseInt).collect(Collectors.toList());
//                child = this.conditionDao.getChild(list);
//            }
//
//        }
//        if (!CollectionUtils.isEmpty(child)) {
//            dto.setLevelOne(child.size());
//            agentIds.addAll(child);
//            //录单经销商数是要准入的基础上去查询商机表到底有多少个经销商
//            dto.getChilds().addAll(child);
//            //查询二级代理商
//            dto.getChilds().addAll(agentService.getAgentListLevelIds(child));
//
//            this.getViewType(dto, userId, "report");
//
//            log.info("总览 dto:{}", dto);
//
//            //准入经销商数
//            vo.setAccessDistributors(String.valueOf(dto.getLevelOne()));
//            Integer ordersDistributors = getOrdersAgents(agentIds, dto.getSTime(), dto.getETime());
//            //录单经销商数
//            vo.setOrdersDistributors(String.valueOf(ordersDistributors));
//            //清空录单ids
//            dto.getOrdersAgentIds().clear();//否则会影响下面sql的if逻辑处理
//
//            log.info("[已]状态的总览 dto:{}", dto);
//            //转化率,已并网除以商机
//            Integer ordersIntentState = this.dao.getOrdersIntentState(dto);
//
//            //处理已经的所有状态
//            doAlreadyStatus(dto, vo, ordersIntentState);
//        }
//    }
//
//    private Integer getOrdersAgents(List<Integer> agentIds, String sTime, String eTime) {
//        //先查询一级代理商是否有录单，如果有就+1，如果没有就查二级，二级如果存在录单，就算+1，
//        Integer ordersDistributors = 0;
//        if (!CollectionUtils.isEmpty(agentIds)) {
//            for (Integer ordersAgentId : agentIds) {
//                Integer orderAgent = this.dao.getOrderAgentReport(Arrays.asList(ordersAgentId), sTime, eTime);
//                log.info("一级代理商ID：{},=====>录单数:{}", ordersAgentId, orderAgent);
//                if (orderAgent != null && orderAgent > 0) {
//                    //如果不为空，就说明这个一级代理商有录单，就不需要查询二级
//                    ordersDistributors++;
//                } else {
//                    //一级代理商没有录单，需要查询二级
//                    List<AgentVO> secondAgent = conditionDao.getSecondAgent(Arrays.asList(ordersAgentId));
//                    if (!CollectionUtils.isEmpty(secondAgent)) {
//                        List<Integer> collect = secondAgent.stream().map(AgentVO::getId).collect(Collectors.toList());
//                        Integer secondAgentOrders = this.dao.getOrderAgentReport(collect, sTime, eTime);
//                        log.info("二级代理商ID：{},=====>录单数:{}", collect, secondAgentOrders);
//                        if (secondAgentOrders != null && secondAgentOrders > 0) {
//                            ordersDistributors++;
//                        }
//                    }
//                }
//            }
//        }
//        return ordersDistributors;
//    }
//
//
//    /***
//     * @desc 处理已经的所有状态
//     * <AUTHOR>
//     * @date 2023/9/19 19:03
//     * @param dto
//     * @param vo
//     * @param ordersIntentState
//     */
//    private void doAlreadyStatus(ZgReportDTO dto, ZgReportVO vo, Integer ordersIntentState) {
//
//        //已下单
//        List<Integer> ordered = this.dao.getOrdered(dto);
//        if (!CollectionUtils.isEmpty(ordered)) {
//            DetailVO orderedDetail =
//                    new DetailVO(
//                            String.valueOf(ordered.size()),
//                            String.valueOf(ordered.stream().mapToInt(Integer::intValue).sum()));
//            vo.setOrdered(orderedDetail);
//        }
//        //已发货
//        List<Integer> shipped = this.dao.getShipped(dto);
//        if (!CollectionUtils.isEmpty(shipped)) {
//            DetailVO shippedDetail =
//                    new DetailVO(
//                            String.valueOf(shipped.size()),
//                            String.valueOf(shipped.stream().mapToInt(Integer::intValue).sum()));
//            vo.setShipped(shippedDetail);
//        }
//        //已完工
//        List<Integer> completed = this.dao.getCompleted(dto);
//        if (!CollectionUtils.isEmpty(completed)) {
//            DetailVO completedDetail =
//                    new DetailVO(
//                            String.valueOf(completed.size()),
//                            String.valueOf(completed.stream().mapToInt(Integer::intValue).sum()));
//            vo.setCompleted(completedDetail);
//        }
//
//        //已并网
//        List<Integer> gridConnected = dao.getGridConnected(dto);
//        if (!CollectionUtils.isEmpty(gridConnected)) {
//            DetailVO gridConnectedDetail =
//                    new DetailVO(
//                            String.valueOf(gridConnected.size()),
//                            String.valueOf(gridConnected.stream().mapToInt(Integer::intValue).sum()));
//            vo.setGridConnected(gridConnectedDetail);
//        }
//        //已建档
//        List<Integer> archived = this.dao.getArchived(dto);
//        String value = "0.00";
//        if (!CollectionUtils.isEmpty(archived)) {
//            DetailVO archivedDetail =
//                    new DetailVO(
//                            String.valueOf(archived.size()),
//                            String.valueOf(archived.stream().mapToInt(Integer::intValue).sum()));
//            vo.setArchived(archivedDetail);
//        }
//
//        //录单数
//        List<Integer> orders = this.dao.getOrders(dto);
//        if (!CollectionUtils.isEmpty(orders)) {
//            DetailVO ordersDetail =
//                    new DetailVO(
//                            String.valueOf(orders.size()),
//                            String.valueOf(orders.stream().mapToInt(Integer::intValue).sum()));
//            vo.setOrders(ordersDetail);
//            //转化率
//            value = new BigDecimal(gridConnected.size()).divide(new BigDecimal(orders.size()), 4, RoundingMode.HALF_UP).multiply(new BigDecimal(100)).setScale(2, RoundingMode.HALF_UP) + "%";
//        }
//
//        //保留两位小数并不进行四舍五入
//        vo.setGridConversionRate(value);
//    }
}
