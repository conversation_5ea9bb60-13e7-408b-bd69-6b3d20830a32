package com.gcl.psmis.monitor.entity.vo.invest.benefit.basic;

import com.gcl.psmis.monitor.entity.vo.invest.benefit.cost.ManageCostPageVO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.ArrayList;
import java.util.List;

/**
 * @ClassName ManageCostPageVO
 * @Description 管理费成本分页
 * <AUTHOR>
 **/
@Data
@ApiModel(value = "管理费成本分页出参", description = "管理费成本分页出参")
public class ManageCostCusPageVO {
    @ApiModelProperty("分页数据集")
    private List<ManageCostPageVO> records = new ArrayList<>();
    @ApiModelProperty("费用计算")
    private ManageCostCalcVO costCalc=new ManageCostCalcVO();
    @ApiModelProperty("总条数")
    private long total = 0;
    @ApiModelProperty("当前页码")
    private long current = 0;
    @ApiModelProperty("总页数")
    private long pages = 0;
}
