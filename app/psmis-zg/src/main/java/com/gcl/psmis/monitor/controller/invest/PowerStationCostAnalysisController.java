package com.gcl.psmis.monitor.controller.invest;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.gcl.psmis.framework.common.ResponseResult;
import com.gcl.psmis.framework.common.annotation.PostGclApiByToken;
import com.gcl.psmis.monitor.constants.ApiConstant;
import com.gcl.psmis.monitor.constants.enums.ExportEnum;
import com.gcl.psmis.monitor.entity.dto.invest.costAnalysis.CostAnalysisDTO;
import com.gcl.psmis.monitor.entity.vo.invest.costAnalysis.ActualCostVO;
import com.gcl.psmis.monitor.entity.vo.invest.costAnalysis.CompareCostVO;
import com.gcl.psmis.monitor.entity.vo.invest.costAnalysis.TheoryCostVO;
import com.gcl.psmis.monitor.service.sun.invest.PowerStationCostAnalysisService;
import com.gcl.psmis.monitor.service.sun.invest.export.AbstractCostAnalysisExportService;
import com.gcl.psmis.monitor.service.sun.invest.export.ExportInitialization;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.validation.Valid;
import java.util.Map;

/**
 * @ClassName PowerStationCostAnalysisController
 * @Description 电站成本分析控制层
 * <AUTHOR>
 * @Date 2024/6/4 16:34
 **/
@Slf4j
@RestController
@Api(tags = "电站成本分析API")
@RequestMapping(ApiConstant.ZG_POWER_STATION_COST_ANALYSIS)
public class PowerStationCostAnalysisController {

    @Autowired
    private PowerStationCostAnalysisService powerStationCostAnalysisService;

    private Map<ExportEnum, AbstractCostAnalysisExportService> exportServiceMap = ExportInitialization.exportServices;

    @ApiOperation("理论成本查询")
    @PostGclApiByToken("/theoryCostPage")
    public IPage<TheoryCostVO> theoryCostPage(@Valid @RequestBody CostAnalysisDTO dto) {
        return powerStationCostAnalysisService.theoryCostPage(new Page<>(dto.getPageNum(), dto.getPageSize()), dto);
    }

    @ApiOperation("理论成本导出")
    @PostGclApiByToken("/theoryCostExport")
    public ResponseResult theoryCostExport(@RequestBody CostAnalysisDTO dto) {
        return exportServiceMap.get(ExportEnum.POWER_STATION_THEORY_COST).export(dto);
    }

    @ApiOperation("实际成本查询")
    @PostGclApiByToken("/actualCostPage")
    public IPage<ActualCostVO> actualCostPage(@Valid @RequestBody CostAnalysisDTO dto) {
        return powerStationCostAnalysisService.actualCostPage(new Page<>(dto.getPageNum(), dto.getPageSize()), dto);
    }

    @ApiOperation("实际成本导出")
    @PostGclApiByToken("/actualCostExport")
    public ResponseResult actualCostExport(@RequestBody CostAnalysisDTO dto) {
        return exportServiceMap.get(ExportEnum.POWER_STATION_ACTUAL_COST).export(dto);
    }

    @ApiOperation("电站成本对比查询")
    @PostGclApiByToken("/compareCostPage")
    public IPage<CompareCostVO> compareCostPage(@Valid @RequestBody CostAnalysisDTO dto) {
        return powerStationCostAnalysisService.compareCostPage(new Page<>(dto.getPageNum(), dto.getPageSize()), dto);
    }

    @ApiOperation("电站成本对比导出")
    @PostGclApiByToken("/compareCostExport")
    public ResponseResult compareCostExport(@RequestBody CostAnalysisDTO dto) {
        return exportServiceMap.get(ExportEnum.POWER_STATION_COMPARE_COST).export(dto);
    }

}
