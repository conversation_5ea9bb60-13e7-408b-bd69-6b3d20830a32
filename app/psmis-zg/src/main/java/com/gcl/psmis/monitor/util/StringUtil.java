package com.gcl.psmis.monitor.util;

import org.apache.commons.lang3.StringUtils;

import java.util.Objects;

/**
 * @description: 字符串处理工具类
 * @author: liujinjing
 * @date: 2025/6/16 13:49
 */
public class StringUtil {

    public static String judgeWithDefaultValue(Object object, String defaultValue) {
        if (object instanceof String) {
                return StringUtils.isNotBlank((String)object) ? (String)object : defaultValue;
        }

        return Objects.nonNull(object) ? object.toString() : defaultValue;
    }


}
