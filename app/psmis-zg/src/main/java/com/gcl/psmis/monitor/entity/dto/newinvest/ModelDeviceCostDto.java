package com.gcl.psmis.monitor.entity.dto.newinvest;

import com.gcl.psmis.framework.common.annotation.ImportConfig;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 投资模型-设备成本
 */
@Builder
@AllArgsConstructor
@NoArgsConstructor
@Data
@ApiModel(value = "ModelDeviceCostDto")
public class ModelDeviceCostDto {

    @ApiModelProperty("设备类型(组件功率)")
    @ImportConfig(value = "组件类型")
    private String deviceType;

    @ApiModelProperty("安装形式")
    @ImportConfig(value = "安装形式")
    private String installForm;

    @ApiModelProperty("建档日期范围")
    @ImportConfig(value = "建档日期")
    private String createdTimeStr;

    @ApiModelProperty("组件成本(元/w)")
    @ImportConfig(value = "组件（元/W）")
    private String assemblyCost;

    @ApiModelProperty("支架成本(元/W)")
    @ImportConfig(value = "支架（元/W）")
    private String bracketCost;

    @ApiModelProperty("并网箱成本(元/w)")
    @ImportConfig(value = "并网箱（元/W）")
    private String gridCost;

    @ApiModelProperty("逆变器成本(元/w)")
    @ImportConfig(value = "逆变器（元/W）")
    private String inverterCost;

    @ApiModelProperty("电气辅材成本(元/w)")
    @ImportConfig(value = "电气辅材（元/W）")
    private String fcCost;

}
