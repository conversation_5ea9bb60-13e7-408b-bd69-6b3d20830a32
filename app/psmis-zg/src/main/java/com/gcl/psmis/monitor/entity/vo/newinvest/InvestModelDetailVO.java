package com.gcl.psmis.monitor.entity.vo.newinvest;

import com.baomidou.mybatisplus.core.metadata.IPage;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;


@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
@ApiModel(value = "投资模型详情")
public class InvestModelDetailVO {
    //管理成本
    @ApiModelProperty("管理成本")
    private List<ModelManageCostVO> modelManageCosts;
    //代理商奖励
    @ApiModelProperty("代理商奖励")
    private List<ModelAgentRewardVO> agentRewards;

    //设备成本
    @ApiModelProperty("设备成本")
    private IPage<ModelDeviceCostVO> modelDevices;

}
