package com.gcl.psmis.monitor.entity.dto.newinvest;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;
import java.util.Date;

/**
 * 投资模型-代理商奖励
 */
@Builder
@AllArgsConstructor
@NoArgsConstructor
@Data
@ApiModel(value = "ModelAgentRewardDto")
public class ModelAgentRewardDto {

    @ApiModelProperty("建档日期范围(格式: yyyy/MM-yyyy/MM)")
    private String createdTimeStr;

    @ApiModelProperty("奖励类型: 0-容量奖励,1-时效奖励")
    private Integer rewardType;

    @ApiModelProperty("奖励金额(元/块)")
    private BigDecimal rewardAmount;

    @ApiModelProperty("创建时间")
    private Date createTime;

}
