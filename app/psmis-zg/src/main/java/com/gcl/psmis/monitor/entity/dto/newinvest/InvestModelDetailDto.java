package com.gcl.psmis.monitor.entity.dto.newinvest;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;


@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
@ApiModel(value = "投资模型详情")
public class InvestModelDetailDto {
    //管理成本
    @ApiModelProperty("管理成本")
    private List<ModelManageCostDto> modelManageCosts;
    //代理商奖励
    @ApiModelProperty("代理商奖励")
    private List<ModelAgentRewardDto> agentRewards;
    //设备成本
    @ApiModelProperty("设备成本")
    private List<ModelDeviceCostDto> deviceCosts;

}
