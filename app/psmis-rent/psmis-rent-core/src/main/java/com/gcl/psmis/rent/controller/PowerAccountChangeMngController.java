package com.gcl.psmis.rent.controller;

import cn.hutool.core.collection.CollUtil;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.gcl.psmis.framework.common.annotation.DataScope;
import com.gcl.psmis.framework.common.annotation.GetGclApiByToken;
import com.gcl.psmis.framework.common.annotation.PostGclApiByToken;
import com.gcl.psmis.framework.common.exception.BussinessException;
import com.gcl.psmis.framework.common.req.payorder.*;
import com.gcl.psmis.framework.common.vo.payorder.*;
import com.gcl.psmis.framework.dict.util.ExpandBeanUtils;
import com.gcl.psmis.framework.export.executor.ExportExecutor;
import com.gcl.psmis.framework.export.task.ExportTask;
import com.gcl.psmis.rent.service.PowerAccountChangeMngService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.validation.Valid;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;

/**
 * @className: PowerAccountChangeMngController
 * @author: wuchenyu
 * @description:
 * @date: 2024/6/25 9:20
 * @version: 1.0
 */
@Api(tags = "账单变更", description = "账单变更")
@RestController
@RequestMapping("powerAccountChangeMng")
public class PowerAccountChangeMngController {

    @Autowired
    private PowerAccountChangeMngService powerAccountChangeMngService;

    @Autowired
    private ExportExecutor exportExecutor;


    /**
     * @param :
     * @return List<RegionTreeVO>
     * <AUTHOR>
     * @description 省市区下拉框
     * @date 2024/7/10 16:31
     */
    @ApiOperation("省市区下拉框")
    @GetGclApiByToken("getRegionListByTree")
    public List<RegionTreeVO> getRegionListByTree() {
        return powerAccountChangeMngService.getRegionListByTree();
    }


    /**
     * @param params:
     * @return BigDecimal
     * <AUTHOR>
     * @description 欠费合计
     * @date 2024/6/25 14:16
     */
    @ApiOperation("欠费合计")
    @PostGclApiByToken("getArrears")
    @DataScope
    public String getArrears(@RequestBody @Valid DeductionQueryReq params) {
        return powerAccountChangeMngService.getArrears(params);
    }



    /**
     * @param params:
     * @return IPage<DeductionListVO>
     * <AUTHOR>
     * @description 抵扣列表
     * @date 2024/6/25 13:23
     */
    @ApiOperation("抵扣列表")
    @PostGclApiByToken("getDeductionList")
    @DataScope
    public IPage<DeductionListVO> getDeductionList(@RequestBody @Valid DeductionQueryReq params) {
        Page<DeductionListVO> page = new Page<>(params.getPageNum(), params.getPageSize());
        IPage<DeductionListVO> infoList = powerAccountChangeMngService.getDeductionList(page, params);

        if (infoList != null && CollUtil.isNotEmpty(infoList.getRecords())) {
            ExpandBeanUtils.convert(infoList, infoList);
        }
        return infoList;
    }


    /**
     * @param params:
     * @return void
     * <AUTHOR>
     * @description 导出抵扣列表
     * @date 2024/6/25 13:57
     */
    @ApiOperation("导出抵扣列表")
    @PostGclApiByToken("exportDeductionList")
    @DataScope
    public void exportDeductionList(@RequestBody DeductionQueryReq params) {

        Page<DeductionListVO> page = new Page<>(-1, -1);
        List<DeductionListVO> infoList = null;

        if (CollUtil.isEmpty(params.getExportParamList())) {
            IPage<DeductionListVO> pageList = powerAccountChangeMngService.getDeductionList(page, params);
            if (pageList == null || CollUtil.isEmpty(pageList.getRecords())) {
                return;
            }
            infoList = pageList.getRecords();
        } else {
            infoList = params.getExportParamList();
        }
        ExpandBeanUtils.convert(infoList, infoList);
        List<DeductionListVO> allList = new ArrayList<>();

        String fileName = null;
        //未抵扣
        if ("1".equals(params.getDeductionStatus())) {
            String arrears = powerAccountChangeMngService.getArrears(params);

            DeductionListVO vo = new DeductionListVO();
            vo.setPsCode("欠费金额(元)");
            vo.setAccountNumber(arrears);
            allList.add(vo);
            allList.addAll(infoList);
            fileName = "未抵扣列表";
        } else {
            allList.addAll(infoList);
            fileName = "已抵扣列表";
        }

        ExportTask exportTask = new ExportTask(allList,fileName,params.getColNameList());
        exportExecutor.addTask(exportTask);
    }


    /**
     * @param deductionId:
     * @return DeductionOrderInfoVO
     * <AUTHOR>
     * @description 查询租金账单
     * @date 2024/6/25 15:10
     */
    @ApiOperation("查询租金账单")
    @GetGclApiByToken("getDeductionOrderInfo")
    public DeductionOrderInfoVO getDeductionOrderInfo(Long deductionId) {
        if (deductionId == null) {
            throw new BussinessException("抵扣账单不能为空");
        }
        return powerAccountChangeMngService.getDeductionOrderInfo(deductionId);
    }


    /**
     * @param params:
     * @return void
     * <AUTHOR>
     * @description 抵扣提交
     * @date 2024/6/25 16:34
     */
    @ApiOperation("抵扣提交")
    @PostGclApiByToken("deductionCommit")
    public void deductionCommit(@RequestBody @Valid DeductionCreateReq params) {
        powerAccountChangeMngService.deductionCommit(params);
    }


    /**
     * @param params:
     * @return void
     * <AUTHOR>
     * @description 变更记录列表
     * @date 2024/6/26 13:46
     */
    @ApiOperation("变更记录列表")
    @PostGclApiByToken("getChangeFolwList")
    @DataScope
    public IPage<ChangeFlowListVO> getChangeFolwList(@RequestBody ChangeFlowQueryReq params) {
        Page<ChangeFlowListVO> page = new Page<>(params.getPageNum(), params.getPageSize());
        IPage<ChangeFlowListVO> infoList = powerAccountChangeMngService.getChangeFolwList(page, params);
        return infoList;
    }


    /**
     * @param params:
     * @return void
     * <AUTHOR>
     * @description 导出变更记录列表
     * @date 2024/6/26 14:03
     */
    @ApiOperation("导出变更记录列表")
    @PostGclApiByToken("exportChangeFolwList")
    @DataScope
    public void exportChangeFolwList(@RequestBody ChangeFlowQueryReq params) {

        Page<ChangeFlowListVO> page = new Page<>(-1, -1);
        List<ChangeFlowListVO> infoList = null;

        if (CollUtil.isEmpty(params.getExportParamList())) {
            IPage<ChangeFlowListVO> pageList = powerAccountChangeMngService.getChangeFolwList(page, params);
            if (pageList == null || CollUtil.isEmpty(pageList.getRecords())) {
                return;
            }
            infoList = pageList.getRecords();
        } else {
            infoList = params.getExportParamList();
        }
        ExportTask exportTask = new ExportTask(infoList,"变更记录列表",params.getColNameList());
        exportExecutor.addTask(exportTask);
    }


    /**
     * @param params:
     * @return IPage<DeductionSumListVO>
     * <AUTHOR>
     * @description 抵扣汇总列表
     * @date 2024/6/26 15:04
     */
    @ApiOperation("抵扣汇总列表")
    @PostGclApiByToken("getDeductionSumList")
    @DataScope(getTable = "t_project_company")
    public IPage<DeductionSumListVO> getDeductionSumList(@RequestBody DeductionSumQueryReq params)  {

        Page<DeductionSumListVO> page = new Page<>(params.getPageNum(), params.getPageSize());
        IPage<DeductionSumListVO> infoList = powerAccountChangeMngService.getDeductionSumList(page, params);

        if (infoList != null && CollUtil.isNotEmpty(infoList.getRecords())) {
            ExpandBeanUtils.convert(infoList, infoList);
        }
        return infoList;
    }


    /**
     * @param params:
     * @return void
     * <AUTHOR>
     * @description 导出抵扣列表
     * @date 2024/6/26 15:07
     */
    @ApiOperation("导出抵扣汇总列表")
    @PostGclApiByToken("exportDeductionSumList")
    @DataScope(getTable = "t_project_company")
    public void exportDeductionSumList(@RequestBody DeductionSumQueryReq params) {

        Page<DeductionSumListVO> page = new Page<>(-1, -1);
        List<DeductionSumListVO> infoList = null;

        if (CollUtil.isEmpty(params.getExportParamList())) {
            IPage<DeductionSumListVO> pageList = powerAccountChangeMngService.getDeductionSumList(page, params);
            if (pageList == null || CollUtil.isEmpty(pageList.getRecords())) {
                return;
            }
            infoList = pageList.getRecords();
        } else {
            infoList = params.getExportParamList();
        }
        ExpandBeanUtils.convert(infoList, infoList);
        ExportTask exportTask = new ExportTask(infoList,"抵扣汇总列表",params.getColNameList());
        exportExecutor.addTask(exportTask);
    }


    /**
     * @param params:
     * @return void
     * <AUTHOR>
     * @description 欠费汇总列表
     * @date 2024/6/27 9:16
     */
    @ApiOperation("欠费汇总列表")
    @PostGclApiByToken("getArrearsSumList")
    @DataScope
    public IPage<ArrearsListVO> getArrearsSumList(@RequestBody ArrearsQueryReq params) {
        Page<ArrearsListVO> page = new Page<>(params.getPageNum(), params.getPageSize());
        IPage<ArrearsListVO> infoList = powerAccountChangeMngService.getArrearsSumList(page, params);

        if (infoList != null && CollUtil.isNotEmpty(infoList.getRecords())) {
            ExpandBeanUtils.convert(infoList, infoList);
        }
        return infoList;
    }


    /**
     * @param params:
     * @return void
     * <AUTHOR>
     * @description 导出欠费汇总列表
     * @date 2024/6/27 9:30
     */
    @ApiOperation("导出欠费汇总列表")
    @PostGclApiByToken("exportArrearsSumList")
    @DataScope
    public void exportArrearsSumList(@RequestBody ArrearsQueryReq params) {

        Page<ArrearsListVO> page = new Page<>(-1, -1);
        List<ArrearsListVO> infoList = null;

        if (CollUtil.isEmpty(params.getExportParamList())) {
            IPage<ArrearsListVO> pageList = powerAccountChangeMngService.getArrearsSumList(page, params);
            if (pageList == null || CollUtil.isEmpty(pageList.getRecords())) {
                return;
            }
            infoList = pageList.getRecords();
        } else {
            infoList = params.getExportParamList();
        }
        ExpandBeanUtils.convert(infoList, infoList);
        ExportTask exportTask = new ExportTask(infoList,"欠费汇总列表",params.getColNameList());
        exportExecutor.addTask(exportTask);
    }


}
