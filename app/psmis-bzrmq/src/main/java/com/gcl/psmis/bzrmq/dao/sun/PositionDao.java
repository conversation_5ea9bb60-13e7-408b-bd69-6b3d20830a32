package com.gcl.psmis.bzrmq.dao.sun;

import com.gcl.psmis.bzrmq.pojo.vo.AreaTownVO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

/**
 * @ClassName PositionDao
 * @Description 省市区镇位置
 * <AUTHOR>
 * @Date 2023/8/9 17:07
 **/
@Mapper
public interface PositionDao {
    AreaTownVO getAreaById(@Param("id") Integer id);

    AreaTownVO getCityById(@Param("id") Integer id);

    AreaTownVO getProvinceById(@Param("id") Integer id);

    AreaTownVO getTownById(@Param("id") Integer id);

}
