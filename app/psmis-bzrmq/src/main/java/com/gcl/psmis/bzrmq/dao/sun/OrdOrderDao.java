package com.gcl.psmis.bzrmq.dao.sun;

import com.baomidou.dynamic.datasource.annotation.DS;
import com.gcl.psmis.bzrmq.pojo.vo.*;
import com.gcl.psmis.framework.mq.event.CreateMaterialEvent;
import com.gcl.psmis.framework.mq.event.CreateSaveConnectedEvent;
import com.gcl.psmis.framework.mq.event.bz.*;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.Date;
import java.util.List;

@Mapper
@DS("xyg")
public interface OrdOrderDao {


    //多场景
    List<MultiSceneVO> getMultiSceneByStation(@Param("stationNo") String stationNo);

}
