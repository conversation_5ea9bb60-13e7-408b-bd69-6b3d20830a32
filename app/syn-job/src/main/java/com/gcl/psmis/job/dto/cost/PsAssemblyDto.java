package com.gcl.psmis.job.dto.cost;

import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 电站组件
 */
@Builder
@AllArgsConstructor
@NoArgsConstructor
@Data
public class PsAssemblyDto {

    @ApiModelProperty("电站编号")
    private String psCode;

    @ApiModelProperty("组件功率(w)")
    private String assemblyPower;

    @ApiModelProperty("组件块数")
    private Integer assemblyNum;

}
