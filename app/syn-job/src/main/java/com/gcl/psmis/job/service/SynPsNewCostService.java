package com.gcl.psmis.job.service;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollStreamUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.date.DateTime;
import cn.hutool.core.date.DateUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.gcl.psmis.framework.common.dto.ele.SceneRoofDTO;
import com.gcl.psmis.framework.common.dto.ele.TheoryHoursDTO;
import com.gcl.psmis.framework.common.req.ele.EleCountReq;
import com.gcl.psmis.framework.common.resp.ele.EleYearCountResp;
import com.gcl.psmis.framework.common.resp.ele.PsRoofResp;
import com.gcl.psmis.framework.common.vo.invest.BsegVO;
import com.gcl.psmis.framework.common.vo.invest.DeviceCostVO;
import com.gcl.psmis.framework.common.vo.invest.MaterialVo;
import com.gcl.psmis.framework.mbg.entity.*;
import com.gcl.psmis.framework.mbg.service.*;
import com.gcl.psmis.job.dao.InvestCostDesignSchemeDao;
import com.gcl.psmis.job.dao.SynPsInfoDao;
import com.gcl.psmis.job.dto.cost.*;
import com.xxl.job.core.context.XxlJobHelper;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.stream.Collectors;


/**
 * 同步并网电站成本
 */

@Slf4j
@Service
@RequiredArgsConstructor
public class SynPsNewCostService {

    private final SynPsInfoDao synPsInfoDao;
    private final TModelPsCostService tModelPsCostService;
    private final TModelAgentRewardService tModelAgentRewardService;
    private final TModelDeviceCostService tModelDeviceCostService;
    private final TModelManageCostService tModelManagerCostService;
    private final TSceneRoofService tSceneRoofService;
    private final TInvestSupplyCostService tInvestSupplyCostService;
    private final TInvestManageCostService tInvestManageCostService;
    private final InvestCostDesignSchemeDao investCostDesignSchemeDao;
    private final FiSettlefeetypeService fiSettlefeetypeService;
    private final SysBurelaService sysBurelaService;
    public static final int MATERIAL_GROUP_ZU = 1;
    //支架
    public static final int MATERIAL_GROUP_SUPPORT = 7;
    //并网箱
    public static final int MATERIAL_GROUP_BOX = 3;
    //逆变器
    public static final int MATERIAL_GROUP_INVERTER = 2;
    //小辅材
    public static final int MATERIAL_GROUP_SMALL_MATERIAL = 8;
    //施工材料，也算到小辅材
    public static final int MATERIAL_GROUP_CONSTRUCTION_MATERIAL = 93;

    /**
     * 同步电站成本(已发货)
     *
     * @param startTime 开始时间结束时间
     * @param endTime   结束时间
     */
    public void synPsDeliveryCostNew(DateTime startTime, DateTime endTime) {
        List<TModelPsCostEntity> results = new ArrayList<>();
        //查询已发货电站基本信息(时间范围内)
        List<ModelPsDetailDto> psDetails = synPsInfoDao.getPsDetails(startTime, endTime);
        if (CollUtil.isEmpty(psDetails)) {
            XxlJobHelper.log("未查询到已发货电站数据");
            return;
        }
        Set<String> psCodes = CollStreamUtil.toSet(psDetails, ModelPsDetailDto::getPsCode);
        //查询电站相关设计方案
        List<PsInstallDto> psInstall = synPsInfoDao.getPsInstall(psCodes);
        Map<String, List<PsInstallDto>> designMap = CollStreamUtil.groupByKey(psInstall, PsInstallDto::getPsCode);
        //查询设计方案对应的安装形式
        List<InstallFormDto> installForm = synPsInfoDao.getInstallForm();
        Map<Integer, String> formMap = CollStreamUtil.toMap(installForm, InstallFormDto::getDesignSchemeId, InstallFormDto::getInstallFormName);
        for (ModelPsDetailDto ps : psDetails) {
            //根据设计方案判断是否拆分数据
            List<PsInstallDto> psInstallDtos = designMap.get(ps.getPsCode());
            if (CollUtil.isEmpty(psInstallDtos)) {
                continue;
            }
            if (psInstallDtos.size() == 1) {
                PsInstallDto psInstallDto = psInstallDtos.get(0);
                //只有一条不是混装封装电站数据
                ps.setAssemblyNum(psInstallDto.getAssemblyNum().intValue());
                ps.setMixFlag(0);
                ps.setAssemblyType(String.valueOf(psInstallDto.getAssemblyPower().intValue()));
                ps.setAssemblyPower(String.valueOf(psInstallDto.getAssemblyPower().intValue()));
                ps.setCapital(psInstallDto.getDesignPower());
                ps.setInstallForm(formMap.get(psInstallDto.getDicdatavalue()));
                //调用获取电站对应的首年小时数
                ps.setFirstYearHour(this.getFirstYearHour(psInstallDto, ps, 0));
                TModelPsCostEntity tModelPsCostEntity = BeanUtil.copyProperties(ps, TModelPsCostEntity.class);
                results.add(tModelPsCostEntity);
            } else {
                //多条数据根据设计方案和组件功率分组
                Map<Integer, Map<BigDecimal, List<PsInstallDto>>> sceneMap = CollStreamUtil.groupBy2Key(psInstallDtos, PsInstallDto::getDicdatavalue, PsInstallDto::getAssemblyPower);
                int mixFlag = 1;
                if (sceneMap.size() == 1) {
                    for (Map.Entry<Integer, Map<BigDecimal, List<PsInstallDto>>> powerMap : sceneMap.entrySet()) {
                        Map<BigDecimal, List<PsInstallDto>> value = powerMap.getValue();
                        if (value.size() == 1) {
                            //不是混装
                            mixFlag = 0;
                            break;
                        }
                    }
                }
                for (Map.Entry<Integer, Map<BigDecimal, List<PsInstallDto>>> powerMap : sceneMap.entrySet()) {
                    Map<BigDecimal, List<PsInstallDto>> value = powerMap.getValue();
                    for (Map.Entry<BigDecimal, List<PsInstallDto>> map : value.entrySet()) {
                        List<PsInstallDto> dtos = map.getValue();
                        //组件块数相加
                        BigDecimal assemblyNum = dtos.stream().map(PsInstallDto::getAssemblyNum).reduce(BigDecimal.ZERO, BigDecimal::add);
                        //设计方案容量相加
                        BigDecimal designPower = dtos.stream().map(PsInstallDto::getDesignPower).reduce(BigDecimal.ZERO, BigDecimal::add);
                        ps.setAssemblyNum(assemblyNum.intValue());
                        ps.setMixFlag(mixFlag);
                        ps.setAssemblyType(String.valueOf(dtos.get(0).getAssemblyPower()));
                        ps.setCapital(designPower);
                        ps.setAssemblyPower(String.valueOf(dtos.get(0).getAssemblyPower()));
                        ps.setInstallForm(formMap.get(powerMap.getKey()));
                        //调用获取电站对应的首年小时数
                        ps.setFirstYearHour(this.getFirstYearHour(dtos.get(0), ps, mixFlag));
                        TModelPsCostEntity tModelPsCostEntity = BeanUtil.copyProperties(ps, TModelPsCostEntity.class);
                        results.add(tModelPsCostEntity);
                    }
                }
            }
        }
        //根据电站获取租金成本
        List<PsRentCostDto> rentCost = this.getPsRentCost(psCodes);
        Map<String, PsRentCostDto> rentMap = CollStreamUtil.toMap(rentCost, PsRentCostDto::getPsCode, v -> v);
        //代理商成本
        this.getTheoryAgentCost(results);

        //取投资模型数据(代理商奖励,管理成本,设备成本)
        this.getModelCost(results);
        //拆分后的数据组装租金成本,代理商成本
        for (TModelPsCostEntity result : results) {
            //租金成本
            PsRentCostDto psRentCostDto = rentMap.get(result.getPsCode());
            if (Objects.nonNull(psRentCostDto)) {
                result.setOnePhaseYearRent(psRentCostDto.getOnePhaseYearRent());
                result.setTwoPhaseYearRent(psRentCostDto.getTwoPhaseYearRent());
                result.setThreePhaseYearRent(psRentCostDto.getThreePhaseYearRent());
                result.setFourPhaseYearRent(psRentCostDto.getFourPhaseYearRent());
                result.setFivePhaseYearRent(psRentCostDto.getFivePhaseYearRent());
                result.setSixPhaseYearRent(psRentCostDto.getSixPhaseYearRent());
            }
        }
        if (CollUtil.isNotEmpty(results)) {
            for (TModelPsCostEntity result : results) {
                //封装理论造价合计 = 理论代理商成本+ 理论代理商奖励+理论设备成本+ 理论管理成本
                result.setTheoryTotalCost(Optional.ofNullable(result.getTheoryDevelopCost()).orElse(BigDecimal.ZERO)
                        .add(Optional.ofNullable(result.getTheoryDeviceCost()).orElse(BigDecimal.ZERO))
                        .add(Optional.ofNullable(result.getTheoryInstallCost()).orElse(BigDecimal.ZERO))
                        .add(Optional.ofNullable(result.getTheoryCapacityReward()).orElse(BigDecimal.ZERO))
                        .add(Optional.ofNullable(result.getTheoryActualEffectReward()).orElse(BigDecimal.ZERO))
                        .add(Optional.ofNullable(result.getTheoryAssembly()).orElse(BigDecimal.ZERO))
                        .add(Optional.ofNullable(result.getTheoryBracket()).orElse(BigDecimal.ZERO))
                        .add(Optional.ofNullable(result.getTheoryBox()).orElse(BigDecimal.ZERO))
                        .add(Optional.ofNullable(result.getTheoryInverter()).orElse(BigDecimal.ZERO))
                        .add(Optional.ofNullable(result.getTheoryFc()).orElse(BigDecimal.ZERO))
                        .add(Optional.ofNullable(result.getTheorySupplyChainCost()).orElse(BigDecimal.ZERO))
                        .add(Optional.ofNullable(result.getTheoryManageCost()).orElse(BigDecimal.ZERO))
                        .add(Optional.ofNullable(result.getTheoryFinanceCost()).orElse(BigDecimal.ZERO)));
                result.setCreateTime(DateUtil.date());
                result.setUpdateTime(DateUtil.date());
                result.setCreateByName("xxl-job");
                result.setUpdateByName("xxl-job");
            }
            tModelPsCostService.saveBatch(results);
        }
    }

    /**
     * 同步电站成本(已并网)
     *
     * @param startTime 开始时间结束时间
     * @param endTime   结束时间
     */
    public void synPsActualCostNew(DateTime startTime, DateTime endTime) {
        String formatTime = DateUtil.format(startTime, "yyyy-MM");
        //查询已并网电站基本信息(时间范围内)
        List<ModelPsDetailDto> psDetails = synPsInfoDao.getPsGridInfo(startTime, endTime);
        if (CollUtil.isEmpty(psDetails)) {
            XxlJobHelper.log("未查询到已并网电站信息");
            return;
        }
        Map<String, Date> timeMap = CollStreamUtil.toMap(psDetails, ModelPsDetailDto::getPsCode, ModelPsDetailDto::getConnectedTime);
        //查询电站成本已存在的数据
        List<TModelPsCostEntity> results = tModelPsCostService.list(Wrappers.<TModelPsCostEntity>lambdaQuery()
                .in(TModelPsCostEntity::getPsCode, psDetails.stream().map(ModelPsDetailDto::getPsCode).collect(Collectors.toSet())));
        if (CollUtil.isEmpty(results)) {
            XxlJobHelper.log("未查询到电站成本数据,请先同步已发货电站成本!");
            return;
        }
        Set<String> psCodes = results.stream().map(TModelPsCostEntity::getPsCode).collect(Collectors.toSet());
        //查询实际代理商成本
        List<TModelPsCostEntity> agentCost = this.getAgentCost(psCodes);
        Map<String, TModelPsCostEntity> agentCostMap = CollStreamUtil.toMap(agentCost, TModelPsCostEntity::getPsCode, v -> v);
        //查询实际代理商奖励
        List<TModelPsCostEntity> agentRewards = this.getAgentRewards(psCodes);
        Map<String, TModelPsCostEntity> agentRewardMap = CollStreamUtil.toMap(agentRewards, TModelPsCostEntity::getPsCode, v -> v);
        //查询实际设备成本
        this.getDeviceCost(results);
        //查询实际管理费成本
        List<TModelPsCostEntity> manageCost = this.getManageCost(results, formatTime);
        Map<Long, TModelPsCostEntity> manageCostMap = CollStreamUtil.toMap(manageCost, TModelPsCostEntity::getProvinceId, v -> v);
        //封装数据
        for (TModelPsCostEntity result : results) {
            //设置并网日期
            result.setConnectedTime(timeMap.get(result.getPsCode()));
            //实际代理商成本
            TModelPsCostEntity agentCostDto = agentCostMap.get(result.getPsCode());
            if (Objects.nonNull(agentCostDto)) {
                result.setActualDevelopCost(this.calculateFee(result, agentCostDto.getActualDevelopCost()));
                result.setActualDeviceCost(this.calculateFee(result, agentCostDto.getActualDeviceCost()));
                result.setActualInstallCost(this.calculateFee(result, agentCostDto.getActualInstallCost()));
            }
            //代理商奖励
            TModelPsCostEntity agentRewardDto = agentRewardMap.get(result.getPsCode());
            if (Objects.nonNull(agentRewardDto)) {
                result.setActualCapacityReward(this.calculateFee(result, agentRewardDto.getActualCapacityReward()));
                result.setActualActualEffectReward(this.calculateFee(result, agentRewardDto.getActualActualEffectReward()));
            }
//            //设备成本
//            TModelPsCostEntity deviceCostDto = deviceCostMap.get(result.getPsCode());
//            if (Objects.nonNull(deviceCostDto)) {
//                result.setActualAssembly(deviceCostDto.getActualAssembly());
//                result.setActualBracket(deviceCostDto.getActualBracket());
//                result.setActualBox(deviceCostDto.getActualBox());
//                result.setActualInverter(deviceCostDto.getActualInverter());
//                result.setActualFc(deviceCostDto.getActualFc());
//            }
            //实际管理成本
            TModelPsCostEntity manageCostDto = manageCostMap.get(result.getProvinceId());
            if (Objects.nonNull(manageCostDto)) {
                result.setActualManageCost(manageCostDto.getActualManageCost());
                result.setActualFinanceCost(manageCostDto.getActualFinanceCost());
                result.setActualSupplyChainCost(manageCostDto.getActualSupplyChainCost());
                result.setActualStorageCost(manageCostDto.getActualStorageCost());
                result.setActualAllotCost(manageCostDto.getActualAllotCost());
                result.setActualMoveCost(manageCostDto.getActualMoveCost());
            }
            //封装实际造价合计 = 实际代理商成本+ 实际代理商奖励+实际设备成本+ 实际管理成本
            result.setTheoryTotalCost(Optional.ofNullable(result.getActualDevelopCost()).orElse(BigDecimal.ZERO)
                    .add(Optional.ofNullable(result.getActualDeviceCost()).orElse(BigDecimal.ZERO))
                    .add(Optional.ofNullable(result.getActualInstallCost()).orElse(BigDecimal.ZERO))
                    .add(Optional.ofNullable(result.getActualCapacityReward()).orElse(BigDecimal.ZERO))
                    .add(Optional.ofNullable(result.getActualActualEffectReward()).orElse(BigDecimal.ZERO))
                    .add(Optional.ofNullable(result.getActualAssembly()).orElse(BigDecimal.ZERO))
                    .add(Optional.ofNullable(result.getActualBracket()).orElse(BigDecimal.ZERO))
                    .add(Optional.ofNullable(result.getActualBox()).orElse(BigDecimal.ZERO))
                    .add(Optional.ofNullable(result.getActualInverter()).orElse(BigDecimal.ZERO))
                    .add(Optional.ofNullable(result.getActualFc()).orElse(BigDecimal.ZERO))
                    .add(Optional.ofNullable(result.getActualSupplyChainCost()).orElse(BigDecimal.ZERO))
                    .add(Optional.ofNullable(result.getActualManageCost()).orElse(BigDecimal.ZERO))
                    .add(Optional.ofNullable(result.getActualFinanceCost()).orElse(BigDecimal.ZERO)));
        }
    }


    /**
     * 实际 设备成本
     *
     * @return
     */
    private void getDeviceCost(List<TModelPsCostEntity> results) {
        //获取SAP电站设备成本
        List<BsegVO> baseAll = investCostDesignSchemeDao.getBsegAll();
        if (CollUtil.isEmpty(baseAll)) {
            XxlJobHelper.log("未查询到SAP电站设备成本数据!");
            return;
        }
        Map<String, List<BsegVO>> sapMap = baseAll.stream().collect(Collectors.groupingBy(BsegVO::getSgtxt));
        //获取材料组
        List<MaterialVo> group = investCostDesignSchemeDao.getGroup();
        Map<String, Integer> groupMap = group.stream().collect(
                Collectors.toMap(MaterialVo::getMaterialcode, MaterialVo::getMaterialgroup));
        for (TModelPsCostEntity result : results) {
            //设备成本 SAP 不含税 【所以值均可能为空】
            DeviceCostVO deviceCost = this.getDeviceCostNoTaxFromSap(sapMap.get(result.getPsCode()), groupMap);
            //组件不含税
            result.setActualAssembly(this.getTaxFee(deviceCost.getZuMoney()));
            //支架不含税
            result.setActualBracket(this.getTaxFee(deviceCost.getSupportMoney()));
            //并网箱不含税
            result.setActualBox(this.getTaxFee(deviceCost.getBoxMoney()));
            //逆变器不含税
            result.setActualInverter(this.getTaxFee(deviceCost.getInverterMoney()));
            //小辅材不含税
            result.setActualFc(this.getTaxFee(deviceCost.getSmallMaterialsMoney()));
        }
    }

    private DeviceCostVO getDeviceCostNoTaxFromSap(List<BsegVO> bsegVOS, Map<String, Integer> groupMap) {
        DeviceCostVO deviceCostVO = new DeviceCostVO();
        BigDecimal zuMoney = BigDecimal.ZERO;
        BigDecimal supportMoney = BigDecimal.ZERO;
        BigDecimal boxMoney = BigDecimal.ZERO;
        BigDecimal inverterMoney = BigDecimal.ZERO;
        BigDecimal smallMaterialsMoney = BigDecimal.ZERO;
        if (CollectionUtils.isNotEmpty(bsegVOS)) {
            for (BsegVO bsegVO : bsegVOS) {
                //材料编码
                String materialCode = bsegVO.getMatnr();
                if (StringUtils.isNotBlank(materialCode)) {
                    //把前面的0去掉
                    materialCode = materialCode.replaceFirst("^0*", "");
                    //根据材料编码获取类型 鑫阳光
                    Integer materialGroup = groupMap.get(materialCode);
                    if (materialGroup != null && bsegVO.getDmbtr() != null) {
                        BigDecimal dmbtr = bsegVO.getDmbtr();
                        //如果是反记账 变为负数
                        String xnegp = bsegVO.getXnegp();
                        if (StringUtils.isNotBlank(xnegp) && "X".equals(xnegp)) {
                            dmbtr = dmbtr.negate();
                        }
                        switch (materialGroup) {
                            case MATERIAL_GROUP_ZU:
                                zuMoney = zuMoney.add(dmbtr);
                                break;
                            case MATERIAL_GROUP_SUPPORT:
                                supportMoney = supportMoney.add(dmbtr);
                                break;
                            case MATERIAL_GROUP_BOX:
                                boxMoney = boxMoney.add(dmbtr);
                                break;
                            case MATERIAL_GROUP_INVERTER:
                                inverterMoney = inverterMoney.add(dmbtr);
                                break;
                            case MATERIAL_GROUP_SMALL_MATERIAL:
                            case MATERIAL_GROUP_CONSTRUCTION_MATERIAL:
                                smallMaterialsMoney = smallMaterialsMoney.add(dmbtr);
                                break;
                            default:
                                //剩余的都加到小辅材
                                smallMaterialsMoney = smallMaterialsMoney.add(dmbtr);
                                break;
                        }
                    }
                }
            }
        }
        deviceCostVO.setZuMoney(zuMoney.compareTo(BigDecimal.ZERO) == 0 ? null : zuMoney)
                .setSupportMoney(supportMoney.compareTo(BigDecimal.ZERO) == 0 ? null : supportMoney)
                .setBoxMoney(boxMoney.compareTo(BigDecimal.ZERO) == 0 ? null : boxMoney)
                .setInverterMoney(inverterMoney.compareTo(BigDecimal.ZERO) == 0 ? null : inverterMoney)
                .setSmallMaterialsMoney(smallMaterialsMoney.compareTo(BigDecimal.ZERO) == 0 ? null : smallMaterialsMoney);
        return deviceCostVO;
    }

    /**
     * 实际 管理成本
     *
     * @return
     */
    private List<TModelPsCostEntity> getManageCost(List<TModelPsCostEntity> data, String formatTime) {
        List<TModelPsCostEntity> results = new ArrayList<>();
        //计算省,装机容量
        Map<Long, BigDecimal> proMap = new HashMap<>();
        Map<Long, List<TModelPsCostEntity>> psMap = CollStreamUtil.groupByKey(data, TModelPsCostEntity::getProvinceId);
        for (Map.Entry<Long, List<TModelPsCostEntity>> map : psMap.entrySet()) {
            //防止混装的电站出现重复数据,根据电站编码去重计算总装机容量
            List<TModelPsCostEntity> collect = map.getValue().stream().collect(Collectors.collectingAndThen(Collectors.toCollection(() -> new TreeSet<>(Comparator.comparing(TModelPsCostEntity::getPsCode))), ArrayList::new));
            proMap.put(map.getKey(), collect.stream().map(TModelPsCostEntity::getCapital).reduce(BigDecimal.ZERO, BigDecimal::add));
        }
        //查询供应链数据
        List<TInvestSupplyCostEntity> supplyCosts = tInvestSupplyCostService.list(Wrappers.<TInvestSupplyCostEntity>lambdaQuery()
                .eq(TInvestSupplyCostEntity::getCostTime, lastMonth(formatTime))
                .isNotNull(TInvestSupplyCostEntity::getCostPrice));
        //财务费默认都是0.048
        BigDecimal actualFinanceCost = BigDecimal.valueOf(0.048);
        //计算管理费
        BigDecimal actualManageCost = this.getActualManageCost(formatTime);
        //封装省对应的成本
        for (Map.Entry<Long, BigDecimal> map : proMap.entrySet()) {
            List<TInvestSupplyCostEntity> proList = supplyCosts.stream().filter(cost -> cost.getAreaId() == map.getKey().intValue()).collect(Collectors.toList());
            TModelPsCostEntity entity = new TModelPsCostEntity();
            entity.setProvinceId(map.getKey());
            //仓储成本
            List<TInvestSupplyCostEntity> ccList = supplyCosts.stream().filter(cost -> "仓储".equals((cost.getCostSubject()))).collect(Collectors.toList());
            if (CollUtil.isNotEmpty(ccList)) {
                TInvestSupplyCostEntity supplyCost = ccList.get(0);
                //计算仓储成本
                entity.setActualStorageCost(supplyCost.getCostPrice() == null || map.getValue().compareTo(BigDecimal.ZERO) == 0 ? null : supplyCost.getCostPrice().divide(map.getValue(), 3, RoundingMode.HALF_UP));
            }
            //仓间调拨
            List<TInvestSupplyCostEntity> cdList = supplyCosts.stream().filter(cost -> "仓间调拨".equals((cost.getCostSubject()))).collect(Collectors.toList());
            if (CollUtil.isNotEmpty(cdList)) {
                TInvestSupplyCostEntity supplyCost = cdList.get(0);
                //计算仓间调拨
                entity.setActualAllotCost(supplyCost.getCostPrice() == null || map.getValue().compareTo(BigDecimal.ZERO) == 0 ? null : supplyCost.getCostPrice().divide(map.getValue(), 3, RoundingMode.HALF_UP));
            }
            //装卸
            List<TInvestSupplyCostEntity> zxList = proList.stream().filter(cost -> "装卸".equals((cost.getCostSubject()))).collect(Collectors.toList());
            if (CollUtil.isNotEmpty(zxList)) {
                TInvestSupplyCostEntity supplyCost = zxList.get(0);
                //计算装卸
                entity.setActualMoveCost(supplyCost.getCostPrice() == null || map.getValue().compareTo(BigDecimal.ZERO) == 0 ? null : supplyCost.getCostPrice().divide(map.getValue(), 3, RoundingMode.HALF_UP));
            }
            //物流
            List<TInvestSupplyCostEntity> wlList = proList.stream().filter(cost -> "物流".equals((cost.getCostSubject()))).collect(Collectors.toList());
            if (CollUtil.isNotEmpty(wlList)) {
                TInvestSupplyCostEntity supplyCost = wlList.get(0);
                //计算物流
                entity.setActualLogisticsCost(supplyCost.getCostPrice() == null || map.getValue().compareTo(BigDecimal.ZERO) == 0 ? null : supplyCost.getCostPrice().divide(map.getValue(), 3, RoundingMode.HALF_UP));
            }
            //供应链 = 仓储+仓间调拨+装卸+物流
            entity.setActualSupplyChainCost(
                    Optional.ofNullable(entity.getActualStorageCost()).orElse(BigDecimal.ZERO)
                            .add(Optional.ofNullable(entity.getActualAllotCost()).orElse(BigDecimal.ZERO))
                            .add(Optional.ofNullable(entity.getActualMoveCost()).orElse(BigDecimal.ZERO))
                            .add(Optional.ofNullable(entity.getActualLogisticsCost()).orElse(BigDecimal.ZERO)));
            //财务费默认都是0.048
            entity.setActualFinanceCost(actualFinanceCost);
            //管理费
            entity.setActualManageCost(actualManageCost);
            results.add(entity);
        }
        return results;
    }

    /**
     * 获取月份对应管理费成本
     *
     * @param formatTime
     * @return
     */
    private BigDecimal getActualManageCost(String formatTime) {
        //获取总七套发货容量
        Long sumQiTao = synPsInfoDao.getSumQiTao();
        //获取管理费
        List<TInvestManageCostEntity> glfList = tInvestManageCostService.list(Wrappers.<TInvestManageCostEntity>lambdaQuery()
                .select(TInvestManageCostEntity::getType, TInvestManageCostEntity::getTotalAmount)
                .eq(TInvestManageCostEntity::getCostTime, formatTime)
                .isNotNull(TInvestManageCostEntity::getTotalAmount));
        if (CollectionUtils.isNotEmpty(glfList)) {
            // 如果在cost.getType() == 1的条件下没有符合的数据，则返回null
            BigDecimal glfy = glfList.stream()
                    .filter(cost -> cost.getType() == 1)
                    .map(TInvestManageCostEntity::getTotalAmount)
                    .filter(Objects::nonNull)
                    .reduce(BigDecimal::add)
                    .orElse(null);
            return glfy == null ? null : glfy.divide(new BigDecimal(sumQiTao), 3, RoundingMode.HALF_UP);
        }
        return null;
    }

    public static String lastMonth(String yearMonth) {
        String day = yearMonth + "-01";
        LocalDate localDate = LocalDate.parse(day, DateTimeFormatter.ofPattern("yyyy-MM-dd"));
        localDate = localDate.minusMonths(1L);
        return localDate.format(DateTimeFormatter.ofPattern("yyyy-MM"));
    }

    /**
     * 实际 代理商奖励
     *
     * @param psCodes 电站编码
     * @return
     */
    private List<TModelPsCostEntity> getAgentRewards(Set<String> psCodes) {
        List<TModelPsCostEntity> results = new ArrayList<>();
        //查询xyg绩效账单(时效,容量)
        List<PsBillFeeDto> billFee = synPsInfoDao.getBillFee(psCodes);
        if (CollUtil.isEmpty(billFee)) {
            return results;
        }
        Map<String, List<PsBillFeeDto>> feeMap = CollStreamUtil.groupByKey(billFee, PsBillFeeDto::getPsCode);
        for (Map.Entry<String, List<PsBillFeeDto>> map : feeMap.entrySet()) {
            List<PsBillFeeDto> value = map.getValue();
            BigDecimal actualEffect = null;
            BigDecimal capacity = null;
            //过滤530-时效奖励账单
            List<PsBillFeeDto> timeRewards = value.stream().filter(fee -> "530".equals(fee.getBillType())).collect(Collectors.toList());
            List<PsBillFeeDto> capacityRewards = value.stream().filter(fee -> "520".equals(fee.getBillType())).collect(Collectors.toList());
            if (CollUtil.isNotEmpty(timeRewards)) {
                actualEffect = timeRewards.stream().map(PsBillFeeDto::getBillFee).reduce(BigDecimal::add).orElse(null);
            }
            if (CollUtil.isNotEmpty(capacityRewards)) {
                capacity = capacityRewards.stream().map(PsBillFeeDto::getBillFee).reduce(BigDecimal::add).orElse(null);
            }
            TModelPsCostEntity.builder()
                    .psCode(map.getKey())
                    .actualActualEffectReward(actualEffect)
                    .actualCapacityReward(capacity)
                    .build();
        }
        //处理封装数据
        return results;
    }

    /**
     * 获取电站对应首年小时数
     *
     * @param psInstallDto
     * @param ps
     * @param mixFlag      0-非混装 1-混装
     * @return
     */
    private BigDecimal getFirstYearHour(PsInstallDto psInstallDto, ModelPsDetailDto ps, int mixFlag) {
        EleYearCountResp resp = new EleYearCountResp();
        resp.setProvinceId(ps.getProvinceId());
        resp.setCityId(ps.getCityId());
        resp.setRegionId(ps.getRegionId());
        resp.setPsCode(ps.getPsCode());
        resp.setRoofTypeCode(psInstallDto.getRoofCode());
        resp.setPsType(1);
        if (mixFlag == 1) {
            return this.getBlendYear(DateUtil.year(ps.getCreatedTime()), resp, ps.getPsCode());
        }
        return this.getSingleYear(DateUtil.year(ps.getCreatedTime()), resp);
    }

    /**
     * 获取理论代理商成本--取结算标准
     *
     * @return
     */
    private void getTheoryAgentCost(List<TModelPsCostEntity> result) {
        //获取结算标准id
        Set<Long> standardIds = CollStreamUtil.toSet(result, TModelPsCostEntity::getStandardId);
        if (CollUtil.isEmpty(standardIds)) {
            return;
        }
        //查询结算标准类型
        List<SysBurelaEntity> burelaList = sysBurelaService.list(Wrappers.<SysBurelaEntity>lambdaQuery().eq(SysBurelaEntity::getBurelatype, 101)
                .in(SysBurelaEntity::getId, standardIds));
        if (CollUtil.isEmpty(burelaList)) {
            return;
        }
        Map<Long, Integer> burelaMap = CollStreamUtil.toMap(burelaList, SysBurelaEntity::getId, SysBurelaEntity::getSettleruletype);

        //查询结算标准类型费用
        List<FiSettlefeetypeEntity> list = fiSettlefeetypeService.list(Wrappers.<FiSettlefeetypeEntity>lambdaQuery()
                .in(FiSettlefeetypeEntity::getBurelaid, standardIds));
        if (CollUtil.isEmpty(list)) {
            return;
        }
        Map<Long, List<FiSettlefeetypeEntity>> standardMap = CollStreamUtil.groupByKey(list, FiSettlefeetypeEntity::getBurelaid);
        for (TModelPsCostEntity entity : result) {
            Integer type = burelaMap.get(entity.getStandardId());
            if (null == type) {
                continue;
            }
            List<FiSettlefeetypeEntity> standards = standardMap.get(entity.getStandardId());
            if (CollUtil.isEmpty(standards)) {
                continue;
            }
            for (FiSettlefeetypeEntity standard : standards) {
                BigDecimal theoryDevelopCost = null;
                BigDecimal theoryInstallCost = null;
                BigDecimal theoryDeviceCost = null;

                if (type == 10) {
                    //按块结算
                    //10-代理商开发费
                    if (standard.getEnumvalue() == 10) {
                        theoryDevelopCost = standard.getFeestandard() == null ? null : standard.getFeestandard().divide(new BigDecimal(entity.getAssemblyPower()), 3, RoundingMode.HALF_UP);
                    }
                    //20-光伏安装费
                    if (standard.getEnumvalue() == 20) {
                        theoryInstallCost = standard.getFeestandard() == null ? null : standard.getFeestandard().divide(new BigDecimal(entity.getAssemblyPower()), 3, RoundingMode.HALF_UP);
                    }
                    //60-设备材料费
                    if (standard.getEnumvalue() == 60) {
                        theoryDeviceCost = standard.getFeestandard() == null ? null : standard.getFeestandard().divide(new BigDecimal(entity.getAssemblyPower()), 3, RoundingMode.HALF_UP);
                    }
                } else {
                    //按W结算
                    if (standard.getEnumvalue() == 10) {
                        theoryDevelopCost = standard.getFeestandard();
                    }
                    //20-光伏安装费
                    if (standard.getEnumvalue() == 20) {
                        theoryInstallCost = standard.getFeestandard();
                    }
                    //60-设备材料费
                    if (standard.getEnumvalue() == 60) {
                        theoryDeviceCost = standard.getFeestandard();
                    }
                    entity.setTheoryDevelopCost(theoryDevelopCost);
                    entity.setTheoryInstallCost(theoryInstallCost);
                    entity.setTheoryDeviceCost(theoryDeviceCost);
                }

            }

        }
    }

    /**
     * 取投资模型成本
     *
     * @param results
     * @return
     */
    private void getModelCost(List<TModelPsCostEntity> results) {
        //代理商奖励
        List<TModelAgentRewardEntity> agentReward = tModelAgentRewardService.list();
        //管理成本
        List<TModelManageCostEntity> manageCosts = tModelManagerCostService.list();

        for (TModelPsCostEntity result : results) {
            //代理商成本
            if (CollUtil.isNotEmpty(agentReward)) {
                Map<Integer, List<TModelAgentRewardEntity>> typeRewardMap = CollStreamUtil.groupByKey(agentReward, TModelAgentRewardEntity::getRewardType);
                for (Map.Entry<Integer, List<TModelAgentRewardEntity>> typeReward : typeRewardMap.entrySet()) {
                    List<TModelAgentRewardEntity> value = typeReward.getValue();
                    List<TModelAgentRewardEntity> agentRewardList = value.stream().filter(item -> "其他条件".equals(item.getCreatedTimeStr())).collect(Collectors.toList());
                    //先设置其他条件的奖励
                    BigDecimal rewardAmount = agentRewardList.get(0).getRewardAmount().divide(new BigDecimal(result.getAssemblyPower()), 3, RoundingMode.HALF_UP);
                    //代理商奖励
                    for (TModelAgentRewardEntity reward : value) {
                        if ("其他条件".equals(reward.getCreatedTimeStr())) {
                            continue;
                        }
                        String[] timeArr = reward.getCreatedTimeStr().split("-");
                        if (timeArr.length != 2) {
                            continue;
                        }
                        DateTime startTime = DateUtil.parse(timeArr[0], DateTimeFormatter.ofPattern("yyyy/MM"));
                        DateTime endTime = DateUtil.parse(timeArr[1], DateTimeFormatter.ofPattern("yyyy/MM"));
                        if (result.getCreatedTime().before(endTime) && result.getCreatedTime().after(startTime)) {
                            //在时间范围内
                            rewardAmount = reward.getRewardAmount().divide(new BigDecimal(result.getAssemblyPower()), 3, RoundingMode.HALF_UP);
                        }
                    }
                    //容量奖励
                    if (typeReward.getKey() == 0) {
                        result.setTheoryCapacityReward(this.calculate(result, rewardAmount));
                    } else {
                        //时效奖励
                        result.setTheoryActualEffectReward(this.calculate(result, rewardAmount));
                    }
                }
            }
            //管理成本
            if (CollUtil.isNotEmpty(manageCosts)) {
                for (TModelManageCostEntity manageCost : manageCosts) {
                    if (result.getCreatedTime().before(manageCost.getCreatedEndTime()) && result.getCreatedTime().after(manageCost.getCreatedBeginTime())) {
                        //在时间范围内
                        result.setTheoryManageCost(manageCost.getManageCost());
                        result.setTheorySupplyChainCost(manageCost.getSupplyChainCost());
                    }
                }
            }
            //设备成本
            List<TModelDeviceCostEntity> deviceCost = tModelDeviceCostService.list(Wrappers.<TModelDeviceCostEntity>lambdaQuery()
                    .like(TModelDeviceCostEntity::getDeviceType, result.getAssemblyType() + "Wp").eq(TModelDeviceCostEntity::getInstallForm, result.getInstallForm()));
            if (CollUtil.isEmpty(deviceCost)) {
                continue;
            }
            for (TModelDeviceCostEntity device : deviceCost) {
                String[] timeArr = device.getCreatedTimeStr().split("-");
                if (timeArr.length != 2) {
                    continue;
                }
                DateTime startTime = DateUtil.parse(timeArr[0], DateTimeFormatter.ofPattern("yyyy.MM.dd"));
                DateTime endTime = "至今".equals(timeArr[1]) ? DateUtil.date() : DateUtil.parse(timeArr[1], DateTimeFormatter.ofPattern("yyyy.MM.dd"));
                if (result.getCreatedTime().before(endTime) && result.getCreatedTime().after(startTime)) {
                    //组件
                    result.setTheoryAssembly(device.getAssemblyCost());
                    //支架
                    result.setTheoryBracket(device.getBracketCost());
                    //并网箱
                    result.setTheoryBox(device.getGridCost());
                    //逆变器
                    result.setTheoryInverter(device.getInverterCost());
                    //电气辅材
                    result.setTheoryFc(device.getFcCost());
                }
            }

        }
    }

    /**
     * 实际--获取代理商成本
     *
     * @return
     */
    private List<TModelPsCostEntity> getAgentCost(Set<String> psCodes) {
        List<TModelPsCostEntity> results = new ArrayList<>();
        //查询XYG成本数据
        List<TModelPsCostEntity> agentCost = synPsInfoDao.getAgentCost(psCodes);
        Map<String, TModelPsCostEntity> agentCostMap = CollStreamUtil.toMap(agentCost, TModelPsCostEntity::getPsCode, v -> v);
        for (String psCode : psCodes) {
            TModelPsCostEntity dto = agentCostMap.get(psCode);
            results.add(dto);
        }
        return results;
    }

    /**
     * 获取电站 租金成本
     *
     * @param psCodes 电站编码
     * @return
     */
    private List<PsRentCostDto> getPsRentCost(Set<String> psCodes) {
        List<PsRentCostDto> result = new ArrayList<>();
        //获取xyg租金数据
        List<PsStandardDto> standardDtos = synPsInfoDao.getPsStandard(psCodes);
        if (CollUtil.isEmpty(standardDtos)) {
            return result;
        }
        //根据电站编码分组
        Map<String, List<PsStandardDto>> standardMap = CollStreamUtil.groupByKey(standardDtos, PsStandardDto::getPsCode);
        for (Map.Entry<String, List<PsStandardDto>> map : standardMap.entrySet()) {
            //封装电站租金年份
            PsRentCostDto dto = new PsRentCostDto();
            dto.setPsCode(map.getKey());
            List<PsStandardDto> standards = map.getValue();
            Map<Integer, Integer> yearRentMap = new HashMap<>();
            for (PsStandardDto standard : standards) {
                int year = (standard.getSatrtDate().intValue() / 12) + 1;
                int inDate = ((standard.getInDate().intValue() + standard.getSatrtDate().intValue()) / 12);
                for (int i = year; i <= inDate; i++) {
                    yearRentMap.put(i, standard.getDard().intValue());
                }

            }
            dto.setOnePhaseYearRent(yearRentMap.get(1) == null ? null : new BigDecimal(yearRentMap.get(1)));
            dto.setTwoPhaseYearRent(yearRentMap.get(2) == null ? null : new BigDecimal(yearRentMap.get(2)));
            dto.setThreePhaseYearRent(yearRentMap.get(6) == null ? null : new BigDecimal(yearRentMap.get(6)));
            dto.setFourPhaseYearRent(yearRentMap.get(11) == null ? null : new BigDecimal(yearRentMap.get(11)));
            dto.setFivePhaseYearRent(yearRentMap.get(16) == null ? null : new BigDecimal(yearRentMap.get(16)));
            dto.setSixPhaseYearRent(yearRentMap.get(21) == null ? null : new BigDecimal(yearRentMap.get(21)));
            result.add(dto);
        }
        return result;
    }

    /**
     * 混合场景 理论小时数
     *
     * @param record
     */
    private BigDecimal getBlendYear(Integer year, EleYearCountResp record, String psCode) {
        //查询理论小时
        EleCountReq req = new EleCountReq();
        req.setStartTime(DateUtil.beginOfDay(DateUtil.parse(year + "/01/01")));
        req.setEndTime(DateUtil.endOfDay(DateUtil.parse(year + "/12/31")));
        Map<String, List<TheoryHoursDTO>> theoryHoursDTOS = getTheoryHours2(req);
        BigDecimal validHours = BigDecimal.ZERO;
        // 获取屋顶类型对应场景
        List<PsRoofResp> psRoofResps = synPsInfoDao.getPsRoofInfo(psCode);
        if (CollUtil.isEmpty(psRoofResps)) {
            return null;
        }
        BigDecimal totalCapacity = psRoofResps.stream().map(PsRoofResp::getRoofTypeCapcity).reduce(BigDecimal::add).orElse(BigDecimal.ZERO);
        if (CollUtil.isNotEmpty(psRoofResps)) {
            for (PsRoofResp psRoofResp : psRoofResps) {
                // 计算装机容量比率 屋顶容量/电站容量
                BigDecimal ratio = psRoofResp.getRoofTypeCapcity().divide(totalCapacity, 4, RoundingMode.HALF_UP);
                // 户用 pid=  年+设计类型+电站类型+城市id
                String pid = year.toString() + "/" + psRoofResp.getSceneCode() + "/" + record.getPsType() + "/" + record.getCityId();
                List<TheoryHoursDTO> hoursDTOList = theoryHoursDTOS.get(pid);
                if (CollUtil.isNotEmpty(hoursDTOList)) {
                    // 理论小时数*装机容量比率
                    validHours = hoursDTOList.get(0).getTheoryHours().multiply(ratio).add(validHours).setScale(2, RoundingMode.HALF_UP);
                }
            }
        }
        return validHours;
    }

    private BigDecimal getSingleYear(Integer year, EleYearCountResp record) {
        //查询理论小时
        EleCountReq req = new EleCountReq();
        req.setStartTime(DateUtil.beginOfDay(DateUtil.parse(year + "/01/01")));
        req.setEndTime(DateUtil.endOfDay(DateUtil.parse(year + "/12/31")));
        Map<String, List<TheoryHoursDTO>> theoryHoursDTOS = getTheoryHours2(req);
        // 获取屋顶对应场景
        TSceneRoofEntity sceneRoofEntity = tSceneRoofService.getOne(new LambdaQueryWrapper<TSceneRoofEntity>()
                .eq(TSceneRoofEntity::getRoofType, record.getRoofTypeCode())
                .eq(TSceneRoofEntity::getDelFlag, 0));
        BigDecimal validHours = BigDecimal.ZERO;
        if (null != sceneRoofEntity) {
            // 户用 pid=  年+设计类型+电站类型+城市id
            String pid = year.toString() + "/" + sceneRoofEntity.getSceneCode() + "/" + record.getPsType() + "/" + record.getCityId();
            List<TheoryHoursDTO> hoursDTOList = theoryHoursDTOS.get(pid);
            if (CollectionUtil.isNotEmpty(hoursDTOList)) {
                validHours = hoursDTOList.get(0).getTheoryHours();
            }
        }
        return validHours;
    }

    /**
     * 查询理论小时
     *
     * @return
     */
    private Map<String, List<TheoryHoursDTO>> getTheoryHours2(EleCountReq eleCountReq) {
        // 查询所有设计场景对应场景类型
        List<SceneRoofDTO> sceneRoofDTOS = synPsInfoDao.listGroupByScenceCode();
        Map<String, String> collect = sceneRoofDTOS.stream().collect(Collectors.toMap(SceneRoofDTO::getSceneCode, SceneRoofDTO::getRoofTypeCodes));

        List<TheoryHoursDTO> theoryHoursDTOS = synPsInfoDao.getTheoryHours2(eleCountReq);
        if (CollUtil.isNotEmpty(theoryHoursDTOS)) {
            for (TheoryHoursDTO theoryHoursDTO : theoryHoursDTOS) {
                if (StringUtils.isNotBlank(theoryHoursDTO.getSceneCode())) {
                    theoryHoursDTO.setRoofTypeCode(collect.get(theoryHoursDTO.getSceneCode()));
                }
            }
        }
        return theoryHoursDTOS.stream().collect(Collectors.groupingBy(TheoryHoursDTO::getKey));
    }

    /**
     * 根据组件功率换算价格
     *
     * @return
     */
    private BigDecimal calculate(TModelPsCostEntity result, BigDecimal amount) {
        if (Objects.isNull(amount)) {
            return null;
        }
        return amount.divide(new BigDecimal(result.getAssemblyPower()), 3, RoundingMode.HALF_UP);
    }

    /**
     * 根据总装机容量换算价格
     *
     * @return
     */
    private BigDecimal calculateFee(TModelPsCostEntity result, BigDecimal amount) {
        if (Objects.isNull(amount)) {
            return null;
        }
        return amount.divide(result.getCapital(), 3, RoundingMode.HALF_UP);
    }

    /**
     * 根据不含税金额计算含税金额(SAP设备成本税率13%)
     *
     * @return
     */
    private BigDecimal getTaxFee(BigDecimal amount) {
        if (Objects.isNull(amount)) {
            return null;
        }
        return amount.multiply(new BigDecimal("1.13"));
    }

}
