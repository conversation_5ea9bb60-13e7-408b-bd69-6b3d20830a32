package com.gcl.psmis.job.dto.cost;

import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;

/**
 * 电站理论成本
 */
@Builder
@AllArgsConstructor
@NoArgsConstructor
@Data
public class PsTheoryCostDto {

    @ApiModelProperty("电站编号")
    private String psCode;

    @ApiModelProperty("理论-代理商成本-开发服务费")
    private BigDecimal theoryDevelopCost;

    @ApiModelProperty("理论-代理商成本-设备材料费")
    private BigDecimal theoryDeviceCost;

    @ApiModelProperty("理论-代理商成本-光伏安装施工费")
    private BigDecimal theoryInstallCost;

    @ApiModelProperty("理论-代理商奖励-容量奖励")
    private BigDecimal theoryCapacityReward;

    @ApiModelProperty("理论-代理商奖励-实效奖励")
    private BigDecimal theoryActualEffectReward;

    @ApiModelProperty("理论-设备成本-组件")
    private BigDecimal theoryAssembly;

    @ApiModelProperty("理论-设备成本-支架")
    private BigDecimal theoryBracket;

    @ApiModelProperty("理论-设备成本-并网箱")
    private BigDecimal theoryBox;

    @ApiModelProperty("理论-设备成本-逆变器")
    private BigDecimal theoryInverter;

    @ApiModelProperty("理论-设备成本-电气辅材")
    private BigDecimal theoryFc;

    @ApiModelProperty("理论-管理成本-供应链")
    private BigDecimal theorySupplyChainCost;

    @ApiModelProperty("理论-管理成本-管理费")
    private BigDecimal theoryManageCost;

    @ApiModelProperty("理论-管理成本-财务费")
    private BigDecimal theoryFinanceCost;

    @ApiModelProperty("理论-造价合计")
    private BigDecimal theoryTotalCost;

}
