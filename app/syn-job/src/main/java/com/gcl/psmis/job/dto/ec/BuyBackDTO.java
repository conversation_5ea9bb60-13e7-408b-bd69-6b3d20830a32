package com.gcl.psmis.job.dto.ec;

import lombok.Data;

/**
 * 回购主表
 *
 * <AUTHOR>
 */
@Data
public class BuyBackDTO {
    /**
     * 所属省份
     */
    private String province;
    /**
     * 所属省公司
     */
    private String companyName;
    /**
     * 备案方式
     */
    private String gridType;
    /**
     * 代理商签约主体
     */
    private String kp;
    /**
     * 电站编号
     */
    private String stationNo;
    /**
     * 电站总容量（W）
     */
    private String p;
    /**
     * 代理商开票纳税人识别号
     */
    private String dlsidx;
    /**
     * 代理商地址
     */
    private String dlsaddress;
    /**
     * 代理商开户银行
     */
    private String dlsopenbank;
    /**
     * 代理商开户账号
     */
    private String dlsbankno;
    /**
     * 我方签约主体
     */
    private String epcName;
    /**
     * 电站状态
     */
    private String state;
    /**
     * 经销商SAP客商编码
     */
    private String erpcode;
    /**
     * 事业部（户用\开发）
     */
    private String companyType;
    /**
     * 电站所属项目公司
     */
    private String projectName;
    /**
     * 自动带出的电站状态中文
     */
    private String stateText;
    /**
     * 建档审核通过时间
     */
    private String archivedTime;

}
