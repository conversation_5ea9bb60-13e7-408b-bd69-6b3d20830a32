package com.gcl.psmis.job.dto.cost;

import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;

/**
 * 电站租金
 */
@Builder
@AllArgsConstructor
@NoArgsConstructor
@Data
public class PsRentCostDto {

    @ApiModelProperty("电站编号")
    private String psCode;

    @ApiModelProperty("第一年租金")
    private BigDecimal onePhaseYearRent;

    @ApiModelProperty("第2到5年租金")
    private BigDecimal twoPhaseYearRent;

    @ApiModelProperty("第6到10年租金")
    private BigDecimal threePhaseYearRent;

    @ApiModelProperty("第11年到15年租金")
    private BigDecimal fourPhaseYearRent;

    @ApiModelProperty("第16年到20年租金")
    private BigDecimal fivePhaseYearRent;

    @ApiModelProperty("第21年到25年租金")
    private BigDecimal sixPhaseYearRent;

}
