package com.gcl.psmis.job.dto.cost;

import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;

/**
 * 电站实际成本
 */
@Builder
@AllArgsConstructor
@NoArgsConstructor
@Data
public class PsActualCostDto {

    @ApiModelProperty("电站编号")
    private String psCode;

    @ApiModelProperty("实际-代理商成本-开发服务费")
    private BigDecimal actualDevelopCost;

    @ApiModelProperty("实际-代理商成本-设备材料费")
    private BigDecimal actualDeviceCost;

    @ApiModelProperty("实际-代理商成本-光伏安装施工费")
    private BigDecimal actualInstallCost;

    @ApiModelProperty("实际-代理商奖励-容量奖励")
    private BigDecimal actualCapacityReward;

    @ApiModelProperty("实际-代理商奖励-实效奖励")
    private BigDecimal actualActualEffectReward;

    @ApiModelProperty("实际-设备成本-组件")
    private BigDecimal actualAssembly;

    @ApiModelProperty("实际-设备成本-支架")
    private BigDecimal actualBracket;

    @ApiModelProperty("实际-设备成本-并网箱")
    private BigDecimal actualBox;

    @ApiModelProperty("实际-设备成本-逆变器")
    private BigDecimal actualInverter;

    @ApiModelProperty("实际-设备成本-电气辅材")
    private BigDecimal actualFc;

    @ApiModelProperty("实际-管理成本-仓储")
    private BigDecimal actualStorageCost;

    @ApiModelProperty("实际-管理成本-仓储调拨")
    private BigDecimal actualAllotCost;

    @ApiModelProperty("实际-管理成本-装卸")
    private BigDecimal actualMoveCost;

    @ApiModelProperty("实际-管理成本-物流")
    private BigDecimal actualLogisticsCost;

    @ApiModelProperty("实际-管理成本-管理费")
    private BigDecimal actualManageCost;

    @ApiModelProperty("实际-管理成本-财务费")
    private BigDecimal actualFinanceCost;

    @ApiModelProperty("实际-造价合计")
    private BigDecimal actualTotalCost;

}
