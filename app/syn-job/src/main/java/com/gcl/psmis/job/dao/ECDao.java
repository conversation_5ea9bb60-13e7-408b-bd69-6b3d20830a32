package com.gcl.psmis.job.dao;

import com.baomidou.dynamic.datasource.annotation.DS;
import com.gcl.psmis.job.dto.ec.BuyBackSendTime;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <AUTHOR>
 */
@DS("ec")
public interface ECDao {

    /**
     * 查询EC省公司id
     *
     * @return
     */
    Long getECProvinceId(@Param("name") String name);

    /**
     * @param stationNo
     * @param ecRequestId
     * @return
     */
    Integer getBuyBackState(@Param("stationNo") String stationNo, @Param("ecRequestId") String ecRequestId);
}
