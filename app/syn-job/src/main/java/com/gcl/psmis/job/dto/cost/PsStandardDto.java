package com.gcl.psmis.job.dto.cost;

import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;

/**
 * 电站租金
 */
@Builder
@AllArgsConstructor
@NoArgsConstructor
@Data
public class PsStandardDto {

    @ApiModelProperty("电站编号")
    private String psCode;

    @ApiModelProperty("租金元/块")
    private BigDecimal dard;

    @ApiModelProperty("开始月份")
    private BigDecimal satrtDate;

    @ApiModelProperty("生效月份")
    private BigDecimal inDate;

}
