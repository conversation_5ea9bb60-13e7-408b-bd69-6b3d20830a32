package com.gcl.psmis.job.dao;

import com.baomidou.dynamic.datasource.annotation.DS;
import com.gcl.psmis.framework.mbg.entity.TInvestSupplyCostEntity;
import com.gcl.psmis.job.dto.ec.BuyBackSendTime;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.math.BigDecimal;
import java.util.List;

/**
 * <AUTHOR>
 */
@Mapper
@DS("zhiyun")
public interface ZyDao {
    List<BuyBackSendTime> getSendTime(List<String> sendNo);


    BigDecimal getCangChuByMonth(@Param("yearMonth") String yearMonth);

    List<TInvestSupplyCostEntity> getWuLiuByMonth(@Param("yearMonth") String yearMonth);

    List<TInvestSupplyCostEntity> getZhuangXieByMonth(@Param("yearMonth") String yearMonth);
}
