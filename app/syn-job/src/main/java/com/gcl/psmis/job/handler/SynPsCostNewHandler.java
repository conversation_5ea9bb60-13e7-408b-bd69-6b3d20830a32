package com.gcl.psmis.job.handler;

import cn.hutool.core.date.DateTime;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.exceptions.ExceptionUtil;
import cn.hutool.core.util.StrUtil;
import com.gcl.psmis.job.service.SynPsNewCostService;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.context.XxlJobHelper;
import com.xxl.job.core.handler.annotation.XxlJob;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.Date;

/**
 * 同步并网电站成本
 */

@Slf4j
@Service
@RequiredArgsConstructor
public class SynPsCostNewHandler {

    private final SynPsNewCostService synPowerStationCost;

    /**
     * 已发货成本电站
     * 入参为All--初始化数据
     * 入参为2025-05 初始化25年5月份数据
     */
    @XxlJob("SynPsCostNewHandler")
    public ReturnT<String> SynPsCostNewHandler() throws Exception {
        XxlJobHelper.log("SynPsCostNewHandler start...");
        try {
            String param = XxlJobHelper.getJobParam();
            //默认时间上个月开始结束
            DateTime startTime = DateUtil.beginOfMonth(DateUtil.offsetMonth(new Date(), -1));
            DateTime endTime = DateUtil.endOfMonth(DateUtil.offsetMonth(new Date(), -1));
            if (StrUtil.isNotBlank(param)) {
                if ("All".equals(param)) {
                    startTime = null;
                    endTime = null;
                } else {
                    startTime = DateUtil.beginOfMonth(DateUtil.parse(param, "yyyy-MM"));
                    endTime = DateUtil.endOfMonth(DateUtil.parse(param, "yyyy-MM"));
                }
            }
            synPowerStationCost.synPsDeliveryCostNew(startTime, endTime);
        } catch (Exception e) {
            XxlJobHelper.log("SynPsCostNewHandler fail..." + ExceptionUtil.stacktraceToString(e));
            return ReturnT.FAIL;
        }
        XxlJobHelper.log("SynPsCostNewHandler end...");
        return ReturnT.SUCCESS;
    }

    /**
     * 已并网成本电站
     * 入参为All--初始化数据
     * 入参为2025-05 初始化25年5月份数据
     */
    @XxlJob("SynPsActualCostNewHandler")
    public ReturnT<String> SynPsActualCostNewHandler() throws Exception {
        XxlJobHelper.log("SynPsActualCostNewHandler start...");
        try {
            String param = XxlJobHelper.getJobParam();
            //默认时间上个月开始结束
            DateTime startTime = DateUtil.beginOfMonth(DateUtil.offsetMonth(new Date(), -1));
            DateTime endTime = DateUtil.endOfMonth(DateUtil.offsetMonth(new Date(), -1));
            if (StrUtil.isNotBlank(param)) {
                if ("All".equals(param)) {
                    startTime = null;
                    endTime = null;
                } else {
                    startTime = DateUtil.beginOfMonth(DateUtil.parse(param, "yyyy-MM"));
                    endTime = DateUtil.endOfMonth(DateUtil.parse(param, "yyyy-MM"));
                }
            }
            synPowerStationCost.synPsActualCostNew(startTime, endTime);
        } catch (Exception e) {
            XxlJobHelper.log("SynPsActualCostNewHandler fail..." + ExceptionUtil.stacktraceToString(e));
            return ReturnT.FAIL;
        }
        XxlJobHelper.log("SynPsActualCostNewHandler end...");
        return ReturnT.SUCCESS;
    }

}
