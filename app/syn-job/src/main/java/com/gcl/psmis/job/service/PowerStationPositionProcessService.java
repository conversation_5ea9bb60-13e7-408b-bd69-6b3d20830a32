package com.gcl.psmis.job.service;

import cn.hutool.core.collection.CollUtil;
import com.gcl.psmis.framework.mbg.entity.TPowerStationEntity;
import com.gcl.psmis.framework.mbg.entity.TPowerStationPositionEntity;
import com.gcl.psmis.framework.mbg.service.TPowerStationPositionService;
import com.gcl.psmis.framework.mbg.service.TPowerStationService;
import com.gcl.psmis.job.dao.ZwDao;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.List;

@Service
public class PowerStationPositionProcessService {

    @Resource
    private ZwDao zwDao;

    @Resource
    private TPowerStationPositionService powerStationPositionService;
    @Resource
    private TPowerStationService powerStationService;

    //处理经纬度分类
    public void processLng() {
        //查询所有电站
        List<TPowerStationEntity> list = powerStationService.lambdaQuery().ne(TPowerStationEntity::getPsSource, 2).select(TPowerStationEntity::getPsCode, TPowerStationEntity::getLng, TPowerStationEntity::getLat).list();
        //查询位置表，排除已经存在的数据
        List<TPowerStationPositionEntity> tPowerStationPositionEntityList = powerStationPositionService.lambdaQuery().select(TPowerStationPositionEntity::getStationNo).list();
        list.removeIf(tPowerStationEntity -> tPowerStationPositionEntityList.stream().anyMatch(tPowerStationPositionEntity -> tPowerStationPositionEntity.getStationNo().equals(tPowerStationEntity.getPsCode())));
        if(CollUtil.isNotEmpty(list)){
            for (TPowerStationEntity powerStationEntity : list) {
                //1.遍历所有电站，获取lng和lat
                //2.查询结果表，所有经纬度
                //3.判断距离，如果小于3公里，则存储当前经纬度，如果超过，则存储该经纬度

                BigDecimal lng = powerStationEntity.getLng();
                BigDecimal lat = powerStationEntity.getLat();
                String psCode = powerStationEntity.getPsCode();
                if(lng != null && lat != null){
                    List<TPowerStationPositionEntity> tPowerStationPositionEntities = zwDao.pspList();
                    TPowerStationPositionEntity powerStationPositionEntity = new TPowerStationPositionEntity();
                    if(CollUtil.isEmpty(tPowerStationPositionEntities)){

                        powerStationPositionEntity.setLng(lng);
                        powerStationPositionEntity.setLat(lat);
                        powerStationPositionEntity.setStationNo(psCode);
                        powerStationPositionService.save(powerStationPositionEntity);
                    }else{
                        int flag = 0;
                        for (TPowerStationPositionEntity tPowerStationPositionEntity : tPowerStationPositionEntities) {
                            BigDecimal lng1 = tPowerStationPositionEntity.getLng();
                            BigDecimal lat1 = tPowerStationPositionEntity.getLat();

                            double v = calculateDistance(lat.doubleValue(), lng.doubleValue(), lat1.doubleValue(), lng1.doubleValue());
                            if(v <= 30){
                                //存储当前经纬度
                                powerStationPositionEntity.setLng(lng1);
                                powerStationPositionEntity.setLat(lat1);
                                flag++;
                                break;
                            }
                        }
                        if(flag == 0){
                            powerStationPositionEntity.setLng(lng);
                            powerStationPositionEntity.setLat(lat);
                        }

                        powerStationPositionEntity.setStationNo(psCode);
                        powerStationPositionService.save(powerStationPositionEntity);
                    }
                }
            }
        }

    }

    private double calculateDistance(double lat1, double lon1, double lat2, double lon2) {
        // 地球半径，单位为千米
        final int EARTH_RADIUS = 6371;

        // 将角度转换为弧度
        lat1 = Math.toRadians(lat1);
        lon1 = Math.toRadians(lon1);
        lat2 = Math.toRadians(lat2);
        lon2 = Math.toRadians(lon2);

        // 计算纬度差和经度差
        double dLat = lat2 - lat1;
        double dLon = lon2 - lon1;

        // 应用Haversine公式
        double a = Math.pow(Math.sin(dLat / 2), 2) + Math.cos(lat1) * Math.cos(lat2) * Math.pow(Math.sin(dLon / 2), 2);
        double c = 2 * Math.atan2(Math.sqrt(a), Math.sqrt(1 - a));

        return EARTH_RADIUS * c; // 距离单位为千米
    }
}
