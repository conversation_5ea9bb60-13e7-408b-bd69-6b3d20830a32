package com.gcl.psmis.job.dto.cost;

import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 设计方案和安装形式对应关系
 */
@Builder
@AllArgsConstructor
@NoArgsConstructor
@Data
public class InstallFormDto {

    @ApiModelProperty("设计方案字典值")
    private Integer designSchemeId;

    @ApiModelProperty("设计方案名称")
    private String designSchemeName;

    @ApiModelProperty("安装形式名称")
    private String installFormName;

}
