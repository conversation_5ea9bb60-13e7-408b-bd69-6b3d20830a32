package com.gcl.psmis.job.dto.cost;

import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;
import java.util.Date;

/**
 * 电站基本信息
 */
@Builder
@AllArgsConstructor
@NoArgsConstructor
@Data
public class ModelPsDetailDto {

    @ApiModelProperty("电站编号")
    private String psCode;

    @ApiModelProperty("产品名称")
    private String productName;

    @ApiModelProperty("是否混装: 0-否,1-是")
    private Integer mixFlag;

    @ApiModelProperty("产权方")
    private String propertyOwner;

    @ApiModelProperty("电费归集方式")
    private String electricityBillMode;

    @ApiModelProperty("省份id")
    private Long provinceId;

    @ApiModelProperty("省名称")
    private String provinceName;

    @ApiModelProperty("城市id")
    private Long cityId;

    @ApiModelProperty("市名称")
    private String cityName;

    @ApiModelProperty("地区id")
    private Long regionId;

    @ApiModelProperty("区名称")
    private String regionName;

    @ApiModelProperty("结算标准id")
    private Long standardId;

    @ApiModelProperty("备案类型")
    private String recordType;

    @ApiModelProperty("电站节点编码")
    private Integer nodeCode;

    @ApiModelProperty("电站状态")
    private String psStatus;

    @ApiModelProperty("组件类型")
    private String assemblyType;

    @ApiModelProperty("安装形式")
    private String installForm;

    @ApiModelProperty("组件功率(w)")
    private String assemblyPower;

    @ApiModelProperty("组件总块数")
    private Integer sumAssemblyNum;

    @ApiModelProperty("组件块数")
    private Integer assemblyNum;

    @ApiModelProperty("装机容量(w)")
    private BigDecimal capital;

    @ApiModelProperty("建档日期")
    private Date createdTime;

    @ApiModelProperty("齐套发货日期")
    private Date qiTaoTime;

    @ApiModelProperty("有效发电首日")
    private Date firstPowerTime;

    @ApiModelProperty("并网通过日期")
    private Date connectedTime;

    @ApiModelProperty("首年理论小时数")
    private BigDecimal firstYearHour;

}
