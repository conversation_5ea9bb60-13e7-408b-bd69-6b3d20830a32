package com.gcl.psmis.job.dao;

import cn.hutool.core.date.DateTime;
import com.baomidou.dynamic.datasource.annotation.DS;
import com.gcl.psmis.framework.common.dto.ele.SceneRoofDTO;
import com.gcl.psmis.framework.common.dto.ele.TheoryHoursDTO;
import com.gcl.psmis.framework.common.req.ele.EleCountReq;
import com.gcl.psmis.framework.common.resp.ele.PsRoofResp;
import com.gcl.psmis.framework.mbg.entity.TModelPsCostEntity;
import com.gcl.psmis.job.dto.cost.*;
import org.apache.ibatis.annotations.Param;

import java.util.List;
import java.util.Set;

/**
 * <AUTHOR>
 */
public interface SynPsInfoDao {

    /**
     * 根据时间获取电站信息
     *
     * @param startTime
     * @param endTime
     * @return
     */
    @DS("xyg")
    List<ModelPsDetailDto> getPsDetails(@Param("startTime") DateTime startTime, @Param("endTime") DateTime endTime);

    /**
     * 根据电站编号获取组件信息
     *
     * @param psCodes
     * @return
     */
    @DS("xyg")
    List<PsAssemblyDto> getAssembly(@Param("psCodes") Set<String> psCodes);

    /**
     * 根据电站编号获取电站安装信息
     *
     * @param psCodes 电站编号
     * @return
     */
    @DS("xyg")
    List<PsInstallDto> getPsInstall(@Param("psCodes") Set<String> psCodes);

    /**
     * 获取安装形式
     *
     * @return
     */
    @DS("zg")
    List<InstallFormDto> getInstallForm();

    /**
     * 根据电站编号获取电站租金信息
     *
     * @param psCodes
     * @return
     */
    @DS("xyg")
    List<PsStandardDto> getPsStandard(@Param("psCodes") Set<String> psCodes);

    /**
     * 查询代理商成本
     *
     * @param psCodes 电站编码
     * @return
     */
    @DS("xyg")
    List<TModelPsCostEntity> getAgentCost(@Param("psCodes") Set<String> psCodes);

    /**
     * 获取 roof信息
     *
     * @return
     */
    List<PsRoofResp> getPsRoofInfo(@Param("psCode") String psCode);

    List<SceneRoofDTO> listGroupByScenceCode();

    List<TheoryHoursDTO> getTheoryHours2(@Param("eleCountReq") EleCountReq eleCountReq);

    /**
     * 查询并网电站信息
     *
     * @param startTime 开始
     * @param endTime   结束
     * @return
     */
    @DS("xyg")
    List<ModelPsDetailDto> getPsGridInfo(@Param("startTime") DateTime startTime, @Param("endTime") DateTime endTime);

    /**
     * 获取账单信息
     *
     * @param psCodes
     * @return
     */
    @DS("xyg")
    List<PsBillFeeDto> getBillFee(@Param("psCodes") Set<String> psCodes);

    @DS("xyg")
    Long getSumQiTao();
}
