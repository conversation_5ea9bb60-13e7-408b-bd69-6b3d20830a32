package com.gcl.psmis.job.dto.cost;

import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;

/**
 * 电站组件
 */
@Builder
@AllArgsConstructor
@NoArgsConstructor
@Data
public class PsInstallDto {

    @ApiModelProperty("电站编号")
    private String psCode;

    @ApiModelProperty("设计方案字典值")
    private Integer dicdatavalue;

    @ApiModelProperty("组件块数")
    private BigDecimal assemblyNum;

    @ApiModelProperty("设计方案总容量")
    private BigDecimal designPower;

    @ApiModelProperty("组件功率")
    private BigDecimal assemblyPower;

    @ApiModelProperty("屋顶类型")
    private String roofName;

    @ApiModelProperty("屋顶编码")
    private String roofCode;

    @ApiModelProperty("倾角")
    private String angle;


}
