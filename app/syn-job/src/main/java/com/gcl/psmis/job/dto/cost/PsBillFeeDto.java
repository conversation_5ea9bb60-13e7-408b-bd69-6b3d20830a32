package com.gcl.psmis.job.dto.cost;

import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;

/**
 * 电站相关绩效账单
 */
@Builder
@AllArgsConstructor
@NoArgsConstructor
@Data
public class PsBillFeeDto {

    @ApiModelProperty("电站编号")
    private String psCode;

    @ApiModelProperty("账单编号")
    private String billNo;

    @ApiModelProperty("账单金额")
    private BigDecimal billFee;

    @ApiModelProperty("账单类型: 520-容量,530时效")
    private String billType;

}
