<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.gcl.psmis.job.dao.SynPsInfoDao">

    <select id="getPsDetails" resultType="com.gcl.psmis.job.dto.cost.ModelPsDetailDto">
        SELECT
        oo.STATIONNO psCode,
        oo.productname productName,
        oo.SETSTANDARDID standardId,
        bo.SHOWNAME propertyOwner,
        bref.node nodeCode,
        CASE

        WHEN bref.node >= 600 THEN
        '已并网'
        WHEN bref.node >= 500 THEN
        '已完工'
        WHEN bref.node >= 300 THEN
        '已完工'
        END AS psStatus,
        CASE
        WHEN pp.powergridtype = 1 THEN
        '个人备案'
        WHEN pp.powergridtype = 2 THEN
        '企业备案'
        END AS recordType,
        CASE
        WHEN oo.feecollect = 1 THEN
        '二类卡'
        WHEN oo.feecollect = 2 THEN
        '电e宝'
        WHEN oo.feecollect = 3 THEN
        '企业账户'
        END AS electricityBillMode,
        sp.id provinceId,
        sp.`name` provinceName,
        sc.id cityId,
        sc.`name` cityName,
        sa.id regionId,
        sa.`name` regionName,
        oo.TOTALTOTALPOWER capital,
        oo.FINANCETIME createdTime,
        oo.TOTALQUANTITY sumAssemblyNum,
        om.ENDCHECKTIME connectedTime,
        oo.QITAOTIME qiTaoTime,
        om.gridpowerfirstdate firstPowerTime
        FROM
        ord_order oo
        LEFT JOIN ord_mergegrid om ON oo.ORDERID = om.ORDERID
        LEFT JOIN prdt_product pp ON oo.productid = pp.productid
        LEFT JOIN ( SELECT FROMID orderId, MAX( NODEVALUE ) node FROM base_node WHERE nodestate = 1 GROUP BY FROMID )
        bref ON bref.orderId = oo.ORDERID
        LEFT JOIN sys_area sa ON oo.PROJECTAREAID = sa.id
        LEFT JOIN sys_city sc ON sa.city_id = sc.id
        LEFT JOIN sys_province sp ON sa.province_id = sp.id
        LEFT JOIN base_organizationinfo bo ON oo.PROPERTYORG = bo.id
        AND ORGTYPE = 16
        WHERE oo.productname not like '%简约%' and oo.STATIONNO = '10202503002989'
        <if test="startTime != null">
            and oo.QITAOTIME >= #{startTime}
        </if>
        <if test="endTime != null">
            AND oo.QITAOTIME &lt;= #{endTime}
        </if>
    </select>
    <select id="getAssembly" resultType="com.gcl.psmis.job.dto.cost.PsAssemblyDto">
        SELECT
        o.stationno AS `psCode`,
        count(*) as assemblyNum,
        c.power AS assemblyPower
        FROM
        ord_order o
        LEFT JOIN ord_equipmentrecord e ON e.ORDERID = o.ORDERID
        INNER JOIN wh_material c ON e.materialcode = c.materialcode
        AND c.MATERIALGROUP = 1
        <if test="psCodes != null and psCodes.size()>0">
            WHERE o.stationno IN
            <foreach collection="psCodes" item="psCode" index="index" open="(" close=")" separator=",">
                #{psCode}
            </foreach>
        </if>
        GROUP BY
        c.materialcode
    </select>
    <select id="getPsInstall" resultType="com.gcl.psmis.job.dto.cost.PsInstallDto">
        SELECT
        oo.STATIONNO psCode,
        od.dicdatavalue,
        od.assemblyAmount assemblyNum,
        od.designpower designPower,
        od.designpower/od.assemblyAmount assemblyPower,
        ld.roof_name roofName,
        ld.roof_code roofCode,
        ld.angle
        FROM
        ord_designscheme od
        INNER JOIN ord_order oo on oo.ORDERID = od.ordorderid
        left join t_lf_roof_design ld on od.dicdatavalue = ld.design_code
        <if test="psCodes != null and psCodes.size()>0">
            WHERE oo.stationno IN
            <foreach collection="psCodes" item="psCode" index="index" open="(" close=")" separator=",">
                #{psCode}
            </foreach>
        </if>
    </select>
    <select id="getInstallForm" resultType="com.gcl.psmis.job.dto.cost.InstallFormDto">
        SELECT idd.design_scheme_id   designSchemeId,
               idd.design_scheme_name designSchemeName,
               iif.install_form_name  installFormName
        FROM t_invest_design_disposition idd
                 LEFT JOIN t_invest_install_form iif ON iif.id = idd.install_form_id
        WHERE idd.install_form_id is not NULL
    </select>
    <select id="getPsStandard" resultType="com.gcl.psmis.job.dto.cost.PsStandardDto">
        SELECT
        psref.psCode,
        ss.STANDARD dard,
        ss.STARTDATE satrtDate,
        ss.INDATE inDate
        FROM
        sys_standard ss
        LEFT JOIN (
        SELECT
        oo.STATIONNO psCode,
        ref.rulelinkid
        FROM
        ord_order oo
        INNER JOIN ( SELECT ORDERID, rulelinkid rulelinkid FROM ord_contract WHERE contractstatus IN ( 10, 20 ) AND
        rulelinkid IS NOT NULL GROUP BY ORDERID ) ref ON ref.ORDERID = oo.ORDERID
        ) psref ON ss.LINKID = psref.rulelinkid
        <if test="psCodes != null and psCodes.size()>0">
            WHERE psref.psCode IN
            <foreach collection="psCodes" item="psCode" index="index" open="(" close=")" separator=",">
                #{psCode}
            </foreach>
        </if>
    </select>
    <select id="getAgentCost" resultType="com.gcl.psmis.framework.mbg.entity.TModelPsCostEntity">
        SELECT
        oo.STATIONNO psCode,
        oc.developfee theoryDevelopCost,
        oc.materialfee theoryDeviceCost,
        oc.installfee theoryInstallCost
        FROM
        ord_order oo
        INNER JOIN ord_completionfee oc ON oc.orderid = oo.ORDERID
        <if test="psCodes != null and psCodes.size()>0">
            WHERE oo.stationno IN
            <foreach collection="psCodes" item="psCode" index="index" open="(" close=")" separator=",">
                #{psCode}
            </foreach>
        </if>
    </select>
    <select id="listGroupByScenceCode" resultType="com.gcl.psmis.framework.common.dto.ele.SceneRoofDTO">
        SELECT scene_code,
               GROUP_CONCAT(roof_type ORDER BY roof_type SEPARATOR ",") AS roofTypeCodes
        FROM t_scene_roof
        WHERE del_flag = 0
          and roof_type is not null
        GROUP BY scene_code
    </select>
    <select id="getTheoryHours2" resultType="com.gcl.psmis.framework.common.dto.ele.TheoryHoursDTO">
        SELECT
        pid as `key`,
        sum( scene_ele ) AS theoryHours
        FROM
        t_scene_parameter
        where dev_type in (0,1)
        <if test="eleCountReq.startTime!= null ">
            AND scene_month &gt;=#{eleCountReq.startTime}
        </if>
        <if test="eleCountReq.endTime!= null">
            AND scene_month &lt;= #{eleCountReq.endTime}
        </if>
        GROUP BY
        pid
    </select>
    <select id="getPsRoofInfo" resultType="com.gcl.psmis.framework.common.resp.ele.PsRoofResp">
        SELECT pr.roof_type_capcity / 1000 as roofTypeCapcity,
               sr.scene_code
        FROM `t_ps_vs_roof` pr
                 LEFT JOIN t_scene_roof sr ON sr.roof_type = pr.roof_type_code
                 left join t_power_station ps ON ps.id = pr.ps_id
        WHERE ps.ps_code = #{psCode}
          AND sr.del_flag = 0
    </select>
    <select id="getPsGridInfo" resultType="com.gcl.psmis.job.dto.cost.ModelPsDetailDto">
        SELECT
        oo.STATIONNO psCode,
        om.ENDCHECKTIME connectedTime
        FROM
        ord_order oo
        LEFT JOIN ord_mergegrid om ON oo.ORDERID = om.ORDERID
        WHERE oo.productname not like '%简约%'
        <if test="startTime != null">
            and om.ENDCHECKTIME >= #{startTime}
        </if>
        <if test="endTime != null">
            AND om.ENDCHECKTIME &lt;= #{endTime}
        </if>
    </select>
    <select id="getBillFee" resultType="com.gcl.psmis.job.dto.cost.PsBillFeeDto">
        SELECT
        fb.billfee billFee,
        fb.billno billNo,
        fb.billtype billType,
        oo.STATIONNO psCode
        FROM
        fi_billfee fb
        LEFT JOIN ord_order oo on oo.ORDERID = fb.orderid
        WHERE
        billtype IN ( 520, 530 )
        <if test="psCodes != null and psCodes.size()>0">
            and oo.stationno IN
            <foreach collection="psCodes" item="psCode" index="index" open="(" close=")" separator=",">
                #{psCode}
            </foreach>
        </if>
    </select>
    <select id="getSumQiTao" resultType="java.lang.Long">
        SELECT sum(ord.TOTALTOTALPOWER)
        FROM ord_order ord
                 INNER JOIN sys_area c ON ord.projectareaid = c.id
            AND ord.intentionstate IN (6, 7)
            AND ord.ishistory = 0
            AND ord.IFQITAO = 1
            AND ord.powerstationtype IN (1, 2, 3)
                 LEFT JOIN base_organizationinfo info ON ord.DEVELOPERAGENTID = info.id
            AND info.orgstate = 1
                 LEFT JOIN base_organizationinfo parent ON parent.id = info.parentid
            AND info.orgstate = 1
                 LEFT JOIN base_orginfoextend t2 ON info.id = t2.orgid
                 LEFT JOIN base_orginfoextend t3 ON parent.id = t3.orgid
                 LEFT JOIN (SELECT a.* FROM base_organizationinfo a WHERE a.orgtype = 8) t6 ON t6.id = t2.ownoffice
                 LEFT JOIN (SELECT a.* FROM base_organizationinfo a WHERE a.orgtype = 8) t7 ON t7.id = t3.ownoffice
        WHERE (
                          t6.showname LIKE '%户用%'
                      OR t7.showname LIKE '%户用%')
    </select>
</mapper>