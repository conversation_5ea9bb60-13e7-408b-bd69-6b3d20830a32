package com.gcl.psmis.app.dynamic.mbg.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import java.io.Serializable;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.*;
    import lombok.experimental.Accessors;

/**
 * <p>
 * 斜拉数量
 * </p>
 *
 * <AUTHOR>
 * @since 2024-12-31
 */
@Builder
@AllArgsConstructor
@NoArgsConstructor
@Getter
@Setter
    @Accessors(chain = true)
@TableName("config_inclined_num")
@ApiModel(value = "ConfigInclinedNumEntity对象", description = "斜拉数量")
public class ConfigInclinedNumEntity extends Model<ConfigInclinedNumEntity> {

private static final long serialVersionUID=1L;

                @TableId(value = "id", type = IdType.AUTO)
                private Long id;

        @ApiModelProperty("层")
    @TableField("level")
        private Integer level;

        @ApiModelProperty("大于")
    @TableField("bigger")
        private Integer bigger;

        @ApiModelProperty("小于等于")
    @TableField("less")
        private Integer less;

        @ApiModelProperty("立柱位置（A,B,C,D。。。）")
    @TableField("local")
        private String local;

@Override
public Serializable pkVal(){
            return this.id;
        }
        }
