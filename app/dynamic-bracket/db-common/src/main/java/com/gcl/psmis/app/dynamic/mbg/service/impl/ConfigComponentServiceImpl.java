package com.gcl.psmis.app.dynamic.mbg.service.impl;

import com.gcl.psmis.app.dynamic.mbg.entity.ConfigComponentEntity;
import com.gcl.psmis.app.dynamic.mbg.mapper.ConfigComponentMapper;
import com.gcl.psmis.app.dynamic.mbg.service.ConfigComponentService;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springframework.stereotype.Service;

/**
 * <p>
 * 组件配置 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-12-31
 */
@Service
public class ConfigComponentServiceImpl extends ServiceImpl<ConfigComponentMapper, ConfigComponentEntity> implements ConfigComponentService {

}
