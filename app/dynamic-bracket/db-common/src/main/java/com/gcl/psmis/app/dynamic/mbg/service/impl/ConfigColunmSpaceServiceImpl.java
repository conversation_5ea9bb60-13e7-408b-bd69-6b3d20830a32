package com.gcl.psmis.app.dynamic.mbg.service.impl;

import com.gcl.psmis.app.dynamic.mbg.entity.ConfigColunmSpaceEntity;
import com.gcl.psmis.app.dynamic.mbg.mapper.ConfigColunmSpaceMapper;
import com.gcl.psmis.app.dynamic.mbg.service.ConfigColunmSpaceService;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springframework.stereotype.Service;

/**
 * <p>
 * 立柱间距 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-12-31
 */
@Service
public class ConfigColunmSpaceServiceImpl extends ServiceImpl<ConfigColunmSpaceMapper, ConfigColunmSpaceEntity> implements ConfigColunmSpaceService {

}
