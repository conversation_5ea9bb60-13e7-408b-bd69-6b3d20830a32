package com.gcl.psmis.app.dynamic.mbg.service.impl;

import com.gcl.psmis.app.dynamic.mbg.entity.ConfigTrussesEntity;
import com.gcl.psmis.app.dynamic.mbg.mapper.ConfigTrussesMapper;
import com.gcl.psmis.app.dynamic.mbg.service.ConfigTrussesService;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springframework.stereotype.Service;

/**
 * <p>
 * 榀间距配置 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-12-31
 */
@Service
public class ConfigTrussesServiceImpl extends ServiceImpl<ConfigTrussesMapper, ConfigTrussesEntity> implements ConfigTrussesService {

}
