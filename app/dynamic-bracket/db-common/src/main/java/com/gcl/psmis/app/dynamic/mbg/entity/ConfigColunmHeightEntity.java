package com.gcl.psmis.app.dynamic.mbg.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import java.io.Serializable;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.*;
    import lombok.experimental.Accessors;

/**
 * <p>
 * 立柱高度
 * </p>
 *
 * <AUTHOR>
 * @since 2024-12-31
 */
@Builder
@AllArgsConstructor
@NoArgsConstructor
@Getter
@Setter
    @Accessors(chain = true)
@TableName("config_colunm_height")
@ApiModel(value = "ConfigColunmHeightEntity对象", description = "立柱高度")
public class ConfigColunmHeightEntity extends Model<ConfigColunmHeightEntity> {

private static final long serialVersionUID=1L;

                @TableId(value = "id", type = IdType.AUTO)
                private Long id;

        @ApiModelProperty("排布方式编码")
    @TableField("layout_code")
        private String layoutCode;

        @ApiModelProperty("立柱位置（A,B,C,D。。。）")
    @TableField("local")
        private String local;

        @ApiModelProperty("立柱高度")
    @TableField("height")
        private Integer height;

@Override
public Serializable pkVal(){
            return this.id;
        }
        }
