package com.gcl.psmis.app.dynamic.mbg.service.impl;

import com.gcl.psmis.app.dynamic.mbg.entity.ConfigInclinedNumEntity;
import com.gcl.psmis.app.dynamic.mbg.mapper.ConfigInclinedNumMapper;
import com.gcl.psmis.app.dynamic.mbg.service.ConfigInclinedNumService;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springframework.stereotype.Service;

/**
 * <p>
 * 斜拉数量 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-12-31
 */
@Service
public class ConfigInclinedNumServiceImpl extends ServiceImpl<ConfigInclinedNumMapper, ConfigInclinedNumEntity> implements ConfigInclinedNumService {

}
