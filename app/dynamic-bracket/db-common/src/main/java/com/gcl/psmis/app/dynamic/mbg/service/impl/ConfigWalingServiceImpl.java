package com.gcl.psmis.app.dynamic.mbg.service.impl;

import com.gcl.psmis.app.dynamic.mbg.entity.ConfigWalingEntity;
import com.gcl.psmis.app.dynamic.mbg.mapper.ConfigWalingMapper;
import com.gcl.psmis.app.dynamic.mbg.service.ConfigWalingService;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springframework.stereotype.Service;

/**
 * <p>
 * 腰梁配置 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-12-31
 */
@Service
public class ConfigWalingServiceImpl extends ServiceImpl<ConfigWalingMapper, ConfigWalingEntity> implements ConfigWalingService {

}
