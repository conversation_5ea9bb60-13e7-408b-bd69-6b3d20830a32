package com.gcl.psmis.app.dynamic.mbg.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import java.io.Serializable;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.*;
    import lombok.experimental.Accessors;

/**
 * <p>
 * 腰梁条件表
 * </p>
 *
 * <AUTHOR>
 * @since 2025-02-07
 */
@Builder
@AllArgsConstructor
@NoArgsConstructor
@Getter
@Setter
    @Accessors(chain = true)
@TableName("config_waling")
@ApiModel(value = "ConfigWalingEntity对象", description = "腰梁条件表")
public class ConfigWalingEntity extends Model<ConfigWalingEntity> {

private static final long serialVersionUID=1L;

                @TableId(value = "id", type = IdType.AUTO)
                private Long id;

        @ApiModelProperty("安装方案编码")
    @TableField("install_scheme")
        private String installScheme;

        @ApiModelProperty("腰梁层数")
    @TableField("level")
        private Integer level;

        @ApiModelProperty("大于")
    @TableField("bigger")
        private Integer bigger;

        @ApiModelProperty("小于等于")
    @TableField("less")
        private Integer less;

        @ApiModelProperty("立柱位置（A,B,C,D。。。）")
    @TableField("local")
        private String local;

@Override
public Serializable pkVal(){
            return this.id;
        }
        }
