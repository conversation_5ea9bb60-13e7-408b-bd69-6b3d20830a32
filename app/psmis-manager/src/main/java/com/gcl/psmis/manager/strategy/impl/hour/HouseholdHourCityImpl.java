package com.gcl.psmis.manager.strategy.impl.hour;

import com.gcl.psmis.framework.common.constant.enums.et.EtReportTypeEnum;
import com.gcl.psmis.framework.common.dto.et.QueryReportDTO;
import com.gcl.psmis.framework.common.req.et.overall.OverallReportReq;
import com.gcl.psmis.framework.common.vo.et.operational.OperationalMetricsVO;
import com.gcl.psmis.framework.common.vo.et.psHour.ReportHouseholdPsHourPageVO;
import com.gcl.psmis.manager.strategy.SelectReportService;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;

import java.util.List;


/**
 * {@code @Author:} wq
 * {@code @Version:} 1.0
 */

@Service
@RequiredArgsConstructor
public class HouseholdHourCityImpl implements SelectReportService {

    private final HouseholdHourServiceImpl householdHourService;

    @Override
    public List<ReportHouseholdPsHourPageVO> findReportByTypeCode(OverallReportReq dto) {
        return householdHourService.returnPage(getStrategyKey(), dto);
    }

    @Override
    public void addReportByTypeCode(QueryReportDTO dto) {

    }

    @Override
    public String getStrategyKey() {
        return EtReportTypeEnum.OWNER_HOURS_CITY.getTypeCode();
    }
}
