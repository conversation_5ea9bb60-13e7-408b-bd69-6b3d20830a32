package com.gcl.psmis.manager.service;

import cn.hutool.core.bean.BeanUtil;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.gcl.psmis.framework.common.constant.enums.FrozenFlagEnum;
import com.gcl.psmis.framework.common.util.LocalDateUtil;
import com.gcl.psmis.framework.common.vo.account.SharingInfoVo;
import com.gcl.psmis.framework.common.vo.payorder.OrderCustomerVO;
import com.gcl.psmis.framework.common.vo.payorder.PowerPayOrderVO;
import com.gcl.psmis.framework.mbg.entity.TOrgRuleEntity;
import com.gcl.psmis.framework.mbg.entity.TPowerPayOrderEntity;
import com.gcl.psmis.framework.mbg.entity.TPowerStationEntity;
import com.gcl.psmis.framework.mbg.entity.TPowerUserBankInfoEntity;
import com.gcl.psmis.framework.mbg.service.TOrgRuleService;
import com.gcl.psmis.framework.mbg.service.TPowerPayOrderService;
import com.gcl.psmis.framework.mbg.service.TPowerStationService;
import com.gcl.psmis.framework.mbg.service.TPowerUserBankInfoService;
import com.gcl.psmis.manager.dao.rent.PowerPayOrdDao;
import com.xxl.job.core.context.XxlJobHelper;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.text.SimpleDateFormat;
import java.time.LocalDate;
import java.time.ZoneId;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.stream.Collectors;

/**
 * @Author: sunjiaxing
 * @CreateTime: 2024-07-18  16:55
 * @Description: 账单生成service
 */
@Service
@Slf4j
public class PayOrderGenerateService {

    @Autowired
    PowerPayOrdDao powerPayOrdDao;

    @Autowired
    TPowerUserBankInfoService tPowerUserBankInfoService;

    @Autowired
    TPowerPayOrderService tPowerPayOrderService;

    @Autowired
    TPowerStationService tPowerStationService;

    @Autowired
    TOrgRuleService tOrgRuleService;

    private static List<String> companyNames = Arrays.asList("苏州鑫固新能源有限公司", "涟水鑫固新能源有限公司", "宿迁宿城区协能新能源有限公司");

    private List<TPowerPayOrderEntity> generatePayOrder(List<String> stationNoList, String date) {
        LocalDate now = LocalDate.now();
        //今天
        if (date != null) {
            now = LocalDate.parse(date, DateTimeFormatter.ofPattern("yyyy-MM-dd"));
        }
        int yearNow = now.getYear();
        int monthValueNow = now.getMonthValue();

        List<PowerPayOrderVO> vos = powerPayOrdDao.getStationsByNos(stationNoList);

        List<TPowerUserBankInfoEntity> powerUserBankInfoEntities = tPowerUserBankInfoService.list();

        Map<String, List<TPowerUserBankInfoEntity>> bankInfoMap = powerUserBankInfoEntities.stream().collect(Collectors.groupingBy(TPowerUserBankInfoEntity::getPsCode));

        //现有账单
        List<TPowerPayOrderEntity> list = tPowerPayOrderService.list();
        log.info("现有账单：{}", list.size());
        Set<String> allCodeAndMonth = list.stream().map(t -> t.getPsCode() + "-" + t.getShareMonth()).collect(Collectors.toSet());


        List<TPowerPayOrderEntity> insertList = new ArrayList<>();

        if (CollectionUtils.isNotEmpty(vos)) {
            List<String> stationNos = vos.stream().map(PowerPayOrderVO::getPsCode).collect(Collectors.toList());

            //获取收益分成模式
            List<SharingInfoVo> sharingInfos = powerPayOrdDao.getSharingInfosByStationNos(stationNos);
            log.info("收益分成模式：{}", sharingInfos.size());
            Map<String, List<SharingInfoVo>> sharingInfoMap = sharingInfos.stream().collect(Collectors.groupingBy(SharingInfoVo::getStationno));


            //计算金额
            for (PowerPayOrderVO vo : vos) {
                //电站编号
                String stationNo = vo.getPsCode();
                //首次发电日期
                Date gridpowerfirstdate = vo.getGridpowerfirstdate();


                if (gridpowerfirstdate == null) {
                    //开发公司如果首日发电日期没有，取并网签署合同时间
                    if (vo.getDevType() == 1) {
                        gridpowerfirstdate = vo.getPacttime();
                        vo.setGridpowerfirstdate(gridpowerfirstdate);
                    }
                }
                LocalDate gridpowerfirstdateLocal = gridpowerfirstdate.toInstant().atZone(ZoneId.systemDefault()).toLocalDate();
                //首次发电年月日
                int year = gridpowerfirstdateLocal.getYear();
                int monthValue = gridpowerfirstdateLocal.getMonthValue();
                String month = LocalDateUtil.fillZero(monthValue);
                //首次发电年月
                String firstYearMonth = year + "-" + month;


                //生成账单
                TPowerUserBankInfoEntity tPowerUserBankInfoEntity = null;
                if (bankInfoMap.containsKey(stationNo)) {
                    tPowerUserBankInfoEntity = bankInfoMap.get(stationNo).stream().filter(t -> t.getXygId() != null && t.getXygId().toString().equals(vo.getOrdercustomerid())).findFirst().orElse(null);
                }

                if (tPowerUserBankInfoEntity == null) {
                    if (StringUtils.isBlank(vo.getOrdercustomerid())) {
                        XxlJobHelper.log("stationNo:" + stationNo);
                        XxlJobHelper.log("vo.getOrdercustomerid():" + vo.getOrdercustomerid());
                    }

                    tPowerUserBankInfoEntity = this.getFromXyg(vo.getOrdercustomerid());
                    if (tPowerUserBankInfoEntity == null) {
                        continue;
                    }
                    //塞回map
                    List<TPowerUserBankInfoEntity> bankInfos = bankInfoMap.get(stationNo);
                    if (bankInfos == null) {
                        bankInfos = new ArrayList<>();
                    }
                    bankInfos.add(tPowerUserBankInfoEntity);
                    bankInfoMap.put(stationNo, bankInfos);
                }

                Integer sharingRule = tPowerUserBankInfoEntity.getSharingRule();
                vo.setSharingRule(sharingRule);
                vo.setBankInfoId(tPowerUserBankInfoEntity.getId());


                if (sharingRule != null) {
                    //个人备案固定7
                    String powerGridMergeType = tPowerUserBankInfoEntity.getPowerGridMergeType();
                    if ("1".equals(powerGridMergeType)) {
                        vo.setSharingRule(7);
                        sharingRule = 7;

                        //铁发3家公司改前置季度
                        String companyName = vo.getCompanyName();
                        if (StringUtils.isNotBlank(companyName) && companyNames.contains(companyName)) {
                            vo.setSharingRule(1);
                            sharingRule = 1;
                        }

                    }
                    //铁发企业备案固定4
                    /*if ("2".equals(powerGridMergeType)) {
                        vo.setSharingRule(4);
                        sharingRule = 4;
                    }*/
                    if (sharingRule == 1) {
                        String thisSeasonLastMonth = "";

                        //前置生成季度账单
                        //生成从发电首日月到本季度最后一个月
                        if (monthValueNow == 1 || monthValueNow == 2 || monthValueNow == 3) {
                            thisSeasonLastMonth = yearNow + "-03";
                        } else if (monthValueNow == 4 || monthValueNow == 5 || monthValueNow == 6) {
                            thisSeasonLastMonth = yearNow + "-06";
                        } else if (monthValueNow == 7 || monthValueNow == 8 || monthValueNow == 9) {
                            thisSeasonLastMonth = yearNow + "-09";
                        } else if (monthValueNow == 10 || monthValueNow == 11 || monthValueNow == 12) {
                            thisSeasonLastMonth = yearNow + "-12";
                        }
                        List<TPowerPayOrderEntity> powerPayOrderEntities = this.preCalcAllMonth(allCodeAndMonth, firstYearMonth, thisSeasonLastMonth, sharingInfoMap, vo, tPowerUserBankInfoEntity);
                        insertList.addAll(powerPayOrderEntities);
                    } else if (sharingRule == 6) {
                        //前置生成年度账单
                        //从首次发电日期到12月
                        String december = yearNow + "-" + 12;
                        List<TPowerPayOrderEntity> powerPayOrderEntities = this.preCalcAllMonth(allCodeAndMonth, firstYearMonth, december, sharingInfoMap, vo, tPowerUserBankInfoEntity);
                        insertList.addAll(powerPayOrderEntities);
                    } else if (sharingRule == 7) {
                        //后置生成季度账单
                        String lastSeasonMonth = "";
                        if (monthValueNow == 1 || monthValueNow == 2 || monthValueNow == 3) {
                            lastSeasonMonth = yearNow - 1 + "-12";
                        } else if (monthValueNow == 4 || monthValueNow == 5 || monthValueNow == 6) {
                            lastSeasonMonth = yearNow + "-03";
                        } else if (monthValueNow == 7 || monthValueNow == 8 || monthValueNow == 9) {
                            lastSeasonMonth = yearNow + "-06";
                        } else if (monthValueNow == 10 || monthValueNow == 11 || monthValueNow == 12) {
                            lastSeasonMonth = yearNow + "-09";
                        }
                        List<TPowerPayOrderEntity> powerPayOrderEntities = this.preCalcAllMonth(allCodeAndMonth, firstYearMonth, lastSeasonMonth, sharingInfoMap, vo, tPowerUserBankInfoEntity);
                        insertList.addAll(powerPayOrderEntities);
                    } else if (sharingRule == 4) {
                        //前置生成连续年账单
                        //从首次发电日期下个月，连续生成1年
                        String startMonth = gridpowerfirstdateLocal.plusMonths(1).format(DateTimeFormatter.ofPattern("yyyy-MM"));
                        String endMonth = gridpowerfirstdateLocal.plusMonths(12).format(DateTimeFormatter.ofPattern("yyyy-MM"));
                        List<TPowerPayOrderEntity> powerPayOrderEntities = this.preCalcAllMonth(allCodeAndMonth, startMonth, endMonth, sharingInfoMap, vo, tPowerUserBankInfoEntity);
                        //insertList.addAll(powerPayOrderEntities);
                    }
                }
            }
        }
        return insertList;
    }

    private TPowerUserBankInfoEntity getFromXyg(String ordercustomerid) {
        OrderCustomerVO vo = powerPayOrdDao.getOrderCustomerById(ordercustomerid);
        if (vo == null) {
            XxlJobHelper.log("ordercustomerid:" + ordercustomerid);
            return null;
        }
        TPowerUserBankInfoEntity tPowerUserBankInfoEntity = new TPowerUserBankInfoEntity();
        tPowerUserBankInfoEntity.setAccountStatus(0);
        tPowerUserBankInfoEntity.setFrozenState(FrozenFlagEnum.NORMAL.type);
        tPowerUserBankInfoEntity.setDevType(vo.getDevType());
        tPowerUserBankInfoEntity.setPsType(1);
        tPowerUserBankInfoEntity.setTotalquantity(vo.getTotalquantity());
        tPowerUserBankInfoEntity.setCompanyCode(vo.getOrgcode());

        if (StringUtils.isNotBlank(vo.getStationno())) {
            TPowerStationEntity powerStation = tPowerStationService.getOne(Wrappers.<TPowerStationEntity>lambdaQuery().eq(TPowerStationEntity::getPsCode, vo.getStationno()), false);
            if (powerStation != null) {
                tPowerUserBankInfoEntity.setPsId(powerStation.getId());
            }

        }


        tPowerUserBankInfoEntity.setPsCode(vo.getStationno());
        tPowerUserBankInfoEntity.setAccountName(vo.getAccountname());
        tPowerUserBankInfoEntity.setBankNo(vo.getBankno());
        tPowerUserBankInfoEntity.setBankName(vo.getBankname());
        tPowerUserBankInfoEntity.setCustomerId(vo.getCusid());
        //银行卡照片
        String cardPic = powerPayOrdDao.getCardPicsByCusid(vo.getCusid());
        tPowerUserBankInfoEntity.setCardPic(cardPic);

        tPowerUserBankInfoEntity.setPhone(vo.getPhone());
        tPowerUserBankInfoEntity.setOwnerName(vo.getGuestname());
        tPowerUserBankInfoEntity.setPowerGridMergeType(vo.getPowergridmergetype());

        tPowerUserBankInfoEntity.setXygId(vo.getOrdercustomerid());
        tPowerUserBankInfoEntity.setIdNumber(vo.getCuscode());
        tPowerUserBankInfoEntity.setCustype(vo.getCustype());

        //项目公司规则
        List<TOrgRuleEntity> orgRuleEntityList = tOrgRuleService.list(Wrappers.<TOrgRuleEntity>lambdaQuery().eq(TOrgRuleEntity::getStatus, 1));
        Map<String, TOrgRuleEntity> orgRuleMap = orgRuleEntityList.stream().collect(
                Collectors.toMap(TOrgRuleEntity::getOrgCode, (p) -> p));

        if (tPowerUserBankInfoEntity.getSharingRule() == null) {
            TOrgRuleEntity tOrgRuleEntity = orgRuleMap.get(vo.getOrgcode());
            if (tOrgRuleEntity != null) {
                tPowerUserBankInfoEntity.setSharingRule(tOrgRuleEntity.getRule());
            }
        }
        tPowerUserBankInfoService.saveOrUpdate(tPowerUserBankInfoEntity);

        return tPowerUserBankInfoEntity;
    }


    public List<TPowerPayOrderEntity> generatePayOrderQyba(List<String> stationNoList, String date) {
        LocalDate now = LocalDate.now();
        //今天
        if (date != null) {
            now = LocalDate.parse(date, DateTimeFormatter.ofPattern("yyyy-MM-dd"));
        }
        int yearNow = now.getYear();
        int monthValueNow = now.getMonthValue();

        List<PowerPayOrderVO> vos = powerPayOrdDao.getStationsByNos(stationNoList);

        List<TPowerUserBankInfoEntity> powerUserBankInfoEntities = tPowerUserBankInfoService.list();

        Map<String, List<TPowerUserBankInfoEntity>> bankInfoMap = powerUserBankInfoEntities.stream().collect(Collectors.groupingBy(TPowerUserBankInfoEntity::getPsCode));

        //现有账单
        List<TPowerPayOrderEntity> list = tPowerPayOrderService.list();
        log.info("现有账单：{}", list.size());
        Set<String> allCodeAndMonth = list.stream().map(t -> t.getPsCode() + "-" + t.getShareMonth()).collect(Collectors.toSet());


        List<TPowerPayOrderEntity> insertList = new ArrayList<>();

        if (CollectionUtils.isNotEmpty(vos)) {
            List<String> stationNos = vos.stream().map(PowerPayOrderVO::getPsCode).collect(Collectors.toList());

            //获取收益分成模式
            List<SharingInfoVo> sharingInfos = powerPayOrdDao.getSharingInfosByStationNos(stationNos);
            log.info("收益分成模式：{}", sharingInfos.size());
            Map<String, List<SharingInfoVo>> sharingInfoMap = sharingInfos.stream().collect(Collectors.groupingBy(SharingInfoVo::getStationno));


            //计算金额
            for (PowerPayOrderVO vo : vos) {
                //电站编号
                String stationNo = vo.getPsCode();
                //首次发电日期
                Date gridpowerfirstdate = vo.getGridpowerfirstdate();


                if (gridpowerfirstdate == null) {
                    //开发公司如果首日发电日期没有，取并网签署合同时间
                    if (vo.getDevType() == 1) {
                        gridpowerfirstdate = vo.getPacttime();
                        vo.setGridpowerfirstdate(gridpowerfirstdate);
                    }
                }
                LocalDate gridpowerfirstdateLocal = gridpowerfirstdate.toInstant().atZone(ZoneId.systemDefault()).toLocalDate();
                //首次发电年月日
                int year = gridpowerfirstdateLocal.getYear();
                int monthValue = gridpowerfirstdateLocal.getMonthValue();
                String month = LocalDateUtil.fillZero(monthValue);
                //首次发电年月
                String firstYearMonth = year + "-" + month;


                //生成账单
                if (bankInfoMap.get(stationNo) == null) {
                    continue;
                }
                TPowerUserBankInfoEntity tPowerUserBankInfoEntity = bankInfoMap.get(stationNo).stream().filter(t -> t.getXygId() != null && t.getXygId().toString().equals(vo.getOrdercustomerid())).findFirst().orElse(null);
                if (tPowerUserBankInfoEntity == null) {
                    continue;
                }

                Integer sharingRule = tPowerUserBankInfoEntity.getSharingRule();
                vo.setSharingRule(sharingRule);
                vo.setBankInfoId(tPowerUserBankInfoEntity.getId());
                if (sharingRule != null) {
                    //个人备案固定7
                    String powerGridMergeType = tPowerUserBankInfoEntity.getPowerGridMergeType();
                    if ("1".equals(powerGridMergeType)) {
                        vo.setSharingRule(7);
                        sharingRule = 7;
                    }
                    //铁发企业备案固定4
                    /*if ("2".equals(powerGridMergeType)) {
                        vo.setSharingRule(4);
                        sharingRule = 4;
                    }*/
                    if (sharingRule == 1) {
                        String thisSeasonLastMonth = "";

                        //前置生成季度账单
                        //生成从发电首日月到本季度最后一个月
                        if (monthValueNow == 1 || monthValueNow == 2 || monthValueNow == 3) {
                            thisSeasonLastMonth = yearNow + "-03";
                        } else if (monthValueNow == 4 || monthValueNow == 5 || monthValueNow == 6) {
                            thisSeasonLastMonth = yearNow + "-06";
                        } else if (monthValueNow == 7 || monthValueNow == 8 || monthValueNow == 9) {
                            thisSeasonLastMonth = yearNow + "-09";
                        } else if (monthValueNow == 10 || monthValueNow == 11 || monthValueNow == 12) {
                            thisSeasonLastMonth = yearNow + "-12";
                        }
                        List<TPowerPayOrderEntity> powerPayOrderEntities = this.preCalcAllMonth(allCodeAndMonth, firstYearMonth, thisSeasonLastMonth, sharingInfoMap, vo, tPowerUserBankInfoEntity);
                        //insertList.addAll(powerPayOrderEntities);
                    } else if (sharingRule == 6) {
                        //前置生成年度账单
                        //从首次发电日期到12月
                        String december = yearNow + "-" + 12;
                        List<TPowerPayOrderEntity> powerPayOrderEntities = this.preCalcAllMonth(allCodeAndMonth, firstYearMonth, december, sharingInfoMap, vo, tPowerUserBankInfoEntity);
                        insertList.addAll(powerPayOrderEntities);
                    } else if (sharingRule == 7) {
                        //后置生成季度账单
                        String lastSeasonMonth = "";
                        if (monthValueNow == 1 || monthValueNow == 2 || monthValueNow == 3) {
                            lastSeasonMonth = yearNow - 1 + "-12";
                        } else if (monthValueNow == 4 || monthValueNow == 5 || monthValueNow == 6) {
                            lastSeasonMonth = yearNow + "-03";
                        } else if (monthValueNow == 7 || monthValueNow == 8 || monthValueNow == 9) {
                            lastSeasonMonth = yearNow + "-06";
                        } else if (monthValueNow == 10 || monthValueNow == 11 || monthValueNow == 12) {
                            lastSeasonMonth = yearNow + "-09";
                        }
                        List<TPowerPayOrderEntity> powerPayOrderEntities = this.preCalcAllMonth(allCodeAndMonth, firstYearMonth, lastSeasonMonth, sharingInfoMap, vo, tPowerUserBankInfoEntity);
                        //insertList.addAll(powerPayOrderEntities);
                    } else if (sharingRule == 4) {
                        //前置生成连续年账单
                        //从首次发电日期下个月，连续生成1年
                        String startMonth = gridpowerfirstdateLocal.plusMonths(1).format(DateTimeFormatter.ofPattern("yyyy-MM"));
                        String endMonth = gridpowerfirstdateLocal.plusMonths(12).format(DateTimeFormatter.ofPattern("yyyy-MM"));
                        List<TPowerPayOrderEntity> powerPayOrderEntities = this.preCalcAllMonth(allCodeAndMonth, startMonth, endMonth, sharingInfoMap, vo, tPowerUserBankInfoEntity);
                        //insertList.addAll(powerPayOrderEntities);
                    }
                }
            }
        }
        return insertList;
    }


    public List<TPowerPayOrderEntity> generatePayOrder(List<String> stationNoList) {
        return this.generatePayOrder(stationNoList, null);
    }

    public List<TPowerPayOrderEntity> generatePayOrderByDate(List<String> stationNoList, String date) {
        return this.generatePayOrder(stationNoList, date);
    }

    public List<TPowerPayOrderEntity> preCalcAllMonth(Set<String> allCodeAndMonth, String firstYearMonth, String lastMonth, Map<String, List<SharingInfoVo>> sharingInfoMap, PowerPayOrderVO vo, TPowerUserBankInfoEntity tPowerUserBankInfoEntity) {
        List<TPowerPayOrderEntity> insertList = new ArrayList<>();


        String stationNo = vo.getPsCode();
        //首次发电日期
        Date gridpowerfirstdate = vo.getGridpowerfirstdate();
        LocalDate gridpowerfirstdateLocal = gridpowerfirstdate.toInstant().atZone(ZoneId.systemDefault()).toLocalDate();
        //首次发电年月日
        int year = gridpowerfirstdateLocal.getYear();
        int monthValue = gridpowerfirstdateLocal.getMonthValue();
        String month = LocalDateUtil.fillZero(monthValue);
        int dayOfMonth = gridpowerfirstdateLocal.getDayOfMonth();

        //组件块数
        Integer totalQuantity = vo.getTotalQuantity();

        List<String> allMonth = LocalDateUtil.getAllMonth(firstYearMonth, lastMonth);
        if (CollectionUtils.isNotEmpty(allMonth)) {
            for (int i = 0; i < allMonth.size(); i++) {
                String shareMonth = allMonth.get(i);
                boolean contains = allCodeAndMonth.contains(stationNo + "-" + shareMonth);
                if (contains) {
                    //已经有了，不生成了
                    continue;
                }
                TPowerPayOrderEntity insertEntity = BeanUtil.copyProperties(vo, TPowerPayOrderEntity.class);

                String endDate = shareMonth + "-01";
                String startDate = firstYearMonth + "-01";
                //判断当前月份在第几期
                long phase = LocalDateUtil.getMonthDiff(startDate, endDate) + 1;
                List<SharingInfoVo> sharingInfoVos = sharingInfoMap.get(stationNo);
                if (CollectionUtils.isEmpty(sharingInfoVos)) {
                    log.error("没有收益分成方式:{}", stationNo);
                    break;
                }
                //获取单价
                BigDecimal unitPrice = sharingInfoVos.stream()
                        .filter(t -> (t.getStartMonth() <= phase && t.getIndate() >= phase))
                        .map(SharingInfoVo::getPrice).findFirst().orElse(BigDecimal.ZERO);
                BigDecimal money = BigDecimal.ZERO;

                //企业备案连续年的不用算当月的
                if (vo.getSharingRule() != null && vo.getSharingRule() == 4 && "2".equals(vo.getPowerGridMergeType())) {
                    money = new BigDecimal(totalQuantity).multiply(unitPrice).divide(new BigDecimal(12), 2, RoundingMode.HALF_UP);
                } else {
                    if (i == 0) {
                        //第一个月计算具体几天
                        int daysByMonth = LocalDateUtil.getDaysByMonth(shareMonth);
                        //计算电费的天数
                        int days = daysByMonth - dayOfMonth + 1;
                        //算钱 组件块数*单价*（天数/月份的天数）/12
                        money = new BigDecimal(totalQuantity)
                                .multiply(unitPrice)
                                .multiply(new BigDecimal(days).divide(new BigDecimal(daysByMonth), 4, RoundingMode.HALF_UP))
                                .divide(new BigDecimal(12), 2, RoundingMode.HALF_UP);
                        //铁发 算钱 组件块数*单价*天数/(月份的天数*12)
                        /*money = new BigDecimal(totalQuantity)
                                .multiply(unitPrice).multiply(new BigDecimal(days))
                                .divide(new BigDecimal(daysByMonth).multiply(new BigDecimal(12)), 2, BigDecimal.ROUND_HALF_UP);*/
                    } else {
                        //之后的月份算整月的钱
                        money = new BigDecimal(totalQuantity).multiply(unitPrice).divide(new BigDecimal(12), 2, RoundingMode.HALF_UP);
                    }
                }


                insertEntity.setCreateTime(new Date());
                insertEntity.setShareFee(money);
                insertEntity.setShareMonth(shareMonth);
                insertEntity.setAccountState(0);
                insertEntity.setShareStandard(unitPrice.toString());
                insertEntity.setPsType(1);
                insertEntity.setDevType(vo.getDevType());
                insertEntity.setAccountName(tPowerUserBankInfoEntity.getAccountName());
                insertEntity.setBankNo(tPowerUserBankInfoEntity.getBankNo());
                insertEntity.setBankName(tPowerUserBankInfoEntity.getBankName());
                insertList.add(insertEntity);
            }
        }
        return insertList;
    }

    public List<TPowerPayOrderEntity> generatePayOrderJiangXiPayOrderQYBA(List<String> stationNoList, String date) {
        //今天
        LocalDate now = LocalDate.now();
        //今天
        if (date != null) {
            now = LocalDate.parse(date, DateTimeFormatter.ofPattern("yyyy-MM-dd"));
        }
        int yearNow = now.getYear();
        int monthValueNow = now.getMonthValue();


        List<PowerPayOrderVO> vos = powerPayOrdDao.getStationsByNos(stationNoList);

        List<TPowerUserBankInfoEntity> powerUserBankInfoEntities = tPowerUserBankInfoService.list();

        Map<String, List<TPowerUserBankInfoEntity>> bankInfoMap = powerUserBankInfoEntities.stream().collect(Collectors.groupingBy(TPowerUserBankInfoEntity::getPsCode));

        //现有账单
        List<TPowerPayOrderEntity> list = tPowerPayOrderService.list();
        log.info("现有账单：{}", list.size());
        Set<String> allCodeAndMonth = list.stream().map(t -> t.getPsCode() + "-" + t.getShareMonth()).collect(Collectors.toSet());


        List<TPowerPayOrderEntity> insertList = new ArrayList<>();

        if (CollectionUtils.isNotEmpty(vos)) {
            List<String> stationNos = vos.stream().map(PowerPayOrderVO::getPsCode).collect(Collectors.toList());

            //获取收益分成模式
            List<SharingInfoVo> sharingInfos = powerPayOrdDao.getSharingInfosByStationNos(stationNos);
            log.info("收益分成模式：{}", sharingInfos.size());
            Map<String, List<SharingInfoVo>> sharingInfoMap = sharingInfos.stream().collect(Collectors.groupingBy(SharingInfoVo::getStationno));


            //计算金额
            for (PowerPayOrderVO vo : vos) {
                //电站编号
                String stationNo = vo.getPsCode();
                //首次发电日期
                Date gridpowerfirstdate = vo.getGridpowerfirstdate();


                if (gridpowerfirstdate == null) {
                    //开发公司如果首日发电日期没有，取并网签署合同时间
                    if (vo.getDevType() == 1) {
                        gridpowerfirstdate = vo.getPacttime();
                        vo.setGridpowerfirstdate(gridpowerfirstdate);
                    }
                }
                LocalDate gridpowerfirstdateLocal = gridpowerfirstdate.toInstant().atZone(ZoneId.systemDefault()).toLocalDate();
                //首次发电年月日
                int year = gridpowerfirstdateLocal.getYear();
                int monthValue = gridpowerfirstdateLocal.getMonthValue();
                String month = LocalDateUtil.fillZero(monthValue);
                //首次发电年月
                String firstYearMonth = year + "-" + month;


                //生成账单
                if (bankInfoMap.get(stationNo) == null) {
                    continue;
                }
                TPowerUserBankInfoEntity tPowerUserBankInfoEntity = bankInfoMap.get(stationNo).stream().filter(t -> t.getXygId() != null && t.getXygId().toString().equals(vo.getOrdercustomerid())).findFirst().orElse(null);
                if (tPowerUserBankInfoEntity == null) {
                    continue;
                }

                Integer sharingRule = tPowerUserBankInfoEntity.getSharingRule();
                vo.setSharingRule(sharingRule);
                vo.setBankInfoId(tPowerUserBankInfoEntity.getId());


                if (sharingRule != null) {
                    //个人备案固定7
                    String powerGridMergeType = tPowerUserBankInfoEntity.getPowerGridMergeType();
                    if ("1".equals(powerGridMergeType)) {
                        vo.setSharingRule(7);
                        sharingRule = 7;
                    }
                    //企业备案固定6
                    if ("2".equals(powerGridMergeType)) {
                        vo.setSharingRule(6);
                        sharingRule = 6;
                    }
                    if (sharingRule == 1) {
                        String thisSeasonLastMonth = "";

                        //前置生成季度账单
                        //生成从发电首日月到本季度最后一个月
                        if (monthValueNow == 1 || monthValueNow == 2 || monthValueNow == 3) {
                            thisSeasonLastMonth = yearNow + "-03";
                        } else if (monthValueNow == 4 || monthValueNow == 5 || monthValueNow == 6) {
                            thisSeasonLastMonth = yearNow + "-06";
                        } else if (monthValueNow == 7 || monthValueNow == 8 || monthValueNow == 9) {
                            thisSeasonLastMonth = yearNow + "-09";
                        } else if (monthValueNow == 10 || monthValueNow == 11 || monthValueNow == 12) {
                            thisSeasonLastMonth = yearNow + "-12";
                        }
                        List<TPowerPayOrderEntity> powerPayOrderEntities = this.preCalcAllMonth(allCodeAndMonth, firstYearMonth, thisSeasonLastMonth, sharingInfoMap, vo, tPowerUserBankInfoEntity);
                        insertList.addAll(powerPayOrderEntities);
                    } else if (sharingRule == 6) {
                        //前置生成年度账单
                        //从首次发电日期到12月
                        String december = yearNow + "-" + 12;
                        List<TPowerPayOrderEntity> powerPayOrderEntities = this.preCalcAllMonth(allCodeAndMonth, firstYearMonth, december, sharingInfoMap, vo, tPowerUserBankInfoEntity);
                        insertList.addAll(powerPayOrderEntities);
                    } else if (sharingRule == 7) {
                        //后置生成季度账单
                        String lastSeasonMonth = "";
                        if (monthValueNow == 1 || monthValueNow == 2 || monthValueNow == 3) {
                            lastSeasonMonth = yearNow - 1 + "-12";
                        } else if (monthValueNow == 4 || monthValueNow == 5 || monthValueNow == 6) {
                            lastSeasonMonth = yearNow + "-03";
                        } else if (monthValueNow == 7 || monthValueNow == 8 || monthValueNow == 9) {
                            lastSeasonMonth = yearNow + "-06";
                        } else if (monthValueNow == 10 || monthValueNow == 11 || monthValueNow == 12) {
                            lastSeasonMonth = yearNow + "-09";
                        }
                        List<TPowerPayOrderEntity> powerPayOrderEntities = this.preCalcAllMonth(allCodeAndMonth, firstYearMonth, lastSeasonMonth, sharingInfoMap, vo, tPowerUserBankInfoEntity);
                        insertList.addAll(powerPayOrderEntities);
                    } else if (sharingRule == 4) {
                        //前置生成连续年账单
                        //从首次发电日期下个月，连续生成1年
                        String startMonth = gridpowerfirstdateLocal.plusMonths(1).format(DateTimeFormatter.ofPattern("yyyy-MM"));
                        String endMonth = gridpowerfirstdateLocal.plusMonths(12).format(DateTimeFormatter.ofPattern("yyyy-MM"));
                        List<TPowerPayOrderEntity> powerPayOrderEntities = this.preCalcAllMonth(allCodeAndMonth, startMonth, endMonth, sharingInfoMap, vo, tPowerUserBankInfoEntity);
                        //insertList.addAll(powerPayOrderEntities);
                    }
                }
            }
        }
        return insertList;
    }

    public List<TPowerPayOrderEntity> generatePayOrderNoMerge(List<String> stationNoList, String date) {
        LocalDate now = LocalDate.now();
        //今天
        if (date != null) {
            now = LocalDate.parse(date, DateTimeFormatter.ofPattern("yyyy-MM-dd"));
        }
        int yearNow = now.getYear();
        int monthValueNow = now.getMonthValue();

        List<PowerPayOrderVO> vos = powerPayOrdDao.getStationsByNosAll(stationNoList);

        List<TPowerUserBankInfoEntity> powerUserBankInfoEntities = tPowerUserBankInfoService.list();

        Map<String, List<TPowerUserBankInfoEntity>> bankInfoMap = powerUserBankInfoEntities.stream().collect(Collectors.groupingBy(TPowerUserBankInfoEntity::getPsCode));

        //现有账单
        List<TPowerPayOrderEntity> list = tPowerPayOrderService.list();
        log.info("现有账单：{}", list.size());
        Set<String> allCodeAndMonth = list.stream().map(t -> t.getPsCode() + "-" + t.getShareMonth()).collect(Collectors.toSet());


        List<TPowerPayOrderEntity> insertList = new ArrayList<>();

        if (CollectionUtils.isNotEmpty(vos)) {
            List<String> stationNos = vos.stream().map(PowerPayOrderVO::getPsCode).collect(Collectors.toList());

            //获取收益分成模式
            List<SharingInfoVo> sharingInfos = powerPayOrdDao.getSharingInfosByStationNos(stationNos);
            log.info("收益分成模式：{}", sharingInfos.size());
            Map<String, List<SharingInfoVo>> sharingInfoMap = sharingInfos.stream().collect(Collectors.groupingBy(SharingInfoVo::getStationno));


            //计算金额
            for (PowerPayOrderVO vo : vos) {
                //电站编号
                String stationNo = vo.getPsCode();
                //首次发电日期
                Date gridpowerfirstdate = vo.getGridpowerfirstdate();

                if (gridpowerfirstdate == null) {
                    //开发公司如果首日发电日期没有，取并网签署合同时间
                    if (vo.getDevType() == 1) {
                        gridpowerfirstdate = vo.getPacttime();
                        vo.setGridpowerfirstdate(gridpowerfirstdate);
                    }
                }
                LocalDate gridpowerfirstdateLocal = gridpowerfirstdate.toInstant().atZone(ZoneId.systemDefault()).toLocalDate();
                //首次发电年月日
                int year = gridpowerfirstdateLocal.getYear();
                int monthValue = gridpowerfirstdateLocal.getMonthValue();
                String month = LocalDateUtil.fillZero(monthValue);
                //首次发电年月
                String firstYearMonth = year + "-" + month;


                //生成账单
                TPowerUserBankInfoEntity tPowerUserBankInfoEntity = null;
                if (bankInfoMap.containsKey(stationNo)) {
                    tPowerUserBankInfoEntity = bankInfoMap.get(stationNo).stream().filter(t -> t.getXygId() != null && t.getXygId().toString().equals(vo.getOrdercustomerid())).findFirst().orElse(null);
                }

                if (tPowerUserBankInfoEntity == null) {
                    if (StringUtils.isBlank(vo.getOrdercustomerid())) {
                        XxlJobHelper.log("stationNo:" + stationNo);
                        XxlJobHelper.log("vo.getOrdercustomerid():" + vo.getOrdercustomerid());
                    }

                    tPowerUserBankInfoEntity = this.getFromXyg(vo.getOrdercustomerid());
                    if (tPowerUserBankInfoEntity == null) {
                        continue;
                    }
                    //塞回map
                    List<TPowerUserBankInfoEntity> bankInfos = bankInfoMap.get(stationNo);
                    if (bankInfos == null) {
                        bankInfos = new ArrayList<>();
                    }
                    bankInfos.add(tPowerUserBankInfoEntity);
                    bankInfoMap.put(stationNo, bankInfos);
                }

                Integer sharingRule = tPowerUserBankInfoEntity.getSharingRule();
                vo.setSharingRule(sharingRule);
                vo.setBankInfoId(tPowerUserBankInfoEntity.getId());


                if (sharingRule != null) {
                    //个人备案固定7
                    String powerGridMergeType = tPowerUserBankInfoEntity.getPowerGridMergeType();
                    if ("1".equals(powerGridMergeType)) {
                        vo.setSharingRule(7);
                        sharingRule = 7;
                    }
                    //铁发企业备案固定4
                    /*if ("2".equals(powerGridMergeType)) {
                        vo.setSharingRule(4);
                        sharingRule = 4;
                    }*/
                    if (sharingRule == 1) {
                        String thisSeasonLastMonth = "";

                        //前置生成季度账单
                        //生成从发电首日月到本季度最后一个月
                        if (monthValueNow == 1 || monthValueNow == 2 || monthValueNow == 3) {
                            thisSeasonLastMonth = yearNow + "-03";
                        } else if (monthValueNow == 4 || monthValueNow == 5 || monthValueNow == 6) {
                            thisSeasonLastMonth = yearNow + "-06";
                        } else if (monthValueNow == 7 || monthValueNow == 8 || monthValueNow == 9) {
                            thisSeasonLastMonth = yearNow + "-09";
                        } else if (monthValueNow == 10 || monthValueNow == 11 || monthValueNow == 12) {
                            thisSeasonLastMonth = yearNow + "-12";
                        }
                        List<TPowerPayOrderEntity> powerPayOrderEntities = this.preCalcAllMonth(allCodeAndMonth, firstYearMonth, thisSeasonLastMonth, sharingInfoMap, vo, tPowerUserBankInfoEntity);
                        insertList.addAll(powerPayOrderEntities);
                    } else if (sharingRule == 6) {
                        //前置生成年度账单
                        //从首次发电日期到12月
                        String december = yearNow + "-" + 12;
                        List<TPowerPayOrderEntity> powerPayOrderEntities = this.preCalcAllMonth(allCodeAndMonth, firstYearMonth, december, sharingInfoMap, vo, tPowerUserBankInfoEntity);
                        insertList.addAll(powerPayOrderEntities);
                    } else if (sharingRule == 7) {
                        //后置生成季度账单
                        String lastSeasonMonth = "";
                        if (monthValueNow == 1 || monthValueNow == 2 || monthValueNow == 3) {
                            lastSeasonMonth = yearNow - 1 + "-12";
                        } else if (monthValueNow == 4 || monthValueNow == 5 || monthValueNow == 6) {
                            lastSeasonMonth = yearNow + "-03";
                        } else if (monthValueNow == 7 || monthValueNow == 8 || monthValueNow == 9) {
                            lastSeasonMonth = yearNow + "-06";
                        } else if (monthValueNow == 10 || monthValueNow == 11 || monthValueNow == 12) {
                            lastSeasonMonth = yearNow + "-09";
                        }
                        List<TPowerPayOrderEntity> powerPayOrderEntities = this.preCalcAllMonth(allCodeAndMonth, firstYearMonth, lastSeasonMonth, sharingInfoMap, vo, tPowerUserBankInfoEntity);
                        insertList.addAll(powerPayOrderEntities);
                    } else if (sharingRule == 4) {
                        //前置生成连续年账单
                        //从首次发电日期下个月，连续生成1年
                        String startMonth = gridpowerfirstdateLocal.plusMonths(1).format(DateTimeFormatter.ofPattern("yyyy-MM"));
                        String endMonth = gridpowerfirstdateLocal.plusMonths(12).format(DateTimeFormatter.ofPattern("yyyy-MM"));
                        List<TPowerPayOrderEntity> powerPayOrderEntities = this.preCalcAllMonth(allCodeAndMonth, startMonth, endMonth, sharingInfoMap, vo, tPowerUserBankInfoEntity);
                        //insertList.addAll(powerPayOrderEntities);
                    }
                }
            }
        }
        return insertList;
    }

    public List<TPowerPayOrderEntity> generatePayOrderNoMergeForDaYouCun(List<String> stationNoList, Integer sharingRule) throws Exception {
        LocalDate now = LocalDate.now();
        int yearNow = now.getYear();
        int monthValueNow = now.getMonthValue();

        List<PowerPayOrderVO> vos = powerPayOrdDao.getStationsByNosAll(stationNoList);

        List<TPowerUserBankInfoEntity> powerUserBankInfoEntities = tPowerUserBankInfoService.list();

        Map<String, List<TPowerUserBankInfoEntity>> bankInfoMap = powerUserBankInfoEntities.stream().collect(Collectors.groupingBy(TPowerUserBankInfoEntity::getPsCode));

        //现有账单
        List<TPowerPayOrderEntity> list = tPowerPayOrderService.list();
        log.info("现有账单：{}", list.size());
        Set<String> allCodeAndMonth = list.stream().map(t -> t.getPsCode() + "-" + t.getShareMonth()).collect(Collectors.toSet());


        List<TPowerPayOrderEntity> insertList = new ArrayList<>();

        if (CollectionUtils.isNotEmpty(vos)) {
            List<String> stationNos = vos.stream().map(PowerPayOrderVO::getPsCode).collect(Collectors.toList());

            //获取收益分成模式
            List<SharingInfoVo> sharingInfos = powerPayOrdDao.getSharingInfosByStationNos(stationNos);
            log.info("收益分成模式：{}", sharingInfos.size());
            Map<String, List<SharingInfoVo>> sharingInfoMap = sharingInfos.stream().collect(Collectors.groupingBy(SharingInfoVo::getStationno));


            //计算金额
            for (PowerPayOrderVO vo : vos) {
                //电站编号
                String stationNo = vo.getPsCode();
                //首次发电日期
                Date gridpowerfirstdate = vo.getGridpowerfirstdate();

                if (gridpowerfirstdate == null) {
                    //开发公司如果首日发电日期没有，取并网签署合同时间
                    if (vo.getDevType() == 1) {
                        gridpowerfirstdate = vo.getPacttime();
                        vo.setGridpowerfirstdate(gridpowerfirstdate);
                    }
                    SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd");
                    gridpowerfirstdate = sdf.parse("2025-04-01");
                    vo.setGridpowerfirstdate(gridpowerfirstdate);
                }


                LocalDate gridpowerfirstdateLocal = gridpowerfirstdate.toInstant().atZone(ZoneId.systemDefault()).toLocalDate();
                //首次发电年月日
                int year = gridpowerfirstdateLocal.getYear();
                int monthValue = gridpowerfirstdateLocal.getMonthValue();
                String month = LocalDateUtil.fillZero(monthValue);
                //首次发电年月
                String firstYearMonth = year + "-" + month;


                //生成账单
                TPowerUserBankInfoEntity tPowerUserBankInfoEntity = null;
                if (bankInfoMap.containsKey(stationNo)) {
                    tPowerUserBankInfoEntity = bankInfoMap.get(stationNo).stream().filter(t -> t.getXygId() != null && t.getXygId().toString().equals(vo.getOrdercustomerid())).findFirst().orElse(null);
                }

                if (tPowerUserBankInfoEntity == null) {
                    if (StringUtils.isBlank(vo.getOrdercustomerid())) {
                        XxlJobHelper.log("stationNo:" + stationNo);
                        XxlJobHelper.log("vo.getOrdercustomerid():" + vo.getOrdercustomerid());
                    }

                    tPowerUserBankInfoEntity = this.getFromXyg(vo.getOrdercustomerid());
                    if (tPowerUserBankInfoEntity == null) {
                        continue;
                    }
                    //塞回map
                    List<TPowerUserBankInfoEntity> bankInfos = bankInfoMap.get(stationNo);
                    if (bankInfos == null) {
                        bankInfos = new ArrayList<>();
                    }
                    bankInfos.add(tPowerUserBankInfoEntity);
                    bankInfoMap.put(stationNo, bankInfos);
                }

                vo.setSharingRule(sharingRule);
                vo.setBankInfoId(tPowerUserBankInfoEntity.getId());


                if (sharingRule != null) {
                    //个人备案固定7
                    String powerGridMergeType = tPowerUserBankInfoEntity.getPowerGridMergeType();
                    if ("1".equals(powerGridMergeType)) {
                        vo.setSharingRule(7);
                        sharingRule = 7;
                    }
                    //铁发企业备案固定4
                    /*if ("2".equals(powerGridMergeType)) {
                        vo.setSharingRule(4);
                        sharingRule = 4;
                    }*/
                    if (sharingRule == 1) {
                        String thisSeasonLastMonth = "";

                        //前置生成季度账单
                        //生成从发电首日月到本季度最后一个月
                        if (monthValueNow == 1 || monthValueNow == 2 || monthValueNow == 3) {
                            thisSeasonLastMonth = yearNow + "-03";
                        } else if (monthValueNow == 4 || monthValueNow == 5 || monthValueNow == 6) {
                            thisSeasonLastMonth = yearNow + "-06";
                        } else if (monthValueNow == 7 || monthValueNow == 8 || monthValueNow == 9) {
                            thisSeasonLastMonth = yearNow + "-09";
                        } else if (monthValueNow == 10 || monthValueNow == 11 || monthValueNow == 12) {
                            thisSeasonLastMonth = yearNow + "-12";
                        }
                        List<TPowerPayOrderEntity> powerPayOrderEntities = this.preCalcAllMonth(allCodeAndMonth, firstYearMonth, thisSeasonLastMonth, sharingInfoMap, vo, tPowerUserBankInfoEntity);
                        insertList.addAll(powerPayOrderEntities);
                    } else if (sharingRule == 6) {
                        //前置生成年度账单
                        //从首次发电日期到12月
                        String december = yearNow + "-" + 12;
                        List<TPowerPayOrderEntity> powerPayOrderEntities = this.preCalcAllMonth(allCodeAndMonth, firstYearMonth, december, sharingInfoMap, vo, tPowerUserBankInfoEntity);
                        insertList.addAll(powerPayOrderEntities);
                    }
                }
            }
        }
        return insertList;
    }
}
