package com.gcl.psmis.manager.dao.rent;

import com.baomidou.dynamic.datasource.annotation.DS;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.gcl.psmis.framework.common.req.payorder.PowerPayOrderReq;
import com.gcl.psmis.framework.common.vo.account.SharingInfoVo;
import com.gcl.psmis.framework.common.vo.payorder.OrderCustomerVO;
import com.gcl.psmis.framework.common.vo.payorder.PowerPayOrderVO;
import com.gcl.psmis.framework.mbg.entity.TCompanyProductRuleEntity;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;
import java.util.Map;

@Mapper
public interface PowerPayOrdDao {
    IPage<PowerPayOrderVO> getBillDetailsPage(@Param("req") PowerPayOrderReq req,
                                              @Param("page") Page<PowerPayOrderVO> page);

    @DS("xyg")
    List<PowerPayOrderVO> getGrbaInfoByStationNos(@Param("stationNos") List<String> stationNos);

    @DS("xyg")
    List<SharingInfoVo> getSharingInfosByStationNos(@Param("stationNos") List<String> grbaStationNos);

    @DS("xyg")
    List<PowerPayOrderVO> getStationsByNos(@Param("stationNos") List<String> stationNos);

    @DS("xyg")
    List<PowerPayOrderVO> getStationsByNosAll(@Param("stationNos") List<String> stationNos);

    @DS("xyg")
    List<PowerPayOrderVO> getStationsByOrgCode(@Param("orgCode") String orgCode);

    @DS("xyg")
    List<PowerPayOrderVO> getYesterdayMergeStations();

    @DS("xyg")
    List<PowerPayOrderVO> getAllMergeStations();

    @DS("xyg")
    List<PowerPayOrderVO> getTFStations();

    @DS("xyg")
    List<PowerPayOrderVO> getJiangxiStations();

    @DS("xyg")
    PowerPayOrderVO getJiangXiStationInfoByOrderId(@Param("orderId") Integer orderId);

    @DS("xyg")
    List<PowerPayOrderVO> getByAfterDate(@Param("date") String date);

    @DS("xyg")
    List<String> getTieFaCompanyCodes();

    @DS("xyg")
    List<String> getGRBAStationNos();

    @DS("xyg")
    List<String> getGRBAStationNosByTime(@Param("date") String format);

    @DS("w-xyg")
    List<TCompanyProductRuleEntity> getProductOrg();

    @DS("w-xyg")
    TCompanyProductRuleEntity getProductOrgById(@Param("id") Integer id);

    @DS("xyg")
    List<Map<String, String>> getHunan();

    @DS("xyg")
    List<PowerPayOrderVO> getJiangxiStationsQYBA();

    @DS("xyg")
    List<String> getTieFaCompanyNames();

    @DS("xyg")
    List<PowerPayOrderVO> getYesterdayMergeStationsKaiFa();

    @DS("xyg")
    List<PowerPayOrderVO> getHuNanStationsQYBA();

    void updatePsId();

    @DS("xyg")
    OrderCustomerVO getOrderCustomerById(@Param("ordercustomerid") String ordercustomerid);

    @DS("xyg")
    String getCardPicsByCusid(@Param("cusid") Long cusid);

    @DS("xyg")
    List<PowerPayOrderVO> getSerialYearCompany(@Param("companyCodes") List<String> companyCodes);

    @DS("xyg")
    List<PowerPayOrderVO> getHuBeiStations();

    @DS("xyg")
    List<PowerPayOrderVO> getThreeCompanies();
}
