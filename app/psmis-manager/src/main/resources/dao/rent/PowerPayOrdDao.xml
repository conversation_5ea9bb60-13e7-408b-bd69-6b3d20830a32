<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.gcl.psmis.manager.dao.rent.PowerPayOrdDao">

    <sql id="select">
        <where>
            <if test="req.powerGridMergeType != null and req.powerGridMergeType != ''">
                and p.power_grid_merge_type = #{req.powerGridMergeType}
            </if>
            <if test="req.psCode != null and req.psCode != ''">
                and p.ps_code = #{req.psCode}
            </if>
            <if test="req.dealerName != null and req.dealerName != ''">
                and p.service_org_name like concat(#{req.dealerName}, '%')
            </if>
            <if test="req.companyName != null and req.companyName != ''">
                and p.company_name like concat(#{req.companyName}, '%')
            </if>
            <if test="req.userName != null and req.userName != ''">
                and p.user_name like concat(#{req.userName}, '%')
            </if>
            <if test="req.settlementState != null">
                and p.account_state = #{req.settlementState}
            </if>
            <if test="req.accountStatus != null">
                and u.account_status = #{req.accountStatus}
            </if>
            <if test="req.shareMonth != null and req.shareMonth != ''">
                and p.share_month = #{req.shareMonth}
            </if>
            <if test="req.shareMonthEnd != null">
                and p.share_month &lt;= #{req.shareMonthEnd}
            </if>
            <if test="req.stationNos != null">
                and p.ps_code in
                <foreach collection="req.stationNos" item="stationNo" index="index" open="(" close=")" separator=",">
                    #{stationNo}
                </foreach>
            </if>

        </where>
    </sql>
    <update id="updatePsId">
        update t_power_user_bank_info i
            join t_power_station p
        on i.ps_code = p.ps_code
            set i.ps_id = p.id, i.ps_type=p.ps_type
        where i.ps_id is null;

        update t_power_pay_order i
            join t_power_station p
        on i.ps_code = p.ps_code
            set i.ps_id = p.id, i.ps_type=p.ps_type
        where i.ps_id is null;


        update t_power_user_bank_info i
            join t_power_station p
        on i.ps_code = p.ps_code
            set i.company_code = p.project_company_code
        where i.company_code is null;


        update t_power_pay_order p
            join t_power_station ps
        on p.ps_id = ps.id
            set p.province_company_code = ps.province_company_code, p.province_company = ps.province_company_name
        where p.ps_type = 1 and p.power_grid_merge_type = 2;

        update t_user_payment_log l
            join t_power_station p
        on l.ps_code = p.ps_code
            set l.ps_id = p.id
        where l.ps_id is null;
    </update>


    <select id="getBillDetailsPage" resultType="com.gcl.psmis.framework.common.vo.payorder.PowerPayOrderVO">
        SELECT
        p.*,
        p.service_org_name as dealerName,
        u.account_name as userName,
        u.bank_no bankNo,
        u.bank_name,
        u.account_status bankAccountState,
        u.customer_id
        FROM
        t_power_pay_order p
        LEFT JOIN `t_power_user_bank_info` u ON p.bank_info_id = u.id
        <include refid="select"></include>
        order by p.create_time desc, p.ps_code desc, p.share_month asc
    </select>


    <select id="getSharingInfosByStationNos"
            resultType="com.gcl.psmis.framework.common.vo.account.SharingInfoVo">
        SELECT r.startyear as startMonth,
        r.yearcount as indate,
        ROUND(r.yearcount / 12, 1) as years,
        round(r.sharemoney / 1000, 2) as price,
        o.stationno
        from ord_order o
        left join ord_ordersharerule r on r.orderid = o.ORDERID
        where r.startyear is not null
        and o.stationno in
        <foreach collection="stationNos" item="stationNo" index="index" open="(" close=")" separator=",">
            #{stationNo}
        </foreach>
        order by r.startyear asc
    </select>
    <select id="getGrbaInfoByStationNos"
            resultType="com.gcl.psmis.framework.common.vo.payorder.PowerPayOrderVO">
        <include refid="orderSql"></include>
        AND o.powergridmergetype = 1
        AND o.stationNo in
        <foreach collection="stationNos" item="stationNo" index="index" open="(" close=")" separator=",">
            #{stationNo}
        </foreach>
    </select>
    <select id="getStationsByNos" resultType="com.gcl.psmis.framework.common.vo.payorder.PowerPayOrderVO">
        SELECT o.ORDERID xyg_ps_id,
        oo.ordercustomerid,
        o.stationno ps_code,
        o.GUESTNAME user_name,
        sp.`name` province_name,
        sp.id province_id,
        sc.`name` city_name,
        sc.id city_id,
        sa.`name` region_name,
        sa.id region_id,
        st.id town_id,
        st.`name` town_name,
        oo.accountname,
        oo.bankno,
        oo.bankname,
        o.guestname owner,
        c.phone,
        c.cuscode,
        o.powergridmergetype,
        oo.ownertype,
        oo.cusid,
        oo.totalquantity,
        case
        when info.PARENTID = 0 then info.SHOWNAME
        when info.PARENTID > 0 then parent.SHOWNAME
        end as `dealerName`,
        case
        when info.PARENTID = 0 then info.SHOWNAME
        when info.PARENTID > 0 then parent.SHOWNAME
        end as `service_org_name`,
        case
        when info.PARENTID = 0 then info.ORGCODE
        when info.PARENTID > 0 then parent.ORGCODE
        end
        as `service_org_code`,
        org.SHOWNAME company_name,
        org.ORGCODE company_code,
        0 as dev_type,
        case
        when t6.SHOWNAME is null then t7.SHOWNAME
        else t6.SHOWNAME
        end
        as provinceCompany,
        case
        when t6.ORGCODE is null then t7.ORGCODE
        else t6.ORGCODE
        end
        as provinceCompanyCode,
        b.ENDCHECKTIME powerCheckTime,
        b.gridpowerfirstdate,
        b.pacttime,
        if((t6.showname LIKE '%户用%' OR t7.showname LIKE '%户用%'),0,1) devType
        FROM ord_ordercustomer oo
        LEFT JOIN ord_order o ON o.ORDERID = oo.orderid
        JOIN base_customer c ON c.cusid = oo.cusid
        LEFT JOIN sys_town st ON st.id = o.PROJECTTOWNID
        LEFT JOIN sys_area sa ON sa.id = o.PROJECTAREAID
        LEFT JOIN sys_city sc ON sc.id = sa.city_id
        LEFT JOIN sys_province sp ON sp.id = sa.province_id
        left join base_organizationinfo org on org.id = o.PROPERTYORG
        LEFT JOIN ord_mergegrid b ON o.orderid = b.orderid
        LEFT JOIN base_approvelink d ON d.areatype = 6
        AND d.linketype = 1
        AND d.fromid = b.id
        LEFT JOIN base_approvelink e ON e.areatype = 6
        AND e.linketype = 10
        AND e.fromid = b.id
        LEFT JOIN ord_check_status cs ON b.id = cs.orderid
        AND checktype = 3
        LEFT JOIN co_dispatchlinkstation cp ON o.orderid = cp.orderid
        LEFT JOIN base_organizationinfo info ON o.DEVELOPERAGENTID = info.id
        LEFT JOIN base_organizationinfo parent ON parent.id = info.parentid
        LEFT JOIN base_orginfoextend t2 ON info.id = t2.orgid
        LEFT JOIN base_orginfoextend t3 ON parent.id = t3.orgid
        LEFT JOIN (SELECT a.* FROM base_organizationinfo a WHERE a.orgtype = 8) t6 ON t6.id = t2.ownoffice
        LEFT JOIN (SELECT a.* FROM base_organizationinfo a WHERE a.orgtype = 8) t7 ON t7.id = t3.ownoffice
        WHERE
        /*(t6.showname LIKE '%户用%' OR t7.showname LIKE '%户用%') AND */
        EXISTS (SELECT 1 FROM co_dispatchlinkstation cd WHERE cd.ORDERID = o.orderid)
        AND b.mergegridstate = 4
        AND o.powerstationtype IN (1, 2, 3, 4)
        AND (
        o.intentionstate IN (6, 7))
        AND cp.overstate = 20
        AND oo.ownertype in (10, 20)
        /*AND o.PROPERTYORG NOT IN (SELECT a.orgid from base_orgsharerelation a where a.shareorgid = 13816)*/
        AND o.stationNo in
        <foreach collection="stationNos" item="stationNo" index="index" open="(" close=")" separator=",">
            #{stationNo}
        </foreach>
    </select>
    <select id="getStationsByOrgCode" resultType="com.gcl.psmis.framework.common.vo.payorder.PowerPayOrderVO">
        <include refid="orderSql"></include>
        and o.stationno is not null
        and org.ORGCODE = #{orgCode}
    </select>
    <select id="getYesterdayMergeStations"
            resultType="com.gcl.psmis.framework.common.vo.payorder.PowerPayOrderVO">
        <include refid="orderSql"></include>
        and DATE (b.ENDCHECKTIME) = DATE_SUB(CURDATE()
        , INTERVAL 1 DAY);
    </select>
    <select id="getAllMergeStations" resultType="com.gcl.psmis.framework.common.vo.payorder.PowerPayOrderVO">
        <include refid="orderSql"></include>
        and DATE(b.ENDCHECKTIME) &lt; now()
    </select>
    <select id="getTFStations" resultType="com.gcl.psmis.framework.common.vo.payorder.PowerPayOrderVO">
        SELECT o.ORDERID      xyg_ps_id,
               oo.ordercustomerid,
               o.stationno    ps_code,
               o.GUESTNAME    user_name,
               sp.`name`      province_name,
               sp.id          province_id,
               sc.`name`      city_name,
               sc.id          city_id,
               sa.`name`      region_name,
               sa.id          region_id,
               st.id          town_id,
               st.`name`      town_name,
               oo.accountname,
               oo.bankno,
               oo.bankname,
               o.guestname    owner,
               c.phone,
               c.cuscode,
               o.powergridmergetype,
               oo.ownertype,
               oo.cusid,
               oo.totalquantity,
               case
                   when info.PARENTID = 0 then info.SHOWNAME
                   when info.PARENTID > 0 then parent.SHOWNAME
                   end as     `dealerName`,
               case
                   when info.PARENTID = 0 then info.SHOWNAME
                   when info.PARENTID > 0 then parent.SHOWNAME
                   end as     `service_org_name`,
               case
                   when info.PARENTID = 0 then info.ORGCODE
                   when info.PARENTID > 0 then parent.ORGCODE
                   end
                       as     `service_org_code`,
               org.SHOWNAME   company_name,
               org.ORGCODE    company_code,
               0       as     dev_type,
               case
                   when t6.SHOWNAME is null then t7.SHOWNAME
                   else t6.SHOWNAME
                   end
                       as     provinceCompany,
               case
                   when t6.ORGCODE is null then t7.ORGCODE
                   else t6.ORGCODE
                   end
                       as     provinceCompanyCode,
               b.ENDCHECKTIME powerCheckTime,
               b.gridpowerfirstdate
        FROM ord_ordercustomer oo
                 LEFT JOIN ord_order o ON o.ORDERID = oo.orderid
                 JOIN base_customer c ON c.cusid = oo.cusid
                 LEFT JOIN sys_town st ON st.id = o.PROJECTTOWNID
                 LEFT JOIN sys_area sa ON sa.id = o.PROJECTAREAID
                 LEFT JOIN sys_city sc ON sc.id = sa.city_id
                 LEFT JOIN sys_province sp ON sp.id = sa.province_id
                 left join base_organizationinfo org on org.id = o.PROPERTYORG
                 LEFT JOIN ord_mergegrid b ON o.orderid = b.orderid
                 LEFT JOIN base_approvelink d ON d.areatype = 6
            AND d.linketype = 1
            AND d.fromid = b.id
                 LEFT JOIN base_approvelink e ON e.areatype = 6
            AND e.linketype = 10
            AND e.fromid = b.id
                 LEFT JOIN ord_check_status cs ON b.id = cs.orderid
            AND checktype = 3
                 LEFT JOIN co_dispatchlinkstation cp ON o.orderid = cp.orderid
                 LEFT JOIN base_organizationinfo info ON o.DEVELOPERAGENTID = info.id
                 LEFT JOIN base_organizationinfo parent ON parent.id = info.parentid
                 LEFT JOIN base_orginfoextend t2 ON info.id = t2.orgid
                 LEFT JOIN base_orginfoextend t3 ON parent.id = t3.orgid
                 LEFT JOIN (SELECT a.* FROM base_organizationinfo a WHERE a.orgtype = 8) t6 ON t6.id = t2.ownoffice
                 LEFT JOIN (SELECT a.* FROM base_organizationinfo a WHERE a.orgtype = 8) t7 ON t7.id = t3.ownoffice
        WHERE (t6.showname LIKE '%户用%' OR t7.showname LIKE '%户用%')
          AND EXISTS (SELECT 1 FROM co_dispatchlinkstation cd WHERE cd.ORDERID = o.orderid)
          AND b.mergegridstate = 4
          AND o.powerstationtype IN (1, 2, 3, 4)
          AND (
            o.intentionstate IN (6, 7))
          AND cp.overstate = 20
          AND oo.ownertype in (10, 20)
          AND o.PROPERTYORG IN (SELECT a.orgid from base_orgsharerelation a where a.shareorgid = 13816)
    </select>
    <select id="getJiangxiStations" resultType="com.gcl.psmis.framework.common.vo.payorder.PowerPayOrderVO">
        SELECT o.ORDERID      xyg_ps_id,
               oo.ordercustomerid,
               o.stationno    ps_code,
               o.GUESTNAME    user_name,
               sp.`name`      province_name,
               sp.id          province_id,
               sc.`name`      city_name,
               sc.id          city_id,
               sa.`name`      region_name,
               sa.id          region_id,
               st.id          town_id,
               st.`name`      town_name,
               oo.accountname,
               oo.bankno,
               oo.bankname,
               o.guestname    owner,
               c.phone,
               c.cuscode,
               o.powergridmergetype,
               oo.ownertype,
               oo.cusid,
               oo.totalquantity,
               case
                   when info.PARENTID = 0 then info.SHOWNAME
                   when info.PARENTID > 0 then parent.SHOWNAME
                   end as     `dealerName`,
               case
                   when info.PARENTID = 0 then info.SHOWNAME
                   when info.PARENTID > 0 then parent.SHOWNAME
                   end as     `service_org_name`,
               case
                   when info.PARENTID = 0 then info.ORGCODE
                   when info.PARENTID > 0 then parent.ORGCODE
                   end
                       as     `service_org_code`,
               org.SHOWNAME   company_name,
               org.ORGCODE    company_code,
               0       as     dev_type,
               case
                   when t6.SHOWNAME is null then t7.SHOWNAME
                   else t6.SHOWNAME
                   end
                       as     provinceCompany,
               case
                   when t6.ORGCODE is null then t7.ORGCODE
                   else t6.ORGCODE
                   end
                       as     provinceCompanyCode,
               b.ENDCHECKTIME powerCheckTime,
               b.gridpowerfirstdate
        FROM ord_ordercustomer oo
                 LEFT JOIN ord_order o ON o.ORDERID = oo.orderid
                 JOIN base_customer c ON c.cusid = oo.cusid
                 LEFT JOIN sys_town st ON st.id = o.PROJECTTOWNID
                 LEFT JOIN sys_area sa ON sa.id = o.PROJECTAREAID
                 LEFT JOIN sys_city sc ON sc.id = sa.city_id
                 LEFT JOIN sys_province sp ON sp.id = sa.province_id
                 left join base_organizationinfo org on org.id = o.PROPERTYORG
                 LEFT JOIN ord_mergegrid b ON o.orderid = b.orderid
                 LEFT JOIN base_approvelink d ON d.areatype = 6
            AND d.linketype = 1
            AND d.fromid = b.id
                 LEFT JOIN base_approvelink e ON e.areatype = 6
            AND e.linketype = 10
            AND e.fromid = b.id
                 LEFT JOIN ord_check_status cs ON b.id = cs.orderid
            AND checktype = 3
                 LEFT JOIN co_dispatchlinkstation cp ON o.orderid = cp.orderid
                 LEFT JOIN base_organizationinfo info ON o.DEVELOPERAGENTID = info.id
                 LEFT JOIN base_organizationinfo parent ON parent.id = info.parentid
                 LEFT JOIN base_orginfoextend t2 ON info.id = t2.orgid
                 LEFT JOIN base_orginfoextend t3 ON parent.id = t3.orgid
                 LEFT JOIN (SELECT a.* FROM base_organizationinfo a WHERE a.orgtype = 8) t6 ON t6.id = t2.ownoffice
                 LEFT JOIN (SELECT a.* FROM base_organizationinfo a WHERE a.orgtype = 8) t7 ON t7.id = t3.ownoffice
        WHERE org.SHOWNAME in ('赣州协能新能源有限公司', '遂川县协能能源有限公司', '武宁县协能能源有限公司',
                               '九江濂溪区协立能源有限公司')
          AND EXISTS (SELECT 1 FROM co_dispatchlinkstation cd WHERE cd.ORDERID = o.orderid)
          AND b.mergegridstate = 4
          AND o.powerstationtype IN (1, 2, 3, 4)
          AND (
            o.intentionstate IN (6, 7))
          AND cp.overstate = 20
          AND oo.ownertype in (10, 20)
    </select>
    <select id="getJiangXiStationInfoByOrderId"
            resultType="com.gcl.psmis.framework.common.vo.payorder.PowerPayOrderVO">
        SELECT o.ORDERID                                                          xyg_ps_id,
               oo.ordercustomerid,
               o.stationno                                                        ps_code,
               o.GUESTNAME                                                        user_name,
               sp.`name`                                                          province_name,
               sp.id                                                              province_id,
               sc.`name`                                                          city_name,
               sc.id                                                              city_id,
               sa.`name`                                                          region_name,
               sa.id                                                              region_id,
               st.id                                                              town_id,
               st.`name`                                                          town_name,
               oo.accountname,
               oo.bankno,
               oo.bankname,
               o.guestname                                                        owner,
               c.phone,
               c.cuscode,
               o.powergridmergetype,
               oo.ownertype,
               oo.cusid                                                           customerId,
               oo.totalquantity,
               case
                   when info.PARENTID = 0 then info.SHOWNAME
                   when info.PARENTID > 0 then parent.SHOWNAME
                   end as                                                         `dealerName`,
               case
                   when info.PARENTID = 0 then info.SHOWNAME
                   when info.PARENTID > 0 then parent.SHOWNAME
                   end as                                                         `service_org_name`,
               case
                   when info.PARENTID = 0 then info.ORGCODE
                   when info.PARENTID > 0 then parent.ORGCODE
                   end
                       as                                                         `service_org_code`,
               org.SHOWNAME                                                       company_name,
               org.ORGCODE                                                        company_code,
               if((t6.showname LIKE '%户用%' OR t7.showname LIKE '%户用%'), 0, 1) devType,
               case
                   when t6.SHOWNAME is null then t7.SHOWNAME
                   else t6.SHOWNAME
                   end
                       as                                                         provinceCompany,
               case
                   when t6.ORGCODE is null then t7.ORGCODE
                   else t6.ORGCODE
                   end
                       as                                                         provinceCompanyCode,
               b.ENDCHECKTIME                                                     powerCheckTime,
               b.gridpowerfirstdate
        FROM ord_ordercustomer oo
                 LEFT JOIN ord_order o ON o.ORDERID = oo.orderid
                 JOIN base_customer c ON c.cusid = oo.cusid
                 LEFT JOIN sys_town st ON st.id = o.PROJECTTOWNID
                 LEFT JOIN sys_area sa ON sa.id = o.PROJECTAREAID
                 LEFT JOIN sys_city sc ON sc.id = sa.city_id
                 LEFT JOIN sys_province sp ON sp.id = sa.province_id
                 left join base_organizationinfo org on org.id = o.PROPERTYORG
                 LEFT JOIN ord_mergegrid b ON o.orderid = b.orderid
                 LEFT JOIN base_approvelink d ON d.areatype = 6
            AND d.linketype = 1
            AND d.fromid = b.id
                 LEFT JOIN base_approvelink e ON e.areatype = 6
            AND e.linketype = 10
            AND e.fromid = b.id
                 LEFT JOIN ord_check_status cs ON b.id = cs.orderid
            AND checktype = 3
                 LEFT JOIN co_dispatchlinkstation cp ON o.orderid = cp.orderid
                 LEFT JOIN base_organizationinfo info ON o.DEVELOPERAGENTID = info.id
                 LEFT JOIN base_organizationinfo parent ON parent.id = info.parentid
                 LEFT JOIN base_orginfoextend t2 ON info.id = t2.orgid
                 LEFT JOIN base_orginfoextend t3 ON parent.id = t3.orgid
                 LEFT JOIN (SELECT a.* FROM base_organizationinfo a WHERE a.orgtype = 8) t6 ON t6.id = t2.ownoffice
                 LEFT JOIN (SELECT a.* FROM base_organizationinfo a WHERE a.orgtype = 8) t7 ON t7.id = t3.ownoffice
        WHERE org.SHOWNAME in ('赣州协能新能源有限公司', '遂川县协能能源有限公司', '武宁县协能能源有限公司',
                               '九江濂溪区协立能源有限公司')
          AND EXISTS (SELECT 1 FROM co_dispatchlinkstation cd WHERE cd.ORDERID = o.orderid)
          AND b.mergegridstate = 4
          AND o.powerstationtype IN (1, 2, 3, 4)
          AND (
            o.intentionstate IN (6, 7))
          AND cp.overstate = 20
          AND oo.ownertype in (10, 20)
          AND o.ORDERID = #{orderId} limit 1
    </select>
    <select id="getByAfterDate" resultType="com.gcl.psmis.framework.common.vo.payorder.PowerPayOrderVO">
        <include refid="orderSql"></include>
        and b.ENDCHECKTIME > #{date}
    </select>
    <select id="getTieFaCompanyCodes" resultType="java.lang.String">
        SELECT o.ORGCODE
        from base_orgsharerelation a
                 join base_organizationinfo o on o.id = a.orgid
        where a.shareorgid = 13816
    </select>
    <select id="getGRBAStationNos" resultType="java.lang.String">
        SELECT o.stationno ps_code

        FROM ord_ordercustomer oo
                 LEFT JOIN ord_order o ON o.ORDERID = oo.orderid
                 JOIN base_customer c ON c.cusid = oo.cusid
                 LEFT JOIN sys_town st ON st.id = o.PROJECTTOWNID
                 LEFT JOIN sys_area sa ON sa.id = o.PROJECTAREAID
                 LEFT JOIN sys_city sc ON sc.id = sa.city_id
                 LEFT JOIN sys_province sp ON sp.id = sa.province_id
                 left join base_organizationinfo org on org.id = o.PROPERTYORG
                 LEFT JOIN ord_mergegrid b ON o.orderid = b.orderid
                 LEFT JOIN base_approvelink d ON d.areatype = 6
            AND d.linketype = 1
            AND d.fromid = b.id
                 LEFT JOIN base_approvelink e ON e.areatype = 6
            AND e.linketype = 10
            AND e.fromid = b.id
                 LEFT JOIN ord_check_status cs ON b.id = cs.orderid
            AND checktype = 3
                 LEFT JOIN co_dispatchlinkstation cp ON o.orderid = cp.orderid
                 LEFT JOIN base_organizationinfo info ON o.DEVELOPERAGENTID = info.id
                 LEFT JOIN base_organizationinfo parent ON parent.id = info.parentid
                 LEFT JOIN base_orginfoextend t2 ON info.id = t2.orgid
                 LEFT JOIN base_orginfoextend t3 ON parent.id = t3.orgid
                 LEFT JOIN (SELECT a.* FROM base_organizationinfo a WHERE a.orgtype = 8) t6 ON t6.id = t2.ownoffice
                 LEFT JOIN (SELECT a.* FROM base_organizationinfo a WHERE a.orgtype = 8) t7 ON t7.id = t3.ownoffice
        WHERE (t6.showname LIKE '%户用%'
            OR t7.showname LIKE '%户用%')
          AND EXISTS (SELECT 1 FROM co_dispatchlinkstation cd WHERE cd.ORDERID = o.orderid)
          AND b.mergegridstate = 4
          AND o.powerstationtype IN (1, 2, 3, 4)
          AND (
            o.intentionstate IN (6, 7))
          AND cp.overstate = 20
          AND oo.ownertype in (10, 20)
          AND o.PROPERTYORG NOT IN (SELECT a.orgid from base_orgsharerelation a where a.shareorgid = 13816)
          AND o.productname not like '%简约版%'
          and o.powerstationtype = 1
          and b.gridpowerfirstdate >= '2024-07-01 00:00:00'
          and b.gridpowerfirstdate &lt;= '2024-10-01 00:00:00'
    </select>


    <select id="getGRBAStationNosByTime" resultType="java.lang.String">
        SELECT o.stationno ps_code

        FROM ord_ordercustomer oo
                 LEFT JOIN ord_order o ON o.ORDERID = oo.orderid
                 JOIN base_customer c ON c.cusid = oo.cusid
                 LEFT JOIN sys_town st ON st.id = o.PROJECTTOWNID
                 LEFT JOIN sys_area sa ON sa.id = o.PROJECTAREAID
                 LEFT JOIN sys_city sc ON sc.id = sa.city_id
                 LEFT JOIN sys_province sp ON sp.id = sa.province_id
                 left join base_organizationinfo org on org.id = o.PROPERTYORG
                 LEFT JOIN ord_mergegrid b ON o.orderid = b.orderid
                 LEFT JOIN base_approvelink d ON d.areatype = 6
            AND d.linketype = 1
            AND d.fromid = b.id
                 LEFT JOIN base_approvelink e ON e.areatype = 6
            AND e.linketype = 10
            AND e.fromid = b.id
                 LEFT JOIN ord_check_status cs ON b.id = cs.orderid
            AND checktype = 3
                 LEFT JOIN co_dispatchlinkstation cp ON o.orderid = cp.orderid
                 LEFT JOIN base_organizationinfo info ON o.DEVELOPERAGENTID = info.id
                 LEFT JOIN base_organizationinfo parent ON parent.id = info.parentid
                 LEFT JOIN base_orginfoextend t2 ON info.id = t2.orgid
                 LEFT JOIN base_orginfoextend t3 ON parent.id = t3.orgid
                 LEFT JOIN (SELECT a.* FROM base_organizationinfo a WHERE a.orgtype = 8) t6 ON t6.id = t2.ownoffice
                 LEFT JOIN (SELECT a.* FROM base_organizationinfo a WHERE a.orgtype = 8) t7 ON t7.id = t3.ownoffice
        WHERE (t6.showname LIKE '%户用%'
            OR t7.showname LIKE '%户用%')
          AND EXISTS (SELECT 1 FROM co_dispatchlinkstation cd WHERE cd.ORDERID = o.orderid)
          AND b.mergegridstate = 4
          AND o.powerstationtype IN (1, 2, 3, 4)
          AND (
            o.intentionstate IN (6, 7))
          AND cp.overstate = 20
          AND oo.ownertype in (10, 20)
          AND o.PROPERTYORG NOT IN (SELECT a.orgid from base_orgsharerelation a where a.shareorgid = 13816)
          AND o.productname not like '%简约版%'
          and o.powerstationtype = 1
          and b.gridpowerfirstdate >= #{date}
    </select>
    <select id="getProductOrg" resultType="com.gcl.psmis.framework.mbg.entity.TCompanyProductRuleEntity">
        SELECT pp.id         xyg_id,
               o.ORGCODE     companyCode,
               o.SHOWNAME    companyName,
               p.productname productName,
               p.productno   productCode
        FROM prdt_productlinkorg pp
                 LEFT JOIN base_organizationinfo o ON pp.orgid = o.ID
                 LEFT JOIN prdt_product p ON pp.productid = p.productid
    </select>
    <select id="getProductOrgById" resultType="com.gcl.psmis.framework.mbg.entity.TCompanyProductRuleEntity">
        SELECT pp.id         xyg_id,
               o.ORGCODE     companyCode,
               o.SHOWNAME    companyName,
               p.productname productName,
               p.productno   productCode
        FROM prdt_productlinkorg pp
                 LEFT JOIN base_organizationinfo o ON pp.orgid = o.ID
                 LEFT JOIN prdt_product p ON pp.productid = p.productid
        where pp.id = #{id}
    </select>
    <select id="getHunan" resultType="java.util.Map">
        SELECT ab.CONTRACTNO                                                              合同编号,
               o.stationno                                                                电站编号,
               org.SHOWNAME,
               o.guestname                                                                出租人,
               CONCAT(sp.`name`, sc.`name`, sa.`name`, st.`name`, o.PROJECTADDRESSDETAIL) 电站地址,
               ab.SIGNDATE                                                                合同签署日,
               oo.accountname                                                             账户名,
               oo.totalquantity                                                           组件块数,
               case o.rule
                   when 1 then '前置生成季度账单'
                   when 2 then '前置生成月度账单'
                   when 3 then '后置生成月度账单'
                   when 4 then '前置生成连续年账单'
                   when 5 then '后置生成连续年账单'
                   when 6 then '前置生成自然年账单' end                                   租金规则,
               qys.filepath
        FROM ord_ordercustomer oo
                 LEFT JOIN ord_order o ON o.ORDERID = oo.orderid
                 left join qys_contract qys on o.ORDERID = qys.orderid

                 JOIN base_customer c ON c.cusid = oo.cusid
                 LEFT JOIN sys_town st ON st.id = o.PROJECTTOWNID
                 LEFT JOIN sys_area sa ON sa.id = o.PROJECTAREAID
                 LEFT JOIN sys_city sc ON sc.id = sa.city_id
                 LEFT JOIN sys_province sp ON sp.id = sa.province_id
                 left join base_organizationinfo org on org.id = o.PROPERTYORG
                 LEFT JOIN ord_mergegrid b ON o.orderid = b.orderid
                 LEFT JOIN base_approvelink d ON d.areatype = 6
            AND d.linketype = 1
            AND d.fromid = b.id
                 LEFT JOIN base_approvelink e ON e.areatype = 6
            AND e.linketype = 10
            AND e.fromid = b.id
                 LEFT JOIN ord_check_status cs ON b.id = cs.orderid
            AND checktype = 3
                 LEFT JOIN co_dispatchlinkstation cp ON o.orderid = cp.orderid
                 LEFT JOIN base_organizationinfo info ON o.DEVELOPERAGENTID = info.id
                 LEFT JOIN base_organizationinfo parent ON parent.id = info.parentid
                 LEFT JOIN base_orginfoextend t2 ON info.id = t2.orgid
                 LEFT JOIN base_orginfoextend t3 ON parent.id = t3.orgid
                 LEFT JOIN (SELECT a.* FROM base_organizationinfo a WHERE a.orgtype = 8) t6 ON t6.id = t2.ownoffice
                 LEFT JOIN (SELECT a.* FROM base_organizationinfo a WHERE a.orgtype = 8) t7 ON t7.id = t3.ownoffice
                 LEFT JOIN ord_contractlinkordercustomer tz ON tz.ordercustomerid = oo.ordercustomerid
                 LEFT JOIN ord_contract ab ON ab.CONTRACTID = tz.contractid
        WHERE EXISTS (SELECT 1 FROM co_dispatchlinkstation cd WHERE cd.ORDERID = o.orderid)
          AND b.mergegridstate = 4
          AND o.powerstationtype IN (1, 2, 3, 4)
          AND (
            o.intentionstate IN (6, 7))
          AND cp.overstate = 20
          AND oo.ownertype in (10, 20)
          and (ab.contractstatus !=-20 or ab.contractstatus is null)
          and org.ORGCODE in ('2210', '2211', '2212', '2228', '650912', '2293')
        order by org.SHOWNAME
    </select>


    <sql id="orderSql">
        SELECT o.ORDERID                                                          xyg_ps_id,
               oo.ordercustomerid,
               o.stationno                                                        ps_code,
               o.GUESTNAME                                                        user_name,
               sp.`name`                                                          province_name,
               sp.id                                                              province_id,
               sc.`name`                                                          city_name,
               sc.id                                                              city_id,
               sa.`name`                                                          region_name,
               sa.id                                                              region_id,
               st.id                                                              town_id,
               st.`name`                                                          town_name,
               oo.accountname,
               oo.bankno,
               oo.bankname,
               o.guestname                                                        owner,
               c.phone,
               c.cuscode,
               o.powergridmergetype,
               oo.ownertype,
               oo.cusid,
               oo.totalquantity,
               case
                   when info.PARENTID = 0 then info.SHOWNAME
                   when info.PARENTID > 0 then parent.SHOWNAME
                   end as                                                         `dealerName`,
               case
                   when info.PARENTID = 0 then info.SHOWNAME
                   when info.PARENTID > 0 then parent.SHOWNAME
                   end as                                                         `service_org_name`,
               case
                   when info.PARENTID = 0 then info.ORGCODE
                   when info.PARENTID > 0 then parent.ORGCODE
                   end
                       as                                                         `service_org_code`,
               org.SHOWNAME                                                       company_name,
               org.ORGCODE                                                        company_code,
               if((t6.showname LIKE '%户用%' OR t7.showname LIKE '%户用%'), 0, 1) devType,
               case
                   when t6.SHOWNAME is null then t7.SHOWNAME
                   else t6.SHOWNAME
                   end
                       as                                                         provinceCompany,
               case
                   when t6.ORGCODE is null then t7.ORGCODE
                   else t6.ORGCODE
                   end
                       as                                                         provinceCompanyCode,
               b.ENDCHECKTIME                                                     powerCheckTime,
               b.gridpowerfirstdate
        FROM ord_ordercustomer oo
                 LEFT JOIN ord_order o ON o.ORDERID = oo.orderid
                 JOIN base_customer c ON c.cusid = oo.cusid
                 LEFT JOIN sys_town st ON st.id = o.PROJECTTOWNID
                 LEFT JOIN sys_area sa ON sa.id = o.PROJECTAREAID
                 LEFT JOIN sys_city sc ON sc.id = sa.city_id
                 LEFT JOIN sys_province sp ON sp.id = sa.province_id
                 left join base_organizationinfo org on org.id = o.PROPERTYORG
                 LEFT JOIN ord_mergegrid b ON o.orderid = b.orderid
                 LEFT JOIN base_approvelink d ON d.areatype = 6
            AND d.linketype = 1
            AND d.fromid = b.id
                 LEFT JOIN base_approvelink e ON e.areatype = 6
            AND e.linketype = 10
            AND e.fromid = b.id
                 LEFT JOIN ord_check_status cs ON b.id = cs.orderid
            AND checktype = 3
                 LEFT JOIN co_dispatchlinkstation cp ON o.orderid = cp.orderid
                 LEFT JOIN base_organizationinfo info ON o.DEVELOPERAGENTID = info.id
                 LEFT JOIN base_organizationinfo parent ON parent.id = info.parentid
                 LEFT JOIN base_orginfoextend t2 ON info.id = t2.orgid
                 LEFT JOIN base_orginfoextend t3 ON parent.id = t3.orgid
                 LEFT JOIN (SELECT a.* FROM base_organizationinfo a WHERE a.orgtype = 8) t6 ON t6.id = t2.ownoffice
                 LEFT JOIN (SELECT a.* FROM base_organizationinfo a WHERE a.orgtype = 8) t7 ON t7.id = t3.ownoffice
        WHERE (t6.showname LIKE '%户用%'
            OR t7.showname LIKE '%户用%')
          AND EXISTS (SELECT 1 FROM co_dispatchlinkstation cd WHERE cd.ORDERID = o.orderid)
          AND b.mergegridstate = 4
          AND o.powerstationtype IN (1, 2, 3, 4)
          AND (
            o.intentionstate IN (6, 7))
          AND cp.overstate = 20
          AND oo.ownertype in (10, 20)
          AND o.PROPERTYORG NOT IN (SELECT a.orgid from base_orgsharerelation a where a.shareorgid = 13816)
          AND (o.productname not like '%简约版%')
    </sql>
    <select id="getJiangxiStationsQYBA" resultType="com.gcl.psmis.framework.common.vo.payorder.PowerPayOrderVO">
        SELECT o.ORDERID      xyg_ps_id,
               oo.ordercustomerid,
               o.stationno    ps_code,
               o.GUESTNAME    user_name,
               sp.`name`      province_name,
               sp.id          province_id,
               sc.`name`      city_name,
               sc.id          city_id,
               sa.`name`      region_name,
               sa.id          region_id,
               st.id          town_id,
               st.`name`      town_name,
               oo.accountname,
               oo.bankno,
               oo.bankname,
               o.guestname    owner,
               c.phone,
               c.cuscode,
               o.powergridmergetype,
               oo.ownertype,
               oo.cusid,
               oo.totalquantity,
               case
                   when info.PARENTID = 0 then info.SHOWNAME
                   when info.PARENTID > 0 then parent.SHOWNAME
                   end as     `dealerName`,
               case
                   when info.PARENTID = 0 then info.SHOWNAME
                   when info.PARENTID > 0 then parent.SHOWNAME
                   end as     `service_org_name`,
               case
                   when info.PARENTID = 0 then info.ORGCODE
                   when info.PARENTID > 0 then parent.ORGCODE
                   end
                       as     `service_org_code`,
               org.SHOWNAME   company_name,
               org.ORGCODE    company_code,
               0       as     dev_type,
               case
                   when t6.SHOWNAME is null then t7.SHOWNAME
                   else t6.SHOWNAME
                   end
                       as     provinceCompany,
               case
                   when t6.ORGCODE is null then t7.ORGCODE
                   else t6.ORGCODE
                   end
                       as     provinceCompanyCode,
               b.ENDCHECKTIME powerCheckTime,
               b.gridpowerfirstdate
        FROM ord_ordercustomer oo
                 LEFT JOIN ord_order o ON o.ORDERID = oo.orderid
                 JOIN base_customer c ON c.cusid = oo.cusid
                 LEFT JOIN sys_town st ON st.id = o.PROJECTTOWNID
                 LEFT JOIN sys_area sa ON sa.id = o.PROJECTAREAID
                 LEFT JOIN sys_city sc ON sc.id = sa.city_id
                 LEFT JOIN sys_province sp ON sp.id = sa.province_id
                 left join base_organizationinfo org on org.id = o.PROPERTYORG
                 LEFT JOIN ord_mergegrid b ON o.orderid = b.orderid
                 LEFT JOIN base_approvelink d ON d.areatype = 6
            AND d.linketype = 1
            AND d.fromid = b.id
                 LEFT JOIN base_approvelink e ON e.areatype = 6
            AND e.linketype = 10
            AND e.fromid = b.id
                 LEFT JOIN ord_check_status cs ON b.id = cs.orderid
            AND checktype = 3
                 LEFT JOIN co_dispatchlinkstation cp ON o.orderid = cp.orderid
                 LEFT JOIN base_organizationinfo info ON o.DEVELOPERAGENTID = info.id
                 LEFT JOIN base_organizationinfo parent ON parent.id = info.parentid
                 LEFT JOIN base_orginfoextend t2 ON info.id = t2.orgid
                 LEFT JOIN base_orginfoextend t3 ON parent.id = t3.orgid
                 LEFT JOIN (SELECT a.* FROM base_organizationinfo a WHERE a.orgtype = 8) t6 ON t6.id = t2.ownoffice
                 LEFT JOIN (SELECT a.* FROM base_organizationinfo a WHERE a.orgtype = 8) t7 ON t7.id = t3.ownoffice
        WHERE org.SHOWNAME in ('汨罗协能新能源有限公司', '长沙县协科新能源有限公司', '龙门县协能能源有限公司',
                               '潮州协能能源科技有限公司', '益阳协能能源有限公司', '岳阳鑫垚协能能源有限公司',
                               '湘阴协科能源有限公司', '泉州协能能源有限公司', '浦城协鑫新能源有限公司',
                               '耒阳协立新能源有限公司', '汨罗协能新能源有限公司常德汉寿分公司',
                               '汨罗协能新能源有限公司岳阳分公司')
          AND EXISTS (SELECT 1 FROM co_dispatchlinkstation cd WHERE cd.ORDERID = o.orderid)
          AND b.mergegridstate = 4
          AND o.powerstationtype IN (1, 2, 3, 4)
          AND (
            o.intentionstate IN (6, 7)
                OR (o.intentionstate = 8 AND b.mergegridstate = 4))
          AND cp.overstate = 20
          AND oo.ownertype in (10, 20)
    </select>
    <select id="getStationsByNosAll" resultType="com.gcl.psmis.framework.common.vo.payorder.PowerPayOrderVO">
        SELECT o.ORDERID xyg_ps_id,
        oo.ordercustomerid,
        o.stationno ps_code,
        o.GUESTNAME user_name,
        sp.`name` province_name,
        sp.id province_id,
        sc.`name` city_name,
        sc.id city_id,
        sa.`name` region_name,
        sa.id region_id,
        st.id town_id,
        st.`name` town_name,
        oo.accountname,
        oo.bankno,
        oo.bankname,
        o.guestname owner,
        c.phone,
        c.cuscode,
        o.powergridmergetype,
        oo.ownertype,
        oo.cusid,
        oo.totalquantity,
        case
        when info.PARENTID = 0 then info.SHOWNAME
        when info.PARENTID > 0 then parent.SHOWNAME
        end as `dealerName`,
        case
        when info.PARENTID = 0 then info.SHOWNAME
        when info.PARENTID > 0 then parent.SHOWNAME
        end as `service_org_name`,
        case
        when info.PARENTID = 0 then info.ORGCODE
        when info.PARENTID > 0 then parent.ORGCODE
        end
        as `service_org_code`,
        org.SHOWNAME company_name,
        org.ORGCODE company_code,
        0 as dev_type,
        case
        when t6.SHOWNAME is null then t7.SHOWNAME
        else t6.SHOWNAME
        end
        as provinceCompany,
        case
        when t6.ORGCODE is null then t7.ORGCODE
        else t6.ORGCODE
        end
        as provinceCompanyCode,
        b.ENDCHECKTIME powerCheckTime,
        b.gridpowerfirstdate,
        b.pacttime,
        if((t6.showname LIKE '%户用%' OR t7.showname LIKE '%户用%'),0,1) devType
        FROM ord_ordercustomer oo
        LEFT JOIN ord_order o ON o.ORDERID = oo.orderid
        JOIN base_customer c ON c.cusid = oo.cusid
        LEFT JOIN sys_town st ON st.id = o.PROJECTTOWNID
        LEFT JOIN sys_area sa ON sa.id = o.PROJECTAREAID
        LEFT JOIN sys_city sc ON sc.id = sa.city_id
        LEFT JOIN sys_province sp ON sp.id = sa.province_id
        left join base_organizationinfo org on org.id = o.PROPERTYORG
        LEFT JOIN ord_mergegrid b ON o.orderid = b.orderid
        LEFT JOIN base_approvelink d ON d.areatype = 6
        AND d.linketype = 1
        AND d.fromid = b.id
        LEFT JOIN base_approvelink e ON e.areatype = 6
        AND e.linketype = 10
        AND e.fromid = b.id
        LEFT JOIN ord_check_status cs ON b.id = cs.orderid
        AND checktype = 3
        LEFT JOIN co_dispatchlinkstation cp ON o.orderid = cp.orderid
        LEFT JOIN base_organizationinfo info ON o.DEVELOPERAGENTID = info.id
        LEFT JOIN base_organizationinfo parent ON parent.id = info.parentid
        LEFT JOIN base_orginfoextend t2 ON info.id = t2.orgid
        LEFT JOIN base_orginfoextend t3 ON parent.id = t3.orgid
        LEFT JOIN (SELECT a.* FROM base_organizationinfo a WHERE a.orgtype = 8) t6 ON t6.id = t2.ownoffice
        LEFT JOIN (SELECT a.* FROM base_organizationinfo a WHERE a.orgtype = 8) t7 ON t7.id = t3.ownoffice
        WHERE
        o.stationNo in
        <foreach collection="stationNos" item="stationNo" index="index" open="(" close=")" separator=",">
            #{stationNo}
        </foreach>
        AND oo.ownertype in (10, 20)
    </select>
    <select id="getTieFaCompanyNames" resultType="java.lang.String">
        SELECT o.SHOWNAME
        from base_orgsharerelation a
                 join base_organizationinfo o on o.id = a.orgid
        where a.shareorgid = 13816
    </select>
    <select id="getYesterdayMergeStationsKaiFa"
            resultType="com.gcl.psmis.framework.common.vo.payorder.PowerPayOrderVO">
        SELECT o.ORDERID                                                          xyg_ps_id,
               oo.ordercustomerid,
               o.stationno                                                        ps_code,
               o.GUESTNAME                                                        user_name,
               sp.`name`                                                          province_name,
               sp.id                                                              province_id,
               sc.`name`                                                          city_name,
               sc.id                                                              city_id,
               sa.`name`                                                          region_name,
               sa.id                                                              region_id,
               st.id                                                              town_id,
               st.`name`                                                          town_name,
               oo.accountname,
               oo.bankno,
               oo.bankname,
               o.guestname                                                        owner,
               c.phone,
               c.cuscode,
               o.powergridmergetype,
               oo.ownertype,
               oo.cusid,
               oo.totalquantity,
               case
                   when info.PARENTID = 0 then info.SHOWNAME
                   when info.PARENTID > 0 then parent.SHOWNAME
                   end as                                                         `dealerName`,
               case
                   when info.PARENTID = 0 then info.SHOWNAME
                   when info.PARENTID > 0 then parent.SHOWNAME
                   end as                                                         `service_org_name`,
               case
                   when info.PARENTID = 0 then info.ORGCODE
                   when info.PARENTID > 0 then parent.ORGCODE
                   end
                       as                                                         `service_org_code`,
               org.SHOWNAME                                                       company_name,
               org.ORGCODE                                                        company_code,
               if((t6.showname LIKE '%户用%' OR t7.showname LIKE '%户用%'), 0, 1) devType,
               case
                   when t6.SHOWNAME is null then t7.SHOWNAME
                   else t6.SHOWNAME
                   end
                       as                                                         provinceCompany,
               case
                   when t6.ORGCODE is null then t7.ORGCODE
                   else t6.ORGCODE
                   end
                       as                                                         provinceCompanyCode,
               b.ENDCHECKTIME                                                     powerCheckTime,
               b.gridpowerfirstdate
        FROM ord_ordercustomer oo
                 LEFT JOIN ord_order o ON o.ORDERID = oo.orderid
                 JOIN base_customer c ON c.cusid = oo.cusid
                 LEFT JOIN sys_town st ON st.id = o.PROJECTTOWNID
                 LEFT JOIN sys_area sa ON sa.id = o.PROJECTAREAID
                 LEFT JOIN sys_city sc ON sc.id = sa.city_id
                 LEFT JOIN sys_province sp ON sp.id = sa.province_id
                 left join base_organizationinfo org on org.id = o.PROPERTYORG
                 LEFT JOIN ord_mergegrid b ON o.orderid = b.orderid
                 LEFT JOIN base_approvelink d ON d.areatype = 6
            AND d.linketype = 1
            AND d.fromid = b.id
                 LEFT JOIN base_approvelink e ON e.areatype = 6
            AND e.linketype = 10
            AND e.fromid = b.id
                 LEFT JOIN ord_check_status cs ON b.id = cs.orderid
            AND checktype = 3
                 LEFT JOIN co_dispatchlinkstation cp ON o.orderid = cp.orderid
                 LEFT JOIN base_organizationinfo info ON o.DEVELOPERAGENTID = info.id
                 LEFT JOIN base_organizationinfo parent ON parent.id = info.parentid
                 LEFT JOIN base_orginfoextend t2 ON info.id = t2.orgid
                 LEFT JOIN base_orginfoextend t3 ON parent.id = t3.orgid
                 LEFT JOIN (SELECT a.* FROM base_organizationinfo a WHERE a.orgtype = 8) t6 ON t6.id = t2.ownoffice
                 LEFT JOIN (SELECT a.* FROM base_organizationinfo a WHERE a.orgtype = 8) t7 ON t7.id = t3.ownoffice
        WHERE (t6.showname LIKE '%开发%' OR t7.showname LIKE '%开发%')
          AND EXISTS (SELECT 1 FROM co_dispatchlinkstation cd WHERE cd.ORDERID = o.orderid)
          AND b.mergegridstate = 4
          AND o.powerstationtype IN (1, 2, 3, 4)
          AND (
            o.intentionstate IN (6, 7))
          AND cp.overstate = 20
          AND oo.ownertype in (10, 20)
          AND o.PROPERTYORG NOT IN (SELECT a.orgid from base_orgsharerelation a where a.shareorgid = 13816)
          AND org.SHOWNAME in ('汨罗协能新能源有限公司', '长沙县协科新能源有限公司', '龙门县协能能源有限公司',
                               '潮州协能能源科技有限公司', '益阳协能能源有限公司', '岳阳鑫垚协能能源有限公司',
                               '湘阴协科能源有限公司', '泉州协能能源有限公司', '浦城协鑫新能源有限公司',
                               '耒阳协立新能源有限公司', '汨罗协能新能源有限公司常德汉寿分公司',
                               '汨罗协能新能源有限公司岳阳分公司', '长沙协鑫阳光能源有限公司',
                               '长沙协鑫阳光能源有限公司望城分公司', '长沙协鑫阳光能源有限公司麻阳分公司',
                               '长沙协鑫阳光能源有限公司中方分公司', '长沙协鑫阳光能源有限公司石门分公司',
                               '长沙协鑫阳光能源有限公司湘潭分公司', '汨罗协能新能源有限公司芷江分公司',
                               '汨罗协能新能源有限公司湘潭县分公司', '赣州协能新能源有限公司', '遂川县协能能源有限公司',
                               '武宁县协能能源有限公司',
                               '九江濂溪区协立能源有限公司', '云霄县协能新能源有限公司',
                               '岳阳鑫垚协能能源有限公司安化县分公司', '汨罗协能新能源有限公司临澧县分公司')
        --           and DATE (b.ENDCHECKTIME) = DATE_SUB(CURDATE()
--             , INTERVAL 1 DAY)
    </select>
    <select id="getHuNanStationsQYBA" resultType="com.gcl.psmis.framework.common.vo.payorder.PowerPayOrderVO">
        SELECT o.ORDERID                                                          xyg_ps_id,
               oo.ordercustomerid,
               o.stationno                                                        ps_code,
               o.GUESTNAME                                                        user_name,
               sp.`name`                                                          province_name,
               sp.id                                                              province_id,
               sc.`name`                                                          city_name,
               sc.id                                                              city_id,
               sa.`name`                                                          region_name,
               sa.id                                                              region_id,
               st.id                                                              town_id,
               st.`name`                                                          town_name,
               oo.accountname,
               oo.bankno,
               oo.bankname,
               o.guestname                                                        owner,
               c.phone,
               c.cuscode,
               o.powergridmergetype,
               oo.ownertype,
               oo.cusid,
               oo.totalquantity,
               case
                   when info.PARENTID = 0 then info.SHOWNAME
                   when info.PARENTID > 0 then parent.SHOWNAME
                   end as                                                         `dealerName`,
               case
                   when info.PARENTID = 0 then info.SHOWNAME
                   when info.PARENTID > 0 then parent.SHOWNAME
                   end as                                                         `service_org_name`,
               case
                   when info.PARENTID = 0 then info.ORGCODE
                   when info.PARENTID > 0 then parent.ORGCODE
                   end
                       as                                                         `service_org_code`,
               org.SHOWNAME                                                       company_name,
               org.ORGCODE                                                        company_code,
               if((t6.showname LIKE '%户用%' OR t7.showname LIKE '%户用%'), 0, 1) devType,
               case
                   when t6.SHOWNAME is null then t7.SHOWNAME
                   else t6.SHOWNAME
                   end
                       as                                                         provinceCompany,
               case
                   when t6.ORGCODE is null then t7.ORGCODE
                   else t6.ORGCODE
                   end
                       as                                                         provinceCompanyCode,
               b.ENDCHECKTIME                                                     powerCheckTime,
               b.gridpowerfirstdate
        FROM ord_ordercustomer oo
                 LEFT JOIN ord_order o ON o.ORDERID = oo.orderid
                 JOIN base_customer c ON c.cusid = oo.cusid
                 LEFT JOIN sys_town st ON st.id = o.PROJECTTOWNID
                 LEFT JOIN sys_area sa ON sa.id = o.PROJECTAREAID
                 LEFT JOIN sys_city sc ON sc.id = sa.city_id
                 LEFT JOIN sys_province sp ON sp.id = sa.province_id
                 left join base_organizationinfo org on org.id = o.PROPERTYORG
                 LEFT JOIN ord_mergegrid b ON o.orderid = b.orderid
                 LEFT JOIN base_approvelink d ON d.areatype = 6
            AND d.linketype = 1
            AND d.fromid = b.id
                 LEFT JOIN base_approvelink e ON e.areatype = 6
            AND e.linketype = 10
            AND e.fromid = b.id
                 LEFT JOIN ord_check_status cs ON b.id = cs.orderid
            AND checktype = 3
                 LEFT JOIN co_dispatchlinkstation cp ON o.orderid = cp.orderid
                 LEFT JOIN base_organizationinfo info ON o.DEVELOPERAGENTID = info.id
                 LEFT JOIN base_organizationinfo parent ON parent.id = info.parentid
                 LEFT JOIN base_orginfoextend t2 ON info.id = t2.orgid
                 LEFT JOIN base_orginfoextend t3 ON parent.id = t3.orgid
                 LEFT JOIN (SELECT a.* FROM base_organizationinfo a WHERE a.orgtype = 8) t6 ON t6.id = t2.ownoffice
                 LEFT JOIN (SELECT a.* FROM base_organizationinfo a WHERE a.orgtype = 8) t7 ON t7.id = t3.ownoffice
        WHERE (t6.showname LIKE '%开发%' OR t7.showname LIKE '%开发%')
          AND EXISTS (SELECT 1 FROM co_dispatchlinkstation cd WHERE cd.ORDERID = o.orderid)
          AND b.mergegridstate = 4
          AND o.powerstationtype IN (1, 2, 3, 4)
          AND (
            o.intentionstate IN (6, 7))
          AND cp.overstate = 20
          AND oo.ownertype in (10, 20)
          AND o.PROPERTYORG NOT IN (SELECT a.orgid from base_orgsharerelation a where a.shareorgid = 13816)
          AND org.SHOWNAME in ('长沙协鑫阳光能源有限公司',
                               '长沙协鑫阳光能源有限公司望城分公司', '长沙协鑫阳光能源有限公司麻阳分公司',
                               '长沙协鑫阳光能源有限公司中方分公司', '长沙协鑫阳光能源有限公司石门分公司',
                               '长沙协鑫阳光能源有限公司湘潭分公司', '汨罗协能新能源有限公司芷江分公司',
                               '汨罗协能新能源有限公司湘潭县分公司')
    </select>
    <select id="getOrderCustomerById" resultType="com.gcl.psmis.framework.common.vo.payorder.OrderCustomerVO">
        select oo.ordercustomerid,
               o.stationno,
               oo.accountname,
               oo.bankno,
               oo.bankname,
               o.guestname,
               c.phone,
               c.cuscode,
               o.powergridmergetype,
               oo.ownertype,
               oo.cusid,
               oo.totalquantity,
               org.orgcode,
               c.custype,
               if((t6.showname LIKE '%户用%' OR t7.showname LIKE '%户用%'), 0, 1) devType
        from ord_ordercustomer oo
                 left join ord_order o
                           on o.ORDERID = oo.orderid
                 join base_customer c on c.cusid = oo.cusid
                 LEFT JOIN base_organizationinfo org ON org.id = o.PROPERTYORG
                 LEFT JOIN base_organizationinfo info ON o.DEVELOPERAGENTID = info.id
                 LEFT JOIN base_organizationinfo parent ON parent.id = info.parentid
                 LEFT JOIN base_orginfoextend t2 ON info.id = t2.orgid
                 LEFT JOIN base_orginfoextend t3 ON parent.id = t3.orgid
                 LEFT JOIN (SELECT a.* FROM base_organizationinfo a WHERE a.orgtype = 8) t6 ON t6.id = t2.ownoffice
                 LEFT JOIN (SELECT a.* FROM base_organizationinfo a WHERE a.orgtype = 8) t7 ON t7.id = t3.ownoffice
        where oo.ownertype in (10, 20)
          and length(STATIONNO)
            > 0
          and length(accountname)
            > 0
          and length(oo.cusid) > 0
          and oo.ordercustomerid = #{ordercustomerid}
    </select>
    <select id="getCardPicsByCusid" resultType="java.lang.String">
        SELECT DOCUMENTPATH
        FROM base_documentinfo
                 JOIN base_documentlinkobj ON base_documentinfo.DOCUMENTID = base_documentlinkobj.DOCUMENTID
                 JOIN ord_contractlinkordercustomer
                      ON base_documentlinkobj.secondtargetid = ord_contractlinkordercustomer.contractlinkordercustomerid
        WHERE ord_contractlinkordercustomer.cusid = #{cusid}
          AND base_documentlinkobj.AREATYPE = 1
          AND LINKETYPE = 435 LIMIT 1
    </select>
    <select id="getSerialYearCompany" resultType="com.gcl.psmis.framework.common.vo.payorder.PowerPayOrderVO">
        SELECT o.ORDERID xyg_ps_id,
        oo.ordercustomerid,
        o.stationno ps_code,
        o.GUESTNAME user_name,
        sp.`name` province_name,
        sp.id province_id,
        sc.`name` city_name,
        sc.id city_id,
        sa.`name` region_name,
        sa.id region_id,
        st.id town_id,
        st.`name` town_name,
        oo.accountname,
        oo.bankno,
        oo.bankname,
        o.guestname owner,
        c.phone,
        c.cuscode,
        o.powergridmergetype,
        oo.ownertype,
        oo.cusid,
        oo.totalquantity,
        case
        when info.PARENTID = 0 then info.SHOWNAME
        when info.PARENTID > 0 then parent.SHOWNAME
        end as `dealerName`,
        case
        when info.PARENTID = 0 then info.SHOWNAME
        when info.PARENTID > 0 then parent.SHOWNAME
        end as `service_org_name`,
        case
        when info.PARENTID = 0 then info.ORGCODE
        when info.PARENTID > 0 then parent.ORGCODE
        end
        as `service_org_code`,
        org.SHOWNAME company_name,
        org.ORGCODE company_code,
        if((t6.showname LIKE '%户用%' OR t7.showname LIKE '%户用%'), 0, 1) devType,
        case
        when t6.SHOWNAME is null then t7.SHOWNAME
        else t6.SHOWNAME
        end
        as provinceCompany,
        case
        when t6.ORGCODE is null then t7.ORGCODE
        else t6.ORGCODE
        end
        as provinceCompanyCode,
        b.ENDCHECKTIME powerCheckTime,
        b.gridpowerfirstdate
        FROM ord_ordercustomer oo
        LEFT JOIN ord_order o ON o.ORDERID = oo.orderid
        JOIN base_customer c ON c.cusid = oo.cusid
        LEFT JOIN sys_town st ON st.id = o.PROJECTTOWNID
        LEFT JOIN sys_area sa ON sa.id = o.PROJECTAREAID
        LEFT JOIN sys_city sc ON sc.id = sa.city_id
        LEFT JOIN sys_province sp ON sp.id = sa.province_id
        left join base_organizationinfo org on org.id = o.PROPERTYORG
        LEFT JOIN ord_mergegrid b ON o.orderid = b.orderid
        LEFT JOIN base_approvelink d ON d.areatype = 6
        AND d.linketype = 1
        AND d.fromid = b.id
        LEFT JOIN base_approvelink e ON e.areatype = 6
        AND e.linketype = 10
        AND e.fromid = b.id
        LEFT JOIN ord_check_status cs ON b.id = cs.orderid
        AND checktype = 3
        LEFT JOIN co_dispatchlinkstation cp ON o.orderid = cp.orderid
        LEFT JOIN base_organizationinfo info ON o.DEVELOPERAGENTID = info.id
        LEFT JOIN base_organizationinfo parent ON parent.id = info.parentid
        LEFT JOIN base_orginfoextend t2 ON info.id = t2.orgid
        LEFT JOIN base_orginfoextend t3 ON parent.id = t3.orgid
        LEFT JOIN (SELECT a.* FROM base_organizationinfo a WHERE a.orgtype = 8) t6 ON t6.id = t2.ownoffice
        LEFT JOIN (SELECT a.* FROM base_organizationinfo a WHERE a.orgtype = 8) t7 ON t7.id = t3.ownoffice
        WHERE (t6.showname LIKE '%开发%' OR t7.showname LIKE '%开发%')
        AND EXISTS (SELECT 1 FROM co_dispatchlinkstation cd WHERE cd.ORDERID = o.orderid)
        AND b.mergegridstate = 4
        AND o.powerstationtype IN (1, 2, 3, 4)
        AND (
        o.intentionstate IN (6, 7))
        AND cp.overstate = 20
        AND oo.ownertype in (10, 20)
        AND o.PROPERTYORG NOT IN (SELECT a.orgid from base_orgsharerelation a where a.shareorgid = 13816)

        -- AND org.SHOWNAME in ('汨罗协能新能源有限公司', '长沙县协科新能源有限公司', '龙门县协能能源有限公司',
        -- '潮州协能能源科技有限公司', '益阳协能能源有限公司', '岳阳鑫垚协能能源有限公司',
        -- '湘阴协科能源有限公司', '泉州协能能源有限公司', '浦城协鑫新能源有限公司',
        -- '耒阳协立新能源有限公司', '汨罗协能新能源有限公司常德汉寿分公司',
        -- '汨罗协能新能源有限公司岳阳分公司', '长沙协鑫阳光能源有限公司',
        -- '长沙协鑫阳光能源有限公司望城分公司', '长沙协鑫阳光能源有限公司麻阳分公司',
        -- '长沙协鑫阳光能源有限公司中方分公司', '长沙协鑫阳光能源有限公司石门分公司',
        -- '长沙协鑫阳光能源有限公司湘潭分公司', '汨罗协能新能源有限公司芷江分公司',
        -- '汨罗协能新能源有限公司湘潭县分公司')
        and org.ORGCODE in
        <foreach collection="stationNos" item="stationNo" index="index" open="(" close=")" separator=",">
            #{stationNo}
        </foreach>


    </select>
    <select id="getHuBeiStations" resultType="com.gcl.psmis.framework.common.vo.payorder.PowerPayOrderVO">
        SELECT o.ORDERID      xyg_ps_id,
               oo.ordercustomerid,
               o.stationno    ps_code,
               o.GUESTNAME    user_name,
               sp.`name`      province_name,
               sp.id          province_id,
               sc.`name`      city_name,
               sc.id          city_id,
               sa.`name`      region_name,
               sa.id          region_id,
               st.id          town_id,
               st.`name`      town_name,
               oo.accountname,
               oo.bankno,
               oo.bankname,
               o.guestname    owner,
               c.phone,
               c.cuscode,
               o.powergridmergetype,
               oo.ownertype,
               oo.cusid,
               oo.totalquantity,
               case
                   when info.PARENTID = 0 then info.SHOWNAME
                   when info.PARENTID > 0 then parent.SHOWNAME
                   end as     `dealerName`,
               case
                   when info.PARENTID = 0 then info.SHOWNAME
                   when info.PARENTID > 0 then parent.SHOWNAME
                   end as     `service_org_name`,
               case
                   when info.PARENTID = 0 then info.ORGCODE
                   when info.PARENTID > 0 then parent.ORGCODE
                   end
                       as     `service_org_code`,
               org.SHOWNAME   company_name,
               org.ORGCODE    company_code,
               0       as     dev_type,
               case
                   when t6.SHOWNAME is null then t7.SHOWNAME
                   else t6.SHOWNAME
                   end
                       as     provinceCompany,
               case
                   when t6.ORGCODE is null then t7.ORGCODE
                   else t6.ORGCODE
                   end
                       as     provinceCompanyCode,
               b.ENDCHECKTIME powerCheckTime,
               b.gridpowerfirstdate
        FROM ord_ordercustomer oo
                 LEFT JOIN ord_order o ON o.ORDERID = oo.orderid
                 JOIN base_customer c ON c.cusid = oo.cusid
                 LEFT JOIN sys_town st ON st.id = o.PROJECTTOWNID
                 LEFT JOIN sys_area sa ON sa.id = o.PROJECTAREAID
                 LEFT JOIN sys_city sc ON sc.id = sa.city_id
                 LEFT JOIN sys_province sp ON sp.id = sa.province_id
                 left join base_organizationinfo org on org.id = o.PROPERTYORG
                 LEFT JOIN ord_mergegrid b ON o.orderid = b.orderid
                 LEFT JOIN base_approvelink d ON d.areatype = 6
            AND d.linketype = 1
            AND d.fromid = b.id
                 LEFT JOIN base_approvelink e ON e.areatype = 6
            AND e.linketype = 10
            AND e.fromid = b.id
                 LEFT JOIN ord_check_status cs ON b.id = cs.orderid
            AND checktype = 3
                 LEFT JOIN co_dispatchlinkstation cp ON o.orderid = cp.orderid
                 LEFT JOIN base_organizationinfo info ON o.DEVELOPERAGENTID = info.id
                 LEFT JOIN base_organizationinfo parent ON parent.id = info.parentid
                 LEFT JOIN base_orginfoextend t2 ON info.id = t2.orgid
                 LEFT JOIN base_orginfoextend t3 ON parent.id = t3.orgid
                 LEFT JOIN (SELECT a.* FROM base_organizationinfo a WHERE a.orgtype = 8) t6 ON t6.id = t2.ownoffice
                 LEFT JOIN (SELECT a.* FROM base_organizationinfo a WHERE a.orgtype = 8) t7 ON t7.id = t3.ownoffice
        WHERE org.SHOWNAME in ('襄阳协能新能源有限公司', '当阳协立新能源有限公司')
          AND EXISTS (SELECT 1 FROM co_dispatchlinkstation cd WHERE cd.ORDERID = o.orderid)
          AND b.mergegridstate = 4
          AND o.powerstationtype IN (1, 2, 3, 4)
          AND (
            o.intentionstate IN (6, 7))
          AND cp.overstate = 20
          AND oo.ownertype in (10, 20)
    </select>
    <select id="getThreeCompanies" resultType="com.gcl.psmis.framework.common.vo.payorder.PowerPayOrderVO">
        SELECT o.ORDERID                                                          xyg_ps_id,
               oo.ordercustomerid,
               o.stationno                                                        ps_code,
               o.GUESTNAME                                                        user_name,
               sp.`name`                                                          province_name,
               sp.id                                                              province_id,
               sc.`name`                                                          city_name,
               sc.id                                                              city_id,
               sa.`name`                                                          region_name,
               sa.id                                                              region_id,
               st.id                                                              town_id,
               st.`name`                                                          town_name,
               oo.accountname,
               oo.bankno,
               oo.bankname,
               o.guestname                                                        owner,
               c.phone,
               c.cuscode,
               o.powergridmergetype,
               oo.ownertype,
               oo.cusid,
               oo.totalquantity,
               case
                   when info.PARENTID = 0 then info.SHOWNAME
                   when info.PARENTID > 0 then parent.SHOWNAME
                   end as                                                         `dealerName`,
               case
                   when info.PARENTID = 0 then info.SHOWNAME
                   when info.PARENTID > 0 then parent.SHOWNAME
                   end as                                                         `service_org_name`,
               case
                   when info.PARENTID = 0 then info.ORGCODE
                   when info.PARENTID > 0 then parent.ORGCODE
                   end
                       as                                                         `service_org_code`,
               org.SHOWNAME                                                       company_name,
               org.ORGCODE                                                        company_code,
               0       as                                                         dev_type,
               case
                   when t6.SHOWNAME is null then t7.SHOWNAME
                   else t6.SHOWNAME
                   end
                       as                                                         provinceCompany,
               case
                   when t6.ORGCODE is null then t7.ORGCODE
                   else t6.ORGCODE
                   end
                       as                                                         provinceCompanyCode,
               b.ENDCHECKTIME                                                     powerCheckTime,
               b.gridpowerfirstdate,
               b.pacttime,
               if((t6.showname LIKE '%户用%' OR t7.showname LIKE '%户用%'), 0, 1) devType
        FROM ord_ordercustomer oo
                 LEFT JOIN ord_order o ON o.ORDERID = oo.orderid
                 JOIN base_customer c ON c.cusid = oo.cusid
                 LEFT JOIN sys_town st ON st.id = o.PROJECTTOWNID
                 LEFT JOIN sys_area sa ON sa.id = o.PROJECTAREAID
                 LEFT JOIN sys_city sc ON sc.id = sa.city_id
                 LEFT JOIN sys_province sp ON sp.id = sa.province_id
                 left join base_organizationinfo org on org.id = o.PROPERTYORG
                 LEFT JOIN ord_mergegrid b ON o.orderid = b.orderid
                 LEFT JOIN base_approvelink d ON d.areatype = 6
            AND d.linketype = 1
            AND d.fromid = b.id
                 LEFT JOIN base_approvelink e ON e.areatype = 6
            AND e.linketype = 10
            AND e.fromid = b.id
                 LEFT JOIN ord_check_status cs ON b.id = cs.orderid
            AND checktype = 3
                 LEFT JOIN co_dispatchlinkstation cp ON o.orderid = cp.orderid
                 LEFT JOIN base_organizationinfo info ON o.DEVELOPERAGENTID = info.id
                 LEFT JOIN base_organizationinfo parent ON parent.id = info.parentid
                 LEFT JOIN base_orginfoextend t2 ON info.id = t2.orgid
                 LEFT JOIN base_orginfoextend t3 ON parent.id = t3.orgid
                 LEFT JOIN (SELECT a.* FROM base_organizationinfo a WHERE a.orgtype = 8) t6 ON t6.id = t2.ownoffice
                 LEFT JOIN (SELECT a.* FROM base_organizationinfo a WHERE a.orgtype = 8) t7 ON t7.id = t3.ownoffice
        WHERE EXISTS (SELECT 1 FROM co_dispatchlinkstation cd WHERE cd.ORDERID = o.orderid)
          AND b.mergegridstate = 4
          AND o.powerstationtype IN (1, 2, 3, 4)
          AND (
            o.intentionstate IN (6, 7))
          AND cp.overstate = 20
          AND oo.ownertype in (10, 20)
          and org.SHOWNAME in ("苏州鑫固新能源有限公司", "涟水鑫固新能源有限公司", "宿迁宿城区协能新能源有限公司")
    </select>
</mapper>
