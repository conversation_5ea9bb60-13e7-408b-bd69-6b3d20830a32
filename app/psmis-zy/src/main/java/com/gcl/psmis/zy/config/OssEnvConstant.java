package com.gcl.psmis.zy.config;

import lombok.Data;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Configuration;

/**
 * @ClassName OssEnvConstant
 * @Description 默认配置常量类
 * <AUTHOR>
 * @Date 2023/8/3 18:10
 **/
@Data
@Configuration
public class OssEnvConstant {
    @Value("${oss-url.tianyi}")
    private String tianyi;

    @Value("${oss-url.bucket}")
    private String bucket;

}
