package com.gcl.psmis.zy.dao.powerstation;

import com.baomidou.dynamic.datasource.annotation.DS;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.gcl.psmis.framework.common.req.powerstation.complete.PowerStationCompleteReq;
import com.gcl.psmis.framework.common.resp.powerStation.check.AcceptResp;
import com.gcl.psmis.framework.common.resp.powerStation.check.CheckAcceptResp;
import com.gcl.psmis.framework.common.resp.powerStation.check.PowerStationCheckResp;
import com.gcl.psmis.framework.common.resp.powerStation.complete.CompleteNumResp;
import com.gcl.psmis.framework.common.resp.powerStation.complete.PowerStationCompleteResp;
import com.gcl.psmis.framework.common.resp.powerStation.complete.PowerStationDeviceResp;
import com.gcl.psmis.framework.common.vo.MergegrVO;
import com.gcl.psmis.framework.common.vo.PictureVO;
import com.gcl.psmis.framework.common.vo.ProductVO;
import com.gcl.psmis.framework.common.vo.zy.RectifyFormVo;
import com.gcl.psmis.framework.common.vo.zy.RectifyPowerImgVo;
import com.gcl.psmis.framework.common.vo.zy.RectifyPowerStationVo;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.Date;
import java.util.List;

/**
 * project: PowerStationCompleteDao
 * Powered 2023-10-19 10:22:22
 *
 * <AUTHOR>
 * @version 1.0
 * @since 1.8
 */

@Mapper
public interface PowerStationCompleteDao {

    @DS("xyg")
    IPage<PowerStationCompleteResp> getPsComplete( Page<PowerStationCompleteResp> page, @Param("params") PowerStationCompleteReq params);

    @DS("xyg")
    CompleteNumResp findDay(@Param("beginOfDay") Date beginOfDay, @Param("endOfDay") Date endOfDay, @Param("propertyOrgIdList") List<Long> propertyOrgIdList);

    @DS("xyg")
    CompleteNumResp findAll(@Param("propertyOrgIdList") List<Long> propertyOrgIdList);

    @DS("xyg")
    PowerStationCompleteResp completeDetail(@Param("intentno") String intentno);

    @DS("xyg")
    List<PowerStationDeviceResp> completeDetailDevice(@Param("intentno") String intentno);

    @DS("xyg")
    List<ProductVO> getProduct();

    @DS("xyg")
    MergegrVO getMergegr(@Param("intentno") String intentno);

    @DS("xyg")
    String findName(@Param("id") Long checkuserid);

    @DS("xyg")
    PowerStationCheckResp getPsCheck(@Param("intentno") String intentno);

    @DS("xyg")
    List<AcceptResp> getAccept(@Param("intentno") String intentno);

    @DS("xyg")
    List<CheckAcceptResp> getCheckAccept(@Param("intentno") String intentno);

    @DS("xyg")
    List<PictureVO> getProjectPicture(@Param("intentno") String intentno);

    @DS("xyg")
    List<PictureVO> getAttachment(@Param("intentno") String intentno);

    /**
     * 查询整改单主表
     * @param intentno
     * @return
     */
    @DS("xyg")
    RectifyFormVo getRectificeForm(@Param("intentno")  String intentno);

    /**
     * 查询整改电站详情
     * @param intentno
     * @return
     */
    @DS("xyg")
    List<RectifyPowerStationVo> getRectificePowerStation(@Param("intentno")  String intentno);

    @DS("xyg")
    RectifyPowerImgVo getPicture(@Param("stationNo") String stationNo);

    @DS("zy")
    Long findOrderId(@Param("psCode") String psCode);

    @DS("xyg")
    List<String> getOwnNames(@Param("propertyOrgIdList") List<Long> propertyOrgIdList);

    CompleteNumResp completeFindDay(@Param("beginOfDay") Date beginOfDay, @Param("endOfDay") Date endOfDay, @Param("ownNames") List<String> ownNames);

    CompleteNumResp completeFindAll( @Param("ownNames") List<String> ownNames);

}
