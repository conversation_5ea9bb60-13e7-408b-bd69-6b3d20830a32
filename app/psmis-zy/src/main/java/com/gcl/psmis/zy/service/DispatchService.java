package com.gcl.psmis.zy.service;

import cn.hutool.core.collection.CollUtil;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.gcl.psmis.framework.common.vo.zy.*;
import com.gcl.psmis.framework.mbg.entity.TDispatchEntity;
import com.gcl.psmis.framework.mbg.entity.TSendEntity;
import com.gcl.psmis.framework.mbg.service.TDispatchService;
import com.gcl.psmis.zy.dao.DispatchDao;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * <AUTHOR> @Description
 * @Date 2023/10/20
 * @Param
 **/
@Service
public class DispatchService {

    @Autowired
    private DispatchDao dispatchDao;
    @Autowired
    private TDispatchService dispatchService;


    public List<String> getDispatchList(String intentno) {

        List<DispatchDetailVo> resultList = dispatchDao.list(intentno);

        if(CollUtil.isNotEmpty(resultList)){
            List<String> collect = resultList.stream().map(item -> item.getDispatchNo()).collect(Collectors.toList());
            return collect;
        }

        return new ArrayList<String>();
    }

    public DispatchDetailVo getDispatchDetail(DispatchQueryVo queryVo) {
        DispatchDetailVo detailVo = dispatchDao.getDispatchDetail(queryVo.getDispatchNo());
        String intentno = null;
         if(StringUtils.isNotBlank(queryVo.getIntentno())){
             intentno = queryVo.getIntentno();
         }
        if(Objects.nonNull(detailVo)){
            List<DispatchDetailBomVo> childrenList = dispatchDao.getDispatchBomDetail(detailVo.getDisId(),intentno);
            detailVo.setChildrenList(childrenList);
        }
        return detailVo;
    }

    public List<DispatchUserVo> getDispatchUserList(String dispatchNo) {
        List<DispatchUserVo> resultList = dispatchDao.getDispatchUserList(dispatchNo);
        return resultList;
    }
}
