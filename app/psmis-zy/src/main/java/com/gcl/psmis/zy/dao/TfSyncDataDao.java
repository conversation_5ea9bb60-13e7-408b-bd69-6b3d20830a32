package com.gcl.psmis.zy.dao;

import com.baomidou.dynamic.datasource.annotation.DS;
import com.gcl.psmis.framework.common.vo.zy.SurveyImgDto;
import com.gcl.psmis.framework.common.vo.zy.TfSyncDataResultVo;
import org.apache.ibatis.annotations.Mapper;

import java.util.List;

/**
 * 铁发定时任务 同步房屋信息
 */
@Mapper
public interface TfSyncDataDao {
    @DS("zy")
    TfSyncDataResultVo getSurveyByOrdId(String ordId);

    @DS("xyg")
    List<SurveyImgDto> getSurveyImgbyOrdId(String ordId);

}
