package com.gcl.psmis.zy.dao.powerstation;

import com.baomidou.dynamic.datasource.annotation.DS;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.gcl.psmis.framework.common.req.zy.RecordedPowerStationReq;
import com.gcl.psmis.framework.common.vo.zy.*;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

@Mapper
public interface ZyPowerStationMngDao {

    @DS("xyg")
    String getAdCode(@Param("orderId") Long orderId);

    @DS("zy")
    IPage<RecordedPowerStationVO> getRecordedPowerStationList(Page<RecordedPowerStationVO> page, @Param("params") RecordedPowerStationReq params);

    @DS("zy")
    RecordedPowerStationCountVO getRecordedPowerStationCount(@Param("day") String day,@Param("month") String month,@Param("year") String year,@Param("params") RecordedPowerStationReq params);

    /**
     * @desc 建档列表统计
     * @date 2023/11/7 13:47
     * @param day
     * @param month
     * @param year
     * @param params
     * @return {@link RecordedPowerStationCountVO}
     */
    @DS("zy")
    RecordedPowerStationCountVO getCreatedPowerStationCount(@Param("day") String day,@Param("month") String month,@Param("year") String year,@Param("params") RecordedPowerStationReq params);

    @DS("zy")
    StationBasicInfoVO getBasicInfo(@Param("intentno") String intentno,@Param("stationNo") String stationNo);

    @DS("xyg")
    Integer getNodeState(@Param("orderId") Long orderId,@Param("nodeName") String nodeName);

    @DS("zy")
    MergeInfoVO getMergeInfo(@Param("orderId") Long orderId);

    List<InvertVO> getInvertInfo(@Param("psId") Long psId);

    @DS("xyg")
    OrgInfoVO getOrgInfo(@Param("orgId") Long orgId);

    @DS("xyg")
    List<AssemblyVO> getAssembly(@Param("orderId") Long orderId);

    @DS("xyg")
    AssemblyVO getGridTieInverter(@Param("orderId") Long orderId);

    @DS("xyg")
    List<String> getSerialCodeList(@Param("orderId") Long orderId);

    @DS("xyg")
    PsDesignInfoVO getPsDesignInfo(@Param("orderId") Long orderId);

    @DS("xyg")
    List<PsDesignInfoSupportVO> listPsDesignSupportInfo(@Param("orderId") Long orderId);

    @DS("xyg")
    List<DesignBomVO> getDesignBomList(@Param("orderId") Long orderId);

    @DS("xyg")
    List<String> getAttachment(@Param("intentno") String intentno,@Param("areatype") Integer areatype,@Param("attributetype") Integer attributetype,@Param("linketype") Integer linketype);
    @DS("xyg")
    List<AssemblyDiagramVO> getAssemblyDiagram(@Param("orderId") Long orderId);

    @DS("zy")
    SyncBasicInfoVO getSyncBasicInfo(@Param("orderId") Long orderId);

    @DS("xyg")
    SyncDesignInfoVO getSyncDesignInfo(@Param("orderId") Long orderId);

    @DS("xyg")
    List<String> getCustomerPic(@Param("intentno") String intentno);

    @DS("xyg")
    List<PsDesignInfoVO> getPsDesignSupportInfo(@Param("orderid")Long orderid);
}
