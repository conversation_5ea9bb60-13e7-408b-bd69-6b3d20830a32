package com.gcl.psmis.zy.dao.region;

import com.baomidou.dynamic.datasource.annotation.DS;
import com.gcl.psmis.framework.common.resp.region.NewRegionResp;
import com.gcl.psmis.framework.common.resp.region.RegionResp;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * project: RegionDao
 * Powered 2023-10-26 11:25:33
 *
 * <AUTHOR>
 * @version 1.0
 * @since 1.8
 */
@Mapper
public interface RegionDao {
    @DS("zy")
    List<RegionResp> getProvince();

    @DS("zy")
    List<RegionResp> getCity(@Param("id") Long id);

    @DS("zy")
    List<RegionResp> getArea(@Param("id") Long id);

    List<RegionResp> getProvinces();

    List<RegionResp> getCityByProvinceId(@Param("provinceId") Long provinceId);

    List<RegionResp> getAreaByCityId(@Param("cityId") Long cityId);

    List<RegionResp> getTownByAreaId(@Param("areaId") Long areaId);

    List<NewRegionResp> getAreaInfo(@Param("pid")  String pid, @Param("level") Integer level);
}
