package com.gcl.psmis.zy.service.powerstation;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.date.DateTime;
import cn.hutool.core.date.DateUtil;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.gcl.psmis.framework.common.req.powerstation.complete.PowerStationCompleteReq;
import com.gcl.psmis.framework.common.resp.powerStation.check.AcceptResp;
import com.gcl.psmis.framework.common.resp.powerStation.check.CheckAcceptResp;
import com.gcl.psmis.framework.common.resp.powerStation.check.PowerStationCheckResp;
import com.gcl.psmis.framework.common.resp.powerStation.complete.CompleteNumResp;
import com.gcl.psmis.framework.common.resp.powerStation.complete.PowerCompleteReq;
import com.gcl.psmis.framework.common.resp.powerStation.complete.PowerStationCompleteResp;
import com.gcl.psmis.framework.common.resp.powerStation.complete.PowerStationDeviceResp;
import com.gcl.psmis.framework.common.util.NumberUtil;
import com.gcl.psmis.framework.common.vo.MergegrVO;
import com.gcl.psmis.framework.common.vo.PictureVO;
import com.gcl.psmis.framework.common.vo.ProductVO;
import com.gcl.psmis.framework.common.vo.zy.*;
import com.gcl.psmis.framework.dict.util.ExpandBeanUtils;
import com.gcl.psmis.framework.mbg.entity.OrdOrderEntity;
import com.gcl.psmis.framework.mbg.entity.TPowerStationEntity;
import com.gcl.psmis.framework.mbg.service.OrdOrderService;
import com.gcl.psmis.framework.mbg.service.TPowerStationService;
import com.gcl.psmis.zy.dao.powerstation.PowerStationCloseDao;
import com.gcl.psmis.zy.dao.powerstation.PowerStationCompleteDao;
import com.gcl.psmis.zy.service.BusinessService;
import com.gcl.psmis.zy.service.ZyOssPathService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.concurrent.CompletableFuture;
import java.util.stream.Collectors;

/**
 * project: PowerStationCompleteService
 * Powered 2023-10-19 10:22:22
 *
 * <AUTHOR>
 * @version 1.0
 * @since 1.8
 */
@Service
@Slf4j
public class PowerStationCompleteService {

    @Autowired
    private PowerStationCompleteDao powerStationCompleteDao;

    @Autowired
    private BusinessService businessService;

    @Autowired
    private PowerStationCloseDao powerStationCloseDao;

    @Autowired
    private ZyOssPathService ossPathService;

    @Autowired
    private OrdOrderService ordOrderService;

    @Autowired
    private TPowerStationService tPowerStationService;


    public IPage<PowerStationCompleteResp> getPsComplete(PowerStationCompleteReq params) {
        Page<PowerStationCompleteResp> page = new Page<>(params.getPageNum(), params.getPageSize());
        if(CollUtil.isEmpty(params.getPropertyOrgIdList())){
            List<Long> orgIdList = getOrgIdList();
            if(CollUtil.isEmpty(orgIdList)){
                IPage<PowerStationCompleteResp> nullPage = new Page<>();
                nullPage.setRecords(new ArrayList<>());
                nullPage.setPages(0);
                nullPage.setTotal(0);
                nullPage.setCurrent(page.getCurrent());
                nullPage.setSize(page.getSize());
                return nullPage;
            }
            params.setPropertyOrgIdList(orgIdList);
        }
//        IPage<PowerStationCompleteResp> list = powerStationCompleteDao.getPsComplete(page,params);

        List<String> ownNames = this.getOwnNames(params.getPropertyOrgIdList());
        if (ownNames!=null){
            params.setOwnNames(ownNames);
        }

        IPage<PowerStationCompleteResp> list = powerStationCloseDao.getPsCompleteNews(page,params);

        return list;
    }

    public List<Long> getOrgIdList(){
        List<Long> orgIdList = new ArrayList<>();
        List<PropertyOrgVo> orgList = businessService.getOrgList();
        if(orgList!=null){
            for(PropertyOrgVo propertyOrgVo:orgList){
                if(propertyOrgVo.getChilds()!=null){
                    for(PropertyOrgVo childVo:propertyOrgVo.getChilds()){
                        orgIdList.add(childVo.getPropertyOrgId());
                    }
                }
            }
        }
        return orgIdList;
    }



    public List<CompleteNumResp> completeStatistics(PowerCompleteReq powerCompleteReq) {
        List<Long> propertyOrgIdList = powerCompleteReq.getPropertyOrgIdList();
        ArrayList<CompleteNumResp> list = new ArrayList<>();
        if(CollUtil.isEmpty(propertyOrgIdList)){
            List<Long> orgIdList = getOrgIdList();
            if(CollUtil.isEmpty(orgIdList)){
                return null;
            }
            propertyOrgIdList = orgIdList;
        }
        List<String> ownName = getOwnNames(propertyOrgIdList);
        List<String> ownNames = null;
        if (ownName!=null){
            ownNames = ownName;
        }

        DateTime date = DateUtil.date();
        //当天时间的开始和结束时间
        Date beginOfDay = DateUtil.beginOfDay(date);
        Date endOfDay = DateUtil.endOfDay(date);
        CompleteNumResp completeNumDay = powerStationCompleteDao.completeFindDay(beginOfDay,endOfDay,ownNames);
        completeNumDay.setTime("1");
        list.add(completeNumDay);
        //当月时间的开始和结束时间
        DateTime beginOfMonth = DateUtil.beginOfMonth(date);
        DateTime endOfMonth = DateUtil.endOfMonth(date);
        CompleteNumResp completeNumMonth = powerStationCompleteDao.completeFindDay(beginOfMonth,endOfMonth,ownNames);
        completeNumMonth.setTime("2");
        list.add(completeNumMonth);
        //当年时间的开始和结束时间
        DateTime beginOfYear = DateUtil.beginOfYear(date);
        DateTime endOfYear = DateUtil.endOfYear(date);
        CompleteNumResp completeNumYear = powerStationCompleteDao.completeFindDay(beginOfYear,endOfYear,ownNames);
        completeNumYear.setTime("3");
        list.add(completeNumYear);
        CompleteNumResp completeNumAll = powerStationCompleteDao.completeFindAll(ownNames);
        completeNumAll.setTime("4");
        list.add(completeNumAll);
        return list;
    }

    public PowerStationCompleteResp completeDetail(String intentno) {
        PowerStationCompleteResp resp = powerStationCompleteDao.completeDetail(intentno);
        if (resp != null &&resp.getSITEPHONE()!=null){
            String phone = resp.getSITEPHONE().substring(0, 3) + "****" + resp.getSITEPHONE().substring(7);
            resp.setSITEPHONE(phone);
        }
        return resp;
    }

    public List<PowerStationDeviceResp> completeDetailDevice(String intentno) {
        List<PowerStationDeviceResp> list = powerStationCompleteDao.completeDetailDevice(intentno);
        Integer count = 1;
        for (PowerStationDeviceResp resp : list) {
            resp.setId(count);
            count++;
        }
        return list;
    }

    public List<ProductVO> getProduct() {
        return powerStationCompleteDao.getProduct();
    }

    public MergegrVO getMergegr(String intentno) {
        MergegrVO mergegrVO = powerStationCompleteDao.getMergegr(intentno);
        if (StringUtils.isNotBlank(mergegrVO.getSTATIONNO())){
            MergegrVO mergegr = powerStationCloseDao.getMergegr(mergegrVO.getSTATIONNO());
            if(mergegr!=null){
                mergegrVO.setCapacity(mergegr.getCapacity());
                mergegrVO.setStartPowertime(mergegr.getStartPowertime());
            }
        }
        return mergegrVO;
    }

    public List<CheckAcceptResp> getCheckAccept(String intentno) {
        List<CheckAcceptResp> list = powerStationCompleteDao.getCheckAccept(intentno);
        List<AcceptResp> respList = powerStationCompleteDao.getAccept(intentno);
        for (CheckAcceptResp checkAcceptResp : list) {
            ArrayList<AcceptResp> arrayList = new ArrayList<>();
            for (AcceptResp acceptResp : respList) {
                if (checkAcceptResp.getGroupOne().equals(acceptResp.getGroupOne())){
                    arrayList.add(acceptResp);
                }
                checkAcceptResp.setAcceptRespList(arrayList);
            }
        }
        return list;
    }

    public PowerStationCheckResp getPsCheck(String intentno) {
        PowerStationCheckResp resp = powerStationCompleteDao.getPsCheck(intentno);
        if (resp!=null && resp.getCREATEUSERID()!=null){
            String name = powerStationCompleteDao.findName(resp.getCREATEUSERID());
            resp.setCREATEUSERNAME(name);
            ExpandBeanUtils.convert(resp, resp);
        }

        return resp;
    }

    public List<PictureVO> getProjectPicture(String intentno) {
        List<PictureVO> resultList = powerStationCompleteDao.getProjectPicture(intentno);

        List<CompletableFuture<PictureVO>> futures = resultList.stream()
                .map(pictureVO -> CompletableFuture.supplyAsync(() -> {
                    try {
                        pictureVO.setPicture(ossPathService.ossUrlAppend(pictureVO.getPicture()));
                        return pictureVO;
                    } catch (Exception e) {
                        log.error("图片转换失败: {}", e.getMessage());
                        e.printStackTrace();
                        return null;
                    }
                }))
                .collect(Collectors.toList());

        List<PictureVO> processedList = futures.stream()
                .map(CompletableFuture::join)
                .collect(Collectors.toList());

        return processedList;
    }

    public List<PictureVO> getAttachment(String intentno) {
        List<PictureVO> resultList = powerStationCompleteDao.getAttachment(intentno);
        List<CompletableFuture<PictureVO>> futures = resultList.stream()
                .map(pictureVO -> CompletableFuture.supplyAsync(() -> {
                    try {
                        pictureVO.setPicture(ossPathService.ossUrlAppend(pictureVO.getPicture()));
                        return pictureVO;
                    } catch (Exception e) {
                        log.error("图片转换失败: {}", e.getMessage());
                        e.printStackTrace();
                        return null;
                    }
                }))
                .collect(Collectors.toList());

        List<PictureVO> processedList = futures.stream()
                .map(CompletableFuture::join)
                .collect(Collectors.toList());

        return processedList;
    }

    public RectifyFormVo getRectificeForm(String intentno) {

        RectifyFormVo vo = powerStationCompleteDao.getRectificeForm(intentno);
        //查询整改电站列表
        if(null!= vo){
         List<RectifyPowerStationVo> resultList =  powerStationCompleteDao.getRectificePowerStation(intentno);
         //塞图片
            for (RectifyPowerStationVo stationVo : resultList) {
                RectifyPowerImgVo imgVo = powerStationCompleteDao.getPicture(stationVo.getStationNo());
                try {
                    imgVo.setBeforePicture(ossPathService.ossUrlAppend(imgVo.getBeforePicture()));
                    imgVo.setAfterPicture(ossPathService.ossUrlAppend(imgVo.getAfterPicture()));
                } catch (Exception e) {
                    log.error("图片转换失败:{}",e.getMessage());
                    e.printStackTrace();
                }

                BeanUtils.copyProperties(imgVo,stationVo);
            }
         vo.setChildrenList(resultList);
        }
        return vo;
    }

    public Long findOrderId(String psCode) {
        return powerStationCompleteDao.findOrderId(psCode);
    }

    public List<String> getOwnNames(List<Long> propertyOrgIdList) {
        return powerStationCompleteDao.getOwnNames(propertyOrgIdList);
    }

    public Integer findOrderState(String intentno) {
        OrdOrderEntity ordOrderEntity = ordOrderService.lambdaQuery()
                .select(OrdOrderEntity::getStationno)
                .eq(OrdOrderEntity::getIntentno, intentno)
                .one();
        if(ordOrderEntity != null && StringUtils.isNotBlank(ordOrderEntity.getStationno())){
            TPowerStationEntity stationEntity = tPowerStationService.lambdaQuery()
                    .eq(TPowerStationEntity::getPsCode, ordOrderEntity.getStationno())
                    .eq(TPowerStationEntity::getArFlag, 1)
                    .one();
            if(stationEntity!=null){
                return 1;
            }
        }
        return 0;
    }
}
