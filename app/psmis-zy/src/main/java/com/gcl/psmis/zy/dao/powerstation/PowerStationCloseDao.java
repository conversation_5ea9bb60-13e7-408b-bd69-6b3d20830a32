package com.gcl.psmis.zy.dao.powerstation;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.gcl.psmis.framework.common.req.powerstation.close.PowerStationCloseReq;
import com.gcl.psmis.framework.common.req.powerstation.complete.PowerStationCompleteReq;
import com.gcl.psmis.framework.common.resp.powerStation.close.PowerStationCloseResp;
import com.gcl.psmis.framework.common.resp.powerStation.complete.CompleteNumResp;
import com.gcl.psmis.framework.common.resp.powerStation.complete.PowerStationCompleteResp;
import com.gcl.psmis.framework.common.vo.MergegrVO;
import com.gcl.psmis.framework.common.vo.PowerStationVO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.Date;
import java.util.List;

/**
 * project: PowerStationCloseDao
 * Powered 2023-10-19 18:21:34
 *
 * <AUTHOR>
 * @version 1.0
 * @since 1.8
 */
@Mapper
public interface PowerStationCloseDao {
    List<Long> getPsidByInverter(@Param("params") PowerStationCloseReq params);

    IPage<PowerStationCloseResp> getPowerStationList(Page<PowerStationCloseResp> page, @Param("params") PowerStationCloseReq params);

    PowerStationVO getTotalCount(@Param("params") PowerStationCloseReq params);

    CompleteNumResp findDay(@Param("beginOfDay") Date beginOfDay, @Param("endOfDay") Date endOfDay, @Param("ownNames") List<String> ownNames);

    CompleteNumResp findAll( @Param("ownNames") List<String> ownNames);

    MergegrVO getMergegr(@Param("psCode") String psCode);

    Long findPsId(@Param("stationno") String stationno);

    IPage<PowerStationCompleteResp> getPsCompleteNews(@Param("page") Page<PowerStationCompleteResp> page, @Param("params") PowerStationCompleteReq params);
}
