package com.gcl.psmis.zy.controller.powerstation;

import com.gcl.psmis.framework.common.annotation.GetGclApiByToken;
import com.gcl.psmis.framework.common.annotation.PostGclApiByToken;
import com.gcl.psmis.framework.common.vo.zy.DispatchDetailVo;
import com.gcl.psmis.framework.common.vo.zy.DispatchQueryVo;
import com.gcl.psmis.framework.common.vo.zy.DispatchUserVo;
import com.gcl.psmis.zy.service.DispatchService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR> @Description 派工
 * @Date 2023/10/20
 * @Param
 **/
@Api(value = "智盈电站派工", description = "智盈电站派工", tags = {"v5", "智盈电站派工"})
@RestController
@RequestMapping("/dispatch")
@Slf4j
public class DispatchController {

    @Autowired
    private DispatchService dispatchService;

    @ApiOperation("派工列表")
    @GetGclApiByToken("getDispatchList")
    public List<String> getDispatchList(@RequestParam(value = "intentno",required = true) String intentno) {

        return dispatchService.getDispatchList(intentno);
    }

    @ApiOperation("派工单明细")
    @PostGclApiByToken("getDispatchDetail")
    public DispatchDetailVo getDispatchDetail(@RequestBody @Validated DispatchQueryVo dispatchQueryVo) {

            DispatchDetailVo vo = dispatchService.getDispatchDetail(dispatchQueryVo);
            return vo;
    }

    @ApiOperation("电站和用户信息列表")
    @GetGclApiByToken("getDispatchUserList")
    public List<DispatchUserVo> getDispatchUserList(@RequestParam(value = "dispatchNo",required = true) String dispatchNo){
        return dispatchService.getDispatchUserList(dispatchNo);
    }




}
