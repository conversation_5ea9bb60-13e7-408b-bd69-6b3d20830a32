package com.gcl.psmis.zy.controller.powerstation;

import cn.hutool.core.collection.CollUtil;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.gcl.psmis.framework.common.annotation.DataScope;
import com.gcl.psmis.framework.common.annotation.PostGclApiByToken;
import com.gcl.psmis.framework.common.constant.CommonResultCode;
import com.gcl.psmis.framework.common.exception.CustomException;
import com.gcl.psmis.framework.common.req.powerstation.close.PowerStationCloseReq;
import com.gcl.psmis.framework.common.resp.powerStation.close.PowerStationCloseResp;
import com.gcl.psmis.framework.common.resp.powerStation.complete.CompleteNumResp;
import com.gcl.psmis.framework.common.resp.powerStation.complete.PowerCompleteReq;
import com.gcl.psmis.framework.common.vo.PowerStationVO;
import com.gcl.psmis.framework.export.executor.ExportExecutor;
import com.gcl.psmis.framework.export.task.ExportTask;
import com.gcl.psmis.zy.service.powerstation.PowerStationCloseService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.io.IOException;
import java.util.List;

/**
 * project: PowerStationCloseController
 * Powered 2023-10-19 18:21:07
 *
 * <AUTHOR>
 * @version 1.0
 * @since 1.8
 */
@Api(tags = "智盈电站并网",description = "智盈电站并网")
@RestController
@RequestMapping("powerStationClose")
@Slf4j
public class PowerStationCloseController {

    @Autowired
    private PowerStationCloseService powerStationCloseService;

    @Autowired
    private ExportExecutor exportExecutor;

    /**
     * 并网列表
     * @param params
     * @return
     */
    @ApiOperation("并网列表")
    @PostGclApiByToken("/getPowerStationList")
    public IPage<PowerStationCloseResp> getPowerStationList(@RequestBody PowerStationCloseReq params) {

        IPage<PowerStationCloseResp> infoList = powerStationCloseService.getList(params);
        return infoList;
    }

    /**
     * 导出并网列表
     * @param params
     * @throws IOException
     */
    @ApiOperation("导出并网列表")
    @PostGclApiByToken("exportPowerStationList")
    public void exportPowerStationList( @RequestBody PowerStationCloseReq params) throws IOException {

        List<PowerStationCloseResp> inverterPositionVOList = null;
        params.setPageNum(-1);
        params.setPageSize(-1);
        IPage<PowerStationCloseResp> inverterDatas = powerStationCloseService.getList(params);
        inverterPositionVOList = inverterDatas == null ? null : inverterDatas.getRecords();

        ExportTask exportTask = null;
        if (CollUtil.isEmpty(params.getColNameList())) {
            exportTask = new ExportTask(inverterPositionVOList,"并网电站列表");
        } else {
            exportTask = new ExportTask(inverterPositionVOList,"并网电站列表",params.getColNameList());
        }
        exportExecutor.addTask(exportTask);
    }

    /**
     * 统计电站数量
     * @param params
     * @return
     */
    @ApiOperation("统计电站数量")
    @PostGclApiByToken("getTotalCount")
    //@DataScope
    public PowerStationVO getTotalCount(@RequestBody PowerStationCloseReq params) {
        if (params.getPsType() == null) {
            throw new CustomException(CommonResultCode.INVALID_PARAM);
        }
        PowerStationVO powerStationVO = powerStationCloseService.getTotalCount(params);
        if (powerStationVO == null) {
            return null;
        }
        return powerStationVO;
    }

    /**
     * 并网统计
     * @return
     */
    @ApiOperation(value = "并网统计" )
    @PostGclApiByToken("completeStatistics")
    //@DataScope
    public List<CompleteNumResp> completeStatistics(@RequestBody PowerCompleteReq powerCompleteReq){
        return powerStationCloseService.completeStatistics(powerCompleteReq);
    }

}
