package com.gcl.psmis.zy.controller.powerstation;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.gcl.psmis.framework.common.annotation.GetGclApiByToken;
import com.gcl.psmis.framework.common.annotation.PostGclApiByToken;
import com.gcl.psmis.framework.common.constant.CommonResultCode;
import com.gcl.psmis.framework.common.exception.BussinessException;
import com.gcl.psmis.framework.common.exception.CustomException;
import com.gcl.psmis.framework.common.req.alarm.AlarmRecordReq;
import com.gcl.psmis.framework.common.vo.PsPowerVO;
import com.gcl.psmis.framework.common.vo.alarm.AlarmRecordVO;
import com.gcl.psmis.framework.common.vo.powerstation.PowerStationDetailVO;
import com.gcl.psmis.framework.common.vo.powerstation.PowerTotalVO;
import com.gcl.psmis.framework.common.vo.zy.FullProcessQueryVo;
import com.gcl.psmis.framework.common.vo.zy.FullProcessVo;
import com.gcl.psmis.framework.dict.util.ExpandBeanUtils;
import com.gcl.psmis.zy.service.powerstation.PowerStationOpsService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import javax.validation.Valid;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;

/**
 * project: PowerStationOpsController
 * Powered 2023-10-12 15:19:59
 *
 * <AUTHOR>
 * @version 1.0
 * @since 1.8
 */
@Api(tags = "智盈电站运维",description = "智盈电站运维")
@RestController
@RequestMapping("powerStationOps")
@Slf4j
public class PowerStationOpsController {

    @Autowired
    private PowerStationOpsService powerStationOpsService;

    /**
     * 查询电站基本信息
     *
     * @param intentno
     * @return
     */
    @ApiOperation(value = "查询电站基本信息" , tags = "v5")
    @GetGclApiByToken("getPowerStationDetail")
    public PowerStationDetailVO getPowerStationDetail(@ApiParam("商机编号") String intentno) {
        if (intentno == null) {
            throw new BussinessException("商机编号不能为空");
        }
        PowerStationDetailVO powerStationDetailVO = powerStationOpsService.getPowerStationDetail(intentno);
        if (powerStationDetailVO != null) {
            ExpandBeanUtils.convert(powerStationDetailVO, powerStationDetailVO);
        }
        return powerStationDetailVO;
    }

    /**
     * 电站发电统计
     *
     * @param intentno
     * @return
     */
    @ApiOperation(value = "电站发电统计" , tags = "v5")
    @GetGclApiByToken("getPowerTotal")
    public PowerTotalVO getPowerTotal(@ApiParam("商机编号") String intentno) {

        if (intentno == null) {
            throw new CustomException(CommonResultCode.INVALID_PARAM);
        }
        PowerTotalVO powerTotalVO = powerStationOpsService.getPowerTotal(intentno);
        ExpandBeanUtils.convert(powerTotalVO, powerTotalVO);

        return powerTotalVO;
    }

    /**
     * 电站发电量趋势图
     *
     * @param intentno
     * @param type
     * @param date
     * @return
     */
    @ApiOperation(value = "电站发电量趋势图" , tags = "v5")
    @GetGclApiByToken("getPowerTrend")
    public List<PsPowerVO> getPowerTrend(@ApiParam("商机编号") String intentno,
                                         @ApiParam("1：天 2：月 3：年 4：全部")  @RequestParam(defaultValue = "1") Integer type,
                                         @ApiParam("日期") String date) {

        if (intentno == null) {
            throw new CustomException(CommonResultCode.INVALID_PARAM);
        }
        List<PsPowerVO> resultList = powerStationOpsService.getPowerTrend(intentno,type,date);

        return resultList;

    }

    /**
     * 实时功率趋势图
     *
     * @param intentno
     * @param date
     * @return
     */
    @ApiOperation(value = "实时功率趋势图" , tags = "v5")
    @GetGclApiByToken("getPacTrend")
    public List<PsPowerVO> getPacTrend(@ApiParam("商机编号") String intentno,@ApiParam("日期") String date) {
        if (intentno == null) {
            throw new CustomException(CommonResultCode.INVALID_PARAM);
        }
        return  powerStationOpsService.getPacTrend(intentno,date);
    }

    /**
     * 查询最新的告警记录
     * @param params
     * @return
     */
    @ApiOperation(value = "查询最新的告警记录" , tags = "v5")
    @PostGclApiByToken("getLatestAlarmList")
    public List<AlarmRecordVO> getLatestAlarmList(@RequestBody @Valid AlarmRecordReq params) {
        IPage<AlarmRecordVO> infoList = powerStationOpsService.getAlarmRecordList(params,params.getPageNum(),params.getPageSize());

        if (infoList != null) {
            ExpandBeanUtils.convert(infoList, infoList);
            return infoList.getRecords();
        }
        return null;
    }

    @ApiOperation(value = "全流程查询" , tags = "v5")
    @PostGclApiByToken("getFullProcessList")
    public List<FullProcessVo> getFullProcessList(@RequestBody @Validated FullProcessQueryVo params) {

        return powerStationOpsService.getFullProcessList(params.getIntentno());

    }

    @ApiOperation(value = "全流程查询(分组后)" , tags = "v5")
    @PostGclApiByToken("getGroupProcessList")
    public Map<String,List<FullProcessVo>> getGroupProcessList(@RequestBody @Validated FullProcessQueryVo params) {

        return powerStationOpsService.getGroupProcessList(params.getIntentno());

    }
}


