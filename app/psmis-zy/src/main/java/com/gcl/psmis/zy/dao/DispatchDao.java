package com.gcl.psmis.zy.dao;

import com.baomidou.dynamic.datasource.annotation.DS;
import com.gcl.psmis.framework.common.vo.zy.DispatchDetailBomVo;
import com.gcl.psmis.framework.common.vo.zy.DispatchDetailVo;
import com.gcl.psmis.framework.common.vo.zy.DispatchQueryVo;
import com.gcl.psmis.framework.common.vo.zy.DispatchUserVo;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <AUTHOR> @Description
 * @Date 2023/10/20
 * @Param
 **/

@Mapper
public interface DispatchDao {

    @DS("xyg")
    List<DispatchDetailVo> list(@Param("intentno") String intentno);

    @DS("xyg")
    List<DispatchDetailBomVo> getDispatchBomDetail(@Param("disId") Integer disId,@Param("intentno")  String intentno);

    /**
     * 派工单号
     * @param dispatchNo
     * @return
     */
    @DS("xyg")
    DispatchDetailVo getDispatchDetail(@Param("dispatchNo") String dispatchNo);

    @DS("xyg")
    List<DispatchUserVo> getDispatchUserList(@Param("dispatchNo") String dispatchNo);
}
