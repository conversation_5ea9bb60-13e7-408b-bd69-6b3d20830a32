package com.gcl.psmis.zy.service;

import cn.hutool.core.collection.CollUtil;
import com.gcl.psmis.framework.common.exception.BussinessException;
import com.gcl.psmis.framework.common.vo.zy.SurveyImgDto;
import com.gcl.psmis.framework.common.vo.zy.TfSyncDataResultVo;
import com.gcl.psmis.zy.dao.TfSyncDataDao;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * 铁发定时任务 同步房屋信息
 */
@Service
public class TfSyncDataService {
    @Autowired
    private TfSyncDataDao tfSyncDataDao;
    @Autowired
    private ZyOssPathService pathService;

    public TfSyncDataResultVo getSurveyByOrdId(String ordId){
        TfSyncDataResultVo result = tfSyncDataDao.getSurveyByOrdId(ordId);
        if(result==null){
            throw new BussinessException("order不存在");
        }

        List<SurveyImgDto> surveyImgDtoList = tfSyncDataDao.getSurveyImgbyOrdId(ordId);
        result.setImgs(surveyImgDtoList);

        if(CollUtil.isNotEmpty(surveyImgDtoList)){
            for(SurveyImgDto dto:surveyImgDtoList){
                String path = pathService.ossUrlAppend(dto.getImgDocumentPath());
                dto.setImgDocumentPath(path);
            }
        }

        return result;
    }

}
