package com.gcl.psmis.zy.service;

import cn.hutool.core.collection.CollUtil;
import com.gcl.psmis.framework.common.dto.zy.DocumentAreaType;
import com.gcl.psmis.framework.common.dto.zy.DocumentAttributeType;
import com.gcl.psmis.framework.common.dto.zy.DocumentLinkType;
import com.gcl.psmis.framework.common.exception.BussinessException;
import com.gcl.psmis.framework.common.vo.zy.*;
import com.gcl.psmis.framework.mbg.entity.OrdOrderExtendEntity;
import com.gcl.psmis.framework.mbg.entity.TContractEntity;
import com.gcl.psmis.framework.mbg.service.OrdOrderExtendService;
import com.gcl.psmis.framework.mbg.service.TContractService;
import com.gcl.psmis.zy.dao.PowerStationDocumentationDao;
import com.gcl.psmis.zy.dao.powerstation.ZyPowerStationMngDao;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;


import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

/**
 * @ClassName: PowerStationDocumentationService
 * @data: 2023/10/25 15:05
 * @author: <EMAIL>
 * @description:
 */
@Service
@Slf4j
public class PowerStationDocumentationService {

    @Autowired
    private PowerStationDocumentationDao powerStationDocumentationDao;

    @Autowired
    private ZyOssPathService ossPathService;

    @Autowired
    private ZyPowerStationMngDao zgPowerStationMngDao;

    @Autowired
    private OrdOrderExtendService ordOrderExtendService;

    @Autowired
    private ZyPowerStationMngDao zyPowerStationMngDao;


    /**
     * 查询建档信息
     */
    public DocumentationInformationVO selectDocumentationInformation(String intentno){
        DocumentationInformationVO documentationInformationVO =  powerStationDocumentationDao.selectDocumentationInformation(intentno);
        return documentationInformationVO;
    }

    /**
     * 查询合同信息
     */
    public ContactInformationVO selectContactInformation(String intentno){

        ContactInformationVO contactInformationVO = powerStationDocumentationDao.selectContactInformation(intentno);
        //合同原件
        List<String> paths = zyPowerStationMngDao.getAttachment(intentno, DocumentAreaType.ORDER.value, null, DocumentLinkType.BUSINESS.value);
        List<String> ossPaths = new ArrayList<>();
        if(CollectionUtils.isNotEmpty(paths)) {

                ossPaths = paths.stream().map(path -> {
                            try {
                                return ossPathService.ossUrlAppend(path);
                            } catch (Exception e) {
                                throw new BussinessException("获取图片失败");
                            }
                        }).collect(Collectors.toList());
        }
        if(contactInformationVO != null){
            contactInformationVO.setContractOrigin(ossPaths);
        }
        return contactInformationVO;
    }

    /**
     * 查询收益分成模式
     */
    public List<ShareRuleInformationVO> selectShareRuleInformation(String intentno){

        List<ShareRuleInformationVO> shareRuleInformationVOS = powerStationDocumentationDao.selectShareRuleInformation(intentno);
        return shareRuleInformationVOS;
    }

    /**
     * 查询收益分享卡信息
     */
    public CardInformationVO selectShareCardInformation(String intentno){

        CardInformationVO cardInformationVO = powerStationDocumentationDao.selectShareCardInformation(intentno);

        //收益分享卡图片
        List<String> paths = zyPowerStationMngDao.getAttachment(intentno, DocumentAreaType.ORDER.value, null, DocumentLinkType.BUSINESS_SHARE.value);
        List<String> ossPaths = new ArrayList<>();
        if (CollectionUtils.isNotEmpty(paths)){

            ossPaths = paths.stream().map(path ->{
                try {
                    return ossPathService.ossUrlAppend(path);
                } catch (Exception e){
                    throw new BussinessException("获取图片失败");
                }
            }).collect(Collectors.toList());

        }
        if (cardInformationVO != null){
            cardInformationVO.setSharePhoto(ossPaths);
        }
        return cardInformationVO;
    }

    /**
     * 查询电网收益卡信息
     */
    public CardIncomeInformationVO selectIncomeCardInformation(String intentno){

        CardIncomeInformationVO cardInformationVO = powerStationDocumentationDao.selectIncomeCardInformation(intentno);
        List<String> pathList = zgPowerStationMngDao.getAttachment(intentno, DocumentAreaType.MERGEGRID.value, DocumentAttributeType.POWERINCOMECARD.value, DocumentLinkType.CREATE.value);
        List<String> fullPathList = new ArrayList<>();

        for (String path : pathList) {
            fullPathList.add(ossPathService.ossUrlAppend(path));
        }
        cardInformationVO.setShareIncomePhoto(fullPathList);
        return cardInformationVO;
    }



}
