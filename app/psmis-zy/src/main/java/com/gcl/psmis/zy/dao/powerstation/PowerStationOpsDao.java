package com.gcl.psmis.zy.dao.powerstation;

import com.baomidou.dynamic.datasource.annotation.DS;
import com.gcl.psmis.framework.common.vo.powerstation.PowerStationDetailVO;
import com.gcl.psmis.framework.common.vo.powerstation.PowerTotalVO;
import com.gcl.psmis.framework.common.vo.zy.FullProcessVo;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * project: PowerStationOpsDao
 * Powered 2023-10-12 15:21:27
 *
 * <AUTHOR>
 * @version 1.0
 * @since 1.8
 */
@Mapper
public interface PowerStationOpsDao {

    PowerStationDetailVO getPowerStationDetail(@Param("intentno") String intentno);

    PowerTotalVO getPowerTotal(@Param("intentno") String intentno);

    @DS("xyg")
    List<FullProcessVo> getFullProcessList(@Param("intentno") String intentno);
}
