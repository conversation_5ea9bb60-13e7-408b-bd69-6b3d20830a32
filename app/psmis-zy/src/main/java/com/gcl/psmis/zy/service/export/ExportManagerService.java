package com.gcl.psmis.zy.service.export;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.gcl.framework.data.holder.CurrentUserHolder;
import com.gcl.psmis.framework.common.req.export.ExportTaskQueryReq;
import com.gcl.psmis.framework.common.resp.export.ExportTaskVO;
import com.gcl.psmis.zy.dao.export.ExportManagerDao;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
* @className: ExportManagerService
* @author: xinan.yuan
* @create: 2023/9/18 14:44
* @description: 导出中心
*/
@Service
@Slf4j
public class ExportManagerService {



    @Autowired
    private ExportManagerDao exportManagerDao;


    public IPage<ExportTaskVO> list(ExportTaskQueryReq req) {
        IPage<ExportTaskVO> page = new Page<>(req.getPageNum(), req.getPageSize());
        String empNo = CurrentUserHolder.getUserDTOThreadLocal().getEmpNo();
        page=exportManagerDao.list(page,req,empNo);
        return page;
    }
}
