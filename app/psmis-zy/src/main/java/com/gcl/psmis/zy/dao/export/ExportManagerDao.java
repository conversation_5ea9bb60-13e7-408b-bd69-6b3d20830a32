package com.gcl.psmis.zy.dao.export;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.gcl.psmis.framework.common.req.export.ExportTaskQueryReq;
import com.gcl.psmis.framework.common.resp.export.ExportTaskVO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

/**
* @className: ExportManagerDao
* @author: xinan.yuan
* @create: 2023/9/18 14:54
* @description:
*/
@Mapper
public interface ExportManagerDao {

    IPage<ExportTaskVO> list(
            @Param("page")IPage<ExportTaskVO> page, @Param("req")ExportTaskQueryReq req,@Param("createByNo")String createByNo);
}
