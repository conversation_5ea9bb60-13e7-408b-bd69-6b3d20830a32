package com.gcl.psmis.zy.service.user;

import cn.hutool.core.util.RandomUtil;
import cn.hutool.http.HttpUtil;
import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import com.baomidou.mybatisplus.core.toolkit.StringUtils;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.gcl.psmis.framework.common.ResponseResult;
import com.gcl.psmis.framework.common.redis.SmsCodeRedisKeyStrategy;
import com.gcl.psmis.framework.common.req.msg.MessageReq;
import com.gcl.psmis.framework.common.req.msg.SendReq;
import com.gcl.psmis.framework.common.util.RedisUtil;
import com.gcl.psmis.framework.common.vo.zy.user.LoginVO;
import com.gcl.psmis.framework.mbg.entity.TMsgTemplateEntity;
import com.gcl.psmis.framework.mbg.entity.TOrderEntity;
import com.gcl.psmis.framework.mbg.entity.TPowerStationEntity;
import com.gcl.psmis.framework.mbg.entity.TUserEntity;
import com.gcl.psmis.framework.mbg.service.TMsgTemplateService;
import com.gcl.psmis.framework.mbg.service.TOrderService;
import com.gcl.psmis.framework.mbg.service.TPowerStationService;
import com.gcl.psmis.framework.mbg.service.TUserService;
import com.gcl.psmis.msg.api.SendRpcService;
import com.google.common.collect.Sets;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import java.util.HashMap;
import java.util.Map;

/**
 * <AUTHOR>
 */
@Service
@Slf4j
public class LoginService {

    @Autowired
    private TMsgTemplateService tMsgTemplateService;
    @Autowired
    SendRpcService sendRpcService;
    @Autowired
    TOrderService orderService;
    @Autowired
    TUserService userService;
    @Value("${jnpf.baseUrl}")
    private String baseUrl;

    /**
     * 根据电站编号，查询用户手机号，并发送短信
     *
     * @param psCode
     * @return
     */
    public LoginVO sendSms(String psCode) throws Exception {
        LoginVO loginVO = new LoginVO();

        //根据psCode获取用户手机号
        TOrderEntity one = orderService.lambdaQuery().eq(TOrderEntity::getStationno, psCode).one();
        if (null != one) {
            loginVO.setPhone(one.getGuestphone());
            //生成随机4位code
            loginVO.setCode(RandomUtil.randomNumbers(4));
            loginVO.setIntentno(one.getIntentno());
            this.sendRentSms(loginVO.getPhone(), loginVO.getCode());

            //发送短信后，将code存redis，设置超时时间为1分钟
            RedisUtil.set(SmsCodeRedisKeyStrategy.SMS_CODE, loginVO.getCode(), loginVO.getPhone());
        }

        return loginVO;
    }

    //租金分享短信
    private void sendRentSms(String phone, String code) throws Exception {
        SendReq sendReq = new SendReq();
        //获取到短信模板
        TMsgTemplateEntity templateServiceOne = tMsgTemplateService.getOne(Wrappers.<TMsgTemplateEntity>lambdaQuery().select(TMsgTemplateEntity::getId).eq(TMsgTemplateEntity::getTmpCode, "validate_sms"));
        Long id = templateServiceOne.getId();
        sendReq.setMessageTemplateId(id);
        MessageReq msg = new MessageReq();
        msg.setReceiver(Sets.newHashSet(phone));
        Map<String, String> map = new HashMap<>();
        map.put("code", code);
        msg.setVariables(map);
        sendReq.setMessageParam(msg);
        ResponseResult send = sendRpcService.send(sendReq);
        log.info(send.getMessage());
    }

    public LoginVO checkSms(String code) {
        LoginVO loginVO = new LoginVO();
        String s = RedisUtil.get(SmsCodeRedisKeyStrategy.SMS_CODE, code, String.class);
        if (StringUtils.isNotBlank(s)) {
            //缓存里有值说明没有过期，可以调用登录接口返回token
            String s1 = HttpUtil.get(baseUrl + "/api/oauth/Login/user");
            JSONObject jsonObject = JSONUtil.parseObj(s1);
            String data = jsonObject.getStr("data");
            JSONObject dataObj = JSONUtil.parseObj(data);
            String token = dataObj.getStr("token");
            loginVO.setToken(token);
        }
        return loginVO;
    }
}
