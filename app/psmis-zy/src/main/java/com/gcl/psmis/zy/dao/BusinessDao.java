package com.gcl.psmis.zy.dao;

import com.baomidou.dynamic.datasource.annotation.DS;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.gcl.psmis.framework.common.vo.zy.*;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 商机
 */
@Mapper
public interface BusinessDao {
    @DS("zy")
    IPage<BusinessResultVo> list(Page<BusinessResultVo> page, BusinessQueryVo params);

    @DS("zy")
    SurveyResultVo getSurveyByOrdId(@Param("intentno") String intentno);

    @DS("xyg")
    List<String> getBuyNo(@Param("intentno") String intentno);

    @DS("xyg")
    BuyMaterialVo getBuyMaterialByBuyNo(String buyNo);

    @DS("xyg")
    List<BuyMaterialPsVo> getPsNo(@Param("params") List<String> buyNoList);

    @DS("xyg")
    List<BuyMaterialBomVo> getBomListByBuyNo(@Param("buyNo") String buyNo);

    @DS("xyg")
    List<BuyMaterialBomVo> getBomListByPsNo(@Param("buyNo") String buyNo,@Param("psNo") String psNo);


    @DS("xyg")
    List<SurveyImgDto> getSurveyImgbyOrdId(String ordId);

    @DS("zy")
    List<PropertyOrgVo> getChildOrgList(@Param("params") List<Long> buyNoList);

    /**
     * @desc 查询勘察视频
     * @date 2023/11/10 09:32
     * @param orderId
     * @return {@link String}
     */
    @DS("xyg")
    List<SurveyImgDto> getSurveyView(@Param("orderId") String orderId);


}
