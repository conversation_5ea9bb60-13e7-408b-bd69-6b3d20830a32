package com.gcl.psmis.framework.taos.domain.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.gcl.psmis.framework.taos.annotation.TdTableName;
import com.gcl.psmis.framework.taos.domain.TdModel;
import lombok.*;

/**
* @className: PsVrIot15StDeviceEntity
* @author: xinan.yuan
* @create: 2024/3/1 14:20
* @description: 虚拟电站设备信息(15分钟维度)
*/
@EqualsAndHashCode(callSuper = true)
@AllArgsConstructor
@NoArgsConstructor
@Data
@Builder
@TdTableName(dbName = "ps_vr_iot15", superTbName = "st_device", tablePrefix = "t_device", separator = "_", tableProperty = {"deviceSn"}, isLowercase = true)
public class PsVrIot15StDeviceEntity extends TdModel {
    /**
     * ts 时间戳
     */
    @TableField("ts")
    private String ts;

    /**
     * 资源序列号 tag
     */
    @TableField("sn")
    private String sn;

    /**
     * resource_type 0资源1用户 tag
     */
    @TableField("resource_type")
    private Integer resourceType;

    /**
     * 用户编号/资源sn tag
     */
    @TableField("user_resource_sn")
    private String userResourceSn;

    /**
     * rt 0资源1用户 普通列
     */
    @TableField("rt")
    private Integer rt;

    /**
     * 用户编号/资源sn 普通列
     */
    @TableField("ursn")
    private String ursn;


    /**
     * 资源序列号
     */
    @TableField("device_sn")
    private String deviceSn;

    /**
     * 累计电量
     */
    @TableField("eto")
    private String eto;


    /**
     * 与上次数据的差值
     */
    @TableField("etd")
    private String etd;

    /**
     * 负荷
     */
    @TableField("load")
    private String load;

    @TableField("p")
    private String p;

    @TableField("q")
    private String q;

    @TableField("pa")
    private String pa;

    @TableField("pb")
    private String pb;

    @TableField("pc")
    private String pc;

    @TableField("qa")
    private String qa;

    @TableField("qb")
    private String qb;

    @TableField("qc")
    private String qc;

    @TableField("c")
    private String c;

    @TableField("ua")
    private String ua;

    @TableField("ub")
    private String ub;

    @TableField("uc")
    private String uc;

    @TableField("ia")
    private String ia;

    @TableField("ib")
    private String ib;

    @TableField("ic")
    private String ic;

    @TableField("rb")
    private String rb;

    @TableField("rf")
    private String rf;

    @TableField("rv")
    private String rv;

    @TableField("rc")
    private String rc;

    @TableField("soc")
    private String soc;

    @TableField("rm")
    private String rm;

    //直流电流
    @TableField("io")
    private String io;

    //直流电压
    @TableField("uo")
    private String uo;

    @TableField("receive_time")
    private String receiveTime;
}
