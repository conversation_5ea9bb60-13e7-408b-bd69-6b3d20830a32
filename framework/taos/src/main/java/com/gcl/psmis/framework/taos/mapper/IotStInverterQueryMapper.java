package com.gcl.psmis.framework.taos.mapper;

import com.baomidou.dynamic.datasource.annotation.DS;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.gcl.psmis.framework.taos.domain.dto.TdTableField;
import com.gcl.psmis.framework.taos.domain.entity.IotStInverterEntity;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * @className: StInverterQueryMapper
 * @author: xinan.yuan
 * @create: 2023/7/18 17:34
 * @description:
 */
@Mapper
@DS("td")
public interface IotStInverterQueryMapper {
    List<TdTableField> getAllColumns();

    List<IotStInverterEntity> listFirstBySnList(List<String> snList);

    IotStInverterEntity findLastValueByDate(@Param("snList") List<String> snList, @Param("startTime") String startTime, @Param("endTime") String endTime);

    IotStInverterEntity last(@Param("conditionSql")String conditionSql);

    List<IotStInverterEntity> listEvery5M(@Param("conditionSql")String conditionSql);

    List<IotStInverterEntity> listEvery1H(@Param("conditionSql")String conditionSql);

    List<IotStInverterEntity> listEvery1Day(@Param("conditionSql")String conditionSql);

    List<IotStInverterEntity> listEvery1Month(@Param("conditionSql")String conditionSql);

    List<IotStInverterEntity> listEvery1Year(@Param("conditionSql")String conditionSql);

    IotStInverterEntity first(@Param("conditionSql") String conditionSql);

    List<IotStInverterEntity> list(@Param("conditionSql")String conditionSql);

    List<IotStInverterEntity> batchLast(@Param("conditionSql") String conditionSql);

    List<IotStInverterEntity> batchFirst(@Param("conditionSql") String conditionSql);

    List<IotStInverterEntity> groupBy(@Param("columnName") String columnName, @Param("conditionSql") String conditionSql);

    IPage<IotStInverterEntity> pageList(@Param("page") IPage<IotStInverterEntity> page, @Param("conditionSql") String conditionSql);
}
