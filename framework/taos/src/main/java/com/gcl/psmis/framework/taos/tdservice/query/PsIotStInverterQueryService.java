package com.gcl.psmis.framework.taos.tdservice.query;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.gcl.psmis.framework.taos.config.strategy.IQueryTdService;
import com.gcl.psmis.framework.taos.domain.dto.TdTableField;
import com.gcl.psmis.framework.taos.domain.entity.IotStInverterEntity;
import com.gcl.psmis.framework.taos.domain.entity.PsIotStInverterEntity;
import com.gcl.psmis.framework.taos.mapper.PsIotStInverterQueryMapper;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

/**
* @className: PsIotStInverterQueryService
* @author: xinan.yuan
* @create: 2023/7/25 11:50
* @description: 
*/
@Service
@Slf4j
public class PsIotStInverterQueryService implements IQueryTdService<PsIotStInverterEntity> {

    @Autowired
    private PsIotStInverterQueryMapper psIotStInverterQueryMapper;

    @Override
    public List<PsIotStInverterEntity> batchLast(String conditionSql) {

        return psIotStInverterQueryMapper.batchLast(conditionSql);
    }

    @Override
    public List<PsIotStInverterEntity> batchFirst(String conditionSql) {
        return psIotStInverterQueryMapper.batchFirst(conditionSql);
    }

    @Override
    public List<TdTableField> getAllColumns() {
        return psIotStInverterQueryMapper.getAllColumns();
    }

    @Override
    public List<PsIotStInverterEntity> groupBy(String columnName, String conditionSql) {

        return psIotStInverterQueryMapper.groupBy(columnName,conditionSql);
    }

    @Override
    public List<PsIotStInverterEntity> list(String conditionSql) {
        return psIotStInverterQueryMapper.list(conditionSql);
    }

    @Override
    public List<PsIotStInverterEntity> listEvery5M(String conditionSql) {
        List<PsIotStInverterEntity> psIotStInverterEntities = psIotStInverterQueryMapper.listEvery5M(conditionSql);

        return psIotStInverterEntities;
    }

    @Override
    public List<PsIotStInverterEntity> listEvery1H(String conditionSql) {

        return psIotStInverterQueryMapper.listEvery1H(conditionSql);
    }

    @Override
    public List<PsIotStInverterEntity> listEvery1Day(String conditionSql) {
        return null;
    }

    @Override
    public List<PsIotStInverterEntity> listEvery1Month(String conditionSql) {
        return null;
    }

    @Override
    public List<PsIotStInverterEntity> listEvery1Year(String conditionSql) {
        return null;
    }

    @Override
    public PsIotStInverterEntity lastRow(String conditionSql) {

        return psIotStInverterQueryMapper.lastRow(conditionSql);
    }

    @Override
    public IPage<PsIotStInverterEntity> pageList(IPage<PsIotStInverterEntity> page, String conditionSql) {
        return null;
    }

    @Override
    public PsIotStInverterEntity firstRow(String toString) {
        return null;
    }

    @Override
    public PsIotStInverterEntity first(String conditionSql) {
        return psIotStInverterQueryMapper.first(conditionSql);
    }

    @Override
    public PsIotStInverterEntity last(String conditionSql) {
        PsIotStInverterEntity last = psIotStInverterQueryMapper.last(conditionSql);
        if(last!=null){
            last.setTs(last.getTs().substring(0, 19));
        }
        return last;
    }

    @Override
    public <R> List<R> convertList (Class<R> clazz,String conditionSql) {
        return null;
    }
}