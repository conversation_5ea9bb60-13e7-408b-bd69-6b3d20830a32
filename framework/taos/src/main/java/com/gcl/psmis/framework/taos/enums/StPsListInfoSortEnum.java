package com.gcl.psmis.framework.taos.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * <AUTHOR>
 * @className StPsListInfoSortEnum
 * @create 2022/8/29 18:11
 * @desc 电站列表排序字段
 **/
@Getter
@AllArgsConstructor
public enum StPsListInfoSortEnum {
    id,
    eday,
    eday_forecast_th,
    eday_efficiency_th,
    emonth,
    emonth_forecast_th,
    emonth_efficiency_th,
    eyear,
    eyear_forecast_th,
    eyear_efficiency_th,
    inverter_etotal,
    etotal,
    etotal_forecast_th,
    etotal_forecast_other,
    etotal_efficiency_th,
    before_etotal,
    power_time,
    init_time,
    capacity, create_time;

    public static StPsListInfoSortEnum findValue(String str) {
        if(str == null){
            return StPsListInfoSortEnum.create_time;
        }
        switch (str) {
            case "id":
                return StPsListInfoSortEnum.id;
            case "eDay":
                return StPsListInfoSortEnum.eday;
            case "forecastDay":
                return StPsListInfoSortEnum.eday_forecast_th;
            case "edayEfficiencyTh":
                return StPsListInfoSortEnum.eday_efficiency_th;
            case "eMonth":
                return StPsListInfoSortEnum.emonth;
            case "forecastMonth":
                return StPsListInfoSortEnum.emonth_forecast_th;
            case "emonthEfficiencyTh":
                return StPsListInfoSortEnum.emonth_efficiency_th;
            case "eYear":
                return StPsListInfoSortEnum.eyear;
            case "eyearForecastTh":
                return StPsListInfoSortEnum.eyear_forecast_th;
            case "eyearEfficiencyTh":
                return StPsListInfoSortEnum.eyear_efficiency_th;
            case "eTotal":
                return StPsListInfoSortEnum.etotal;
            case "etotalForecastTh":
                return StPsListInfoSortEnum.etotal_forecast_th;
            case "etotalEfficiencyTh":
                return StPsListInfoSortEnum.etotal_efficiency_th;
            case "powerTime":
                return StPsListInfoSortEnum.power_time;
            case "inverterTotle":
                return StPsListInfoSortEnum.inverter_etotal;
            case "btotle":
                return StPsListInfoSortEnum.before_etotal;
            case "capacity":
                return StPsListInfoSortEnum.capacity;
            case "initTime":
                return StPsListInfoSortEnum.init_time;
            case "createTime":
                return StPsListInfoSortEnum.create_time;
            default:
                return StPsListInfoSortEnum.create_time;
        }
    }
}
