package com.gcl.psmis.framework.taos.config.strategy.helper;

import com.gcl.psmis.framework.taos.domain.TdModel;
import com.gcl.psmis.framework.taos.config.strategy.IQueryTdService;
import org.springframework.beans.factory.InitializingBean;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * @className: QueryTdHelper
 * @author: xinan.yuan
 * @create: 2022/12/20 14:45
 * @desc: 实体类获取query实现
 */
@Component
public class QueryTdHelper implements InitializingBean {


    @Autowired
    List<IQueryTdService> queryTdServices;
    private static Map<Class<? extends TdModel>, IQueryTdService> queryTdHelper = new HashMap<>();


    @Override
    public void afterPropertiesSet() throws Exception {
        queryTdHelper = queryTdServices.stream().collect(Collectors.toMap(IQueryTdService::getEntityClass, Function.identity(), (existing, replacement) -> replacement));
    }

    public static IQueryTdService getService(Class<? extends TdModel> model) {
        IQueryTdService queryTdService = queryTdHelper.get(model);
        if(queryTdService==null){
            throw new RuntimeException("未找到model:"+model.getName()+"通用service！");
        }
        return queryTdService;
    }
}
