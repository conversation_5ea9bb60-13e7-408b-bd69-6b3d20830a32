package com.gcl.psmis.framework.taos.domain.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.gcl.psmis.framework.taos.annotation.TdTableName;
import com.gcl.psmis.framework.taos.domain.TdModel;
import lombok.*;

/**
* @className: IotPointAlarmStPointEntity
* @author: xinan.yuan
* @create: 2024/12/16 17:59
* @description:
*/
@EqualsAndHashCode(callSuper = true)
@AllArgsConstructor
@NoArgsConstructor
@Builder
@Data
@TdTableName(dbName = "iot_point_alarm", superTbName = "st_point", tablePrefix = "t_point", separator = "_", tableProperty = {"sn", "psCode","alarmCode"}, isLowercase = true)
public class IotPointAlarmStPointEntity extends TdModel {

    /**
     * sn tag
     */
    @TableField("sn")
    private String sn;

    @TableField("tplType")
    private Integer tplType;

    /**
     * ps_code tag
     */
    @TableField("ps_code")
    private String psCode;

    /**
     * 设备类型 tag
     */
    @TableField("device_type")
    private Integer deviceType;

    /**
     * 告警类型 tag
     */
    @TableField("alarm_code")
    private String alarmCode;


    /**
     * ts 时间戳 保存告警的开始时间
     */
    @TableField("ts")
    private String ts;


    /**
     * 设备号 普通列
     */
    @TableField("device_sn")
    private String deviceSn;


    /**
     * 告警类型 普通列
     */
    @TableField("alarm_type")
    private String alarmType;
    /**
     * 告警等级 普通列
     */
    @TableField("alarm_level")
    private Integer alarmLevel;

    /**
     * 告警状态 普通列 0未恢复 1已恢复
     */
    @TableField("alarm_status")
    private Integer alarmStatus;

    /**
     * 结束时间 tag
     */
    @TableField("end_time")
    private String endTime;

    /**
     * factory_code 厂家编码
     */
    @TableField("factory_code")
    private String factoryCode;

    /**
     * alarmName 告警名称
     */
    @TableField("alarm_name")
    private String alarmName;

    /**
     * 告警编号
     */
    @TableField("alarm_id")
    private String alarmId;


}
