package com.gcl.psmis.framework.taos.enums;

import lombok.Getter;

// 电站测点时间间隔枚举
@Getter
public enum IotPointEnum {
	FIVE_MINUTES("5m"),
	ONE_HOUR("1h"),
	ONE_DAY("1d"),
	ONE_WEEK("7d"),
	ONE_MONTH("1n"),
	ONE_YEAR("1y");


	private final String type;

	IotPointEnum(String type) {
		this.type = type;
	}

	public String getType() {
		return type;
	}

	public static boolean contains(String value) {
		if (value == null) {
			return false;
		}
		for (IotPointEnum pointEnum : IotPointEnum.values()) {
			if (pointEnum.name().equalsIgnoreCase(value)) {
				return true;
			}
		}
		return false;
	}

	public static IotPointEnum getIotPointEnumByValue(String value) {
		if (value == null) {
			return null;
		}
		for (IotPointEnum pointEnum : IotPointEnum.values()) {
			if (pointEnum.name().equalsIgnoreCase(value)) {
				return pointEnum;
			}
		}
		return null;
	}

}
