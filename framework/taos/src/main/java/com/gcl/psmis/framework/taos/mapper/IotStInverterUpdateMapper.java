package com.gcl.psmis.framework.taos.mapper;

import com.baomidou.dynamic.datasource.annotation.DS;
import com.gcl.psmis.framework.taos.domain.entity.IotStInverterEntity;
import com.gcl.psmis.framework.taos.domain.entity.IotStInverterSonEntity;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
* @className: StInverterUpdateMapper
* @author: xinan.yuan
* @create: 2023/7/19 08:42
* @description: 
*/
@Mapper
@DS("td")
public interface IotStInverterUpdateMapper {

    void insertBatch(@Param("iotList") List<IotStInverterSonEntity> iotList);


    void insertBatchEntity(@Param("entityList") List<IotStInverterEntity> entityList);

}
