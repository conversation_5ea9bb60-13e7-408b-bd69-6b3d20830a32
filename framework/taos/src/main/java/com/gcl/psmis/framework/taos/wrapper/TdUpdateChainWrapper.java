package com.gcl.psmis.framework.taos.wrapper;

import cn.hutool.core.util.StrUtil;
import com.baomidou.dynamic.datasource.annotation.Slave;
import com.baomidou.mybatisplus.core.toolkit.support.SFunction;
import com.gcl.psmis.framework.taos.config.strategy.IUpdateTdService;
import com.gcl.psmis.framework.taos.config.strategy.helper.UpdateTdHelper;
import com.gcl.psmis.framework.taos.domain.TdModel;
import com.gcl.psmis.framework.taos.enums.TaosEnums;
import com.gcl.psmis.framework.taos.util.FieldUtil;
import lombok.extern.slf4j.Slf4j;
import lombok.val;
import org.apache.commons.lang3.StringUtils;
import org.springframework.util.CollectionUtils;

import java.lang.reflect.Field;
import java.util.Collection;
import java.util.List;


/**
 * @className: TdUpdateChainWrapper
 * @author: xinan.yuan
 * @create: 2022/12/16 11:39
 * @desc: 数据修改、插入tdegnine库操作封装
 */
@Slf4j
public class TdUpdateChainWrapper<T extends TdModel> extends TdQueryChain<TdUpdateChainWrapper<T>,T> {

    /**
     * 数据库表映射实体类
     */
    private Class<T> entityClass;

    protected TdUpdateChainWrapper(){
    }

    private IUpdateTdService service;
    public TdUpdateChainWrapper(Class<T> entityClass) {
        this.entityClass=entityClass;
        this.service = UpdateTdHelper.getService(this.entityClass);
        super.conditionSql=new StringBuffer();
    }

    /**
     * 插入数据
     * @param entity
     * @return
     */
    public boolean save(T entity) {
        return service.save(entity);
    }


    /**
     * 插入（批量）
     * @param entityList
     * @return
     */
    public boolean saveBatch(List<T> entityList) {
        if(CollectionUtils.isEmpty(entityList)){
            return true;
        }
        return service.saveBatch(entityList);
    }

    /**
     * 插入并创建表
     * @param entity
     * @return
     */
    public boolean saveAndCreate(T entity) {
        return service.saveAndCreate(entity);
    }

    /**
     * 更新Tag
     * @param entity 用于构造表名(具体到子表)
     * @param column 更新tag字段名
     * @param val tag值
     * @return
     */
    public boolean updateTag(T entity,SFunction<T,?> column, Object val) {
        Field field= FieldUtil.getField(column);
        String columnName= StrUtil.toUnderlineCase(field.getName());
        Class<?> type = field.getType();
        if(String.class.isAssignableFrom(type)){
            val="'"+val+"'";
        }
        return service.updateTag(entity,columnName,val);
    }


    /**
     * 先查询匹配表，再更新Tag
     * @param column 更新tag字段名
     * @param val tag值
     * @return
     */
    public boolean queryAndUpdateTag(SFunction<T,?> column, Object val) {
        Field field= FieldUtil.getField(column);
        String columnName= StrUtil.toUnderlineCase(field.getName());
        Class<?> type = field.getType();
        if(String.class.isAssignableFrom(type)){
            val="'"+val+"'";
        }
        if(StringUtils.isBlank(super.conditionSql.toString())){
            throw new RuntimeException("条件不能为空！");
        }
        return service.queryAndUpdateTag(columnName,val,super.conditionSql.toString());
    }

    /**
     * @desc 超级表添加字段
     * @date 2023/7/18 11:24
     * @param column
     * @param columnType
     * @return {@link boolean}
     */
    public boolean addColumn(String column, TaosEnums.ColumnType columnType) {
        Field field = null;
        try {
            field = entityClass.getField(StrUtil.toCamelCase(column));
        } catch (NoSuchFieldException e) {
            log.warn("{}对象未设置{}字段属性请及时添加！否则将无法在业务系统中查询到次字段！",entityClass.getName(),column);
        }

        return service.addColumn(column,columnType);
    }

}
