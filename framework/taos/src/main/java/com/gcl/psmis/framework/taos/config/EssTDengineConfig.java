package com.gcl.psmis.framework.taos.config;


import lombok.Data;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Configuration;

/**
 * @className TDengineConfig
 * <AUTHOR>
 * @create 2022/9/6 15:50
 * @desc tdengine config
 **/
@Configuration
@Data
public class EssTDengineConfig {
    @Value("${spring.datasource.dynamic.datasource.ess-td.driver-class-name:''}")
    private String driverClassName;
    @Value("${spring.datasource.dynamic.datasource.ess-td.url:''}")
    private String url;
    @Value("${spring.datasource.dynamic.datasource.ess-td.username:''}")
    private String username;
    @Value("${spring.datasource.dynamic.datasource.ess-td.password:''}")
    private String password;

}
