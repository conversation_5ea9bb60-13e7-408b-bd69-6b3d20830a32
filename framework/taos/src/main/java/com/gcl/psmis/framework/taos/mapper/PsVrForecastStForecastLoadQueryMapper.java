package com.gcl.psmis.framework.taos.mapper;

import com.baomidou.dynamic.datasource.annotation.DS;
import com.gcl.psmis.framework.taos.domain.entity.PsVrForecastStForecastLoadEntity;
import com.gcl.psmis.framework.taos.domain.entity.PsVrIot15StDeviceEntity;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 预测负荷
 */
@Mapper
@DS("td")
public interface PsVrForecastStForecastLoadQueryMapper {
    

    List<PsVrForecastStForecastLoadEntity> list(@Param("conditionSql")String conditionSql);

    PsVrForecastStForecastLoadEntity last(@Param("conditionSql")String conditionSql);

}
