package com.gcl.psmis.framework.taos.wrapper;

import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.enums.SqlKeyword;
import com.baomidou.mybatisplus.core.toolkit.StringPool;
import com.baomidou.mybatisplus.core.toolkit.support.SFunction;
import com.gcl.psmis.framework.taos.util.FieldUtil;
import org.apache.commons.lang3.StringUtils;
import org.springframework.util.CollectionUtils;

import java.lang.reflect.Field;
import java.util.Collection;
import java.util.List;
import java.util.stream.Collectors;


/**
 * @className: TdQueryChain
 * @author: xinan.yuan
 * @create: 2022/12/20 15:58
 * @desc: 查询条件
 */
public abstract class TdQueryChain<E extends TdQueryChain<E,T>,T>  {


    protected StringBuffer conditionSql;

    protected boolean permissionFlag=true;

    protected final E typedThis = (E) this;


    /**
     * @desc 无权限过滤
     * @date 2023/11/27 16:52
     * @param
     * @return {@link E}
     */
    public E noPermission(){
        permissionFlag=false;
        return typedThis;
    }


    /**
     * 等于 =
     * @param column 字段
     * @param val 值
     * @return
     */
    public E eq(SFunction<T, ?> column, Object val){
        Field field= FieldUtil.getField(column);
        String columnName= StrUtil.toUnderlineCase(field.getName());
        return eq(columnName,val);
    }

    /**
     * 等于 =
     * @param column 字段
     * @param val 值
     * @return
     */
    public E eq(String column, Object val){
        conditionSql.append(" and ").append(column);
        this.addValue(SqlKeyword.EQ,val);
        return typedThis;
    }

    /**
     * 不等于 !=
     * @param column 字段
     * @param val 值
     * @return
     */
    public E ne(SFunction<T, ?> column, Object val){
        Field field= FieldUtil.getField(column);
        String columnName= StrUtil.toUnderlineCase(field.getName());
        return ne(columnName,val);
    }

    /**
     * 不等于 !=
     * @param column 字段
     * @param val 值
     * @return
     */
    public E ne(String column, Object val){
        conditionSql.append(" and ").append(column);
        this.addValue(SqlKeyword.NE,val);
        return typedThis;
    }

    /**
     * 大于 >
     * @param column 字段
     * @param val 值
     * @return
     */
    public E gt(SFunction<T,?> column, Object val){
        Field field= FieldUtil.getField(column);
        String columnName= StrUtil.toUnderlineCase(field.getName());
        return this.gt(columnName,val);
    }

    /**
     * 大于 >
     * @param column 字段
     * @param val 值
     * @return
     */
    public E gt(String column, Object val){
        conditionSql.append(" and ").append(column);
        this.addValue(SqlKeyword.GT,val);
        return typedThis;
    }

    /**
     * 大于等于 >=
     * @param column 字段
     * @param val 值
     * @return
     */
    public E ge(SFunction<T,?> column, Object val){
        Field field= FieldUtil.getField(column);
        String columnName= StrUtil.toUnderlineCase(field.getName());
        return this.ge(columnName,val);
    }

    /**
     * 大于等于 >=
     * @param column 字段
     * @param val 值
     * @return
     */
    public E ge(String column, Object val){
        conditionSql.append(" and ").append(column);
        this.addValue(SqlKeyword.GE,val);
        return typedThis;
    }

    /**
     * 小于 <
     * @param column 字段
     * @param val 值
     * @return
     */
    public E lt(SFunction<T,?> column, Object val){
        Field field= FieldUtil.getField(column);
        String columnName= StrUtil.toUnderlineCase(field.getName());
        return this.lt(columnName,val);
    }

    /**
     * 小于 <
     * @param column 字段
     * @param val 值
     * @return
     */
    public E lt(String column, Object val){
        conditionSql.append(" and ").append(column);
        this.addValue(SqlKeyword.LT,val);
        return typedThis;
    }

    /**
     * 小于等于 <=
     * @param column 字段
     * @param val 值
     * @return
     */
    public E le(SFunction<T,?> column, Object val){
        Field field= FieldUtil.getField(column);
        String columnName= StrUtil.toUnderlineCase(field.getName());
        return this.le(columnName,val);
    }

    /**
     * 小于等于 <=
     * @param column 字段
     * @param val 值
     * @return
     */
    public E le(String column, Object val){
        conditionSql.append(" and ").append(column);
        this.addValue(SqlKeyword.LE,val);
        return typedThis;
    }

    private void addValue(SqlKeyword keyword,Object val){
        conditionSql.append(" "+keyword.getSqlSegment()+" ");
        if(val instanceof String){
            val="'"+val+"'";
        }
        conditionSql.append(val);
    }

    /**
     * in
     * @param column
     * @param coll
     * @return
     */
    public E in(SFunction<T,?> column, Collection<?> coll) {
        if(CollectionUtils.isEmpty(coll)){
            throw new RuntimeException("in 查询集合不能为空！！！");
        }
        for (Object o : coll) {
            if(StringUtils.isBlank(o.toString())){
                throw new RuntimeException("in 查询集合不能有空值！！！");
            }
        }
        Field field= FieldUtil.getField(column);
        String columnName= StrUtil.toUnderlineCase(field.getName());
        conditionSql.append(" and ").append(columnName).append(" ")
                .append(SqlKeyword.IN).append(" ").append(inExpression(coll));
        return typedThis;
    }

    /**
     * 获取in表达式 包含括号
     *
     * @param value 集合
     */
    public String inExpression(Collection<?> value) {
        List<String> values = value.stream().map(i -> {
            String val = "";
            if (i instanceof String) {
                val = "'" + i + "'";
            } else {
                val = i.toString();
            }
            return val;
        }).collect(Collectors.toList());
        String inValues = String.join(StringPool.COMMA, values);

        return StringPool.LEFT_BRACKET+inValues+StringPool.RIGHT_BRACKET;
    }


}
