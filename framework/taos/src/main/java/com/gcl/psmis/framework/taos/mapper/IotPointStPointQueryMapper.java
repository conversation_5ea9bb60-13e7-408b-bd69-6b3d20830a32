package com.gcl.psmis.framework.taos.mapper;

import com.baomidou.dynamic.datasource.annotation.DS;
import com.gcl.psmis.framework.taos.domain.entity.IotPointStPointEntity;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;
import java.util.Map;


@Mapper
@DS("ess-td")
public interface IotPointStPointQueryMapper {

    // 根据设备进行分组
    List<Map<String, Object>> convertListSn(@Param("fieldNameList") List<String> fieldNameList, @Param("conditionSql") String conditionSql);

    // 根据电站进行分组、ts
    List<Map<String, Object>> convertListPsTsCode(@Param("fieldNameList") List<String> fieldNameList, @Param("conditionSql") String conditionSql);

    // 根据电站进行分组、一天聚合一条数据
    List<Map<String, Object>> convertListDay(@Param("fieldNameList") List<String> fieldNameList, @Param("conditionSql") String conditionSql);

    // 根据电站进行分组
    List<Map<String, Object>> convertListPsCode(@Param("fieldNameList") List<String> fieldNameList, @Param("conditionSql") String conditionSql);


    List<IotPointStPointEntity> batchLast(@Param("conditionSql")String conditionSql);

    List<IotPointStPointEntity> list(@Param("conditionSql") String conditionSql);
}
