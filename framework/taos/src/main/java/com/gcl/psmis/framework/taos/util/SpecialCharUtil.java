package com.gcl.psmis.framework.taos.util;

public class SpecialCharUtil {

    //子表名只能由字母、数字和下划线组成，且不能以数字开头，不区分大小写
    public static final String regex = "[^a-zA-Z0-9_]";

    /**
     * @desc 格式化子表名
     * @date 2023/7/24 14:05
     * @param tableName
     * @return {@link String}
     */
    public static String formatTableName(String tableName) {
        String result = tableName.replaceAll(regex, "");
        return result;
    }

}
