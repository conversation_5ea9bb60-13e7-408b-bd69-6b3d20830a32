package com.gcl.psmis.framework.taos.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

@Getter
@AllArgsConstructor
public enum PsCalSortEnum {
    cal_date,
    create_time,
    fault_time,
    offline_time,
    forcast_ratio,
    stop_start_time,
    stop_end_time;

    public static PsCalSortEnum findValue(String str) {
        if (str == null) {
            return PsCalSortEnum.create_time;
        }
        switch (str) {
            case "calDate":
                return PsCalSortEnum.cal_date;
            case "faultTime":
                return PsCalSortEnum.fault_time;
            case "offlineTime":
                return PsCalSortEnum.offline_time;
            case "forcastRatio":
                return PsCalSortEnum.forcast_ratio;
            case "stopStartTime":
                return PsCalSortEnum.stop_start_time;
            case "forecastMonth":
                return PsCalSortEnum.stop_end_time;
            case "createTime":
                return PsCalSortEnum.create_time;
            default:
                return PsCalSortEnum.create_time;
        }
    }
}
