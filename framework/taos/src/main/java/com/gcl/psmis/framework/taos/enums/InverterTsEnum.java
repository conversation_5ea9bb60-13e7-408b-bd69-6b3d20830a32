package com.gcl.psmis.framework.taos.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * @className InverterTsEnum
 * <AUTHOR>
 * @create 2022/8/18 10:28
 * @desc 逆变器分时数据字段
 **/
@Getter
@AllArgsConstructor
public enum InverterTsEnum  {
	create_time,
	protocol_version,
	e_day,
	e_month,
	e_total,
	t_total,
	e_yesterday,
	e_last_month,
	server_time,
	is_original_data_flag,
	is_actual_data_flag,
	temperature,
	error_code,
	alert_code,
	pac,
	pac_ratio,
	active_power,
	reactive_power,
	inspecting_power,
	power_factor,
	power_curve_number,
	switch_status_flag,
	pen_check_flag,
	display_version,
	status,
	national_standard,
	error_message,
	filter_flag,
	extension,
	vpv1,
	vpv2,
	vpv3,
	vpv4,
	vpv5,
	vpv6,
	vpv7,
	vpv8,
	vpv9,
	vpv10,
	vpv11,
	vpv12,
	vpv13,
	vpv14,
	vpv15,
	vpv16,
	vpv17,
	vpv18,
	vpv19,
	vpv20,
	vpv21,
	vpv22,
	vpv23,
	vpv24,
	vpv25,
	vpv26,
	vpv27,
	vpv28,
	vpv29,
	vpv30,
	ipv1,
	ipv2,
	ipv3,
	ipv4,
	ipv5,
	ipv6,
	ipv7,
	ipv8,
	ipv9,
	ipv10,
	ipv11,
	ipv12,
	ipv13,
	ipv14,
	ipv15,
	ipv16,
	ipv17,
	ipv18,
	ipv19,
	ipv20,
	ipv21,
	ipv22,
	ipv23,
	ipv24,
	ipv25,
	ipv26,
	ipv27,
	ipv28,
	ipv29,
	ipv30,
	vac1,
	vac2,
	vac3,
	iac1,
	iac2,
	iac3,
	fac1,
	fac2,
	fac3,
	reserved05,
	reserved06,
	reserved07,
	reserved08,
	reserved09,
	reserved10,
	reserved11,
	m_id,
	inverter_sn,
	collector_sn,
	collector_sn2,
	inverter_model,
	supplier,;

	public static boolean contains(String value) {
		for (int i = 0; i < InverterTsEnum.values().length; i++) {
			if (InverterTsEnum.values()[i].name().equalsIgnoreCase(value)) {
				return true;
			}
		}
		return false;
	}

	public static InverterTsEnum getInverterTsEnumByValue(String value) {
		for (int i = 0; i < InverterTsEnum.values().length; i++) {
			if (InverterTsEnum.values()[i].name().equalsIgnoreCase(value)) {
				return InverterTsEnum.values()[i];
			}
		}
		return null;
	}

}
