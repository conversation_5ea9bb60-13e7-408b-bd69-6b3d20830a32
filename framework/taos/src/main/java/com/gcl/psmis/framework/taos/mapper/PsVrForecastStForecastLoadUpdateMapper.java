package com.gcl.psmis.framework.taos.mapper;

import com.baomidou.dynamic.datasource.annotation.DS;
import com.gcl.psmis.framework.taos.domain.entity.PsVrForecastStForecastLoadEntity;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
* @className: PsVrIotStDeviceUpdateMapper
* @author: xinan.yuan
* @create: 2024/2/23 15:01
* @description:
*/
@Mapper
@DS("td")
public interface PsVrForecastStForecastLoadUpdateMapper {

    void insertBatch(@Param("psVrForecastStForecastLoadEntities") List<PsVrForecastStForecastLoadEntity> psVrForecastStForecastLoadEntities);

}
