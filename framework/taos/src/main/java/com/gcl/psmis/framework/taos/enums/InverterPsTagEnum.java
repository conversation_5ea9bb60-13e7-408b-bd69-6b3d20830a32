package com.gcl.psmis.framework.taos.enums;

import lombok.Getter;

/**
 * @className InverterPsTagEnum
 * <AUTHOR>
 * @create 2022/8/25 16:35
 * @desc 电站tag
 **/
@Getter
public enum InverterPsTagEnum {
	ps_id("VARCHAR"),
	sn("VARCHAR"),
	inverter_brand("VARCHAR"),
	sn_list("VARCHAR"),
	brand_list("VARCHAR"),
	owner("VARCHAR"),
	phone_number("VARCHAR"),
	user_id("LONG"),
	company_no("VARCHAR"),
	company_name("VARCHAR"),
	loan_type_code("VARCHAR"),
	create_time("TIMESTAMP"),
	power_time("TIMESTAMP"),
	province_id("VARCHAR"),
	city_id("VARCHAR"),
	region_id("VARCHAR"),
	loan_no("VARCHAR"),
	ps_code("VARCHAR"),
	dealer_code("VARCHAR"),
	subsidy("DOUBLE"),
	is_subsidy("INT"),
	recover_offline_time("TIMESTAMP"),
	recover_exception_time("TIMESTAMP"),
	create_by("VARCHAR"),
	maintenance_end_date("TIMESTAMP"),
	fault_time("TIMESTAMP"),
	inverter_maintenance_date("TIMESTAMP"),
	insure_end_date("TIMESTAMP"),
	finance_province_id("VARCHAR"),
	finance_city_id("VARCHAR"),
	finance_region_id("VARCHAR"),
	remarks("VARCHAR"),
	remarks_time("TIMESTAMP"),
	exception_type("INT"),
	alert_state("INT"),
	offline_state("INT"),
	offline_time("TIMESTAMP"),
	offlinereturn_time("TIMESTAMP"),
	runtime_state("INT"),
	capacity("INT"),
	statekwhr_total("DOUBLE"),
	statekwhr_related_forcast("DOUBLE"),
	ps_name("VARCHAR"),
	ps_type("INT"),
	is_bind("INT"),
	install_level("INT"),
	create_source("INT");


	private final String type;

	InverterPsTagEnum(String type) {
		this.type = type;
	}

	public String getType() {
		return type;
	}

	public static boolean contains(String value) {
		for (int i = 0; i < InverterPsTagEnum.values().length; i++) {
			if (InverterPsTagEnum.values()[i].name().equalsIgnoreCase(value)) {
				return true;
			}
		}
		return false;
	}

	public static InverterPsTagEnum getInverterTsEnumByValue(String value) {
		for (int i = 0; i < InverterPsTagEnum.values().length; i++) {
			if (InverterPsTagEnum.values()[i].name().equalsIgnoreCase(value)) {
				return InverterPsTagEnum.values()[i];
			}
		}
		return null;
	}

}
