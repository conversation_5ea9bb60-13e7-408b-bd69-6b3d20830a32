package com.gcl.psmis.framework.taos.mapper;

import com.baomidou.dynamic.datasource.annotation.DS;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.gcl.psmis.framework.taos.domain.entity.IotPointAlarmStPointEntity;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
* @className: IotPointAlarmStPointQueryMapper
* @author: xinan.yuan
* @create: 2024/12/16 18:24
* @description:
*/
@Mapper
@DS("ess-td")
public interface IotPointAlarmStPointQueryMapper {

    List<IotPointAlarmStPointEntity> list(@Param("conditionSql")String conditionSql);
    IPage<IotPointAlarmStPointEntity> listPage(@Param("page")IPage<IotPointAlarmStPointEntity> page,@Param("conditionSql")String conditionSql);


    IotPointAlarmStPointEntity first(@Param("conditionSql") String conditionSql);

    IotPointAlarmStPointEntity last(@Param("conditionSql") String conditionSql);

    List<IotPointAlarmStPointEntity> groupBy(@Param("columnName") String columnName, @Param("conditionSql") String conditionSql);
}
