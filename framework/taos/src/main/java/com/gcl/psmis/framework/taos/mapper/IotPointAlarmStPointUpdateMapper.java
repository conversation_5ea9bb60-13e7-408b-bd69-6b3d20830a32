package com.gcl.psmis.framework.taos.mapper;

import com.baomidou.dynamic.datasource.annotation.DS;
import com.gcl.psmis.framework.taos.domain.entity.IotPointAlarmStPointEntity;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
* @className: IotPointAlarmStPointUpdateMapper
* @author: xinan.yuan
* @create: 2024/12/18 11:19
* @description: 
*/
@Mapper
@DS("td")
public interface IotPointAlarmStPointUpdateMapper {

    void insertBatch(@Param("entityList") List<IotPointAlarmStPointEntity> entityList);


    void insert(@Param("entity")IotPointAlarmStPointEntity entity);
}
