package com.gcl.psmis.framework.taos.domain.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.gcl.psmis.framework.taos.annotation.TdTableName;
import com.gcl.psmis.framework.taos.domain.TdModel;
import lombok.*;

/**
* @author: xjl
* @create: 20230807 11:00
* @description: 告警记录
*/
@EqualsAndHashCode(callSuper = true)
@AllArgsConstructor
@NoArgsConstructor
@Builder
@Data
@TdTableName(dbName = "ps_iot_alarm", superTbName = "st_inverter", tablePrefix = "t_inverter", separator = "_", tableProperty = {"deviceSn", "psStationId","faultCode"}, isLowercase = true)
public class PsIotAlarmStInverterEntity extends TdModel {

    /**
     * 设备号 普通列
     */
    @TableField("device_sn")
    private String deviceSn;
    /**
     * 电站id 普通列
     */
    @TableField("ps_station_id")
    private String psStationId;
    /**
     * 规则名称 普通列
     */
    @TableField("rule_name")
    private String ruleName;
    /**
     * 告警类型 普通列
     */
    @TableField("alarm_type")
    private String alarmType;
    /**
     * 告警等级 普通列
     */
    @TableField("alarm_level")
    private Integer alarmLevel;
    /**
     * 告警状态 普通列 (group 数量)
     */
    @TableField("alarm_status")
    private Integer alarmStatus;

    /**
     * 结束时间 tag
     */
    @TableField("end_time")
    private String endTime;

    /**
     * sn tag
     */
    @TableField("sn")
    private String sn;
    /**
     * 电站id tag
     */
    @TableField("ps_id")
    private String psId;
    /**
     * 电站类型 tag
     */
    @TableField("ps_type")
    private Integer psType;

    //0户用公司 1开发公司
    @TableField("dev_type")
    private Integer devType;

    /**
     * 是否是er 告警
     */
    @TableField("er_flag")
    private Integer erFlag;

    /**
     * 设备类型 tag
     */
    @TableField("device_type")
    private String deviceType;

    /**
     * 规则code tag
     */
    @TableField("rule_code")
    private String ruleCode;

    /**
     * 系统统一告警编码 tag
     */
    @TableField("fault_code")
    private String faultCode;

    /**
     * 是否并网 tag
     */
    @TableField("ar_flag")
    private Integer arFlag;

    /**
     * 逆变器品牌 tag
     */
    @TableField("inverter_brand")
    private String inverterBrand;

    /**
     * 服务商code tag
     */
    @TableField("service_org_code")
    private String serviceOrgCode;

    /**
     * 服务商负责人id tag
     */
    @TableField("service_emp_id")
    private String serviceEmpId;


    /**
     * ts 时间戳 保存告警的开始时间
     */
    @TableField("ts")
    private String ts;

    /**
     * 告警编号
     */
    @TableField("alarm_id")
    private String alarmId;


    /**
     * 置牌状态 0 不置牌 1 置牌
     */
    @TableField("upFlag")
    private Integer upFlag;


    //省id
    @TableField("province_id")
    private String provinceId;

    //市id
    @TableField("city_id")
    private String cityId;

    //区id
    @TableField("region_id")
    private String regionId;

    //县id
    @TableField("town_id")
    private String townId;

    //所属省公司Code
    @TableField("province_company_code")
    private String provinceCompanyCode;

    private double pac;//功率
    private int capacity;//容量
    private double regionPacAvg;//区单瓦功率均值
    private String roofTypeCode;//场景编码

}
