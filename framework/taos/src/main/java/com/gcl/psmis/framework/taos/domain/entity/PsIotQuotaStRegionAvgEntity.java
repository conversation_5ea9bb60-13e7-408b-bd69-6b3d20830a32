package com.gcl.psmis.framework.taos.domain.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.gcl.psmis.framework.taos.annotation.TdTableName;
import com.gcl.psmis.framework.taos.domain.TdModel;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

/**
* @author: xjl
* @create: 20230807 11:00
* @description: 区域单瓦功率均值
*/
@EqualsAndHashCode(callSuper = true)
@AllArgsConstructor
@NoArgsConstructor
@Data
@TdTableName(dbName = "ps_iot_quota", superTbName = "st_region_avg", tablePrefix = "t_region_avg", separator = "_", tableProperty = {"regionId", "tbRoofTypeCode"}, isLowercase = true)
public class PsIotQuotaStRegionAvgEntity extends TdModel {

    /**
     * 区域单瓦功率均值 普通列
     */
    @TableField("avg_value")
    private String avgValue;

    //区id tag
    @TableField("region_id")
    private String regionId;

    //场景编码 tag
    @TableField("roof_type_code")
    private String roofTypeCode;

    /**
     * ts 时间戳
     */
    @TableField("ts")
    private String ts;

    //场景编码
    private String tbRoofTypeCode;//生成表名时使用 适配为null的情况


}
