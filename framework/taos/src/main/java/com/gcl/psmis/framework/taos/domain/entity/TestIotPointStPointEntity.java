package com.gcl.psmis.framework.taos.domain.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.gcl.psmis.framework.taos.annotation.TdTableName;
import com.gcl.psmis.framework.taos.domain.TdModel;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;


@Data
public class TestIotPointStPointEntity {

    private String dayTime;

    private String ps_code;

    private Float PCS7Yc;

    private Float PCS6Yc;
    
}
