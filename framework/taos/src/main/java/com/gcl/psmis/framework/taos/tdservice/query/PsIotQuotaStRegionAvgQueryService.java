package com.gcl.psmis.framework.taos.tdservice.query;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.gcl.psmis.framework.taos.config.strategy.IQueryTdService;
import com.gcl.psmis.framework.taos.domain.dto.TdTableField;
import com.gcl.psmis.framework.taos.domain.entity.PsIotQuotaStRegionAvgEntity;
import com.gcl.psmis.framework.taos.mapper.PsIotQuotaStRegionAvgMapper;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * @className: PsIotQuotaStRegionAvgQueryService
 * @author: wuchenyu
 * @description:
 * @date: 2023/8/9 15:30
 * @version: 1.0
 */
@Service
@Slf4j
public class PsIotQuotaStRegionAvgQueryService implements IQueryTdService<PsIotQuotaStRegionAvgEntity> {

    @Autowired
    private PsIotQuotaStRegionAvgMapper psIotQuotaStRegionAvgMapper;



    @Override
    public List<PsIotQuotaStRegionAvgEntity> list(String conditionSql) {
        return psIotQuotaStRegionAvgMapper.list(conditionSql);
    }

    @Override
    public List<PsIotQuotaStRegionAvgEntity> listEvery5M(String conditionSql) {
        return null;
    }

    @Override
    public List<PsIotQuotaStRegionAvgEntity> listEvery1H(String conditionSql) {
        return null;
    }

    @Override
    public List<PsIotQuotaStRegionAvgEntity> listEvery1Day(String conditionSql) {
        return null;
    }

    @Override
    public List<PsIotQuotaStRegionAvgEntity> listEvery1Month(String conditionSql) {
        return null;
    }

    @Override
    public List<PsIotQuotaStRegionAvgEntity> listEvery1Year(String conditionSql) {
        return null;
    }

    @Override
    public PsIotQuotaStRegionAvgEntity lastRow(String conditionSql) {
        return null;
    }

    @Override
    public IPage<PsIotQuotaStRegionAvgEntity> pageList(IPage<PsIotQuotaStRegionAvgEntity> page, String conditionSql) {
        return null;
    }

    @Override
    public PsIotQuotaStRegionAvgEntity firstRow(String toString) {
        return null;
    }

    @Override
    public PsIotQuotaStRegionAvgEntity first(String conditionSql) {
        return null;
    }

    @Override
    public PsIotQuotaStRegionAvgEntity last(String toString) {
        return null;
    }

    @Override
    public List<PsIotQuotaStRegionAvgEntity> batchLast(String conditionSql) {
        return null;
    }

    @Override
    public List<PsIotQuotaStRegionAvgEntity> batchFirst(String conditionSql) {
        return null;
    }

    @Override
    public List<TdTableField> getAllColumns() {
        return null;
    }

    @Override
    public List<PsIotQuotaStRegionAvgEntity> groupBy(String columnName, String conditionSql) {
        return null;
    }

    @Override
    public <R> List<R> convertList (Class<R> clazz,String conditionSql) {
        return null;
    }
}
