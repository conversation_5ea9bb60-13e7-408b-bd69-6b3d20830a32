package com.gcl.psmis.framework.taos.domain.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.gcl.psmis.framework.taos.annotation.TdTableName;
import com.gcl.psmis.framework.taos.domain.TdModel;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;
import java.sql.Timestamp;

/**
 * @className: IotStInverterSonEntity
 * @author: wuchenyu
 * @description:
 * @date: 2023/12/19 9:01
 * @version: 1.0
 */
@Data
@TdTableName(dbName = "iot", superTbName = "st_inverter", tablePrefix = "t_inverter", separator = "_", tableProperty = {"sn"}, isLowercase = true)
public class IotStInverterSonEntity extends TdModel {

    /**
     * sn tag
     */
    @TableField("sn")
    private String sn;

    @TableField("receive_time")
    private String receiveTime;

    /**
     * ts 时间戳
     */
    @TableField("ts")
    private String ts;
    /**
     * 采集器序列号
     */
    @TableField("csn")
    private String csn;

    /**
     * isn 逆变器序列号
     */
    @TableField("isn")
    private String isn;

    /**
     * 内部温度
     */
    @TableField("tmp")
    private String tmp;

    /**
     * 总发电量
     */
    @TableField("eto")
    private String eto;

    /**
     * 总工作时长
     */
    @TableField("hto")
    private String hto;

    /**
     * W相温度
     */
    @TableField("tw")
    private String tw;

    /**
     * invdata
     */
    @TableField("data_tag")
    private String dataTag;

    /**
     * 4G信号强度
     */
    @TableField("csq")
    private String csq;

    /**
     * 数据类型标识符
     */
    @TableField("flg")
    private String flg;

    /**
     * 采集逆变器数据时间
     */
    @TableField("date")
    private String date;

    /**
     * 电网频率
     */
    @TableField("fac")
    private String fac;

    /**
     * 有功功率
     */
    @TableField("pac")
    private String pac;

    /**
     * 日发电量
     */
    @TableField("etd")
    private String etd;

    /**
     * 故障码
     */
    @TableField("er")
    private String er;

    /**
     * 功率因素
     */
    @TableField("pf")
    private String pf;

    /**
     * L1 相电压
     */
    @TableField("va1")
    private String va1;

    /**
     * L2 相电压
     */
    @TableField("va2")
    private String va2;

    /**
     * L3 相电压
     */
    @TableField("va3")
    private String va3;

    /**
     * L1 相电流
     */
    @TableField("ia1")
    private String ia1;

    /**
     * L2 相电流
     */
    @TableField("ia2")
    private String ia2;

    /**
     * L3 相电流
     */
    @TableField("ia3")
    private String ia3;

    /**
     * 无功功率
     */
    @TableField("prc")
    private String prc;

    /**
     *  视在功率
     */
    @TableField("sac")
    private String sac;

    /**
     * 输出功率
     */
    @TableField("输出功率")
    private String power;

    /**
     * 第1路MPPT电压
     */
    @TableField("v1")
    private String v1;

    /**
     * 第2路MPPT电压
     */
    @TableField("v2")
    private String v2;

    /**
     * 第3路MPPT电压
     */
    @TableField("v3")
    private String v3;

    /**
     * 第4路MPPT电压
     */
    @TableField("v4")
    private String v4;

    /**
     * 第5路MPPT电压
     */
    @TableField("v5")
    private String v5;

    /**
     * 第6路MPPT电压
     */
    @TableField("v6")
    private String v6;

    /**
     * 第7路MPPT电压
     */
    @TableField("v7")
    private String v7;

    /**
     * 第8路MPPT电压
     */
    @TableField("v8")
    private String v8;

    /**
     * 第9路MPPT电压
     */
    @TableField("v9")
    private String v9;

    /**
     * 第10路MPPT电压
     */
    @TableField("v10")
    private String v10;


    /**
     * 第1路MPPT电流
     */
    @TableField("i1")
    private String i1;

    /**
     * 第2路MPPT电流
     */
    @TableField("i2")
    private String i2;

    /**
     * 第3路MPPT电流
     */
    @TableField("i3")
    private String i3;

    /**
     * 第4路MPPT电流
     */
    @TableField("i4")
    private String i4;

    /**
     * 第5路MPPT电流
     */
    @TableField("i5")
    private String i5;

    /**
     * 第6路MPPT电流
     */
    @TableField("i6")
    private String i6;

    /**
     * 第7路MPPT电流
     */
    @TableField("i7")
    private String i7;

    /**
     * 第8路MPPT电流
     */
    @TableField("i8")
    private String i8;

    /**
     * 第9路MPPT电流
     */
    @TableField("i9")
    private String i9;

    /**
     * 第10路MPPT电流
     */
    @TableField("i10")
    private String i10;

    /**
     * 第1组串电流
     */
    @TableField("s1")
    private String s1;

    /**
     * 第2组串电流
     */
    @TableField("s2")
    private String s2;

    /**
     * 第3组串电流
     */
    @TableField("s3")
    private String s3;

    /**
     * 第4组串电流
     */
    @TableField("s4")
    private String s4;

    /**
     * 第5组串电流
     */
    @TableField("s5")
    private String s5;

    /**
     * 第6组串电流
     */
    @TableField("s6")
    private String s6;

    /**
     * 第7组串电流
     */
    @TableField("s7")
    private String s7;

    /**
     * 第8组串电流
     */
    @TableField("s8")
    private String s8;

    /**
     * 第9组串电流
     */
    @TableField("s9")
    private String s9;

    /**
     * 第10组串电流
     */
    @TableField("s10")
    private String s10;

    /**
     * 第11组串电流
     */
    @TableField("s11")
    private String s11;

    /**
     * 第12组串电流
     */
    @TableField("s12")
    private String s12;

    /**
     * 第13组串电流
     */
    @TableField("s13")
    private String s13;

    /**
     * 第14组串电流
     */
    @TableField("s14")
    private String s14;

    /**
     * 第15组串电流
     */
    @TableField("s15")
    private String s15;

    /**
     * 第16组串电流
     */
    @TableField("s16")
    private String s16;

    /**
     * 第17组串电流
     */
    @TableField("s17")
    private String s17;

    /**
     * 第18组串电流
     */
    @TableField("s18")
    private String s18;

    /**
     * 第19组串电流
     */
    @TableField("s19")
    private String s19;

    /**
     * 第20组串电流
     */
    @TableField("s20")
    private String s20;

    /**
     * 组串1输入电压
     */
    @TableField("sv1")
    private String sv1;

    /**
     * 组串2输入电压
     */
    @TableField("sv2")
    private String sv2;

    /**
     * 组串3输入电压
     */
    @TableField("sv3")
    private String sv3;

    /**
     * 组串4输入电压
     */
    @TableField("sv4")
    private String sv4;

    /**
     * 组串5输入电压
     */
    @TableField("sv5")
    private String sv5;

    /**
     * 组串6输入电压
     */
    @TableField("sv6")
    private String sv6;

    /**
     * 组串7输入电压
     */
    @TableField("sv7")
    private String sv7;

    /**
     * 组串8输入电压
     */
    @TableField("sv8")
    private String sv8;

    /**
     * 组串9输入电压
     */
    @TableField("sv9")
    private String sv9;

    /**
     * 组串10输入电压
     */
    @TableField("sv10")
    private String sv10;

    /**
     * 组串11输入电压
     */
    @TableField("sv11")
    private String sv11;

    /**
     * 组串12输入电压
     */
    @TableField("sv12")
    private String sv12;

    /**
     * 组串13输入电压
     */
    @TableField("sv13")
    private String sv13;

    /**
     * 组串14输入电压
     */
    @TableField("sv14")
    private String sv14;

    /**
     * 组串15输入电压
     */
    @TableField("sv15")
    private String sv15;

    /**
     * 组串16输入电压
     */
    @TableField("sv16")
    private String sv16;

    /**
     * 组串17输入电压
     */
    @TableField("sv17")
    private String sv17;

    /**
     * 组串18输入电压
     */
    @TableField("sv18")
    private String sv18;

    /**
     * 组串19输入电压
     */
    @TableField("sv19")
    private String sv19;

    /**
     * 组串20输入电压
     */
    @TableField("sv20")
    private String sv20;


    /**
     * 转发标识
     */
    @TableField("relayFlag")
    private Integer relayFlag;

    /**
     * 逆变器效率
     */
    @TableField("inverterefficiency")
    private String inverterefficiency;

    /**
     * 直流输入功率(kW)
     */
    @TableField("dcinputpower")
    private String dcinputpower;

    /**
     * 日运行时间(h)
     */
    @TableField("dailyrunningtime")
    private String dailyrunningtime;


}

