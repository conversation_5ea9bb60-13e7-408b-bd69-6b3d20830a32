package com.gcl.psmis.framework.taos.domain;

import cn.hutool.core.util.ReflectUtil;
import com.alibaba.fastjson.annotation.JSONField;
import com.fasterxml.jackson.annotation.JsonIgnore;
import com.gcl.psmis.framework.taos.annotation.TdTableName;
import com.gcl.psmis.framework.taos.util.SpecialCharUtil;
import org.apache.commons.lang3.StringUtils;

import java.io.Serializable;
import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

/**
 * @className: TdModel
 * @author: xinan.yuan
 * @create: 2022/12/16 9:01
 * @desc: tdengine 基础抽象model
 */
public abstract class TdModel implements Serializable {

    private static final long serialVersionUID = 1L;

    @JsonIgnore
    @JSONField(serialize = false)
    private String tableName;
    @JsonIgnore
    @JSONField(serialize = false)
    private String superTableName;
    @JsonIgnore
    @JSONField(serialize = false)
    private String dbName;


    /**
     * 获取表名(带dbname)
     * @return
     */
    public synchronized String getTableName() {
        TdTableName annotation = getClass().getAnnotation(TdTableName.class);
        List<String> namePortions = new ArrayList();
        namePortions.add(annotation.tablePrefix());
        String[] property = annotation.tableProperty();
        if(property!=null&&property.length>0){
            for (String filedName : property) {
                Object fieldValue1 = ReflectUtil.getFieldValue(this, filedName);
                if(fieldValue1==null|| StringUtils.isBlank(String.valueOf(fieldValue1))){
                    throw new RuntimeException(filedName+":表名组成字段不能为空！");
                }
                String fieldValue = String.valueOf(fieldValue1);
                namePortions.add(fieldValue);
            }
        }
        if(StringUtils.isNotBlank(annotation.tableSuffix())){
            namePortions.add(annotation.tableSuffix());
        }

        tableName=annotation.dbName()+".`"+ SpecialCharUtil.formatTableName(namePortions.stream().collect(Collectors.joining(annotation.separator())))+"`";
        if(annotation.isLowercase()){
            tableName=tableName.toLowerCase();
        }
        return tableName;
    }

    public String getSuperTableName() {
        TdTableName annotation = getClass().getAnnotation(TdTableName.class);
        superTableName = annotation.dbName()+"."+annotation.superTbName();
        return superTableName;
    }

    public String getDbName() {
        TdTableName annotation = getClass().getAnnotation(TdTableName.class);
        dbName=annotation.dbName();
        return dbName;
    }
}
