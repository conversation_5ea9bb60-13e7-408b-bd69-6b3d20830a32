package com.gcl.psmis.framework.taos.enums;

import cn.hutool.core.util.StrUtil;
import lombok.AllArgsConstructor;
import lombok.Getter;

/**
* @className: TaosEnums
* @author: xinan.yuan
* @create: 2023/7/18 11:01
* @description: taos模块枚举定义
*/
public class TaosEnums {

    /**
     * 消息类型
     */
    @Getter
    @AllArgsConstructor
    public enum ColumnType {
        Integer("Integer","INT"),
        Double("Double","DOUBLE"),
        <PERSON>("Long","INT"),
        Timestamp("Timestamp","TIMESTAMP"),
        Tinyint("Tinyint","TINYINT"),
        String("String","BINARY(20)");

        private String code;

        private String tdType;

        public static ColumnType parse(String code) {
            if (StrUtil.isBlank(code)) {
                return null;
            }
            for (ColumnType columnType : values()) {
                if (code.equals(columnType.getCode())) {
                    return columnType;
                }
            }
            return null;
        }
    }

    /**
     * 审核状态
     */
    @Getter
    @AllArgsConstructor
    public enum AuditStatus {
        待发起审批("00"), 审批中("01"), 审批驳回("-01"), 审批通过("10"), 作废("99");

        private String status;

        public boolean in(AuditStatus... auditStatuses) {
            for (AuditStatus auditStatus : auditStatuses) {
                if (auditStatus.equals(this)) {
                    return true;
                }
            }
            return false;
        }

        public static AuditStatus parse(String status) {
            if (StrUtil.isBlank(status)) {
                return null;
            }
            for (AuditStatus as : values()) {
                if (status.equals(as.getStatus())) {
                    return as;
                }
            }
            return null;
        }

        public boolean finish() {
            return this.in(AuditStatus.审批通过, AuditStatus.作废);
        }
    }

    /**
     * 交易状态
     */
    @Getter
    @AllArgsConstructor
    public enum TransStatus {
        待处理("11"), 银行处理中("15"), 部分交易成功("20"), 交易失败("30"), 交易完成("90");

        private String status;

        public boolean in(TransStatus... transStatuses) {
            for (TransStatus ts : transStatuses) {
                if (ts.equals(this)) {
                    return true;
                }
            }
            return false;
        }

        public static TransStatus parse(String status) {
            if (StrUtil.isBlank(status)) {
                return null;
            }
            for (TransStatus ts : values()) {
                if (status.equals(ts.getStatus())) {
                    return ts;
                }
            }
            return null;
        }

        public boolean finish() {
            return this.in(TransStatus.部分交易成功, TransStatus.交易失败, TransStatus.交易完成);
        }
    }

    /**
     * 业务消息状态
     */
    @Getter
    @AllArgsConstructor
    public enum NotifyMsgStatus {
        待处理("0"), 处理成功("1"), 处理失败("2");

        private String status;
    }

    /**
     * 清分状态
     */
    @Getter
    @AllArgsConstructor
    public enum EntryStatus {
        未处理("0"), 清分成功("1"), 清分失败("2");

        private String status;
    }

    /**
     * 账户分录
     */
    public enum EntryNo {
        /**
         * 余额
         */
        AMOUNT,
        /**
         * 冻结金额
         */
        FROZEN_AMOUNT,
        /**
         * 可用余额
         */
        AVAILABLE_AMOUNT,
        /**
         * 累计提现
         */
        WITHDRAW_AMOUNT,

        /**
         * 累计收入
         */
        REVENUE_AMOUNT
    }

    /**
     * 卡状态
     */
    @Getter
    @AllArgsConstructor
    public enum CardStatus {
        绑定("1"), 解绑("2");
        private String status;
    }

    /**
     * 页面操作按钮
     */
    public enum Button {
        /**
         * 编辑
         */
        EDIT,
        /**
         * 详情
         */
        DETAIL,
        /**
         * 审核
         */
        AUDIT,
        /**
         * 作废/取消
         */
        CANCEL
    }

    /**
     * 提现类型
     */
    @Getter
    @AllArgsConstructor
    public enum WithdrawType {
        用户提现("00"),
        平台提现("01");

        String code;
    }

    /**
     * 提现类型
     */
    @Getter
    @AllArgsConstructor
    public enum CheckType {
        账户金额("ACC_AMT");

        String code;
    }

    @Getter
    @AllArgsConstructor
    public enum OpenAccountFlag{
        正常(1),
        开户失败(0),
        绑卡失败(2),
        设置提现卡失败(3);

        private Integer flag;
    }
}
