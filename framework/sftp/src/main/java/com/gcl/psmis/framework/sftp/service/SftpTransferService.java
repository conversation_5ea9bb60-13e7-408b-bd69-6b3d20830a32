package com.gcl.psmis.framework.sftp.service;

import com.jcraft.jsch.*;
import org.springframework.stereotype.Service;

/**
 * @Author: sunjiaxing
 * @CreateTime: 2024-05-23  11:42
 * @Description: sftp
 */
@Service
public class SftpTransferService {

    private static final String REMOTE_HOST = "gw.open.icbc.com.cn";  //远程主机ip
    private static final String USERNAME = "scxnny";  //登录用户名
    private static final int REMOTE_PORT = 8001;   //ssh协议默认端口
    private static final int SESSION_TIMEOUT = 10000; //session超时时间
    private static final int CHANNEL_TIMEOUT = 5000; //管道流超时时间


    public static void main(String[] args) {
        Session jschSession = null;

        try {

            JSch jsch = new JSch();
            jschSession = jsch.getSession(USERNAME, REMOTE_HOST, REMOTE_PORT);

            // 通过ssh私钥的方式登录认证
            jsch.addIdentity("D:\\project\\npsmis\\framework\\sftp\\src\\main\\resources\\gclet_登录_rsa");

            // 通过密码的方式登录认证
            jschSession.connect(SESSION_TIMEOUT);

            Channel sftp = jschSession.openChannel("sftp");  //建立sftp文件传输管道
            sftp.connect(CHANNEL_TIMEOUT);

            ChannelSftp channelSftp = (ChannelSftp) sftp;

            channelSftp.get("/EntrustFile");
            // 传输本地文件到远程主机
            //channelSftp.put(localFile, remoteFile);

            channelSftp.exit();

        } catch (JSchException | SftpException e) {
            e.printStackTrace();
        } finally {
            if (jschSession != null) {
                jschSession.disconnect();
            }
        }
    }
}
