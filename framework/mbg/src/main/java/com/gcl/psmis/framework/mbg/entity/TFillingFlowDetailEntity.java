package com.gcl.psmis.framework.mbg.entity;

import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.extension.activerecord.Model;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.*;
import lombok.experimental.Accessors;

/**
 * <p>
 * 流程详情表
 * </p>
 *
 * <AUTHOR>
 * @since 2025-04-09
 */
@Builder
@AllArgsConstructor
@NoArgsConstructor
@Getter
@Setter
@Accessors(chain = true)
@TableName("t_filling_flow_detail")
@ApiModel(value = "TFillingFlowDetailEntity对象", description = "流程详情表")
public class TFillingFlowDetailEntity extends Model<TFillingFlowDetailEntity> {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty("自增主键id")
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    @ApiModelProperty("单体流程ID")
    @TableField("single_flow_id")
    private Long singleFlowId;

    @ApiModelProperty("汇集流程Id")
    @TableField("sum_flow_id")
    private Long sumFlowId;

    @ApiModelProperty("指标编码")
    @TableField("index_no")
    private String indexNo;

    @ApiModelProperty("指标名称")
    @TableField("index_name")
    private String indexName;

    @ApiModelProperty("指标单位")
    @TableField("index_unit")
    private String indexUnit;

    @ApiModelProperty("指标上级分类")
    @TableField("index_parent")
    private String indexParent;

    @ApiModelProperty("填报层级分类")
    @TableField("level_type")
    private String levelType;

    @ApiModelProperty("填报层级")
    @TableField("fill_level")
    private String fillLevel;

    @ApiModelProperty("可填报公司code")
    @TableField("company_code")
    private String companyCode;

    @ApiModelProperty("所属汇总公司code")
    @TableField("parent_code")
    private String parentCode;

    @ApiModelProperty("年月(YYYY-MM)")
    @TableField("year_monthly")
    private String yearMonthly;

    @ApiModelProperty("年度目标值")
    @TableField("year_target")
    private BigDecimal yearTarget;

    @ApiModelProperty("月度分解")
    @TableField("monthly_analyze")
    private BigDecimal monthlyAnalyze;

    @ApiModelProperty("月度计划")
    @TableField("monthly_plan")
    private BigDecimal monthlyPlan;

    @ApiModelProperty("月度实际")
    @TableField("monthly_actual")
    private BigDecimal monthlyActual;

    @ApiModelProperty("计划流程是否归档，0-是，1-否")
    @TableField("plan_filed_flag")
    private Integer planFiledFlag;

    @ApiModelProperty("实际流程是否归档，0-是，1-否")
    @TableField("actual_filed_flag")
    private Integer actualFiledFlag;

    @ApiModelProperty("实际值")
    @TableField("actual_value")
    private BigDecimal actualValue;

    @ApiModelProperty("年度目标是否手工填写，0-是，1-否")
    @TableField("year_target_sign")
    private Integer yearTargetSign;

    @ApiModelProperty("月度实际是否手工填写，0-是，1-否")
    @TableField("month_actual_sign")
    private Integer monthActualSign;

    @ApiModelProperty("月计划是否手工填写，0-是，1-否")
    @TableField("monthly_plan_sign")
    private Integer monthlyPlanSign;

    @ApiModelProperty("月分解是否手工填写，0-是，1-否")
    @TableField("monthly_analyze_sign")
    private Integer monthlyAnalyzeSign;

    @ApiModelProperty("数据创建时间")
    @TableField(value = "create_time", fill = FieldFill.INSERT)
    private Date createTime;

    @ApiModelProperty("编辑时间")
    @TableField(value = "update_time", fill = FieldFill.INSERT_UPDATE)
    private Date updateTime;

    @ApiModelProperty("年度目标所属来源系统")
    @TableField(value = "target_system_source")
    private String targetSystemSource;
    @ApiModelProperty("月度实际所属来源系统")
    @TableField(value = "actual_system_source")
    private String actualSystemSource;
    @ApiModelProperty("月度分解所属来源系统")
    @TableField(value = "analyze_system_source")
    private String analyzeSystemSource;
    @ApiModelProperty("月度计划所属来源系统")
    @TableField(value = "plan_system_source")
    private String planSystemSource;
    @ApiModelProperty("年度目标编辑时间")
    @TableField(value = "year_target_update_time", fill = FieldFill.INSERT_UPDATE)
    private Date yearTargetUpdateTime;
    @ApiModelProperty("月度分解编辑时间")
    @TableField(value = "monthly_analyze_update_time", fill = FieldFill.INSERT_UPDATE)
    private Date monthlyAnalyzeUpdateTime;
    @ApiModelProperty("月度计划编辑时间")
    @TableField(value = "monthly_plan_update_time", fill = FieldFill.INSERT_UPDATE)
    private Date monthlyPlanUpdateTime;
    @ApiModelProperty("月度实际编辑时间")
    @TableField(value = "monthly_actual_update_time", fill = FieldFill.INSERT_UPDATE)
    private Date monthlyActualUpdateTime;

    @ApiModelProperty("汇总底稿分工组织分类")
    @TableField(value = "sum_org_type")
    private String sumOrgType;

    @ApiModelProperty("指标填写说明")
    @TableField(value = "index_describe")
    private String indexDescribe;

    @ApiModelProperty("备注")
    @TableField(value = "remark")
    private String remark;

    @ApiModelProperty("排序")
    @TableField(value = "sort")
    private BigDecimal sort;

    @ApiModelProperty("关联t_index_company_config的id")
    @TableField("config_id")
    private Integer configId;

    @ApiModelProperty("月度计划t+1(滚测)-下个月")
    @TableField("monthly_plan_t1")
    private BigDecimal monthlyPlanT1;

    @ApiModelProperty("月度计划t+2(滚测)-下下个月")
    @TableField("monthly_plan_t2")
    private BigDecimal monthlyPlanT2;
    @Override
    public Serializable pkVal() {
        return this.id;
    }
}
