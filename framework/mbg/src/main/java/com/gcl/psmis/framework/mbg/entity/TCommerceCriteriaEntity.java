package com.gcl.psmis.framework.mbg.entity;

import com.baomidou.mybatisplus.annotation.*;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.*;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.util.Date;

/**
 * <p>
 * 工商业验收标准基础表
 * </p>
 *
 * <AUTHOR>
 * @since 2025-02-26
 */
@Builder
@AllArgsConstructor
@NoArgsConstructor
@Getter
@Setter
@Accessors(chain = true)
@TableName("t_commerce_criteria")
@ApiModel(value = "TCommerceCriteriaEntity对象", description = "工商业验收标准基础表")
public class TCommerceCriteriaEntity extends Model<TCommerceCriteriaEntity> {

    private static final long serialVersionUID = 1L;

    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    @ApiModelProperty("验收标准名称")
    @TableField("acceptance_criteria_name")
    private String acceptanceCriteriaName;

    @ApiModelProperty("验收版本")
    @TableField("version")
    private String version;

    @ApiModelProperty("导入文件地址")
    @TableField("file_url")
    private String fileUrl;

    @ApiModelProperty("状态;1:启用,2:禁用")
    @TableField("state")
    private Integer state;

    @ApiModelProperty("是否删除0：未删除，1：已删除")
    @TableField("del_flag")
    private Integer delFlag;

    @ApiModelProperty("创建时间")
    @TableField(value = "create_time", fill = FieldFill.INSERT)
    private Date createTime;

    @ApiModelProperty("创建人工号")
    @TableField(value = "create_by_no", fill = FieldFill.INSERT)
    private String createByNo;

    @ApiModelProperty("创建人姓名")
    @TableField(value = "create_by_name", fill = FieldFill.INSERT)
    private String createByName;

    @ApiModelProperty("更新时间")
    @TableField(value = "update_time", fill = FieldFill.INSERT_UPDATE)
    private Date updateTime;

    @ApiModelProperty("更新人工号")
    @TableField(value = "update_by_no", fill = FieldFill.UPDATE)
    private String updateByNo;

    @ApiModelProperty("更新人姓名")
    @TableField(value = "update_by_name", fill = FieldFill.UPDATE)
    private String updateByName;

    @Override
    public Serializable pkVal() {
        return this.id;
    }
}
