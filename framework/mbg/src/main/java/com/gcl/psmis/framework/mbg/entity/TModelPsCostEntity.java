package com.gcl.psmis.framework.mbg.entity;

import com.baomidou.mybatisplus.annotation.*;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.*;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * <p>
 *
 * </p>
 *
 * <AUTHOR>
 * @since 2025-06-06
 */
@Builder
@AllArgsConstructor
@NoArgsConstructor
@Getter
@Setter
@Accessors(chain = true)
@TableName("t_model_ps_cost")
@ApiModel(value = "TModelPsCostEntity对象", description = "")
public class TModelPsCostEntity extends Model<TModelPsCostEntity> {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty("主键id")
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    @ApiModelProperty("电站编号")
    @TableField("ps_code")
    private String psCode;

    @ApiModelProperty("产品名称")
    @TableField("product_name")
    private String productName;

    @ApiModelProperty("是否混装: 0-否,1-是")
    @TableField("mix_flag")
    private Integer mixFlag;

    @ApiModelProperty("产权方")
    @TableField("property_owner")
    private String propertyOwner;

    @ApiModelProperty("电费归集方式")
    @TableField("electricity_bill_mode")
    private String electricityBillMode;

    @ApiModelProperty("省份id")
    @TableField("province_id")
    private Long provinceId;

    @ApiModelProperty("省名称")
    @TableField("province_name")
    private String provinceName;

    @ApiModelProperty("城市id")
    @TableField("city_id")
    private Long cityId;

    @ApiModelProperty("市名称")
    @TableField("city_name")
    private String cityName;

    @ApiModelProperty("地区id")
    @TableField("region_id")
    private Long regionId;

    @ApiModelProperty("区名称")
    @TableField("region_name")
    private String regionName;

    @ApiModelProperty("结算标准id")
    @TableField("standard_id")
    private Long standardId;

    @ApiModelProperty("备案类型")
    @TableField("record_type")
    private String recordType;

    @ApiModelProperty("电站状态")
    @TableField("ps_status")
    private String psStatus;

    @ApiModelProperty("组件块数(总数)")
    @TableField("sum_assembly_num")
    private Integer sumAssemblyNum;

    @ApiModelProperty("组件类型")
    @TableField("assembly_type")
    private String assemblyType;

    @ApiModelProperty("安装形式")
    @TableField("install_form")
    private String installForm;

    @ApiModelProperty("组件功率(w)")
    @TableField("assembly_power")
    private String assemblyPower;

    @ApiModelProperty("组件块数(拆分后)")
    @TableField("assembly_num")
    private Integer assemblyNum;

    @ApiModelProperty("装机容量(w)")
    @TableField("capital")
    private BigDecimal capital;

    @ApiModelProperty("建档日期")
    @TableField("created_time")
    private Date createdTime;

    @ApiModelProperty("齐套发货日期")
    @TableField("qi_tao_time")
    private Date qiTaoTime;

    @ApiModelProperty("有效发电首日")
    @TableField("first_power_time")
    private Date firstPowerTime;

    @ApiModelProperty("并网通过日期")
    @TableField("connected_time")
    private Date connectedTime;

    @ApiModelProperty("首年理论小时数")
    @TableField("first_year_hour")
    private BigDecimal firstYearHour;

    @ApiModelProperty("第一年租金")
    @TableField("one_phase_year_rent")
    private BigDecimal onePhaseYearRent;

    @ApiModelProperty("第2到5年租金")
    @TableField("two_phase_year_rent")
    private BigDecimal twoPhaseYearRent;

    @ApiModelProperty("第6到10年租金")
    @TableField("three_phase_year_rent")
    private BigDecimal threePhaseYearRent;

    @ApiModelProperty("第11年到15年租金")
    @TableField("four_phase_year_rent")
    private BigDecimal fourPhaseYearRent;

    @ApiModelProperty("第16年到20年租金")
    @TableField("five_phase_year_rent")
    private BigDecimal fivePhaseYearRent;

    @ApiModelProperty("第21年到25年租金")
    @TableField("six_phase_year_rent")
    private BigDecimal sixPhaseYearRent;

    @ApiModelProperty("理论-代理商成本-开发服务费")
    @TableField("theory_develop_cost")
    private BigDecimal theoryDevelopCost;

    @ApiModelProperty("理论-代理商成本-设备材料费")
    @TableField("theory_device_cost")
    private BigDecimal theoryDeviceCost;

    @ApiModelProperty("理论-代理商成本-光伏安装施工费")
    @TableField("theory_install_cost")
    private BigDecimal theoryInstallCost;

    @ApiModelProperty("理论-代理商奖励-容量奖励")
    @TableField("theory_capacity_reward")
    private BigDecimal theoryCapacityReward;

    @ApiModelProperty("理论-代理商奖励-实效奖励")
    @TableField("theory_actual_effect_reward")
    private BigDecimal theoryActualEffectReward;

    @ApiModelProperty("理论-设备成本-组件")
    @TableField("theory_assembly")
    private BigDecimal theoryAssembly;

    @ApiModelProperty("理论-设备成本-支架")
    @TableField("theory_bracket")
    private BigDecimal theoryBracket;

    @ApiModelProperty("理论-设备成本-并网箱")
    @TableField("theory_box")
    private BigDecimal theoryBox;

    @ApiModelProperty("理论-设备成本-逆变器")
    @TableField("theory_inverter")
    private BigDecimal theoryInverter;

    @ApiModelProperty("理论-设备成本-电气辅材")
    @TableField("theory_fc")
    private BigDecimal theoryFc;

    @ApiModelProperty("理论-管理成本-供应链")
    @TableField("theory_supply_chain_cost")
    private BigDecimal theorySupplyChainCost;

    @ApiModelProperty("理论-管理成本-管理费")
    @TableField("theory_manage_cost")
    private BigDecimal theoryManageCost;

    @ApiModelProperty("理论-管理成本-财务费")
    @TableField("theory_finance_cost")
    private BigDecimal theoryFinanceCost;

    @ApiModelProperty("理论-造价合计")
    @TableField("theory_total_cost")
    private BigDecimal theoryTotalCost;

    @ApiModelProperty("实际-代理商成本-开发服务费")
    @TableField("actual_develop_cost")
    private BigDecimal actualDevelopCost;

    @ApiModelProperty("实际-代理商成本-设备材料费")
    @TableField("actual_device_cost")
    private BigDecimal actualDeviceCost;

    @ApiModelProperty("实际-代理商成本-光伏安装施工费")
    @TableField("actual_install_cost")
    private BigDecimal actualInstallCost;

    @ApiModelProperty("实际-代理商奖励-容量奖励")
    @TableField("actual_capacity_reward")
    private BigDecimal actualCapacityReward;

    @ApiModelProperty("实际-代理商奖励-实效奖励")
    @TableField("actual_actual_effect_reward")
    private BigDecimal actualActualEffectReward;

    @ApiModelProperty("实际-设备成本-组件")
    @TableField("actual_assembly")
    private BigDecimal actualAssembly;

    @ApiModelProperty("实际-设备成本-支架")
    @TableField("actual_bracket")
    private BigDecimal actualBracket;

    @ApiModelProperty("实际-设备成本-并网箱")
    @TableField("actual_box")
    private BigDecimal actualBox;

    @ApiModelProperty("实际-设备成本-逆变器")
    @TableField("actual_inverter")
    private BigDecimal actualInverter;

    @ApiModelProperty("实际-设备成本-电气辅材")
    @TableField("actual_fc")
    private BigDecimal actualFc;

    @ApiModelProperty("实际-管理成本-仓储")
    @TableField("actual_storage_cost")
    private BigDecimal actualStorageCost;

    @ApiModelProperty("实际-管理成本-仓储调拨")
    @TableField("actual_allot_cost")
    private BigDecimal actualAllotCost;

    @ApiModelProperty("实际-管理成本-装卸")
    @TableField("actual_move_cost")
    private BigDecimal actualMoveCost;

    @ApiModelProperty("实际-管理成本-物流")
    @TableField("actual_logistics_cost")
    private BigDecimal actualLogisticsCost;

    @ApiModelProperty("实际-管理成本-供应链")
    @TableField("actual_supply_chain_cost")
    private BigDecimal actualSupplyChainCost;

    @ApiModelProperty("实际-管理成本-管理费")
    @TableField("actual_manage_cost")
    private BigDecimal actualManageCost;

    @ApiModelProperty("实际-管理成本-财务费")
    @TableField("actual_finance_cost")
    private BigDecimal actualFinanceCost;

    @ApiModelProperty("实际-造价合计")
    @TableField("actual_total_cost")
    private BigDecimal actualTotalCost;

    @ApiModelProperty("创建时间")
    @TableField(value = "create_time", fill = FieldFill.INSERT)
    private Date createTime;

    @ApiModelProperty("创建人工号")
    @TableField(value = "create_by_no", fill = FieldFill.INSERT)
    private String createByNo;

    @ApiModelProperty("创建用户名称")
    @TableField(value = "create_by_name", fill = FieldFill.INSERT)
    private String createByName;

    @ApiModelProperty("更新时间")
    @TableField(value = "update_time", fill = FieldFill.INSERT_UPDATE)
    private Date updateTime;

    @ApiModelProperty("更新人工号")
    @TableField(value = "update_by_no", fill = FieldFill.UPDATE)
    private String updateByNo;

    @ApiModelProperty("更新人姓名")
    @TableField(value = "update_by_name", fill = FieldFill.UPDATE)
    private String updateByName;

    @Override
    public Serializable pkVal() {
        return this.id;
    }
}
