package com.gcl.psmis.framework.mbg.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.*;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * <p>
 * 结算发票表
 * </p>
 *
 * <AUTHOR>
 * @since 2024-11-26
 */
@Builder
@AllArgsConstructor
@NoArgsConstructor
@Getter
@Setter
@Accessors(chain = true)
@TableName("fi_balance_invoice")
@ApiModel(value = "FiBalanceInvoiceEntity对象", description = "结算发票表")
public class FiBalanceInvoiceEntity extends Model<FiBalanceInvoiceEntity> {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty("发票id")
    @TableId(value = "invoiceid", type = IdType.AUTO)
    private Long invoiceid;

    @ApiModelProperty("结算单id(代理商结算、业主分享结算)")
    @TableField("balanceid")
    private Long balanceid;

    @ApiModelProperty("发票日期")
    @TableField("invoicetime")
    private Date invoicetime;

    @ApiModelProperty("发票号")
    @TableField("invoiceno")
    private String invoiceno;

    @ApiModelProperty("发票含税总额")
    @TableField("taxincludedamount")
    private BigDecimal taxincludedamount;

    @ApiModelProperty("文件id")
    @TableField("documentid")
    private Long documentid;

    @ApiModelProperty("备注")
    @TableField("remark")
    private String remark;

    @ApiModelProperty("发票不含税金额")
    @TableField("taxnotincludedamount")
    private BigDecimal taxnotincludedamount;

    @ApiModelProperty("发票税额")
    @TableField("taxamount")
    private BigDecimal taxamount;

    @ApiModelProperty("税率")
    @TableField("taxrate")
    private BigDecimal taxrate;

    @ApiModelProperty("来源类型 FiInvoiceType")
    @TableField("fromtype")
    private Integer fromtype;

    @ApiModelProperty("同一张发票的唯一标识")
    @TableField("uuid")
    private String uuid;

    @ApiModelProperty("验真需要用的发票校验码")
    @TableField("check_code")
    private String checkCode;

    @ApiModelProperty("验真需要用的发票类型")
    @TableField("invoice_type")
    private String invoiceType;

    @ApiModelProperty("购方名称")
    @TableField("buyer_name")
    private String buyerName;

    @ApiModelProperty("购方税号")
    @TableField("buyer_tax_no")
    private String buyerTaxNo;

    @ApiModelProperty("购方账户信息")
    @TableField("buyer_bank_account")
    private String buyerBankAccount;

    @ApiModelProperty("购方地址电话")
    @TableField("buyer_address_phone")
    private String buyerAddressPhone;

    @ApiModelProperty("销方名称")
    @TableField("seller_name")
    private String sellerName;

    @ApiModelProperty("销方税号")
    @TableField("seller_tax_no")
    private String sellerTaxNo;

    @ApiModelProperty("销方账户信息")
    @TableField("seller_bank_account")
    private String sellerBankAccount;

    @ApiModelProperty("销方地址电话")
    @TableField("seller_address_phone")
    private String sellerAddressPhone;

    @ApiModelProperty("商品名称")
    @TableField("goods_name")
    private String goodsName;

    @ApiModelProperty("规格型号")
    @TableField("goods_specification")
    private String goodsSpecification;

    @ApiModelProperty("单位")
    @TableField("goods_unit")
    private String goodsUnit;

    @ApiModelProperty("数量")
    @TableField("goods_number")
    private String goodsNumber;

    @ApiModelProperty("单价")
    @TableField("goods_price")
    private String goodsPrice;

    @ApiModelProperty("价税合计")
    @TableField("total_amount")
    private String totalAmount;

    @Override
    public Serializable pkVal() {
        return this.invoiceid;
    }
}
