package com.gcl.psmis.framework.mbg.entity;

import com.baomidou.mybatisplus.annotation.*;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.*;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * <p>
 * 运维商账单(工单费用)
 * </p>
 *
 * <AUTHOR>
 * @since 2024-11-12
 */
@Builder
@AllArgsConstructor
@NoArgsConstructor
@Getter
@Setter
@Accessors(chain = true)
@TableName("t_dev_ops_gd_fill")
@ApiModel(value = "TDevOpsGdFillEntity对象", description = "运维商账单(工单费用)")
public class TDevOpsGdFillEntity extends Model<TDevOpsGdFillEntity> {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty("主键")
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    @ApiModelProperty("账单编号")
    @TableField("fill_no")
    private String fillNo;

    @ApiModelProperty("合并账单账编号")
    @TableField("merge_no")
    private String mergeNo;

    @ApiModelProperty("运维商名称")
    @TableField("dev_ops_name")
    private String devOpsName;

    @ApiModelProperty("运维商编号")
    @TableField("dev_ops_code")
    private String devOpsCode;

    @ApiModelProperty("账期")
    @TableField("fill_date")
    private String fillDate;

    @ApiModelProperty("账单类型:0-运维,1-巡检")
    @TableField("fill_type")
    private Integer fillType;

    @ApiModelProperty("电站数量")
    @TableField("ps_num")
    private Integer psNum;

    @ApiModelProperty("工单数量")
    @TableField("gd_num")
    private Integer gdNum;

    @ApiModelProperty("上门费(元)")
    @TableField("to_door_fee")
    private BigDecimal toDoorFee;

    @ApiModelProperty("服务费(元)")
    @TableField("service_fee")
    private BigDecimal serviceFee;

    @ApiModelProperty("考核费(元)")
    @TableField("check_fee")
    private BigDecimal checkFee;

    @ApiModelProperty("实际考核费(元)")
    @TableField("actual_check_fee")
    private BigDecimal actualCheckFee;

    @ApiModelProperty("结算金额(元)")
    @TableField("settlement_fee")
    private BigDecimal settlementFee;

    @ApiModelProperty("结算状态: 3-待提交,4-上传发票,5-待发票确认,6-待付款,7-付款中,8-已付款")
    @TableField("fill_status")
    private Integer fillStatus;

    @ApiModelProperty("账单状态:-1-待确认, 0-待审核, 1-已审核,2-已驳回,3-不予结算")
    @TableField("gd_status")
    private Integer gdStatus;

    @ApiModelProperty("是否删除")
    @TableField("del_flag")
    private Integer delFlag;

    @ApiModelProperty("创建时间")
    @TableField(value = "create_time", fill = FieldFill.INSERT)
    private Date createTime;

    @ApiModelProperty("创建人工号")
    @TableField(value = "create_by_no", fill = FieldFill.INSERT)
    private String createByNo;

    @ApiModelProperty("创建用户名称")
    @TableField(value = "create_by_name", fill = FieldFill.INSERT)
    private String createByName;

    @ApiModelProperty("更新时间")
    @TableField(value = "update_time", fill = FieldFill.INSERT_UPDATE)
    private Date updateTime;

    @ApiModelProperty("更新人工号")
    @TableField(value = "update_by_no", fill = FieldFill.UPDATE)
    private String updateByNo;

    @ApiModelProperty("更新人姓名")
    @TableField(value = "update_by_name", fill = FieldFill.UPDATE)
    private String updateByName;

    @ApiModelProperty("确认人")
    @TableField("confirm_person")
    private String confirmPerson;

    @ApiModelProperty("确认人id")
    @TableField("confirm_person_id")
    private String confirmPersonId;

    @ApiModelProperty("确认时间")
    @TableField("confirm_date")
    private Date confirmDate;

    @ApiModelProperty("审核人")
    @TableField("audit_person")
    private String auditPerson;

    @ApiModelProperty("审核人id")
    @TableField("audit_person_id")
    private String auditPersonId;

    @TableField("audit_date")
    private Date auditDate;

    @Override
    public Serializable pkVal() {
        return this.id;
    }
}
