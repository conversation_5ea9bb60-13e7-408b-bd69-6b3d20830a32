package com.gcl.psmis.framework.mbg.entity;

import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.*;
import lombok.experimental.Accessors;

/**
 * <p>
 * 
 * </p>
 *
 * <AUTHOR>
 * @since 2025-05-27
 */
@Builder
@AllArgsConstructor
@NoArgsConstructor
@Getter
@Setter
@Accessors(chain = true)
@TableName("gcl_finance_report")
@ApiModel(value = "GclFinanceReportEntity对象", description = "")
public class GclFinanceReportEntity extends Model<GclFinanceReportEntity> {

    private static final long serialVersionUID = 1L;

    @TableId(value = "id", type = IdType.AUTO)
    private Integer id;

    @ApiModelProperty("账单序号")
    @TableField("bill_id")
    private String billId;

    @ApiModelProperty("账单编号")
    @TableField("bill_no")
    private String billNo;

    @ApiModelProperty("电站编号")
    @TableField("station_no")
    private String stationNo;

    @ApiModelProperty("费用大类")
    @TableField("fee_type")
    private String feeType;

    @ApiModelProperty("年月")
    @TableField("bill_create_time")
    private Date billCreateTime;

    @ApiModelProperty("账单类型")
    @TableField("bill_type")
    private String billType;

    @ApiModelProperty("内部订单号")
    @TableField("inner_order_no")
    private String innerOrderNo;

    @ApiModelProperty("项目公司id")
    @TableField("project_company_code")
    private String projectCompanyCode;

    @ApiModelProperty("项目公司name")
    @TableField("project_company_name")
    private String projectCompanyName;

    @ApiModelProperty("开发商id")
    @TableField("kfs_org_id")
    private String kfsOrgId;

    @ApiModelProperty("开发商name")
    @TableField("kfs_org_name")
    private String kfsOrgName;

    @ApiModelProperty("服务商id")
    @TableField("fws_org_id")
    private String fwsOrgId;

    @ApiModelProperty("服务商name")
    @TableField("fws_org_name")
    private String fwsOrgName;

    @ApiModelProperty("省公司id")
    @TableField("company_id")
    private String companyId;

    @ApiModelProperty("省公司name")
    @TableField("company_name")
    private String companyName;

    @ApiModelProperty("成本中心")
    @TableField("cost_center")
    private String costCenter;

    @ApiModelProperty("利润中心")
    @TableField("profit_center")
    private String profitCenter;

    @ApiModelProperty("业主id")
    @TableField("cus_id")
    private String cusId;

    @ApiModelProperty("业主name")
    @TableField("cus_name")
    private String cusName;

    @ApiModelProperty("装机容量")
    @TableField("power")
    private String power;

    @ApiModelProperty("并网时间")
    @TableField("grid_time")
    private Date gridTime;

    @ApiModelProperty("是否并网：已并网/未并网")
    @TableField("grid_flag")
    private String gridFlag;

    @ApiModelProperty("奖励金额")
    @TableField("bill_fee")
    private BigDecimal billFee;

    @ApiModelProperty("费用大类对应税率")
    @TableField("tax_rate")
    private BigDecimal taxRate;

    @ApiModelProperty("税率")
    @TableField("tax_ratio")
    private BigDecimal taxRatio;

    @ApiModelProperty("奖励不含税")
    @TableField("bill_fee_no_tax")
    private BigDecimal billFeeNoTax;

    @ApiModelProperty("结算状态：待结算、已结算、未结算")
    @TableField("settle_status")
    private String settleStatus;

    @ApiModelProperty("结转状态：已结转、未结转")
    @TableField("transfer_status")
    private String transferStatus;

    @ApiModelProperty("创建时间")
    @TableField(value = "create_time", fill = FieldFill.INSERT)
    private Date createTime=new Date();

    @ApiModelProperty("修改时间")
    @TableField(value = "update_time", fill = FieldFill.INSERT_UPDATE)
    private Date updateTime;

    @Override
    public Serializable pkVal() {
        return this.id;
    }
}
