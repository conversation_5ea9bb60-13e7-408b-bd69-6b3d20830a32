package com.gcl.psmis.framework.mbg.entity;

import com.baomidou.mybatisplus.annotation.*;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.*;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * <p>
 * 投资模型-管理成本
 * </p>
 *
 * <AUTHOR>
 * @since 2025-06-06
 */
@Builder
@AllArgsConstructor
@NoArgsConstructor
@Getter
@Setter
@Accessors(chain = true)
@TableName("t_model_manage_cost")
@ApiModel(value = "TModelManageCostEntity对象", description = "投资模型-管理成本")
public class TModelManageCostEntity extends Model<TModelManageCostEntity> {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty("主键id")
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    @ApiModelProperty("建档日期开始日期")
    @TableField("created_begin_time")
    private Date createdBeginTime;

    @ApiModelProperty("建档日期开始日期")
    @TableField("created_end_time")
    private Date createdEndTime;

    @ApiModelProperty("供应链成本(元/w)")
    @TableField("supply_chain_cost")
    private BigDecimal supplyChainCost;

    @ApiModelProperty("管理成本(元/w)")
    @TableField("manage_cost")
    private BigDecimal manageCost;

    @ApiModelProperty("财务成本(元/w)")
    @TableField("finance_cost")
    private BigDecimal financeCost;

    @ApiModelProperty("创建时间")
    @TableField(value = "create_time", fill = FieldFill.INSERT)
    private Date createTime;

    @ApiModelProperty("创建人工号")
    @TableField(value = "create_by_no", fill = FieldFill.INSERT)
    private String createByNo;

    @ApiModelProperty("创建用户名称")
    @TableField(value = "create_by_name", fill = FieldFill.INSERT)
    private String createByName;

    @ApiModelProperty("更新时间")
    @TableField(value = "update_time", fill = FieldFill.INSERT_UPDATE)
    private Date updateTime;

    @ApiModelProperty("更新人工号")
    @TableField(value = "update_by_no", fill = FieldFill.UPDATE)
    private String updateByNo;

    @ApiModelProperty("更新人姓名")
    @TableField(value = "update_by_name", fill = FieldFill.UPDATE)
    private String updateByName;

    @Override
    public Serializable pkVal() {
        return this.id;
    }
}
