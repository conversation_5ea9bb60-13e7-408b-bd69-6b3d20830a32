package com.gcl.psmis.framework.mbg.entity;

import com.baomidou.mybatisplus.annotation.*;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.*;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * <p>
 * 投资模型-代理商奖励
 * </p>
 *
 * <AUTHOR>
 * @since 2025-06-06
 */
@Builder
@AllArgsConstructor
@NoArgsConstructor
@Getter
@Setter
@Accessors(chain = true)
@TableName("t_model_agent_reward")
@ApiModel(value = "TModelAgentRewardEntity对象", description = "投资模型-代理商奖励")
public class TModelAgentRewardEntity extends Model<TModelAgentRewardEntity> {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty("主键id")
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    @ApiModelProperty("建档日期范围(格式: yyyy/MM-yyyy/MM)")
    @TableField("created_time_str")
    private String createdTimeStr;

    @ApiModelProperty("奖励类型: 0-容量奖励,1-时效奖励")
    @TableField("reward_type")
    private Integer rewardType;

    @ApiModelProperty("奖励金额(元/块)")
    @TableField("reward_amount")
    private BigDecimal rewardAmount;

    @ApiModelProperty("创建时间")
    @TableField(value = "create_time", fill = FieldFill.INSERT)
    private Date createTime;

    @ApiModelProperty("创建人工号")
    @TableField(value = "create_by_no", fill = FieldFill.INSERT)
    private String createByNo;

    @ApiModelProperty("创建用户名称")
    @TableField(value = "create_by_name", fill = FieldFill.INSERT)
    private String createByName;

    @ApiModelProperty("更新时间")
    @TableField(value = "update_time", fill = FieldFill.INSERT_UPDATE)
    private Date updateTime;

    @ApiModelProperty("更新人工号")
    @TableField(value = "update_by_no", fill = FieldFill.UPDATE)
    private String updateByNo;

    @ApiModelProperty("更新人姓名")
    @TableField(value = "update_by_name", fill = FieldFill.UPDATE)
    private String updateByName;

    @Override
    public Serializable pkVal() {
        return this.id;
    }
}
