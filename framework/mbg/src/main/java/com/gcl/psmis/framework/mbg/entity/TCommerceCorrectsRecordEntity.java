package com.gcl.psmis.framework.mbg.entity;

import com.baomidou.mybatisplus.annotation.*;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.*;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.util.Date;

/**
 * <p>
 * 整改历史记录
 * </p>
 *
 * <AUTHOR>
 * @since 2025-03-04
 */
@Builder
@AllArgsConstructor
@NoArgsConstructor
@Getter
@Setter
@Accessors(chain = true)
@TableName("t_commerce_corrects_record")
@ApiModel(value = "TCommerceCorrectsRecordEntity对象", description = "整改历史记录")
public class TCommerceCorrectsRecordEntity extends Model<TCommerceCorrectsRecordEntity> {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty("主键id")
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    @ApiModelProperty("t_commerce_acceptance 主键id")
    @TableField("acceptance_id")
    private Long acceptanceId;

    @ApiModelProperty("设备名称")
    @TableField("standard_name")
    private String standardName;

    @ApiModelProperty("t_commerce_standard 主键id")
    @TableField("standard_id")
    private Long standardId;

    @ApiModelProperty("t_commerce_inspect_item 主键id(大项)")
    @TableField("big_inspect_id")
    private Long bigInspectId;

    @ApiModelProperty("t_commerce_inspect_item 主键id(小项)")
    @TableField("small_inspect_id")
    private Long smallInspectId;

    @ApiModelProperty("操作人编码")
    @TableField("operate_no")
    private String operateNo;

    @ApiModelProperty("提交时间")
    @TableField("operate_time")
    private Date operateTime;

    @ApiModelProperty("操作类型: 0-验收,1-整改,2-审核")
    @TableField("operate_type")
    private Integer operateType;

    @ApiModelProperty("图片地址")
    @TableField("url")
    private String url;

    @ApiModelProperty("审核结果:0-整改合格,1-整改不合格")
    @TableField("result")
    private Integer result;

    @ApiModelProperty("描述")
    @TableField("`describe`")
    private String describe;

    @ApiModelProperty("删除标识 0:未删除  1:已删除")
    @TableField("del_flag")
    private Integer delFlag;

    @ApiModelProperty("创建时间")
    @TableField(value = "create_time", fill = FieldFill.INSERT)
    private Date createTime;

    @ApiModelProperty("创建人")
    @TableField(value = "create_by_no", fill = FieldFill.INSERT)
    private String createByNo;

    @ApiModelProperty("创建人")
    @TableField(value = "create_by_name", fill = FieldFill.INSERT)
    private String createByName;

    @ApiModelProperty("更新时间")
    @TableField(value = "update_time", fill = FieldFill.INSERT_UPDATE)
    private Date updateTime;

    @ApiModelProperty("更新用户")
    @TableField(value = "update_by_no", fill = FieldFill.UPDATE)
    private String updateByNo;

    @ApiModelProperty("更新人")
    @TableField(value = "update_by_name", fill = FieldFill.UPDATE)
    private String updateByName;

    @Override
    public Serializable pkVal() {
        return this.id;
    }
}
