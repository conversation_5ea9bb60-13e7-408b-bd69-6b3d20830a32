package com.gcl.psmis.framework.mbg.entity;

import com.baomidou.mybatisplus.annotation.*;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.*;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * <p>
 * 投资模型-设备成本
 * </p>
 *
 * <AUTHOR>
 * @since 2025-06-06
 */
@Builder
@AllArgsConstructor
@NoArgsConstructor
@Getter
@Setter
@Accessors(chain = true)
@TableName("t_model_device_cost")
@ApiModel(value = "TModelDeviceCostEntity对象", description = "投资模型-设备成本")
public class TModelDeviceCostEntity extends Model<TModelDeviceCostEntity> {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty("主键id")
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    @ApiModelProperty("设备类型(组件功率)")
    @TableField("device_type")
    private String deviceType;

    @ApiModelProperty("安装形式")
    @TableField("Install_form")
    private String installForm;

    @ApiModelProperty("建档日期范围")
    @TableField("created_time_str")
    private String createdTimeStr;

    @ApiModelProperty("组件成本(元/w)")
    @TableField("assembly_cost")
    private BigDecimal assemblyCost;

    @ApiModelProperty("支架成本(元/w)")
    @TableField("bracket_cost")
    private BigDecimal bracketCost;

    @ApiModelProperty("并网箱成本(元/w)")
    @TableField("grid_cost")
    private BigDecimal gridCost;

    @ApiModelProperty("逆变器成本(元/w)")
    @TableField("inverter_cost")
    private BigDecimal inverterCost;

    @ApiModelProperty("电气辅材成本(元/w)")
    @TableField("fc_cost")
    private BigDecimal fcCost;

    @ApiModelProperty("创建时间")
    @TableField(value = "create_time", fill = FieldFill.INSERT)
    private Date createTime;

    @ApiModelProperty("创建人工号")
    @TableField(value = "create_by_no", fill = FieldFill.INSERT)
    private String createByNo;

    @ApiModelProperty("创建用户名称")
    @TableField(value = "create_by_name", fill = FieldFill.INSERT)
    private String createByName;

    @ApiModelProperty("更新时间")
    @TableField(value = "update_time", fill = FieldFill.INSERT_UPDATE)
    private Date updateTime;

    @ApiModelProperty("更新人工号")
    @TableField(value = "update_by_no", fill = FieldFill.UPDATE)
    private String updateByNo;

    @ApiModelProperty("更新人姓名")
    @TableField(value = "update_by_name", fill = FieldFill.UPDATE)
    private String updateByName;

    @Override
    public Serializable pkVal() {
        return this.id;
    }
}
