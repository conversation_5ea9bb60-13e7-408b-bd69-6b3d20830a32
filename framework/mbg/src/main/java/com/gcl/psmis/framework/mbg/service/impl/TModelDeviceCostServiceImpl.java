package com.gcl.psmis.framework.mbg.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.gcl.psmis.framework.mbg.entity.TModelDeviceCostEntity;
import com.gcl.psmis.framework.mbg.mapper.TModelDeviceCostMapper;
import com.gcl.psmis.framework.mbg.service.TModelDeviceCostService;
import org.springframework.stereotype.Service;

/**
 * <p>
 * 投资模型-设备成本 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-06-06
 */
@Service
public class TModelDeviceCostServiceImpl extends ServiceImpl<TModelDeviceCostMapper, TModelDeviceCostEntity> implements TModelDeviceCostService {

}
