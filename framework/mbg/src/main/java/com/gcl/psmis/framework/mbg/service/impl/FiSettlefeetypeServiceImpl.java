package com.gcl.psmis.framework.mbg.service.impl;

import com.baomidou.dynamic.datasource.annotation.DS;
import com.gcl.psmis.framework.mbg.entity.FiSettlefeetypeEntity;
import com.gcl.psmis.framework.mbg.mapper.FiSettlefeetypeMapper;
import com.gcl.psmis.framework.mbg.service.FiSettlefeetypeService;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springframework.stereotype.Service;

/**
 * <p>
 * 结算费用类型标准 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-04-28
 */
@Service
@DS("xyg")
public class FiSettlefeetypeServiceImpl extends ServiceImpl<FiSettlefeetypeMapper, FiSettlefeetypeEntity> implements FiSettlefeetypeService {

}
