package com.gcl.psmis.framework.mbg.entity;

import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.*;
import lombok.experimental.Accessors;

/**
 * <p>
 * 电站表
 * </p>
 *
 * <AUTHOR>
 * @since 2024-09-09
 */
@Builder
@AllArgsConstructor
@NoArgsConstructor
@Getter
@Setter
@Accessors(chain = true)
@TableName("t_power_station")
@ApiModel(value = "TPowerStationEntity对象", description = "电站表")
public class TPowerStationEntity extends Model<TPowerStationEntity> {

    private static final long serialVersionUID = 1L;

    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    @ApiModelProperty("电站地址")
    @TableField("address")
    private String address;

    @ApiModelProperty("经度")
    @TableField("lng")
    private BigDecimal lng;

    @ApiModelProperty("纬度")
    @TableField("lat")
    private BigDecimal lat;

    @ApiModelProperty("装机容量W")
    @TableField("capacity")
    private Integer capacity;

    @ApiModelProperty("并网审核通过时间")
    @TableField("power_check_time")
    private Date powerCheckTime;

    @ApiModelProperty("建转运时间")
    @TableField("power_time")
    private Date powerTime;

    @ApiModelProperty("鑫阳光有效发电首日,完工审核通过时间之后，电站连续三天大于10度（每次鑫阳光点击更新）")
    @TableField("sun_powertime")
    private Date sunPowertime;

    @ApiModelProperty("鑫阳光rmq同步过来的有效发电首日")
    @TableField("start_powertime")
    private Date startPowertime;

    @ApiModelProperty("完工审核通过日期（鑫阳光）")
    @TableField("completed_approval_time")
    private Date completedApprovalTime;

    @ApiModelProperty("智维有效发电时间（最准确），完工审核通过时间之后，电站连续三天大于10度")
    @TableField("real_sun_powertime")
    private Date realSunPowertime;

    @ApiModelProperty("终端用户id")
    @TableField("user_id")
    private Long userId;

    @ApiModelProperty("经销商code")
    @TableField("dealer_code")
    private String dealerCode;

    @ApiModelProperty("屋顶编码(场景类型)")
    @TableField("roof_type_code")
    private String roofTypeCode;

    @ApiModelProperty("省份id")
    @TableField("province_id")
    private Long provinceId;

    @ApiModelProperty("城市id")
    @TableField("city_id")
    private Long cityId;

    @ApiModelProperty("地区id")
    @TableField("region_id")
    private Long regionId;

    @ApiModelProperty("乡镇id")
    @TableField("town_id")
    private Long townId;

    @ApiModelProperty("电站编号（鑫阳光）")
    @TableField("ps_code")
    private String psCode;

    @ApiModelProperty("电站备注")
    @TableField("remarks")
    private String remarks;

    @ApiModelProperty("电站备注最新时间")
    @TableField("remarks_time")
    private Date remarksTime;

    @ApiModelProperty("是否有贷款0:无 1有")
    @TableField("is_loan")
    private Integer isLoan;

    @ApiModelProperty("0自发自用余电上网 1自发自用余电不上网 2全额上网")
    @TableField("use_type")
    private Integer useType;

    @ApiModelProperty("电站类型 -1测试电站  1户用  2工商业 ")
    @TableField("ps_type")
    private Integer psType;

    @ApiModelProperty("贷款类型编码")
    @TableField("loan_type_code")
    private String loanTypeCode;

    @ApiModelProperty("贷款单号")
    @TableField("loan_no")
    private String loanNo;

    @ApiModelProperty("创建来源 1.监控系统 2.报装系统 3.天合富家")
    @TableField("create_source")
    private Integer createSource;

    @ApiModelProperty("合作公司编号（资方编号）")
    @TableField("company_no")
    private String companyNo;

    @ApiModelProperty("合作公司名称（资方名称）")
    @TableField("company_name")
    private String companyName;

    @ApiModelProperty("执行并网任务的结果备注")
    @TableField("powertime_remark")
    private String powertimeRemark;

    @ApiModelProperty("安装时间")
    @TableField("init_time")
    private Date initTime;

    @ApiModelProperty("所属片区")
    @TableField("area")
    private String area;

    @ApiModelProperty("所属省公司")
    @TableField("province_company_code")
    private String provinceCompanyCode;

    @ApiModelProperty("所属省公司Name")
    @TableField("province_company_name")
    private String provinceCompanyName;

    @ApiModelProperty("组件单片功率")
    @TableField("singlepower")
    private Integer singlepower;

    @ApiModelProperty("组件块数")
    @TableField("actualnum")
    private Integer actualnum;

    @ApiModelProperty("作废时间")
    @TableField("discard_date")
    private Date discardDate;

    @ApiModelProperty("作废状态 1已作废0未作废")
    @TableField("is_discard")
    private Integer isDiscard;

    @ApiModelProperty("自身规则生成")
    @TableField("ps_number")
    private String psNumber;

    @ApiModelProperty("epc")
    @TableField("epc")
    private String epc;

    @ApiModelProperty("产权方、项目公司")
    @TableField("owne")
    private String owne;

    @ApiModelProperty("开发商、经销商、代理商、服务商")
    @TableField("dev")
    private String dev;

    @ApiModelProperty("运维商")
    @TableField("server")
    private String server;

    @ApiModelProperty("业务员")
    @TableField("emp_no")
    private String empNo;

    @ApiModelProperty("制单人")
    @TableField("operation_agent")
    private String operationAgent;

    @ApiModelProperty("产品编号")
    @TableField("product_code")
    private String productCode;

    @ApiModelProperty("产品名称")
    @TableField("product_name")
    private String productName;

    @ApiModelProperty("业务编码")
    @TableField("business_code")
    private String businessCode;

    @ApiModelProperty("业务名称")
    @TableField("business_name")
    private String businessName;

    @ApiModelProperty("发电户号")
    @TableField("account_number")
    private String accountNumber;

    @ApiModelProperty("电表号")
    @TableField("elec_meter")
    private String elecMeter;

    @ApiModelProperty("企业主体")
    @TableField("business_entity")
    private String businessEntity;

    @ApiModelProperty("企业名称")
    @TableField("enterprise_name")
    private String enterpriseName;

    @ApiModelProperty("工商业组织机构代码")
    @TableField("org_code")
    private String orgCode;

    @ApiModelProperty("工商业联系电话")
    @TableField("phone_num")
    private String phoneNum;

    @ApiModelProperty("并网申请时间")
    @TableField("power_time_apply")
    private Date powerTimeApply;

    @ApiModelProperty("并网申请回执时间")
    @TableField("power_time_back")
    private Date powerTimeBack;

    @ApiModelProperty("是否定制支架")
    @TableField("customized_stand")
    private Integer customizedStand;

    @ApiModelProperty("工商业电站全称")
    @TableField("name")
    private String name;

    @ApiModelProperty("工商业电站简称")
    @TableField("abb_name")
    private String abbName;

    @ApiModelProperty("全容并网时间")
    @TableField("all_power_time")
    private Date allPowerTime;

    @ApiModelProperty("代理商code")
    @TableField("service_org_code")
    private String serviceOrgCode;

    @ApiModelProperty("服务商负责人id")
    @TableField("service_emp_id")
    private String serviceEmpId;

    @ApiModelProperty("过验状态0未过验  1过验")
    @TableField("ar_flag")
    private Integer arFlag;

    @ApiModelProperty("组件数量")
    @TableField("component_num")
    private Integer componentNum;

    @ApiModelProperty("组件料号")
    @TableField("component_code")
    private String componentCode;

    @ApiModelProperty("组件功率")
    @TableField("component_pac")
    private Integer componentPac;

    @ApiModelProperty("组件型号")
    @TableField("component_mode")
    private String componentMode;

    @ApiModelProperty("创建时间")
    @TableField(value = "create_time", fill = FieldFill.INSERT)
    private Date createTime;

    @ApiModelProperty("创建人工号")
    @TableField(value = "create_by_no", fill = FieldFill.INSERT)
    private String createByNo;

    @ApiModelProperty("创建人姓名")
    @TableField(value = "create_by_name", fill = FieldFill.INSERT)
    private String createByName;

    @ApiModelProperty("更新人工号")
    @TableField(value = "update_by_no", fill = FieldFill.UPDATE)
    private String updateByNo;

    @ApiModelProperty("更新人姓名")
    @TableField(value = "update_by_name", fill = FieldFill.UPDATE)
    private String updateByName;

    @ApiModelProperty("更新时间")
    @TableField(value = "update_time", fill = FieldFill.INSERT_UPDATE)
    private Date updateTime;

    @ApiModelProperty("创建人")
    @TableField("create_by")
    private String createBy;

    @ApiModelProperty("删除标识")
    @TableField("del_flag")
    private Integer delFlag;

    @ApiModelProperty("0户用公司 1开发公司")
    @TableField("dev_type")
    private Integer devType;

    @ApiModelProperty("商机编号")
    @TableField("intentno")
    private String intentno;

    @ApiModelProperty("安装商")
    @TableField("install")
    private String install;

    @ApiModelProperty("电站来源：1：鑫阳光 2：智维 3:ec")
    @TableField("ps_source")
    private Integer psSource;

    @ApiModelProperty("项目公司编号")
    @TableField("project_company_code")
    private String projectCompanyCode;

    @ApiModelProperty("变电所编码")
    @TableField("substation_code")
    private String substationCode;

    @ApiModelProperty("备案类型：2企业备案，1自然人备案")
    @TableField("filing_type")
    private Integer filingType;

    @ApiModelProperty("运维商code 关联t_operation")
    @TableField("operation_code")
    private String operationCode;

    @Override
    public Serializable pkVal() {
        return this.id;
    }
}
