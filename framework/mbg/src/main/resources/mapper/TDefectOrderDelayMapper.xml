<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.gcl.psmis.framework.mbg.mapper.TDefectOrderDelayMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.gcl.psmis.framework.mbg.entity.TDefectOrderDelayEntity">
        <id column="id" property="id" />
        <result column="order_id" property="orderId" />
        <result column="delay_code" property="delayCode" />
        <result column="delay_day" property="delayDay" />
        <result column="reason" property="reason" />
        <result column="status" property="status" />
        <result column="user_no" property="userNo" />
        <result column="del_flag" property="delFlag" />
        <result column="create_time" property="createTime" />
        <result column="create_by_no" property="createByNo" />
        <result column="create_by_name" property="createByName" />
        <result column="update_time" property="updateTime" />
        <result column="update_by_no" property="updateByNo" />
        <result column="update_by_name" property="updateByName" />
        <result column="f_flowtaskid" property="fFlowtaskid" />
        <result column="f_flowid" property="fFlowid" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id, order_id, delay_code, delay_day, reason, status, user_no, del_flag, create_time, create_by_no, create_by_name, update_time, update_by_no, update_by_name, f_flowtaskid, f_flowid
    </sql>

</mapper>
