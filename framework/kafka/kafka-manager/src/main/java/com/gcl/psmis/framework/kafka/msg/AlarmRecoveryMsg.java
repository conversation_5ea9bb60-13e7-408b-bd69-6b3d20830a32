package com.gcl.psmis.framework.kafka.msg;

import cn.hutool.json.JSONUtil;
import com.gcl.psmis.framework.redis.enums.IotRedisKeyEnum;
import lombok.*;
import lombok.experimental.Accessors;

import java.io.Serializable;

/**
* @className: AlarmRecoveryMsg
* @author: xinan.yuan
* @create: 2023/8/17 10:12
* @description: 告警恢复消息
*/
@Builder
@AllArgsConstructor
@NoArgsConstructor
@Getter
@Setter
@Accessors(chain = true)
public class AlarmRecoveryMsg implements Serializable {

    private static final long serialVersionUID = 1L;


    //缺陷代码
    private String faultCode;

    //电站id
    private Long psId;

    private String alarmId;

    //恢复时间 yyyy-MM-dd HH:mm:ss
    private String recoveryTime;

    private Integer erFlag;

    //逆变器序列号
    private String sn;

    public void checkData(){
        if (this.getFaultCode() == null ||this.getSn()==null||this.getPsId()==null) {
            throw new RuntimeException("告警恢复数据缺失！"+ JSONUtil.toJsonStr(this));
        }
    }
    public String recoveryRedisKey(){
        return String.format(IotRedisKeyEnum.IOT_RECOVERY_ALARM_DATE.getKey(),
                this.getSn(),
                this.getPsId(),
                this.getFaultCode(),
                this.getAlarmId());
    }

    public String alarmRedisKey(){
        return String.format(IotRedisKeyEnum.IOT_ALARM_ID.getKey(),
                this.getSn(),
                this.getPsId(),
                this.getFaultCode());
    }

}
