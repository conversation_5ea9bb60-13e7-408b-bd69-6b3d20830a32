package com.gcl.psmis.framework.kafka.msg;

import cn.hutool.json.JSONUtil;
import com.fasterxml.jackson.annotation.JsonAlias;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.gcl.psmis.framework.redis.enums.IotRedisKeyEnum;
import lombok.*;
import lombok.experimental.Accessors;

import java.io.Serializable;

@Builder
@AllArgsConstructor
@NoArgsConstructor
@Getter
@Setter
@Accessors(chain = true)
public class PointAlarmMsg implements Serializable {

    private static final long serialVersionUID = 1L;

    //告警id
    private String alarmId;

    //电站编号
    private String psCode;

    //告警代码
    private String alarmCode;

    //厂家码
    private String factoryCode;

    //设备类型
    private Integer deviceType;

    //设备sn
    private String sn;


    //开始时间 yyyy-MM-dd HH:mm:ss
    private String startTime;

    //结束时间
    private String endTime;

    //遥测值 0恢复 1告警
    @JsonProperty("data")
    private Integer data;

    //协议类型 0工商业电站协议 1储能电站协议
    private Integer tplType;




    public boolean checkData() {
        return  (this.getAlarmCode() == null
                || this.getDeviceType() == null
                || this.getPsCode() == null || this.getSn() == null
                || this.getStartTime()==null
        ||this.getFactoryCode()==null||this.data==null||this.tplType==null);
    }


    /**
     * @desc 告警redis key
     * @date 2023/8/31 15:33
     * @param
     * @return {@link String}
     */
    public String alarmRedisKey(){
        return String.format(IotRedisKeyEnum.IOT_ALARM_ID.getKey(),
                this.getSn(),
                this.getPsCode(),
                this.getAlarmCode(),
                this.getDeviceType());
    }




}
