package com.gcl.psmis.framework.kafka.msg;

import cn.hutool.json.JSONUtil;
import com.gcl.psmis.framework.redis.enums.IotRedisKeyEnum;
import lombok.*;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.util.HashMap;
import java.util.Map;

@Builder
@AllArgsConstructor
@NoArgsConstructor
@Getter
@Setter
@Accessors(chain = true)
public class AlarmMsg implements Serializable {

    private static final long serialVersionUID = 1L;

    //告警id
    private String alarmId;


    private String townId;


    private String provinceId;


    private String cityId;


    private String regionId;


    //缺陷代码
    private String faultCode;

    //设备类型
    private String deviceType;

    //逆变器序列号
    private String sn;


    //开始时间 yyyy-MM-dd HH:mm:ss
    private String startTime;

    //电站id
    private Long psId;

    //电站类型
    private Integer psType;

    //规则名称
    private String ruleName;

    //规则code
    private String ruleCode;

    //告警类型
    private String alarmType;

    //告警等级
    private Integer alarmLevel;

    private Integer erFlag;

    public void checkData() {
        if (this.getAlarmId() == null
                || this.getAlarmLevel() == null
                || this.getFaultCode() == null
                || this.getAlarmType() == null
                || this.getDeviceType() == null
                || this.getPsId() == null || this.getSn() == null
                || this.getStartTime()==null
        ||this.getProvinceId()==null||
        this.getCityId()==null||
        this.getRegionId()==null) {
            throw new RuntimeException("告警必填数据缺失！" + JSONUtil.toJsonStr(this));
        }
        if(this.getErFlag()==null){
            this.erFlag=0;
        }
    }

    /**
     * @desc 告警恢复redis key
     * @date 2023/8/31 15:38
     * @param
     * @return {@link String}
     */
    public String recoveryRedisKey(){
        return String.format(IotRedisKeyEnum.IOT_RECOVERY_ALARM_DATE.getKey(),
                this.getSn(),
                this.getPsId(),
                this.getFaultCode(),
                this.getAlarmId());
    }

    /**
     * @desc 告警redis key
     * @date 2023/8/31 15:33
     * @param
     * @return {@link String}
     */
    public String alarmRedisKey(){
        return String.format(IotRedisKeyEnum.IOT_ALARM_ID.getKey(),
                this.getSn(),
                this.getPsId(),
                this.getFaultCode());
    }

    public String alarmUpFlagRedisKey(){
        return String.format(IotRedisKeyEnum.IOT_ALARM_UP_FLAG.getKey(),
                this.getSn(), this.getPsId(), this.getFaultCode());
    }


}
