package com.gcl.psmis.framework.kafka.msg;

import lombok.*;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.util.List;
import java.util.Map;

/**
 * @className: XygBinlogMsg
 * @author: xinan.yuan
 * @create: 2023/10/20 11:37
 * @description: 鑫阳光binlog信息
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
@Getter
@Setter
@Accessors(chain = true)
public class XygBinlogMsg implements Serializable {

    private static final long serialVersionUID = 1L;

    private List<Map<String, String>> data;
    private List<Map<String, String>> old;
    private String database;
    private long es;
    private int id;
    private boolean isDdl;
    private List<String> pkNames;
    private String sql;
    private String table;
    private long ts;
    private String type;


}
