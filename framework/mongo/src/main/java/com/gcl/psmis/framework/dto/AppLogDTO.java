package com.gcl.psmis.framework.dto;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.springframework.data.mongodb.core.mapping.Document;

import java.util.Date;

/**
 * <AUTHOR>
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
@Document(collection = "app_log")
public class AppLogDTO {
    /**
     * 手机号
     */
    private String telNum;
    /**
     * 电站商机编号
     */
    private String intentno;
    /**
     * 操作人
     */
    private String name;
    /**
     * 接口描述
     */
    private String msg;
    /**
     * 接口名称
     */
    private String interfaceName;
    /**
     * 操作时间
     */
    private Date date;
}
