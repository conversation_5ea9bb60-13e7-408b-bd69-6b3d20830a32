package com.gcl.psmis.framework.repository;


import com.gcl.psmis.framework.dto.message.MsgLogCollection;
import org.springframework.data.mongodb.repository.MongoRepository;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * @className MsgLogCollectionRepository
 * <AUTHOR>
 * @create 2021/8/2 17:45
 * @desc 消息日志 集合
 **/
@Repository
public interface MsgLogCollectionRepository extends MongoRepository<MsgLogCollection,String> {

    List<MsgLogCollection> findMsgLogCollectionsByReadFlag(Integer readFlag);

    List<MsgLogCollection> findByPsIdAndSnAndMsgType(String psId,String sn,String msgType);

}
