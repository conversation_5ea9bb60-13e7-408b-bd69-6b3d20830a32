package com.gcl.psmis.framework.dto.message;

import lombok.*;
import lombok.experimental.Accessors;

import java.io.Serializable;

@Builder
@AllArgsConstructor
@NoArgsConstructor
@Getter
@Setter
@Accessors(chain = true)
public class AlarmAuditMsgLog implements Serializable {

    private static final long serialVersionUID = 1L;

    //告警id
    private String alarmId;

    //告警规则编号
    private String ruleCode;

    //缺陷代码
    private String faultCode;


    //逆变器序列号
    private String sn;


    //开始时间
    private String startTime;

    //电站id
    private Long psId;
}
