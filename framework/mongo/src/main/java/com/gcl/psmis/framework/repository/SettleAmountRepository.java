package com.gcl.psmis.framework.repository;

import com.gcl.psmis.framework.dto.settle.SettleAmountDTO;
import com.gcl.psmis.framework.dto.settle.SettleDetailDTO;
import org.springframework.data.mongodb.repository.MongoRepository;
import org.springframework.stereotype.Repository;

/**
 * <AUTHOR> @Description
 * @Date 2024/1/26
 * @Param
 **/
@Repository
public interface SettleAmountRepository extends MongoRepository<SettleAmountDTO, String> {

    @Override
    SettleAmountDTO insert(SettleAmountDTO entity);


}
