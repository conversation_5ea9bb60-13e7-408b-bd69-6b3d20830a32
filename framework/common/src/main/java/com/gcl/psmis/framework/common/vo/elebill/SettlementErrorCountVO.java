package com.gcl.psmis.framework.common.vo.elebill;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @ClassName AettlementErrorCountVO
 * @description: TODO
 * @date 2024年05月27日
 * @version: 1.0
 */
@Data
public class SettlementErrorCountVO {

    @ApiModelProperty("企业备案统计")
    private SettlementErrVO EnterpriseErrVO;

    @ApiModelProperty("个人备案统计")
    private SettlementErrVO PersonalErrVO;

    @Data
    public static class SettlementErrVO {

        @ApiModelProperty("欠费户数")
        private Integer arrearsNo;

        @ApiModelProperty("欠费月数")
        private Integer arrearsMonth;

        @ApiModelProperty("欠费金额")
        private BigDecimal arrearsAmount;

    }
}
