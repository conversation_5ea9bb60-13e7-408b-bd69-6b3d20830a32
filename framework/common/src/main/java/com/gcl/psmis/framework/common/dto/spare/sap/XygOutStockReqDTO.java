package com.gcl.psmis.framework.common.dto.spare.sap;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.List;

/**
* @className: XygOutStockReqDTO
* @author: xinan.yuan
* @create: 2024/9/5 14:56
* @description: 前置仓储备量出库
*/
@Builder
@AllArgsConstructor
@NoArgsConstructor
@Data
@ApiModel(value = "XygOutStockReqDTO", description = "前置仓储备量出库入参")
public class XygOutStockReqDTO implements Serializable {

    @ApiModelProperty("仓库编码")
    private String wareHouseNo;

    @ApiModelProperty("物料列表")
    private List<Material> materialList;

    @NoArgsConstructor
    @Data
    public static class Material implements Serializable {

        @ApiModelProperty("物料编码")
        private String materialCode;

        @ApiModelProperty("出库数量")
        private Integer outStockNum;

    }
}
