package com.gcl.psmis.framework.common.vo.zy;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

/**
 * @ClassName: SelectRelevantInformationVO
 * @data: 2023/10/25 13:37
 * @author: <EMAIL>
 * @description:
 */
@Data
public class SelectRelevantInformationVO implements Serializable {

    @ApiModelProperty("产权方ID")
    private Long propertyOrgID;
    @ApiModelProperty("产权方")
    private String propertyOrg;

    @ApiModelProperty("EPC方ID")
    private Long epcOrgID;
    @ApiModelProperty("EPC方")
    private String epcOrg;


    @ApiModelProperty("开发商ID")
    private Long developerAgentID;
    @ApiModelProperty("开发商")
    private String developerAgent;

    @ApiModelProperty("安装商ID")
    private Long installOrgID;
    @ApiModelProperty("安装商")
    private String installOrg;

    @ApiModelProperty("运维商ID")
    private Long serviceOrgID;
    @ApiModelProperty("运维商")
    private String serviceOrg;
}
