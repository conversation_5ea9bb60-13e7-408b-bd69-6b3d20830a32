package com.gcl.psmis.framework.common.req.et.operational;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotNull;

/**
* @className: DeviationAnalysisReq
* @author: xinan.yuan
* @create: 2024/4/19 14:37
* @description: 运营指标偏差分析入参
*/
@Data
@ApiModel(value = "运营指标偏差分析入参",description = "运营指标偏差分析入参")
public class DeviationAnalysisReq {

    @ApiModelProperty("id")
    @NotNull(message = "id不能为空")
    private Long id;

    @ApiModelProperty("偏差分析")
    private String deviationAnalyse;


}
