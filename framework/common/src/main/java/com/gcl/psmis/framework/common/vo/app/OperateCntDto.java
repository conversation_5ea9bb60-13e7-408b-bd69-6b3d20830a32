package com.gcl.psmis.framework.common.vo.app;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;

@Data
@ApiModel
public class OperateCntDto {
    @ApiModelProperty("商机编号")
    @NotBlank(message = "商机编号不能为空!")
    private String intentno;

    @ApiModelProperty("0:近7日,1:上个月,2:本年,3:总")
    @NotNull(message = "类型不能为空!")
    private Integer type;
}
