package com.gcl.psmis.framework.common.req.vr.require;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * @className: UserResourceTreeQueryReq
 * @author: wuchenyu
 * @description:
 * @date: 2024/3/21 11:24
 * @version: 1.0
 */
@Data
@ApiModel
public class UserResourceTreeQueryReq {

    private Integer pageNum = 1;

    private Integer pageSize = 10;

    @ApiModelProperty("需求id")
    private Long requireId;

    @ApiModelProperty("策略")
    private String strategyType;

    @ApiModelProperty("资源名称或用户名称")
    private String query;
}
