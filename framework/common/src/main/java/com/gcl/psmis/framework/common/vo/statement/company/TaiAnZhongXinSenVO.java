package com.gcl.psmis.framework.common.vo.statement.company;

import com.gcl.psmis.framework.common.annotation.ExportConfig;
import com.gcl.psmis.framework.common.annotation.ImportConfig;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * project: TaiAnZhongXinSenVO
 * Powered 2024-06-12 14:45:02
 * 泰安众鑫森电力有限公司
 * <AUTHOR>
 * @version 1.0
 * @since 1.8
 */
@Data
public class TaiAnZhongXinSenVO {

    @ApiModelProperty("项目名称")
    @ImportConfig(value="项目名称")
    private String companyName;

    @ApiModelProperty("项目编号")
    @ImportConfig(value="项目编号")
    private String accountNumber;

    @ApiModelProperty("电费年月")
    @ImportConfig(value="电费年月")
    private String powerTime;

    @ApiModelProperty("发电量")
    @ImportConfig(value="发电量")
    private String electricQuantity;

    @ApiModelProperty("上网电量")
    @ImportConfig(value="上网电量")
    private String etd;

    @ApiModelProperty("应付购电费")
    @ImportConfig(value="应付购电费")
    private String yesTaxMoney;

    @ApiModelProperty("应付购电税额")
    @ImportConfig(value="应付购电税额")
    private String taxDiff;

}
