package com.gcl.psmis.framework.common.vo.et.manageCost;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;

/**
 * {@code @Author:} wq
 * {@code @Version:} 1.0
 */
@Builder
@AllArgsConstructor
@NoArgsConstructor
@Data
@ApiModel(value = "ManageExpVO", description = "")
public class ManageExpVO {

    @ApiModelProperty("公司")
    private String companyName;

    @ApiModelProperty("报表数据类型: MONTH--月数据,TOTAL--累计数据,PART--不含本部的累计数据")
    private String reportType;

    @ApiModelProperty("累计（万元）")
    private BigDecimal totalExp;

    @ApiModelProperty("累计预算（万元）")
    private BigDecimal totalBudget;

    @ApiModelProperty("全年预算（万元）")
    private BigDecimal yearTotalBudget;

    @ApiModelProperty("累计预算使用率（%）")
    private BigDecimal usageRate;

    @ApiModelProperty("全年预算使用率")
    private BigDecimal yearUsageRate;

}
