package com.gcl.psmis.framework.common.vo.insurance;

import com.alibaba.excel.annotation.ExcelIgnore;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.gcl.psmis.framework.common.annotation.ExportConfig;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.springframework.format.annotation.DateTimeFormat;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * <AUTHOR>
 * @date 2024/8/5 14:30
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@ApiModel(description = "公司赔付")
public class CompanyPayoutPageVO implements Serializable {
    @ApiModelProperty("主键id")
    @ExcelIgnore
    private Long id;

    @ApiModelProperty("案件编号")
    @ExportConfig(width = 150, value = "案件编号")
    private String caseNum;

    @ApiModelProperty("运维商名称")
    @ExportConfig(width = 150, value = "运维商")
    private String maintenanceCompanyName;

    @ApiModelProperty("省公司名称")
    @ExportConfig(width = 150, value = "省公司")
    private String provinceCompanyName;

    @ApiModelProperty("业务员")
    @ExportConfig(width = 150, value = "业务员")
    private String businessPerson;

    @ApiModelProperty("上报人")
    @ExportConfig(width = 150, value = "上报人")
    private String createByName;

    @ApiModelProperty("涉及电站数")
    @ExportConfig(width = 150, value = "涉及电站数")
    private Integer stationNum;

    @ApiModelProperty("公司赔付金额（元）")
    @ExportConfig(width = 150, value = "公司赔付金额（元）")
    private BigDecimal amount;

    @ApiModelProperty("创建日期")
    @ExportConfig(width = 150, value = "创建日期", dateFormat = "yyyy/MM/dd")
    @DateTimeFormat(pattern = "yyyy/MM/dd")
    @JsonFormat(pattern = "yyyy/MM/dd", timezone = "GMT+8")
    private Date submitTime;

}
