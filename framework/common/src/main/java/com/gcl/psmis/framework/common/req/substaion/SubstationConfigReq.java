package com.gcl.psmis.framework.common.req.substaion;

import com.gcl.psmis.framework.common.req.base.BasePageReq;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

@Data
@ApiModel(value = "SubstationConfigReq", description = "SubstationConfigReq")
public class SubstationConfigReq extends BasePageReq {

    @ApiModelProperty("主键")
    private List<Long> ids;

    @ApiModelProperty("供电所编码")
    private String code;

    @ApiModelProperty("供电所名称")
    private String name;

    @ApiModelProperty("省Id")
    private Long provinceId;

    @ApiModelProperty("市Id")
    private Long cityId;

    @ApiModelProperty("区id")
    private Long areaId;

    @ApiModelProperty("镇id")
    private Long townId;

    @ApiModelProperty("周期:1-月度.2-季度,3年度")
    private Integer cycle;

    @ApiModelProperty("启用状态:0-禁用,1-启用")
    private Integer enable;

    @ApiModelProperty("导出列表所选列")
    private List<String> colNameList;

}