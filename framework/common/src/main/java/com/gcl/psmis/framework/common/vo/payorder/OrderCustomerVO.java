package com.gcl.psmis.framework.common.vo.payorder;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * @Author: sunjiaxing
 * @CreateTime: 2024-06-26  18:32
 * @Description:
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class OrderCustomerVO {
    private Long ordercustomerid;
    private String stationno;
    private String accountname;
    private String bankno;
    private String bankname;
    private String guestname;
    private String powergridmergetype;
    private Integer ownertype;
    private Long cusid;
    private String phone;
    private String cuscode;
    private Integer totalquantity;
    private String orgcode;
    private Integer custype;
    private Integer devType;

}
