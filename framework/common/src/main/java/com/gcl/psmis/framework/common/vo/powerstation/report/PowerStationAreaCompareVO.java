package com.gcl.psmis.framework.common.vo.powerstation.report;

import com.gcl.psmis.framework.common.annotation.ExportConfig;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;

@Data
@ApiModel
public class PowerStationAreaCompareVO {

    @ApiModelProperty("地址id")
    private Long addressId;


    @ApiModelProperty("区")
    @ExportConfig(width = 150, value = "区")
    private String address;

    @ApiModelProperty("平均单瓦功率")
    @ExportConfig(width = 150, value = "平均实时单瓦功率")
    private BigDecimal avgSinglePower;

    @ApiModelProperty("加权实时单瓦功率")
    @ExportConfig(width = 150, value = "加权实时单瓦功率")
    private BigDecimal weightSinglePower;


    @ApiModelProperty("平均有效小时利用数")
    @ExportConfig(width = 150, value = "平均有效利用小时数")
    private BigDecimal avgHours;

    @ApiModelProperty("加权有效利用小时数")
    @ExportConfig(width = 150, value = "加权有效利用小时数")
    private BigDecimal weightHours;

    @ApiModelProperty("低效占比")
    @ExportConfig(width = 150, value = "低效占比")
    private String inefficiencyRatio;

}
