package com.gcl.psmis.framework.common.dto.otherOut;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;

/**
 * <AUTHOR>
 */
@Builder
@AllArgsConstructor
@NoArgsConstructor
@Data
@ApiModel(value = "ProcessResultDto")
public class ProcessResultDto {

    @ApiModelProperty("出库单号")
    @NotBlank(message = "出库单号不能为空")
    private String outCode;

    @ApiModelProperty("审核结果: 0-通过,1-驳回")
    @NotNull(message = "审核结果不能为空")
    private Integer resultFlag;

    @ApiModelProperty("审核意见")
    private String msg;

}
