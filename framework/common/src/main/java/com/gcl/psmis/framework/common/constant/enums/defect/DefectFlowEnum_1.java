package com.gcl.psmis.framework.common.constant.enums.defect;

import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.Objects;

/**
 * 缺陷流程版本1
 */
@AllArgsConstructor
@Getter
public enum DefectFlowEnum_1 implements BaseEnum{


    DR_1("DR_1", "缺陷登记"),

    DR_2("DR_2", "缺陷审核"),

    DR_3("DR_3", "缺陷处理"),

    DR_4("DR_4", "缺陷验收");


    /**
     * 枚举值
     */
    private final String code;

    /**
     * 枚举描述
     */
    private final String desc;

    public static String getDescByCode(String code) {
        for (DefectFlowEnum_1 typeEnum : DefectFlowEnum_1.values()) {
            if (Objects.equals(code, typeEnum.code)) {
                return typeEnum.getDesc();
            }
        }
        return null;
    }

    @Override
    public String getCode() {
        return code;
    }

    @Override
    public String getDesc() {
        return desc;
    }
}
