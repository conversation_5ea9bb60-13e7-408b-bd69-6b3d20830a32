package com.gcl.psmis.framework.common.vo.et.self;

import com.gcl.psmis.framework.common.annotation.ExportConfig;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

/**
 * @ClassName: SelfRateVO
 * @data: 2024/4/17 9:17
 * @author: <EMAIL>
 * @description:
 */
@Builder
@AllArgsConstructor
@NoArgsConstructor
@Data
@ApiModel(value = "SelfRateVO", description = "单体自发自用比例数据")
public class SelfRateVO implements Serializable {

    @ApiModelProperty("主键id")
    private Long id;

    @ApiModelProperty("电站id")
    private Long psId;

    @ApiModelProperty("电站简称")
    @ExportConfig(width = 150, value = "电站简称")
    private String psAbbName;

    @ApiModelProperty("年累实际")
    @ExportConfig(width = 150, value = "年累实际(%)")
    private Long yearSumActual;

    @ApiModelProperty("过会目标")
    @ExportConfig(width = 150, value = "过会目标(%)")
    private Long target;

    @ApiModelProperty("完成率")
    @ExportConfig(width = 150, value = "完成率(%)")
    private Long completeRate;

    @ApiModelProperty("偏差分析")
    @ExportConfig(width = 150, value = "偏差分析")
    private String deviationAnalyse;

}
