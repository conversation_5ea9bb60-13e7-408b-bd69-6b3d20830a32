package com.gcl.psmis.framework.mq.event.bz;

import lombok.Data;

/**
 * @ClassName MultiSceneVO
 * @Description 多场景
 * <AUTHOR>
 * @Date 2024/5/8 9:46
 **/
@Data
public class MultiSceneVO {
    //场景拼接
    private String montage;
    //总功率
    private Integer power;
    //屋顶功率
    private Integer housePower;
    //屋顶类型
    private String houseRoof;
    //倾角
    private String angle;
    //单片功率
    private Integer singlePower;

    //设计方案编号
    private String designCode;
    //设计方案名称
    private String designName;
}
