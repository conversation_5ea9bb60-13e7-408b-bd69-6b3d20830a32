package com.gcl.psmis.framework.mq.event;

import com.gcl.psmis.framework.mq.event.mc.McTaskInfo;
import lombok.*;

import java.util.Date;
import java.util.List;

/**
* @className: BaseDelayedEvent
* @author: plx
* @description: 延迟消息基类
*/
@Data
@NoArgsConstructor
public class BaseDelayedEvent {

    /**
     * 初始延迟时间
     */
    private long initDelaySec;

    /**
     * 消息的目标发送时间
     */
    private Date targetTime;

    /**
     * 周期设定为 为一天
     */
    public static final long SECONDS_IN_A_DAY = 24 * 60 * 60;

    public BaseDelayedEvent(Date targetTime) {
        this.targetTime = targetTime;
        recalculate();
    }

    /**
     * 实时计算圈数、剩余秒数以及初始延迟时间
     */
    public void recalculate() {
        long totalSeconds = Math.max(0,(targetTime.getTime() - System.currentTimeMillis()) / 1000);  //处理为负数的情况
        long cycNum = totalSeconds / SECONDS_IN_A_DAY;
        long surplusSec = totalSeconds % SECONDS_IN_A_DAY;
        this.initDelaySec = cycNum == 0 ? surplusSec : SECONDS_IN_A_DAY;
    }
}
