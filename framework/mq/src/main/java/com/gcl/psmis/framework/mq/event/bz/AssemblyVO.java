package com.gcl.psmis.framework.mq.event.bz;

import cn.hutool.core.util.NumberUtil;
import lombok.Data;

import java.io.Serializable;

@Data
public class AssemblyVO implements Serializable {
    private static final long serialVersionUID = 123456789L;
    //组件料号
    private String assemblyNum;
    //功率
    private Integer zuPower;
    //组件类型
    private String crystal;
    //块数
    private Integer zuKuai;
    //总功率
    private Integer zuTotalPower;
    //逆变器序列号
    private String equipmentNo;
    //规格
    private String materialSpeci;
}
