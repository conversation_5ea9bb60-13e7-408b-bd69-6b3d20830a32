package com.gcl.psmis.framework.mq.event.bz;

import lombok.Data;

import java.io.Serializable;

@Data
public class InverterVO implements Serializable {
    private static final long serialVersionUID = 123456789L;
    //逆变器料号、物料编码
    private String inverterNum;
    //参考功率
    private Integer power;
    //品牌
    private String brand;
    //相位,1-单相，2-多相
    private String inverterConnType;
    //验证码
    private String authCode;
    //逆变器编号
    private String sn;
    //逆变器序列号
    private String equipmentNo;
    //规格
    private String materialSpeci;
    //并网点编号
    private String gridCode;
    //装机容量
    private Integer capacity;
    //物料名称
    private String materialName;
    private String materialUnit;
}


