package com.gcl.psmis.framework.mq.event;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Date;

/**
* @className: PsPowerTimeEvent
* @author: xinan.yuan
* @create: 2023/8/1 20:38
* @description: 电站并网时间消息
*/
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class PsPowerTimeEvent {


    //电站id
    private Long psId;

    //并网时间
    private Date powerTime;



}